name: Python test and linting

on: 
  push:
  pull_request:


jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.10", "3.11", "3.12"]
      fail-fast: false  # Continue with other versions even if one fails

    steps:
      - uses: actions/checkout@v4
  
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
  
      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: 2.1.1
          virtualenvs-create: true
          virtualenvs-in-project: true
  
      - name: Load cached venv
        id: cached-poetry-dependencies
        uses: actions/cache@v3
        with:
          path: .venv
          key: venv-${{ runner.os }}-${{ matrix.python-version }}-${{ hashFiles('**/poetry.lock') }}-v1 # change version in case of prooblems with resolving environment
  
      - name: Install dependencies
        run: poetry install --with dev
  
      - name: Lint src with pylint
        run: |
          poetry run pylint src

      - name: Lint tests with pylint
        run: |
          poetry run pylint --rcfile tests/.pylintrc tests
          
      - name: Run tests with pytest
        run: |
          poetry run pytest tests -v --ignore=tests/x_examples

  publish-doc:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"
      
      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: 2.1.1
          virtualenvs-create: true
          virtualenvs-in-project: true
    
      - name: Load cached venv
        id: cached-poetry-dependencies
        uses: actions/cache@v3
        with:
          path: .venv
          key: venv-${{ runner.os }}-3.10-${{ hashFiles('**/poetry.lock') }}-v1 # change version in case of prooblems with resolving environment
    
      - name: Install dependencies
        run: poetry install --with dev
      
      - name: pdoc
        run: |
          mkdir -p docs/_build/html
          poetry run pdoc3 --html --force -o docs/api src/secfsdstools
          cp README.md ./docs/read.md

      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: "docs"
          destination_dir: doc_${{ github.ref_name }}
          enable_jekyll: true

      - name: Trigger GitHub pages rebuild
        run: |
          curl --fail --request POST \
            --url https://api.github.com/repos/${{ github.repository }}/pages/builds \
            --header "Authorization: Bearer $USER_TOKEN"
        env:
          USER_TOKEN: ${{ secrets.GITHUB_TOKEN }}

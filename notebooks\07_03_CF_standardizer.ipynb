{"cells": [{"cell_type": "code", "execution_count": 1, "id": "85c29dd4-27dc-4861-bfa6-5c56e3aa58be", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "# ensure that all columns are shown and that colum content is not cut\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.max_colwidth', None)\n", "pd.set_option('display.width',1000)\n", "pd.set_option('display.max_rows', 500) # ensure that all rows are shown"]}, {"cell_type": "markdown", "id": "b6109579-ebbe-4258-9ec4-f3255c9c32d6", "metadata": {}, "source": ["# `CashFlowStandardizer`"]}, {"cell_type": "markdown", "id": "83754c90-c7e1-4057-8a8d-9f01bc884131", "metadata": {"tags": []}, "source": ["<span style=\"color: #FF8C00;\">==========================================================</span>\n", "\n", "**If you find this tool useful, a sponsorship would be greatly appreciated!**\n", "\n", "**https://github.com/sponsors/HansjoergW**\n", "\n", "How to get in touch\n", "\n", "* Found a bug: https://github.com/HansjoergW/sec-fincancial-statement-data-set/issues\n", "* Have a remark: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/general\n", "* Have an idea: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/ideas\n", "* Have a question: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/q-a\n", "* Have something to show: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/show-and-tell\n", "\n", "<span style=\"color: #FF8C00;\">==========================================================</span>"]}, {"cell_type": "markdown", "id": "1f4c69ae-dd2e-4b70-9c4c-0788792576bb", "metadata": {}, "source": ["In the `07_00_stanardizer_basics.ipynb` we looked at the basic principles of the standardizer. And now we are going to explore the details of the `CashFlowStandardizer`."]}, {"cell_type": "markdown", "id": "bc6364c8-0ab6-49fa-86b3-2c051f1b5d8f", "metadata": {}, "source": ["## Main Goal\n", "The main Goal of the `CashFlowStandardizer` is to provide a consilidated, standardized view that contains the main positions of an cash flow statement.\n", "\n", "The current implementation tries to find/calculate the values for the following positions:\n", "\n", "<pre>\n", "        NetCashProvidedByUsedInOperatingActivities\n", "          CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations\n", "          NetCashProvidedByUsedInOperatingActivitiesContinuingOperations\n", "              DepreciationDepletionAndAmortization\n", "              DeferredIncomeTaxExpenseBenefit\n", "              ShareBasedCompensation\n", "              IncreaseDecreaseInAccountsPayable\n", "              IncreaseDecreaseInAccruedLiabilities\n", "              InterestPaidNet\n", "              IncomeTaxesPaidNet\n", "\n", "        NetCashProvidedByUsedInInvestingActivities\n", "            CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations\n", "            NetCashProvidedByUsedInInvestingActivitiesContinuingOperations\n", "              PaymentsToAcquirePropertyPlantAndEquipment\n", "              ProceedsFromSaleOfPropertyPlantAndEquipment\n", "              PaymentsToAcquireInvestments\n", "              ProceedsFromSaleOfInvestments\n", "              PaymentsToAcquireBusinessesNetOfCashAcquired\n", "              ProceedsFromDivestitureOfBusinessesNetOfCashDivested\n", "              PaymentsToAcquireIntangibleAssets\n", "              ProceedsFromSaleOfIntangibleAssets\n", "\n", "        NetCashProvidedByUsedInFinancingActivities\n", "            CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations\n", "            NetCashProvidedByUsedInFinancingActivitiesContinuingOperations\n", "              ProceedsFromIssuanceOfCommonStock\n", "              ProceedsFromStockOptionsExercised\n", "              PaymentsForRepurchaseOfCommonStock\n", "              ProceedsFromIssuanceOfDebt\n", "              RepaymentsOfDebt\n", "              PaymentsOfDividends\n", "\n", "\n", "        EffectOfExchangeRateFinal\n", "        CashPeriodIncreaseDecreaseIncludingExRateEffectFinal\n", "\n", "        CashAndCashEquivalentsEndOfPeriod\n", "</pre>\n", "\n", "**Note:**\n", "- EffectOfExchangeRateFinal is the final value for different EffectOnExchangeRate related tags\n", "- CashPeriodIncreaseDecreaseIncludingExRateEffectFinal is the final value for Increase/Decrease of Cash\n", "- CashAndCashEquivalentsEndOfPeriod: only Cash at EndOfPeriod is shown.\n", "\n", "The CashFlow is way more straight forward than the IncomeStatement of the  BalanceSheet, when it comes to fill in and calculate gaps."]}, {"cell_type": "markdown", "id": "c83fb1c5-a9d9-4f3f-aceb-9edca099d66a", "metadata": {}, "source": ["## Prepare the dataset"]}, {"cell_type": "markdown", "id": "8028c20e-7844-4d49-ab43-133603817d53", "metadata": {}, "source": ["As input, we are going to use the dataset which was created with the `06_bulk_data_processing_deep_dive.ipynb`. That dataset contains all available data for the Cash Flow statements. The path to this dataset - on my machine - is either `set/parallel/CF/joined` or `set/serial/CF/joined` depending whether it was produced with the faster parallel or slower serial processing approach.\n", "\n", "The data is already filtered for 10-K and 10-Q reports. Moreover, the following filters were applied as well: `ReportPeriodRawFilter`, `MainCoregRawFilter`, `OfficialTagsOnlyRawFilter`, `USDOnlyRawFilter`. The dataset is already joined, so we can use it directly with the `CashFlowStandardizer`.\n", "\n", "Of course, if you prefer another dataset, for instance all data of a few companies, or all data of a single year, feel free to do so.\n", "\n", "    # As an alternative, using the data of a single year\n", "    from secfsdstools.d_container.databagmodel import JoinedDataBag\n", "    from secfsdstools.e_collector.zipcollecting import ZipCollector\n", "    from secfsdstools.u_usecases.bulk_loading import default_postloadfilter\n", "\n", "    collector = ZipCollector.get_zip_by_names(names=[\"2022q1.zip\", \"2022q2.zip\", \"2022q3.zip\", \"2022q4.zip\"], \n", "                                              forms_filter=[\"10-K\", \"10-Q\"],                                        \n", "                                              stmt_filter=[\"CF\"], post_load_filter=default_postloadfilter)\n", "\n", "    all_is_joinedbag: JoinedDataBag = collector.collect().join()\n", "    \n", "    from secfsdstools.d_container.databagmodel import JoinedDataBag\n", "    from secfsdstools.f_standardize.cf_standardize import CashFlowStandardizer\n", "\n", "    cf_standardizer = CashFlowStandardizer()\n", "\n", "    # standardize the data\n", "    all_cf_joinedbag.present(cf_standardizer)    \n", "\n", "This might take 30-60 seconds, depending on your hardware."]}, {"cell_type": "code", "execution_count": 2, "id": "7c4c7669-020a-40a5-b260-e5f0f5b03ebd", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-04 06:44:49,215 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-04 06:45:02,179 [INFO] standardizing  start PRE processing ...\n", "2025-02-04 06:45:19,882 [INFO] standardizing  start MAIN processing ...\n", "2025-02-04 06:45:20,262 [INFO] standardizing  start POST processing ...\n", "2025-02-04 06:45:20,531 [INFO] standardizing  start FINALIZE ...\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>adsh</th>\n", "      <th>cik</th>\n", "      <th>name</th>\n", "      <th>form</th>\n", "      <th>fye</th>\n", "      <th>fy</th>\n", "      <th>fp</th>\n", "      <th>date</th>\n", "      <th>filed</th>\n", "      <th>coreg</th>\n", "      <th>report</th>\n", "      <th>ddate</th>\n", "      <th>qtrs</th>\n", "      <th>NetCashProvidedByUsedInOperatingActivitiesContinuingOperations</th>\n", "      <th>NetCashProvidedByUsedInFinancingActivitiesContinuingOperations</th>\n", "      <th>NetCashProvidedByUsedInInvestingActivitiesContinuingOperations</th>\n", "      <th>NetCashProvidedByUsedInOperatingActivities</th>\n", "      <th>NetCashProvidedByUsedInFinancingActivities</th>\n", "      <th>NetCashProvidedByUsedInInvestingActivities</th>\n", "      <th>CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations</th>\n", "      <th>CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations</th>\n", "      <th>CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations</th>\n", "      <th>EffectOfExchangeRateFinal</th>\n", "      <th>CashPeriodIncreaseDecreaseIncludingExRateEffectFinal</th>\n", "      <th>CashAndCashEquivalentsEndOfPeriod</th>\n", "      <th>DepreciationDepletionAndAmortization</th>\n", "      <th>DeferredIncomeTaxExpenseBenefit</th>\n", "      <th>ShareBasedCompensation</th>\n", "      <th>IncreaseDecreaseInAccountsPayable</th>\n", "      <th>IncreaseDecreaseInAccruedLiabilities</th>\n", "      <th>InterestPaidNet</th>\n", "      <th>IncomeTaxesPaidNet</th>\n", "      <th>PaymentsToAcquirePropertyPlantAndEquipment</th>\n", "      <th>ProceedsFromSaleOfPropertyPlantAndEquipment</th>\n", "      <th>PaymentsToAcquireInvestments</th>\n", "      <th>ProceedsFromSaleOfInvestments</th>\n", "      <th>PaymentsToAcquireBusinessesNetOfCashAcquired</th>\n", "      <th>ProceedsFromDivestitureOfBusinessesNetOfCashDivested</th>\n", "      <th>PaymentsToAcquireIntangibleAssets</th>\n", "      <th>ProceedsFromSaleOfIntangibleAssets</th>\n", "      <th>ProceedsFromIssuanceOfCommonStock</th>\n", "      <th>ProceedsFromStockOptionsExercised</th>\n", "      <th>PaymentsForRepurchaseOfCommonStock</th>\n", "      <th>ProceedsFromIssuanceOfDebt</th>\n", "      <th>RepaymentsOfDebt</th>\n", "      <th>PaymentsOfDividends</th>\n", "      <th>BaseOpAct_error</th>\n", "      <th>BaseOpAct_cat</th>\n", "      <th>BaseFinAct_error</th>\n", "      <th>BaseFinAct_cat</th>\n", "      <th>BaseInvAct_error</th>\n", "      <th>BaseInvAct_cat</th>\n", "      <th>NetCashContOp_error</th>\n", "      <th>NetCashContOp_cat</th>\n", "      <th>CashEoP_error</th>\n", "      <th><PERSON>Eo<PERSON>_cat</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>199922</th>\n", "      <td>**********-21-001168</td>\n", "      <td>1089297</td>\n", "      <td>NOVAGANT CORP</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2004.0</td>\n", "      <td>FY</td>\n", "      <td>2004-12-31</td>\n", "      <td>********</td>\n", "      <td></td>\n", "      <td>6</td>\n", "      <td>********</td>\n", "      <td>4</td>\n", "      <td>-2.100000e+01</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>-2.100000e+01</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-2.100000e+01</td>\n", "      <td>0.000000e+00</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>199923</th>\n", "      <td>**********-21-001172</td>\n", "      <td>1089297</td>\n", "      <td>NOVAGANT CORP</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2005.0</td>\n", "      <td>FY</td>\n", "      <td>2005-12-31</td>\n", "      <td>********</td>\n", "      <td></td>\n", "      <td>6</td>\n", "      <td>20051231</td>\n", "      <td>4</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>199926</th>\n", "      <td>**********-21-001180</td>\n", "      <td>1089297</td>\n", "      <td>NOVAGANT CORP</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2006.0</td>\n", "      <td>FY</td>\n", "      <td>2006-12-31</td>\n", "      <td>********</td>\n", "      <td></td>\n", "      <td>6</td>\n", "      <td>20061231</td>\n", "      <td>4</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>199927</th>\n", "      <td>**********-21-001182</td>\n", "      <td>1089297</td>\n", "      <td>NOVAGANT CORP</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2007.0</td>\n", "      <td>FY</td>\n", "      <td>2007-12-31</td>\n", "      <td>********</td>\n", "      <td></td>\n", "      <td>6</td>\n", "      <td>20071231</td>\n", "      <td>4</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>199928</th>\n", "      <td>**********-21-001184</td>\n", "      <td>1089297</td>\n", "      <td>NOVAGANT CORP</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2008.0</td>\n", "      <td>FY</td>\n", "      <td>2008-12-31</td>\n", "      <td>********</td>\n", "      <td></td>\n", "      <td>6</td>\n", "      <td>20081231</td>\n", "      <td>4</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>161057</th>\n", "      <td>0001193125-24-281288</td>\n", "      <td>40704</td>\n", "      <td>GENERAL MILLS INC</td>\n", "      <td>10-Q</td>\n", "      <td>0531</td>\n", "      <td>2025.0</td>\n", "      <td>Q2</td>\n", "      <td>2024-11-30</td>\n", "      <td>20241218</td>\n", "      <td></td>\n", "      <td>8</td>\n", "      <td>20241130</td>\n", "      <td>2</td>\n", "      <td>1.774700e+09</td>\n", "      <td>4.221000e+08</td>\n", "      <td>-3.059000e+08</td>\n", "      <td>1.774700e+09</td>\n", "      <td>4.221000e+08</td>\n", "      <td>-3.059000e+08</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-16100000.0</td>\n", "      <td>1.874800e+09</td>\n", "      <td>2.292800e+09</td>\n", "      <td>2.691000e+08</td>\n", "      <td>NaN</td>\n", "      <td>4.660000e+07</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-3.012000e+08</td>\n", "      <td>900000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>33800000.0</td>\n", "      <td>-600400000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-6.758000e+08</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>162336</th>\n", "      <td>0001558370-24-016392</td>\n", "      <td>23217</td>\n", "      <td>CONAGRA BRANDS INC.</td>\n", "      <td>10-Q</td>\n", "      <td>0531</td>\n", "      <td>2025.0</td>\n", "      <td>Q2</td>\n", "      <td>2024-11-30</td>\n", "      <td>20241219</td>\n", "      <td></td>\n", "      <td>6</td>\n", "      <td>20241130</td>\n", "      <td>2</td>\n", "      <td>7.542000e+08</td>\n", "      <td>-4.252000e+08</td>\n", "      <td>-3.660000e+08</td>\n", "      <td>7.542000e+08</td>\n", "      <td>-4.252000e+08</td>\n", "      <td>-3.660000e+08</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-4600000.0</td>\n", "      <td>-4.160000e+07</td>\n", "      <td>3.740000e+07</td>\n", "      <td>1.966000e+08</td>\n", "      <td>NaN</td>\n", "      <td>2.980000e+07</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-2.154000e+08</td>\n", "      <td>2700000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-230600000.0</td>\n", "      <td>76800000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-64000000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-3.351000e+08</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>160502</th>\n", "      <td>0001070235-24-000156</td>\n", "      <td>1070235</td>\n", "      <td>BLACKBERRY LTD</td>\n", "      <td>10-Q</td>\n", "      <td>0229</td>\n", "      <td>2025.0</td>\n", "      <td>Q3</td>\n", "      <td>2024-11-30</td>\n", "      <td>20241220</td>\n", "      <td></td>\n", "      <td>8</td>\n", "      <td>20241130</td>\n", "      <td>3</td>\n", "      <td>-2.500000e+07</td>\n", "      <td>3.000000e+06</td>\n", "      <td>2.200000e+07</td>\n", "      <td>-2.500000e+07</td>\n", "      <td>3.000000e+06</td>\n", "      <td>2.200000e+07</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000e+00</td>\n", "      <td>2.000000e+08</td>\n", "      <td>3.900000e+07</td>\n", "      <td>NaN</td>\n", "      <td>2.100000e+07</td>\n", "      <td>-7.000000e+06</td>\n", "      <td>5000000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-3.000000e+06</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-6000000.0</td>\n", "      <td>NaN</td>\n", "      <td>3000000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>-0.000000e+00</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>160102</th>\n", "      <td>0000950170-24-134973</td>\n", "      <td>1341439</td>\n", "      <td>ORACLE CORP</td>\n", "      <td>10-Q</td>\n", "      <td>0531</td>\n", "      <td>2025.0</td>\n", "      <td>Q2</td>\n", "      <td>2024-11-30</td>\n", "      <td>20241210</td>\n", "      <td></td>\n", "      <td>7</td>\n", "      <td>20241130</td>\n", "      <td>2</td>\n", "      <td>8.731000e+09</td>\n", "      <td>-1.647000e+09</td>\n", "      <td>-6.553000e+09</td>\n", "      <td>8.731000e+09</td>\n", "      <td>-1.647000e+09</td>\n", "      <td>-6.553000e+09</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-44000000.0</td>\n", "      <td>4.870000e+08</td>\n", "      <td>1.094100e+10</td>\n", "      <td>2.927000e+09</td>\n", "      <td>-601000000.0</td>\n", "      <td>2.176000e+09</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-6.273000e+09</td>\n", "      <td>NaN</td>\n", "      <td>-636000000.0</td>\n", "      <td>NaN</td>\n", "      <td>-0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>307000000.0</td>\n", "      <td>NaN</td>\n", "      <td>-300000000.0</td>\n", "      <td>NaN</td>\n", "      <td>-9.700000e+09</td>\n", "      <td>-2.221000e+09</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>163328</th>\n", "      <td>0000909832-24-000079</td>\n", "      <td>909832</td>\n", "      <td>COSTCO WHOLESALE CORP /NEW</td>\n", "      <td>10-Q</td>\n", "      <td>0831</td>\n", "      <td>2025.0</td>\n", "      <td>Q1</td>\n", "      <td>2024-11-30</td>\n", "      <td>20241219</td>\n", "      <td></td>\n", "      <td>7</td>\n", "      <td>20241130</td>\n", "      <td>1</td>\n", "      <td>3.260000e+09</td>\n", "      <td>-1.193000e+09</td>\n", "      <td>-9.850000e+08</td>\n", "      <td>3.260000e+09</td>\n", "      <td>-1.193000e+09</td>\n", "      <td>-9.850000e+08</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-81000000.0</td>\n", "      <td>1.001000e+09</td>\n", "      <td>1.090700e+10</td>\n", "      <td>5.480000e+08</td>\n", "      <td>NaN</td>\n", "      <td>4.630000e+08</td>\n", "      <td>2.601000e+09</td>\n", "      <td>NaN</td>\n", "      <td>44000000.0</td>\n", "      <td>NaN</td>\n", "      <td>-1.264000e+09</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-207000000.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-5.150000e+08</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>346106 rows × 56 columns</p>\n", "</div>"], "text/plain": ["                        adsh      cik                        name  form   fye      fy  fp       date     filed coreg  report     ddate  qtrs  NetCashProvidedByUsedInOperatingActivitiesContinuingOperations  NetCashProvidedByUsedInFinancingActivitiesContinuingOperations  NetCashProvidedByUsedInInvestingActivitiesContinuingOperations  NetCashProvidedByUsedInOperatingActivities  NetCashProvidedByUsedInFinancingActivities  NetCashProvidedByUsedInInvestingActivities  CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations  CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations  CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations  EffectOfExchangeRateFinal  CashPeriodIncreaseDecreaseIncludingExRateEffectFinal  CashAndCashEquivalentsEndOfPeriod  DepreciationDepletionAndAmortization  DeferredIncomeTaxExpenseBenefit  ShareBasedCompensation  IncreaseDecreaseInAccountsPayable  IncreaseDecreaseInAccruedLiabilities  InterestPaidNet  IncomeTaxesPaidNet  \\\n", "199922  **********-21-001168  1089297               NOVAGANT CORP  10-K  1231  2004.0  FY 2004-12-31  ********             6  ********     4                                                   -2.100000e+01                                                    0.000000e+00                                                    0.000000e+00                               -2.100000e+01                                0.000000e+00                                0.000000e+00                                                            0.0                                                            0.0                                                            0.0                        0.0                                         -2.100000e+01                       0.000000e+00                                   NaN                              NaN                     NaN                                NaN                                   NaN              NaN                 0.0   \n", "199923  **********-21-001172  1089297               NOVAGANT CORP  10-K  1231  2005.0  FY 2005-12-31  ********             6  20051231     4                                                    0.000000e+00                                                    0.000000e+00                                                    0.000000e+00                                0.000000e+00                                0.000000e+00                                0.000000e+00                                                            0.0                                                            0.0                                                            0.0                        0.0                                          0.000000e+00                       0.000000e+00                                   NaN                              NaN                     NaN                                NaN                                   NaN              NaN                 0.0   \n", "199926  **********-21-001180  1089297               NOVAGANT CORP  10-K  1231  2006.0  FY 2006-12-31  ********             6  20061231     4                                                    0.000000e+00                                                    0.000000e+00                                                    0.000000e+00                                0.000000e+00                                0.000000e+00                                0.000000e+00                                                            0.0                                                            0.0                                                            0.0                        0.0                                          0.000000e+00                       0.000000e+00                                   NaN                              NaN                     NaN                                NaN                                   NaN              NaN                 0.0   \n", "199927  **********-21-001182  1089297               NOVAGANT CORP  10-K  1231  2007.0  FY 2007-12-31  ********             6  20071231     4                                                    0.000000e+00                                                    0.000000e+00                                                    0.000000e+00                                0.000000e+00                                0.000000e+00                                0.000000e+00                                                            0.0                                                            0.0                                                            0.0                        0.0                                          0.000000e+00                       0.000000e+00                                   NaN                              NaN                     NaN                                NaN                                   NaN              NaN                 0.0   \n", "199928  **********-21-001184  1089297               NOVAGANT CORP  10-K  1231  2008.0  FY 2008-12-31  ********             6  20081231     4                                                    0.000000e+00                                                    0.000000e+00                                                    0.000000e+00                                0.000000e+00                                0.000000e+00                                0.000000e+00                                                            0.0                                                            0.0                                                            0.0                        0.0                                          0.000000e+00                       0.000000e+00                                   NaN                              NaN                     NaN                                NaN                                   NaN              NaN                 0.0   \n", "...                      ...      ...                         ...   ...   ...     ...  ..        ...       ...   ...     ...       ...   ...                                                             ...                                                             ...                                                             ...                                         ...                                         ...                                         ...                                                            ...                                                            ...                                                            ...                        ...                                                   ...                                ...                                   ...                              ...                     ...                                ...                                   ...              ...                 ...   \n", "161057  0001193125-24-281288    40704           GENERAL MILLS INC  10-Q  0531  2025.0  Q2 2024-11-30  20241218             8  20241130     2                                                    1.774700e+09                                                    4.221000e+08                                                   -3.059000e+08                                1.774700e+09                                4.221000e+08                               -3.059000e+08                                                            0.0                                                            0.0                                                            0.0                -16100000.0                                          1.874800e+09                       2.292800e+09                          2.691000e+08                              NaN            4.660000e+07                                NaN                                   NaN              NaN                 NaN   \n", "162336  0001558370-24-016392    23217         CONAGRA BRANDS INC.  10-Q  0531  2025.0  Q2 2024-11-30  20241219             6  20241130     2                                                    7.542000e+08                                                   -4.252000e+08                                                   -3.660000e+08                                7.542000e+08                               -4.252000e+08                               -3.660000e+08                                                            0.0                                                            0.0                                                            0.0                 -4600000.0                                         -4.160000e+07                       3.740000e+07                          1.966000e+08                              NaN            2.980000e+07                                NaN                                   NaN              NaN                 NaN   \n", "160502  0001070235-24-000156  1070235              BLACKBERRY LTD  10-Q  0229  2025.0  Q3 2024-11-30  20241220             8  20241130     3                                                   -2.500000e+07                                                    3.000000e+06                                                    2.200000e+07                               -2.500000e+07                                3.000000e+06                                2.200000e+07                                                            0.0                                                            0.0                                                            0.0                        0.0                                          0.000000e+00                       2.000000e+08                          3.900000e+07                              NaN            2.100000e+07                      -7.000000e+06                             5000000.0              NaN                 NaN   \n", "160102  0000950170-24-134973  1341439                 ORACLE CORP  10-Q  0531  2025.0  Q2 2024-11-30  20241210             7  20241130     2                                                    8.731000e+09                                                   -1.647000e+09                                                   -6.553000e+09                                8.731000e+09                               -1.647000e+09                               -6.553000e+09                                                            0.0                                                            0.0                                                            0.0                -44000000.0                                          4.870000e+08                       1.094100e+10                          2.927000e+09                     -601000000.0            2.176000e+09                                NaN                                   NaN              NaN                 NaN   \n", "163328  0000909832-24-000079   909832  COSTCO WHOLESALE CORP /NEW  10-Q  0831  2025.0  Q1 2024-11-30  20241219             7  20241130     1                                                    3.260000e+09                                                   -1.193000e+09                                                   -9.850000e+08                                3.260000e+09                               -1.193000e+09                               -9.850000e+08                                                            0.0                                                            0.0                                                            0.0                -81000000.0                                          1.001000e+09                       1.090700e+10                          5.480000e+08                              NaN            4.630000e+08                       2.601000e+09                                   NaN       44000000.0                 NaN   \n", "\n", "        PaymentsToAcquirePropertyPlantAndEquipment  ProceedsFromSaleOfPropertyPlantAndEquipment  PaymentsToAcquireInvestments  ProceedsFromSaleOfInvestments  PaymentsToAcquireBusinessesNetOfCashAcquired  ProceedsFromDivestitureOfBusinessesNetOfCashDivested  PaymentsToAcquireIntangibleAssets  ProceedsFromSaleOfIntangibleAssets  ProceedsFromIssuanceOfCommonStock  ProceedsFromStockOptionsExercised  PaymentsForRepurchaseOfCommonStock  ProceedsFromIssuanceOfDebt  RepaymentsOfDebt  PaymentsOfDividends  BaseOpAct_error  BaseOpAct_cat  BaseFinAct_error  BaseFinAct_cat  BaseInvAct_error  BaseInvAct_cat  NetCashContOp_error  NetCashContOp_cat  CashEoP_error  CashEoP_cat  \n", "199922                                         NaN                                          NaN                           NaN                            NaN                                           NaN                                                   NaN                                NaN                                 NaN                                NaN                                NaN                                 NaN                         NaN               NaN                  NaN              0.0            0.0               0.0             0.0               0.0             0.0                  0.0                0.0            0.0          0.0  \n", "199923                                         NaN                                          NaN                           NaN                            NaN                                           NaN                                                   NaN                                NaN                                 NaN                                NaN                                NaN                                 NaN                         NaN               NaN                  NaN              0.0            0.0               0.0             0.0               0.0             0.0                  0.0                0.0            0.0          0.0  \n", "199926                                         NaN                                          NaN                           NaN                            NaN                                           NaN                                                   NaN                                NaN                                 NaN                                NaN                                NaN                                 NaN                         NaN               NaN                  NaN              0.0            0.0               0.0             0.0               0.0             0.0                  0.0                0.0            0.0          0.0  \n", "199927                                         NaN                                          NaN                           NaN                            NaN                                           NaN                                                   NaN                                NaN                                 NaN                                NaN                                NaN                                 NaN                         NaN               NaN                  NaN              0.0            0.0               0.0             0.0               0.0             0.0                  0.0                0.0            0.0          0.0  \n", "199928                                         NaN                                          NaN                           NaN                            NaN                                           NaN                                                   NaN                                NaN                                 NaN                                NaN                                NaN                                 NaN                         NaN               NaN                  NaN              0.0            0.0               0.0             0.0               0.0             0.0                  0.0                0.0            0.0          0.0  \n", "...                                            ...                                          ...                           ...                            ...                                           ...                                                   ...                                ...                                 ...                                ...                                ...                                 ...                         ...               ...                  ...              ...            ...               ...             ...               ...             ...                  ...                ...            ...          ...  \n", "161057                               -3.012000e+08                                     900000.0                           NaN                            NaN                                           NaN                                                   NaN                                NaN                                 NaN                                NaN                         33800000.0                        -600400000.0                         NaN               NaN        -6.758000e+08              0.0            0.0               0.0             0.0               0.0             0.0                  0.0                0.0            0.0          0.0  \n", "162336                               -2.154000e+08                                    2700000.0                           NaN                            NaN                                  -230600000.0                                            76800000.0                                NaN                                 NaN                                NaN                                NaN                         -64000000.0                         NaN               NaN        -3.351000e+08              0.0            0.0               0.0             0.0               0.0             0.0                  0.0                0.0            0.0          0.0  \n", "160502                               -3.000000e+06                                          NaN                           NaN                            NaN                                           NaN                                                   NaN                         -6000000.0                                 NaN                          3000000.0                                NaN                                 NaN                         0.0     -0.000000e+00                  NaN              0.0            0.0               0.0             0.0               0.0             0.0                  0.0                0.0            0.0          0.0  \n", "160102                               -6.273000e+09                                          NaN                  -636000000.0                            NaN                                          -0.0                                                   NaN                                NaN                                 NaN                        307000000.0                                NaN                        -300000000.0                         NaN     -9.700000e+09        -2.221000e+09              0.0            0.0               0.0             0.0               0.0             0.0                  0.0                0.0            0.0          0.0  \n", "163328                               -1.264000e+09                                          NaN                           NaN                            NaN                                           NaN                                                   NaN                                NaN                                 NaN                                NaN                                NaN                        -207000000.0                         NaN               NaN        -5.150000e+08              0.0            0.0               0.0             0.0               0.0             0.0                  0.0                0.0            0.0          0.0  \n", "\n", "[346106 rows x 56 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from secfsdstools.d_container.databagmodel import JoinedDataBag\n", "from secfsdstools.f_standardize.cf_standardize import CashFlowStandardizer\n", "\n", "print(\"loading data ...\")\n", "all_cf_joinedbag:JoinedDataBag = JoinedDataBag.load(target_path=\"set/parallel/CF/joined\")\n", "cf_standardizer = CashFlowStandardizer()\n", "\n", "# standardize the data\n", "all_cf_joinedbag.present(cf_standardizer)"]}, {"cell_type": "markdown", "id": "f42c9d12-f3ca-40d9-ad34-96560aab033b", "metadata": {}, "source": ["First, we will save the results, including all the logs, so that we can use the data directly in the future, without the need to process it again.<br>"]}, {"cell_type": "code", "execution_count": 3, "id": "47953a5f-b05b-4563-990e-439b62f7d80f", "metadata": {}, "outputs": [], "source": ["import os\n", "target_path = \"standardized/CF\"\n", "os.makedirs(target_path, exist_ok=True)\n", "\n", "cf_standardizer.get_standardize_bag().save(target_path)"]}, {"cell_type": "markdown", "id": "e715bbc9-88e1-482a-8448-f9815d831687", "metadata": {}, "source": ["## Load the dataset\n", "Once the data has been processed and saved, you can load it directly with the following code."]}, {"cell_type": "code", "execution_count": 4, "id": "7f7576ec-6e5a-42ba-bdd0-eabda14e9ee2", "metadata": {"tags": []}, "outputs": [], "source": ["from secfsdstools.f_standardize.standardizing import StandardizedBag\n", "\n", "cf_standardizer_result_bag = StandardizedBag.load(\"standardized/CF\")"]}, {"cell_type": "markdown", "id": "d7e8f196-bf59-45a2-a6e0-5c547a4d7b1b", "metadata": {}, "source": ["## Overview"]}, {"cell_type": "markdown", "id": "3f338fcc-5c17-45d4-886f-3ce551c74820", "metadata": {}, "source": ["Before we dive into what the `CashFlowStandardizer` does in detail, lets get a first impression of the the produced data. First, let us see how many rows we have."]}, {"cell_type": "code", "execution_count": 5, "id": "77709469-52b9-46e0-a334-9fc771f165ae", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cash flow statements:  346106\n", "filed reports:  342641\n"]}], "source": ["print(\"Cash flow statements: \", len(cf_standardizer_result_bag.result_df))\n", "print(\"filed reports: \", len(cf_standardizer_result_bag.result_df.adsh.unique()))"]}, {"cell_type": "markdown", "id": "bf99859f-c312-422b-b6ef-3b5656d56285", "metadata": {}, "source": ["We have about 342'000 reports (which is also about the same number of balance sheets, which we analyzed in notebook 07_01_BS_standardizer)."]}, {"cell_type": "markdown", "id": "56607925-ed18-4ab6-b5c4-c7c7bbf8e454", "metadata": {}, "source": ["Next, a good idea is to look at the `validation_overview_df`. This table gives an idea about the \"quality\" of the dateset based on the summary of the results of the applied validation rules."]}, {"cell_type": "code", "execution_count": 6, "id": "4261f80a-6990-472b-9c06-8a90ecb250ab", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>BaseOpAct_cat</th>\n", "      <th>BaseFinAct_cat</th>\n", "      <th>BaseInvAct_cat</th>\n", "      <th>NetCashContOp_cat</th>\n", "      <th><PERSON>Eo<PERSON>_cat</th>\n", "      <th>BaseOpAct_cat_pct</th>\n", "      <th>BaseFinAct_cat_pct</th>\n", "      <th>BaseInvAct_cat_pct</th>\n", "      <th>NetCashContOp_cat_pct</th>\n", "      <th>CashEoP_cat_pct</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>345882</td>\n", "      <td>346079</td>\n", "      <td>346025</td>\n", "      <td>334571</td>\n", "      <td>333744.0</td>\n", "      <td>99.94</td>\n", "      <td>99.99</td>\n", "      <td>99.98</td>\n", "      <td>96.67</td>\n", "      <td>96.43</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>26</td>\n", "      <td>8</td>\n", "      <td>14</td>\n", "      <td>3358</td>\n", "      <td>NaN</td>\n", "      <td>0.01</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.97</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>4</td>\n", "      <td>1860</td>\n", "      <td>NaN</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.54</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>7</td>\n", "      <td>4</td>\n", "      <td>6</td>\n", "      <td>881</td>\n", "      <td>NaN</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.25</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>100</th>\n", "      <td>187</td>\n", "      <td>13</td>\n", "      <td>57</td>\n", "      <td>5436</td>\n", "      <td>12362.0</td>\n", "      <td>0.05</td>\n", "      <td>0.00</td>\n", "      <td>0.02</td>\n", "      <td>1.57</td>\n", "      <td>3.57</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     BaseOpAct_cat  BaseFinAct_cat  BaseInvAct_cat  NetCashContOp_cat  CashEoP_cat  BaseOpAct_cat_pct  BaseFinAct_cat_pct  BaseInvAct_cat_pct  NetCashContOp_cat_pct  CashEoP_cat_pct\n", "0           345882          346079          346025             334571     333744.0              99.94               99.99               99.98                  96.67            96.43\n", "1               26               8              14               3358          NaN               0.01                0.00                0.00                   0.97              NaN\n", "5                4               2               4               1860          NaN               0.00                0.00                0.00                   0.54              NaN\n", "10               7               4               6                881          NaN               0.00                0.00                0.00                   0.25              NaN\n", "100            187              13              57               5436      12362.0               0.05                0.00                0.02                   1.57             3.57"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["cf_standardizer_result_bag.validation_overview_df"]}, {"cell_type": "markdown", "id": "e9bdfa26-163e-4d78-b7ab-af4f336a4045", "metadata": {}, "source": ["This seems to be quite ok, since we have around 96% of the data in the first two to three categories. As a reminder, Category 0 means it is an exact match, catagory 1 means that it is less than 1 percent off the expected value (see notebook `07_00_standardizer_basics.ipynb` for details)."]}, {"cell_type": "markdown", "id": "bc651e14-9205-458a-b3a7-fff4cd1bda4c", "metadata": {"tags": []}, "source": ["## Analysis on the whole dataset\n", "The following examples are just some ideas to show, what we can do now with the standardized cash flow statement dataset.\n", "\n", "First let us have a look at the distribution of CashPeriodIncreaseDecreaseIncludingExRateEffectFinal with a box plot."]}, {"cell_type": "code", "execution_count": 7, "id": "7be96761-fd1e-4df2-b698-dc6c4d80fbc7", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["{'whiskers': [<matplotlib.lines.Line2D at 0x203d4aa4a00>,\n", "  <matplotlib.lines.Line2D at 0x203d4aa4ca0>],\n", " 'caps': [<matplotlib.lines.Line2D at 0x203d4aa4f40>,\n", "  <matplotlib.lines.Line2D at 0x203d4aa51e0>],\n", " 'boxes': [<matplotlib.lines.Line2D at 0x203d4aa4760>],\n", " 'medians': [<matplotlib.lines.Line2D at 0x203d4aa5480>],\n", " 'fliers': [<matplotlib.lines.Line2D at 0x203d4aa5720>],\n", " 'means': []}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "data = cf_standardizer_result_bag.result_df.CashPeriodIncreaseDecreaseIncludingExRateEffectFinal\n", "data = data[~data.isnull()]\n", "\n", "plt.boxplot(data, vert=False)\n", "#plt.xscale('log') # using a logarithmic scale, we will lose negativ values though"]}, {"cell_type": "markdown", "id": "1421a47d-c717-41cb-ba49-2d030863a144", "metadata": {}, "source": ["Let's figure out, which report has the highest CashPeriodIncreaseDecreaseIncludingExRateEffectFinal and then try to show the history of the CashPeriodIncreaseDecreaseIncludingExRateEffectFinal for that company."]}, {"cell_type": "code", "execution_count": 8, "id": "699bcc8f-96f7-481f-96ad-2b93fb598784", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>adsh</th>\n", "      <th>cik</th>\n", "      <th>name</th>\n", "      <th>form</th>\n", "      <th>fye</th>\n", "      <th>fy</th>\n", "      <th>fp</th>\n", "      <th>date</th>\n", "      <th>filed</th>\n", "      <th>coreg</th>\n", "      <th>report</th>\n", "      <th>ddate</th>\n", "      <th>qtrs</th>\n", "      <th>NetCashProvidedByUsedInOperatingActivitiesContinuingOperations</th>\n", "      <th>NetCashProvidedByUsedInFinancingActivitiesContinuingOperations</th>\n", "      <th>NetCashProvidedByUsedInInvestingActivitiesContinuingOperations</th>\n", "      <th>NetCashProvidedByUsedInOperatingActivities</th>\n", "      <th>NetCashProvidedByUsedInFinancingActivities</th>\n", "      <th>NetCashProvidedByUsedInInvestingActivities</th>\n", "      <th>CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations</th>\n", "      <th>CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations</th>\n", "      <th>CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations</th>\n", "      <th>EffectOfExchangeRateFinal</th>\n", "      <th>CashPeriodIncreaseDecreaseIncludingExRateEffectFinal</th>\n", "      <th>CashAndCashEquivalentsEndOfPeriod</th>\n", "      <th>DepreciationDepletionAndAmortization</th>\n", "      <th>DeferredIncomeTaxExpenseBenefit</th>\n", "      <th>ShareBasedCompensation</th>\n", "      <th>IncreaseDecreaseInAccountsPayable</th>\n", "      <th>IncreaseDecreaseInAccruedLiabilities</th>\n", "      <th>InterestPaidNet</th>\n", "      <th>IncomeTaxesPaidNet</th>\n", "      <th>PaymentsToAcquirePropertyPlantAndEquipment</th>\n", "      <th>ProceedsFromSaleOfPropertyPlantAndEquipment</th>\n", "      <th>PaymentsToAcquireInvestments</th>\n", "      <th>ProceedsFromSaleOfInvestments</th>\n", "      <th>PaymentsToAcquireBusinessesNetOfCashAcquired</th>\n", "      <th>ProceedsFromDivestitureOfBusinessesNetOfCashDivested</th>\n", "      <th>PaymentsToAcquireIntangibleAssets</th>\n", "      <th>ProceedsFromSaleOfIntangibleAssets</th>\n", "      <th>ProceedsFromIssuanceOfCommonStock</th>\n", "      <th>ProceedsFromStockOptionsExercised</th>\n", "      <th>PaymentsForRepurchaseOfCommonStock</th>\n", "      <th>ProceedsFromIssuanceOfDebt</th>\n", "      <th>RepaymentsOfDebt</th>\n", "      <th>PaymentsOfDividends</th>\n", "      <th>BaseOpAct_error</th>\n", "      <th>BaseOpAct_cat</th>\n", "      <th>BaseFinAct_error</th>\n", "      <th>BaseFinAct_cat</th>\n", "      <th>BaseInvAct_error</th>\n", "      <th>BaseInvAct_cat</th>\n", "      <th>NetCashContOp_error</th>\n", "      <th>NetCashContOp_cat</th>\n", "      <th>CashEoP_error</th>\n", "      <th><PERSON>Eo<PERSON>_cat</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>316227</th>\n", "      <td>**********-21-000236</td>\n", "      <td>19617</td>\n", "      <td>JPMORGAN CHASE &amp; CO</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2020.0</td>\n", "      <td>FY</td>\n", "      <td>2020-12-31</td>\n", "      <td>********</td>\n", "      <td></td>\n", "      <td>8</td>\n", "      <td>********</td>\n", "      <td>4</td>\n", "      <td>-7.991000e+10</td>\n", "      <td>5.966450e+11</td>\n", "      <td>-2.619120e+11</td>\n", "      <td>-7.991000e+10</td>\n", "      <td>5.966450e+11</td>\n", "      <td>-2.619120e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>9.155000e+09</td>\n", "      <td>2.639780e+11</td>\n", "      <td>5.276090e+11</td>\n", "      <td>NaN</td>\n", "      <td>-3.981000e+09</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.307700e+10</td>\n", "      <td>7.661000e+09</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5.767500e+10</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-6.517000e+09</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-1.269000e+10</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        adsh    cik                 name  form   fye      fy  fp       date     filed coreg  report     ddate  qtrs  NetCashProvidedByUsedInOperatingActivitiesContinuingOperations  NetCashProvidedByUsedInFinancingActivitiesContinuingOperations  NetCashProvidedByUsedInInvestingActivitiesContinuingOperations  NetCashProvidedByUsedInOperatingActivities  NetCashProvidedByUsedInFinancingActivities  NetCashProvidedByUsedInInvestingActivities  CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations  CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations  CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations  EffectOfExchangeRateFinal  CashPeriodIncreaseDecreaseIncludingExRateEffectFinal  CashAndCashEquivalentsEndOfPeriod  DepreciationDepletionAndAmortization  DeferredIncomeTaxExpenseBenefit  ShareBasedCompensation  IncreaseDecreaseInAccountsPayable  IncreaseDecreaseInAccruedLiabilities  InterestPaidNet  IncomeTaxesPaidNet  \\\n", "316227  **********-21-000236  19617  JPMORGAN CHASE & CO  10-K  1231  2020.0  FY 2020-12-31  ********             8  ********     4                                                   -7.991000e+10                                                    5.966450e+11                                                   -2.619120e+11                               -7.991000e+10                                5.966450e+11                               -2.619120e+11                                                            0.0                                                            0.0                                                            0.0               9.155000e+09                                          2.639780e+11                       5.276090e+11                                   NaN                    -3.981000e+09                     NaN                                NaN                                   NaN     1.307700e+10        7.661000e+09   \n", "\n", "        PaymentsToAcquirePropertyPlantAndEquipment  ProceedsFromSaleOfPropertyPlantAndEquipment  PaymentsToAcquireInvestments  ProceedsFromSaleOfInvestments  PaymentsToAcquireBusinessesNetOfCashAcquired  ProceedsFromDivestitureOfBusinessesNetOfCashDivested  PaymentsToAcquireIntangibleAssets  ProceedsFromSaleOfIntangibleAssets  ProceedsFromIssuanceOfCommonStock  ProceedsFromStockOptionsExercised  PaymentsForRepurchaseOfCommonStock  ProceedsFromIssuanceOfDebt  RepaymentsOfDebt  PaymentsOfDividends  BaseOpAct_error  BaseOpAct_cat  BaseFinAct_error  BaseFinAct_cat  BaseInvAct_error  BaseInvAct_cat  NetCashContOp_error  NetCashContOp_cat  CashEoP_error  CashEoP_cat  \n", "316227                                         NaN                                          NaN                           NaN                   5.767500e+10                                           NaN                                                   NaN                                NaN                                 NaN                                NaN                                NaN                       -6.517000e+09                         NaN               NaN        -1.269000e+10              0.0            0.0               0.0             0.0               0.0             0.0                  0.0                0.0            0.0          0.0  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["cf_standardizer_result_bag.result_df[cf_standardizer_result_bag.result_df.CashPeriodIncreaseDecreaseIncludingExRateEffectFinal == cf_standardizer_result_bag.result_df.CashPeriodIncreaseDecreaseIncludingExRateEffectFinal.max()]"]}, {"cell_type": "markdown", "id": "83b210fd-e9a6-467c-938b-bfbccfcffa68", "metadata": {}, "source": ["Since we used the `present` method of the standardizer, the cik, form, fye, fy, and fp attributes from the sub_df were directly merged in the result. Also a `date` column with a date datatype was added and the data is already sorted by date."]}, {"cell_type": "markdown", "id": "8316c00d-8e5e-4568-a600-765036091177", "metadata": {}, "source": ["Next, get all reports for this company and filter our standardized cash flow statement data for it.\n", "We will show this as a barchart, and for the quarterly reports, we will us the data that contains the results from the beginning of the financial year (for Q2 we use qtrs=2 and for Q3 we use qtrs=3). This means, we will see the growth of CashPeriodIncreaseDecreaseIncludingExRateEffectFinal during the year. "]}, {"cell_type": "code", "execution_count": 9, "id": "65fd1988-7064-4eb6-8167-03f0222127de", "metadata": {"tags": []}, "outputs": [], "source": ["mask_19617 = cf_standardizer_result_bag.result_df.cik==19617\n", "mask_FY = (cf_standardizer_result_bag.result_df.fp==\"FY\") & (cf_standardizer_result_bag.result_df.qtrs==4)\n", "mask_Q1 = cf_standardizer_result_bag.result_df.fp==\"Q1\"\n", "mask_Q2 = (cf_standardizer_result_bag.result_df.fp==\"Q2\") & (cf_standardizer_result_bag.result_df.qtrs==2)\n", "mask_Q3 = (cf_standardizer_result_bag.result_df.fp==\"Q3\") & (cf_standardizer_result_bag.result_df.qtrs==3)\n", "\n", "reports_of_19617 = cf_standardizer_result_bag.result_df[mask_19617 & (mask_FY | mask_Q1 | mask_Q2 | mask_Q3)]"]}, {"cell_type": "code", "execution_count": 10, "id": "c11f23ab-c925-4b0b-82fb-b3a48b7fba9e", "metadata": {"tags": []}, "outputs": [], "source": ["reports_of_19617 = reports_of_19617[['date', 'CashPeriodIncreaseDecreaseIncludingExRateEffectFinal', 'fp']].reset_index()"]}, {"cell_type": "code", "execution_count": 11, "id": "974ce55f-e754-4ef9-9b7c-ad76e11cb03e", "metadata": {"tags": []}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA4UAAAKNCAYAAACX9M55AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjguMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8g+/7EAAAACXBIWXMAAA9hAAAPYQGoP6dpAAB7yUlEQVR4nO3deXyU9bX48TOTfQEiS0iQLCBQgqAsyqqIFYFIK+CCChpxLQit1haUqqBee12rtxYBiwJSRbneH/UqIEspFEUQgbIIBIOAQdYImIQtZDm/P3wx1zEL+U7yTZ6Z5/N+vfJ6OTMnZ87zXR48meXxqKoKAAAAAMCVvPVdAAAAAACg/tAUAgAAAICL0RQCAAAAgIvRFAIAAACAi9EUAgAAAICL0RQCAAAAgIvRFAIAAACAi9EUAgAAAICL0RQCAAAAgIvRFAIAAACAiwVNU7hq1Sr55S9/KS1atBCPxyMffPCB0e+fOXNGRo0aJZ06dZLw8HAZOnRouZiDBw/KiBEjpF27duL1euWhhx6qldoBAAAAwKmCpik8efKkXHrppfLaa68F9PulpaUSExMjv/nNb6R///4VxhQVFUmzZs3k8ccfl0svvbQm5QIAAABAUAiv7wKqKzMzUzIzMyt9vKioSB577DF599135fvvv5eOHTvK888/L/369RMRkbi4OJk2bZqIiKxevVq+//77cjnS09Plz3/+s4iIzJw5s9aPAQAAAACcJmheKTyfcePGyZo1a+S9996TLVu2yM033yyDBg2SnJyc+i4NAAAAABwrJJrC3NxcmTVrlrz//vty5ZVXykUXXSS///3v5YorrpBZs2bVd3kAAAAA4FhB8/bRqmzdulVKS0ulXbt2fvcXFRVJkyZN6qkqAAAAAHC+kGgKT5w4IWFhYbJhwwYJCwvzeyw+Pr6eqgIAAAAA5wuJprBLly5SWloqR44ckSuvvLK+ywEAAACAoBE0TeGJEydk165dvtt79uyRTZs2SePGjaVdu3YycuRIycrKkj/96U/SpUsXycvLk+XLl8sll1wigwcPFhGR7du3y9mzZ+XYsWNSWFgomzZtEhGRzp07+/Keu+/EiROSl5cnmzZtksjISOnQoUNdHSoAAAAA1BmPqmp9F1EdK1eulKuvvrrc/XfeeafMnj1biouL5ZlnnpE5c+bI/v37pWnTptKzZ0956qmnpFOnTiLywyUnvvnmm3I5fjwEHo+n3ONpaWmyd+/e2jsYAAAAAHCIoGkKAQAAAAC1LyQuSQEAAAAACAxNIQAAAAC4mKO/aKasrEwOHDggDRo0qPCzfgAAAADcQVWlsLBQWrRoIV4vr23VJkc3hQcOHJCUlJT6LgMAAACAQ+zbt09atmxZ32WEFEc3hQ0aNBCRHya+YcOG9VwNAAAAgPpSUFAgKSkpvh4BtcfRTeG5t4w2bNiQphAAAAAAHyuzgDfjAgAAAICL0RQCAAAAgIvRFAIAAACAi9EUAgAAAICL0RQCAAAAgIvRFAIAAACAi9EUAgAAAICL0RQCAAAAgIvRFAIAAACAi9EUAgAAAICL0RQCAAAAgIvRFAIAAACAi9EUAgAAAICL0RQCAAAAgIvRFAIAAACAi9EUAgAAAICL0RQCAAAAgIvRFAIAAACAi4XXdwEAAAChKmbwqxXef3rhb+q4EgCoHK8UAgAAAICL0RQCAAAAgIvRFAIAAACAi9EUAgAAAICL0RQCAAAAgIvRFAIAAACAi9EUAgAAAICL0RQCAAAAgIvRFAIAAACAi9EUAgAAAICL0RQCAAAAgIvRFAIAAACAi9EUAgAAAICL0RQCAAAAgIvRFAIAAACAi9EUAgAAAICL0RQCAAAAgIvRFAIAAACAi9EUAgAAAICL0RQCAAAAgIvRFAIAAACAi9EUAgAAAICL0RQCAAAAgIvRFAIAAACAi9EUAgAAAICL0RQCAAAAgIvRFAIAAACAi9EUAgAAAICLhdd3AQAAALCv3YTFFd7/****************************************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\n", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# using fp to define the color\n", "unique_fp = ['Q1', 'Q2', 'Q3', 'FY']\n", "blues = plt.cm.Blues(np.linspace(0.4, 0.9, len(unique_fp)))\n", "color_map = {fp: color for fp, color in zip(unique_fp, blues)}\n", "\n", "# create bar chart\n", "plt.figure(figsize=(10, 6)) \n", "\n", "# calculate bar width\n", "bar_width = 2.0 / len(reports_of_19617['fp'].unique())\n", "\n", "for index, row in reports_of_19617.iterrows():\n", "    # add bars in the right color\n", "    plt.bar(index, row['CashPeriodIncreaseDecreaseIncludingExRateEffectFinal'], width=bar_width, color=color_map[row['fp']], label=row['fp'])\n", "\n", "# add legend\n", "legend_labels = [plt.Rectangle((0,0),1,1, color=color_map[fp]) for fp in unique_fp]\n", "plt.legend(legend_labels, unique_fp, title='fp', loc='center left', bbox_to_anchor=(1, 0.5))\n", "\n", "# x-axis labels\n", "plt.xticks(range(len(reports_of_19617)), reports_of_19617['date'], rotation=90)\n", "\n", "# show diagramm\n", "plt.show()"]}, {"cell_type": "markdown", "id": "ca75e634-c65c-41b9-a85f-4c6a4df9e680", "metadata": {}, "source": ["Let us visualize some data for apple: -> cik 320193\n", "\n", "But this time, we will just visualze the data for the annual reports."]}, {"cell_type": "code", "execution_count": 12, "id": "79087c33-9ef9-4319-87ac-afd330a9f1c5", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["<matplotlib.legend.Legend at 0x203d56c2620>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAi8AAAGsCAYAAAAVGEevAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjguMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8g+/7EAAAACXBIWXMAAA9hAAAPYQGoP6dpAACakUlEQVR4nOzdd1xV9R/H8de9lz1lI4gyBPfeG1Nzryy1nKXt4WiolaaV+UuzsqysrJyp5d6zzHLviSiIIiJD2fuO8/vj6k1yoQKX8Xk+HjyAc84953NBue/7Pd+hUhRFQQghhBCilFCbuwAhhBBCiAch4UUIIYQQpYqEFyGEEEKUKhJehBBCCFGqSHgRQgghRKki4UUIIYQQpYqEFyGEEEKUKhJehBBCCFGqSHgRQgghRKki4UUIIYQQpUqZDi+7du2iZ8+e+Pj4oFKpWL169QM9Picnh+HDh1OnTh0sLCzo06fPbcdcvXqVZ555hpCQENRqNaNHjy6U2oUQQghxZ2U6vGRmZlKvXj2++eabh3q8Xq/H1taWN954g44dO97xmNzcXDw8PHj//fepV6/eo5QrhBBCiAKwMHcBRalr16507dr1rvtzc3N57733WLJkCSkpKdSuXZtPP/2U0NBQAOzt7fnuu+8A2L17NykpKbedw9/fn1mzZgHw888/F/pzEEIIIUR+Zbrl5X5ee+019u7dy9KlSzlx4gRPPfUUXbp04fz58+YuTQghhBB3UW7DS3R0NL/88gu///47bdq0ISgoiLfeeovWrVvzyy+/mLs8IYQQQtxFmb5tdC8nT55Er9cTEhKSb3tubi5ubm5mqkoIIYQQ91Nuw0tGRgYajYbDhw+j0Wjy7XNwcDBTVUIIIYS4n3IbXho0aIBerychIYE2bdqYuxwhhBBCFFCZDi8ZGRlERESYvo+KiuLYsWO4uroSEhLCoEGDGDp0KDNnzqRBgwYkJiayY8cO6tatS/fu3QE4c+YMeXl5JCUlkZ6ezrFjxwCoX7++6bw3t2VkZJCYmMixY8ewsrKiZs2axfVUhRBCiHJDpSiKYu4iisrOnTtp3779bduHDRvGvHnz0Gq1fPzxxyxYsIArV67g7u5O8+bNmTJlCnXq1AGMQ6EvXbp02zlu/bGpVKrb9lepUoWLFy8W3pMRQgghBFDGw4sQQgghyp5yO1RaCCGEEKWThBchhBBClCplrsOuwWAgNjYWR0fHO/ZFEUIIIUTJoygK6enp+Pj4oFbfu22lzIWX2NhY/Pz8zF2GEEIIIR7C5cuXqVSp0j2PKXPhxdHRETA+eScnJzNXI4QQQoiCSEtLw8/Pz/Q6fi9lLrzcvFXk5OQk4UUIIYQoZQrS5UM67AohhBCiVJHwIoQQQohSRcKLEEIIIUqVMtfnpSAURUGn06HX681diigkGo0GCwsLGR4vhBDlQLkLL3l5eVy9epWsrCxzlyIKmZ2dHRUrVsTKysrcpQghhChC5Sq8GAwGoqKi0Gg0+Pj4YGVlJe/UywBFUcjLyyMxMZGoqCiCg4PvO8GREEKI0qtchZe8vDwMBgN+fn7Y2dmZuxxRiGxtbbG0tOTSpUvk5eVhY2Nj7pKEEEIUkXL59lTelZdN8nsVQojyQf7aCyGEEKJUkfAihBBCiFJFwosocsOHD6dPnz7mLkMIIUQZIeGlFLl8+TLPPfecaaRUlSpVGDVqFNevXzd3aQBcvHgRlUrFsWPH8m2fNWsW8+bNM0tNQgghyh4JL6XEhQsXaNy4MefPn2fJkiVEREQwZ84cduzYQYsWLUhKSiqya+fl5T3S452dnalQoULhFCOEECVIcmYes/84z74LJeNNZHlR7sOLoihk5enM8qEoSoHrfPXVV7GysmLr1q20a9eOypUr07VrV7Zv386VK1d47733APD39+ejjz7i6aefxt7eHl9fX7755pt850pJSWHkyJF4eHjg5OTEY489xvHjx037J0+eTP369Zk7dy4BAQGmYcebN2+mdevWVKhQATc3N3r06EFkZKTpcQEBAQA0aNAAlUpFaGgocPtto9DQUN544w3eeecdXF1d8fb2ZvLkyflqPHv2LK1bt8bGxoaaNWuyfft2VCoVq1evLvDPTAghitL5+HR6f7Obz7aeY+AP+xj28wFOXUk1d1nlQrma5+VOsrV6ak7aYpZrn/mwM3ZW9/8VJCUlsWXLFqZOnYqtrW2+fd7e3gwaNIhly5bx7bffAjBjxgzeffddpkyZwpYtWxg1ahQhISF06tQJgKeeegpbW1s2bdqEs7Mz33//PR06dODcuXO4uroCEBERwYoVK1i5ciUajQaAzMxMxo4dS926dcnIyGDSpEn07duXY8eOoVarOXDgAE2bNmX79u3UqlXrnjPdzp8/n7Fjx7J//3727t3L8OHDadWqFZ06dUKv19OnTx8qV67M/v37SU9P580333yon7EQQhSFP8MTeOPXo6Tn6nB3sCYlK4+/ziXy17lEetbz4c1OIfi725u7zDKr3IeX0uD8+fMoikKNGjXuuL9GjRokJyeTmJgIQKtWrRg/fjwAISEh7N69my+++IJOnTrxzz//cODAARISErC2tgbgs88+Y/Xq1SxfvpwXXngBMN4qWrBgAR4eHqbr9OvXL991f/75Zzw8PDhz5gy1a9c2Hevm5oa3t/c9n1PdunX54IMPAAgODmb27Nns2LGDTp06sW3bNiIjI9m5c6fpPFOnTjWFLyGEMBdFUfjpnyg+2RiGQYGm/q58N7ghGbk6Zm49x9rjsaw7Hsumk1cZ0MSPUR2C8XSSSTMLW7kPL7aWGs582Nls134QBb3N1KJFi9u+//LLLwE4fvw4GRkZuLm55TsmOzs73y2gKlWq5AsuYAxRkyZNYv/+/Vy7dg2DwQBAdHQ0tWvXfqDnUrdu3XzfV6xYkYSEBADCw8Px8/PLF4CaNm36QOcXQojClqcz8P7qk/x2KAaAAY39+KhPbaws1Lg5WPPV0w14sV0gM7aEszM8kcX7o1lxJIbnWgXwYrsgnG0tzfwMyo5yH15UKlWBbt2YU9WqVVGpVISFhdG3b9/b9oeFheHi4nJb2LiTjIwMKlasyM6dO2/bd2unWnv725s7e/bsSZUqVfjxxx/x8fHBYDBQu3bth+rQa2mZ/z+xSqUyhSEhhChprmfk8vKiIxy4mIRaBe91r8lzrfxvWx+vlo8z855tyr4L15m++SxHolP4dmcki/dH83JoEMNb+mPzgG9cS5qIhHRSs3U0quJithpK9qu2AIy3YTp16sS3337LmDFj8vV7iYuLY/HixQwdOtT0n2jfvn35Hr9v3z7TLaeGDRsSFxeHhYUF/v7+Ba7h+vXrhIeH8+OPP9KmTRsA/vnnn3zH3OzjotfrH/g53qpatWpcvnyZ+Ph4vLy8ADh48OAjnVMIIR7W2bg0Rsw7xJWUbBytLfj6mQaEVvO852OaB7qx4uWWbA9LYMaWs5yLz+B/m87yy+4oRncM4alGlbDQlJ4xM7Ep2aw7HsuaY7GcuZpGPb8KrHm1ldnqkfBSSsyePZuWLVvSuXNnPv74YwICAjh9+jRvv/02vr6+TJ061XTs7t27mT59On369GHbtm38/vvvbNiwAYCOHTvSokUL+vTpw/Tp0wkJCSE2NpYNGzbQt29fGjdufMfru7i44Obmxg8//EDFihWJjo429au5ydPTE1tbWzZv3kylSpWwsbHB2dn5gZ9rp06dCAoKYtiwYUyfPp309HTef/99AFkFXAhRrLadiWf00qNk5ump4mbHT8MaU9XTsUCPValUdKrpxWPVPVl19ApfbDvHlZRsJqw8yY+7LvDm49XoWtsbtbpk/l1Lycpj48k41hy7woGLSdzsuWChVuHhYEWuTo+1hXlakUpP7CvngoODOXToEIGBgfTv35+goCBeeOEF2rdvz969e02jhADefPNNDh06RIMGDfj444/5/PPP6dzZ2K9HpVKxceNG2rZty7PPPktISAgDBw7k0qVLplaOO1Gr1SxdupTDhw9Tu3ZtxowZw4wZM/IdY2FhwVdffcX333+Pj48PvXv3fqjnqtFoWL16NRkZGTRp0oSRI0eahoLLatFCiOKgKArf7YzkhYWHyMzT0zLIjdWvtCpwcLmVRq3iyUaV+OOtdkzqURNXeysuXMvk1V+P0Pub3fx9PrEInsHDyc7Ts/Z4LCPnH6TJ1O28u+ok+6OMwaVpgCtT+9bm4HsdmTusidmCC4BKeZDJRkqBtLQ0nJ2dSU1NxcnJKd++nJwcoqKi8s1dUtb4+/szevRoRo8ebe5SCtXu3btp3bo1ERERBAUF3fGY8vD7FUIUvRytnndXnmTl0SsADGpWmcm9amFZSLd5MnJ1zP37Aj/uukBmnvE2e8sgN97pUp36fhUK5RoPQqs38E/ENdYei2XL6Tiy8v699V+zohO96/vQs54PPhVs73GWR3ev1+//kttGokRatWoVDg4OBAcHExERwahRo2jVqtVdg4sQQhSGhPQcXlx4mKPRKWjUKib3rMmQFv6Feg0HawtGdwxhSPMqfPNnJIv2XWJP5HX6fLObLrW8eatzNap6OhTqNf9LURSORCez5lgsG05c5XrmvwMv/Fxt6V3Pl971fQj2evCWpuIg4UWUSOnp6YwbN47o6Gjc3d3p2LEjM2fONHdZQogy7NSVVF5YcIjY1BycbCz4dlAjWge7F9n13BysmdSzJs+28ufL7edZeTSGzafj2Homjqca+TGqY3Cht3aEx6Wz5tgV1h6PJSY5+99a7K3oUbcivRv40sCvQonvXyi3jUSZIb9fIcTD2nzqKmOWHSdbqyfQw565QxsT6FG0rR//FR6Xzmdbw9l2Jh4AKws1w1pU4ZXQqrjY333G8vuJSc5i3fGrrDl2hbNx6abt9lYaOtf2pnd9X1oFuZl99JPcNhJCCCEKQFEUZv8Rwcxt5wBoE+zO7GcammVCuWrejvw4tDGHLyXz6eazHIhK4se/o1h64DIvtgvk2VYB2FsX7GU7KTOPjSeNgeXgxWTTdkuNitBqnvSu70OH6l7YWpXOOWckvAghhCiXcrR63l5+gnXHYwEY3tKf97vXMHsLRKMqLix7oTk7zyUyfXM4YVfT+GzrOebtucQbHaoysEllrCxurzErT8e2M/GsORbLrnOJ6AzGGysqFTQPcKN3fR+61q6Is13pn+lXwosQQohyJz4th+cXHOJETCoWahUf9q7NM80qm7ssE5VKRftqnrQL9mDdiVhmbj1HdFIWk9ac5se/L/Bmp2r0queDXlH4+3wia47FsvV0PNnaf0cK1fZ1onc9X3rUq0hF56IdKVTcJLwIIYQoV07EpPD8gkPEp+VSwc6S7wY1okWQ2/0faAZqtYre9X3pWrsiyw5d5qsd57mclM3oZcf4+o/zJGXmkZylNR1fxc2O3vV86FXft8hHLJmThBchhBDlxrrjsbz1+3FydQaCPR34aVgTKrvZmbus+7KyUDOkeRX6NfTll90XmbMzksjETADcHazpWa8ivev7Uq+Sc4kfKVQYJLwIIYQo8wwGhS93nOerHecBaF/Ng6+eboCjTenq/2FnZcGr7avyTNPKbDkdh08FW1qWgJFCxa18PdsyKDQ0tMTNpnvx4kVUKhXHjh0zdylCCEFWno5Xfz1iCi7Ptwlg7rAmpS643MrF3oqBTSvTNsSj3AUXkJaXUmP48OHMnz//tu379+83rRhdUvj5+XH16lXc3YtucichhCiI2JRsnl9wiNOxaVhqVEztW4f+jf3MXZZ4RBJeSpEuXbrwyy+/5Nvm4eGBRlOyxulrNBq8vb3NXYYQopw7Gp3M8wsOcy0jFzd7K74f0ojG/q73f6Ao8cpfW9N/KQrkZZrn4wEnN7a2tsbb2zvfR4cOHfLdNvL39+eTTz7hueeew9HRkcqVK/PDDz/kO8+4ceMICQnBzs6OwMBAJk6ciFb7b2/1yZMnU79+fRYuXIi/vz/Ozs4MHDiQ9PR/Z2Y0GAxMnz6dqlWrYm1tTeXKlZk6dSpw+22jnTt3olKp2LFjB40bN8bOzo6WLVsSHh6er66PP/4YT09PHB0dGTlyJOPHj6d+/foP9DMSQgiAVUdjGPDDPq5l5FLd25HVr7aS4FKGSMuLNgs+8THPtd+NBSv7Qj/tzJkz+eijj3j33XdZvnw5L7/8Mu3ataNatWoAODo6Mm/ePHx8fDh58iTPP/88jo6OvPPOO6ZzREZGsnr1atavX09ycjL9+/fnf//7nymgTJgwgR9//JEvvviC1q1bc/XqVc6ePXvPut577z1mzpyJh4cHL730Es899xy7d+8GYPHixUydOpVvv/2WVq1asXTpUmbOnElAQECh/3yEEGWXwaAwY2s43+2MBKBTTS++HFC/wDPTitJBfpulyPr163Fw+HfcfteuXe94XLdu3XjllVcAYyvLF198wZ9//mkKL++//77pWH9/f9566y2WLl2aL7wYDAbmzZuHo6NxRdEhQ4awY8cOpk6dSnp6OrNmzWL27NkMGzYMgKCgIFq3bn3P+qdOnUq7du0AGD9+PN27dycnJwcbGxu+/vprRowYwbPPPgvApEmT2Lp1KxkZGQ/0MxJClF8ZuTrGLDtmWhvoldAg3nq8Gmp12R86XN5IeLG0M7aAmOvaD6B9+/Z89913pu/t7e15+umnbzuubt26pq9VKhXe3t4kJCSYti1btoyvvvqKyMhIMjIy0Ol0ty2C5e/vbwouABUrVjSdIywsjNzcXDp06PBA9d9aV8WKFQFISEigcuXKhIeHmwLXTU2bNuWPP/54oGsIIcqnuNQchv9ygLNx6VhZqJnery59GviauyxRRCS8qFRFcuumKNjb21O1atX7HmdpmX/4n0qlwmAwALB3714GDRrElClT6Ny5M87OzqZbNAU9h63tw00zfes5b06idPOcQgjxsJIy8xj8034iEjJwd7Dmh6GNaFjZxdxliSIkHXbLmT179lClShXee+89GjduTHBwMJcuXXqgcwQHB2Nra8uOHTsKra5q1apx8ODBfNv++70QQvxXeo6WYT8fICIhg4rONqx6paUEl3KgSMPLrl276NmzJz4+PqhUKlavXn3fx+zcuZOGDRtibW1N1apVmTdvXlGWWO4EBwcTHR3N0qVLiYyM5KuvvmLVqlUPdA4bGxvGjRvHO++8w4IFC4iMjGTfvn389NNPD13X66+/zk8//cT8+fM5f/48H3/8MSdOnCgX01wLIR5OjlbPyPmHOHklFVd7KxaOaIafa8mf6l88uiINL5mZmdSrV49vvvmmQMdHRUXRvXt32rdvz7Fjxxg9ejQjR45ky5YtRVlmudKrVy/GjBnDa6+9Rv369dmzZw8TJ0584PNMnDiRN998k0mTJlGjRg0GDBiQr1/Ngxo0aBATJkzgrbfeomHDhkRFRTF8+HBsbGwe+pxCiLJLqzfw6uIj7I9KwtHaggXPNS3TCxGK/FSK8oCTjTzshVQqVq1aRZ8+fe56zLhx49iwYQOnTp0ybRs4cCApKSls3ry5QNdJS0vD2dmZ1NTU2zqh5uTkEBUVRUBAgLwolgKdOnXC29ubhQsXFuh4+f0KUT4YDApjfjvGmmOxWFuoWfBcU5oFlsxVoUXB3ev1+79KVIfdvXv30rFjx3zbOnfufM+1e3Jzc8nNzTV9n5aWVlTliSKUlZXFnDlz6Ny5MxqNhiVLlrB9+3a2bdtm7tKEECWIoih8sPY0a47FYqFWMWdwIwku5VCJ6rAbFxeHl5dXvm1eXl6kpaWRnZ19x8dMmzYNZ2dn04efn6xZURqpVCo2btxI27ZtadSoEevWrWPFihW3hVkhRPk2c+s5Fu67hEoFnw+oT/vqnuYuSZhBiWp5eRgTJkxg7Nixpu/T0tIkwJRCtra2bN++3dxlCCFKsB92RTL7zwgAPu5Tm171zDQ7ujC7EhVevL29iY+Pz7ctPj4eJyenu84tYm1tjbW1dXGUJ4QQwkyWHojmk43GJUje6VKNQc2qmLkiYU4l6rZRixYtbps7ZNu2bbRo0cJMFQkhhDC3DSeuMmHVSQBeahfEK6H3n6xTlG1FGl4yMjI4duyYaXXhqKgojh07RnR0NGC85TN06FDT8S+99BIXLlzgnXfe4ezZs3z77bf89ttvjBkzpijLFEIIUULtDE9g9LKjKAo806wy47pUM3dJogQo0vBy6NAhGjRoQIMGDQAYO3YsDRo0YNKkSQBcvXrVFGQAAgIC2LBhA9u2baNevXrMnDmTuXPn0rlz56IsUwghRAl06GISLy06jFav0KNuRT7qXVsmrhRAEfd5CQ0N5V7TyNxp9tzQ0FCOHj1ahFUJIYQo6U7HpvLsvIPkaA2EVvPg8/710cjq0OKGEtXnRQghhLiQmMHQnw6QnqOjqb8r3w1qhJWFvFyJf8m/BlFohg8ffs8ZlIUQ4n5iU7IZPHc/1zPzqOXjxNzhjbG10pi7LFHCSHgpJUpSMLh48SIqlcrUEfumWbNmyUKaQoiHdi0jl8E/7Sc2NYdAD3vmP9cUJxtLc5clSqASNc+LKN2cnZ3NXYIQopRKy9Ey7OcDXEjMxLeCLYtGNMPdQebwEndW7lteFEUhS5tllo+HXRMzNDSUN954g3feeQdXV1e8vb2ZPHmyaf8zzzzDgAED8j1Gq9Xi7u7OggULADAYDEybNo2AgABsbW2pV68ey5cvNx2fnJzMoEGD8PDwwNbWluDgYH755RfAOCoMoEGDBqhUKkJDQ4HbW4fuVyfA2bNnad26NTY2NtSsWZPt27ejUqlYvXr1Q/1shBClT3aenpHzDnE6Ng13BysWjmiKT4U7T0wqBEjLC9m6bJr92sws197/zH7sLO0e6rHz589n7Nix7N+/n7179zJ8+HBatWpFp06dGDRoEE899RQZGRk4OBiXiN+yZQtZWVn07dsXMK4JtWjRIubMmUNwcDC7du1i8ODBeHh40K5dOyZOnMiZM2fYtGkT7u7uREREmNaXOnDgAE2bNmX79u3UqlULKyurh6pTr9fTp08fKleuzP79+0lPT+fNN998qJ+HEKJ0ytMZeHnxYQ5cTMLRxoL5zzUl0MPB3GWJEq7ch5fSqm7dunzwwQcABAcHM3v2bHbs2EGnTp3o3Lkz9vb2rFq1iiFDhgDw66+/0qtXLxwdHcnNzeWTTz5h+/btptmLAwMD+eeff/j+++9p164d0dHRNGjQgMaNGwPg7+9vuraHhwcAbm5ueHt7P3Sd27ZtIzIykp07d5rOM3XqVDp16lR4PyghRImlNyiM/e0YO8MTsbFU88vwJtTykdvP4v7KfXixtbBl/zP7zXbth1W3bt1831esWJGEhAQALCws6N+/P4sXL2bIkCFkZmayZs0ali5dCkBERARZWVm3hYS8vDzThIIvv/wy/fr148iRIzz++OP06dOHli1bFmqd4eHh+Pn55QtATZs2feBrCCFKH0VReH/1KdafuIqlRsWcwY1o7O9q7rJEKVHuw4tKpXroWzfmZGmZvwe+SqXCYDCYvh80aBDt2rUjISGBbdu2YWtrS5cuXQDjsg0AGzZswNfXN995bi5y2bVrVy5dusTGjRvZtm0bHTp04NVXX+Wzzz4r1DqFEOXTp5vDWXIgGpUKvhhQn9BqnuYuSZQi5T68lFUtW7bEz8+PZcuWsWnTJp566ilTkKhZsybW1tZER0fTrl27u57Dw8ODYcOGMWzYMNq0acPbb7/NZ599ZurjotfrH6nGatWqcfnyZeLj4/Hy8gLg4MGDj3ROIUTJ993OSOb8FQnAtL516FHXx8wVidJGwksZ9swzzzBnzhzOnTvHn3/+adru6OjIW2+9xZgxYzAYDLRu3ZrU1FR2796Nk5MTw4YNY9KkSTRq1IhatWqRm5vL+vXrqVGjBgCenp7Y2tqyefNmKlWqhI2NzUMNk+7UqRNBQUEMGzaM6dOnk56ezvvvvw8g65cIUUYt3n+JTzefBeDdbtUZ2LSymSsSpVG5Hypdlg0aNIgzZ87g6+tLq1at8u376KOPmDhxItOmTaNGjRp06dKFDRs2mIZBW1lZMWHCBOrWrUvbtm3RaDSmPjMWFhZ89dVXfP/99/j4+NC7d++Hqk+j0bB69WoyMjJo0qQJI0eO5L333gPAxsbmEZ65EKIkWns8lvdXnwLg1fZBvNA2yMwVidJKpTzsZCMlVFpaGs7OzqSmpuLk5JRvX05ODlFRUQQEBMiLYwm1e/duWrduTUREBEFBD/aHTX6/QpRcf5yN54UFh9EZFIY0r8KHvWtJC6vI516v3/8lt42EWa1atQoHBweCg4OJiIhg1KhRtGrV6oGDixCi5Np/4TovLzqCzqDQu74PU3pJcBGPRsKLMKv09HTGjRtHdHQ07u7udOzYkZkzZ5q7LCFEITl1JZWR8w+RqzPQobonnz1VD7Vagot4NBJehFkNHTqUoUOHmrsMIUQRiEjIYOjPB0jP1dEswJVvBjXEUiNdLcWjk39FQgghCl1MchZDftpPUmYedSs5M3dYY2wsNeYuS5QREl6EKKdSs7WcjUszdxmiDEpMz2XITwe4mppDVU8H5j3bFEcby/s/UIgCkttGQpRDF69l8vSP+7iamsOTjSoxuVctHKzlz4F4dPFpOQz/5SBR1zLxrWDLwhFNcbW/++KtQjwM+WslRDlzITGDp3/cR3xaLgDLD8dwICqJLwbUp1EVFzNXJ0qraxm5zNkZycJ9l8jVGXB3sGbxyGZUdH74NdyEuBu5bSREORKRkMHAH4zBJcTLgTmDG+FbwZbopCz6f7+XL7adQ6eXtadEwaVmaZmx5Sxtp//J3H+iyNUZaFzFhSXPN8Pf3d7c5YkySlpehCgnzsen8/SP+7mWkUt1b0cWjWyGu4M1LYLc+GDNKVYfi2XWjvP8dS6RLwfUlxcecU8ZuTp++SeKH/6+QHqODoA6vs68+XgI7UI8ZB4XUaSk5aWUGD58OH369CmUc+3cuROVSoVKpUKtVuPs7EyDBg145513uHr1aqFcQ5Qs4XHpPP3jPq5l5FKjohO/Pt8cdwfjCuLOtpZ8ObABswbWx9HGgmOXU+j21d8sOxhNGZuAWxSC7Dw9P+yKpM2nfzBz2znSc3RU83Lk+yGNWPtaK0KreUpwEUVOWl7KsfDwcJycnEhLS+PIkSNMnz6dn376iZ07d1KnTh1zlycKydm4NJ750ThktZaPE4tGNMPlDh0oe9f3pbG/K2OXHWN/VBLjVpzkj7MJ/O+Junc8XtzftYxcpm4I469ziTSq4kK/hr60r+6JtUXpGzKcq9Oz9MBlZv8ZQWK6sb9UoLs9ozuF0KNORZl4ThQraXkphUJDQ3njjTd45513cHV1xdvbm8mTJ+c7JiUlhRdffBEvLy9sbGyoXbs269evz3eMp6cn3t7ehISEMHDgQHbv3o2Hhwcvv/xyvuPmzp1LjRo1sLGxoXr16nz77bf59sfExPD000/j6uqKvb09jRs3Zv/+/UXy3MWDORObxtM/7CMpM486vs4sHnnn4HKTbwVbfn2+OeO7VsdSo2LL6Xg6f7mLXecSi7Hq0k9RFH4/dJmOn//FqqNXSMrMY9uZeF5adIRmn+xg4upTHI1OLhUtW1q9gaUHomk/YycfrD1NYnoulVxsmfFkXbaOaUuvej4SXESxK/ctL4qioGRnm+XaKlvbh25enT9/PmPHjmX//v3s3buX4cOH06pVKzp16oTBYKBr166kp6ezaNEigoKCOHPmDBrNvd/t2dra8tJLLzFmzBgSEhLw9PRk8eLFTJo0idmzZ9OgQQOOHj3K888/j729PcOGDSMjI4N27drh6+vL2rVr8fb25siRIxgM0unT3E5dSWXwT/tJydJSr5IzC0Y0w9n2/nNtaNQqXmoXROuq7oxaepTIxEyG/nyAZ1v5M65LdZlo7D4uXsvk3VUn2RN5HYAaFZ0Y2ymEQ5eSWH30CvFpuSzcd4mF+y4R6GFPv4aV6NPAF98KJWtUjt6gsPb4Fb7cfp5L17MA8Hay4bXHqtK/sR9WFvLeV5iPhJfsbMIbNjLLtasdOYzKzu6hHlu3bl0++OADAIKDg5k9ezY7duygU6dObN++nQMHDhAWFkZISAgAgYGBBTpv9erVAbh48SKenp588MEHzJw5kyeeeAKAgIAAzpw5w/fff8+wYcP49ddfSUxM5ODBg7i6ugJQtWrVh3pOovCcjEll0Nx9pOXoqO9XgQUjmuL0gJOE1fZ1Zv3rbfhkYxgL913il90X2R1xjVkDG1Cj4r1XfC2PtHoDP+y6wFc7zpOrM2BjqWZMxxCeax2ApUZNp5pevNO5Onsir7HicAybT8dxITGTGVvC+WxrOC0C3XiiYSW61vbG3oxz7hgMCptOxfHF9nNEJGQA4O5gxcuhVRnUrLKEV1EilPvwUlrVrVs33/cVK1YkISEBgGPHjlGpUiVTcHkQN5uxVSoVmZmZREZGMmLECJ5//nnTMTqdDmdnZ9O1GjRoYAouwvyOX05h8E/7Sc/R0bByBeY/9/Czm9paafioT20eq+7J28uPcy4+g96zd/NOl2o81ypAbhfccDQ6mQkrT3I2Lh2ANsHufNynNlXc8o/Y0qhVtAn2oE2wBxm5OjaevMrKIzHsu5DEnsjr7Im8zsTVp+ha25snGlaiRZAbmmL6GSuKwh9nE5i59RxnrhpnXna2teTFdoEMa+Fv1kAlxH+V+3+NKltbqh05bLZrPyxLy/wvRiqVynSrxvYRzhsWFgaAv78/GRnGd10//vgjzZo1y3fczVtQj3ItUfiORicz9CfjQnhN/F345dmmhTJzbvvqnmwe3ZbxK06wPSyBjzeE8Wd4AjOfqo+3s00hVF46ZeTq+GxLOPP3XkRRwMXOkok9atK3ge99bwk7WFvQv7Ef/Rv7EZOcxaojV1h59ApR1zJZedT4dUVnG/o08KVfQ1+qejoWyXNQFIV/Iq4xc+s5jl1OMdU2onUAI9oEPHCLnRDFQcKLSvXQt25Kqrp16xITE8O5c+ceqPUlOzubH374gbZt2+Lh4QGAj48PFy5cYNCgQXe91ty5c0lKSpLWFzM7fCmJYT8fJCNXR9MAV34Z3qRQ3y27O1jz49DG/Hogmo/Wn2F3xHU6f7mLaU/UoVudioV2ndJi+5l4Jq45xdXUHACeaOjL+91rPtRU+JVc7Hi9QzCvPVaVo5dTWHE4hnXHY7mamsN3OyP5bmck9So580TDSvSs51No0+0fiEris63hHIhKAsDWUsOwlv682DZQRpiJEq3ch5eyqF27drRt25Z+/frx+eefU7VqVc6ePYtKpaJLly6m4xISEsjJySE9PZ3Dhw8zffp0rl27xsqVK03HTJkyhTfeeANnZ2e6dOlCbm4uhw4dIjk5mbFjx/L000/zySef0KdPH6ZNm0bFihU5evQoPj4+tGjRwhxPv1w6eDGJ4T8fIDNPT/NAV34e3gQ7q8L/761SqRjUrArNA90YvfQYJ6+k8sriIzzZqBIf9KxZLhbfS0jLYfK602w8GQdAZVc7Pulbh9bB7o98bpVKRcPKLjSs7MKknjX5IyyBFUdi2BmeyPGYVI7HpPLxhjO0r+bJEw0r8Vh1z4fqOHv8cgozt50zjSKzslAzuFkVXg4NwsPR+pGfhxBFTcJLGbVixQreeustnn76aTIzM6latSr/+9//8h1TrVo1VCoVDg4OBAYG8vjjjzN27Fi8vb1Nx4wcORI7OztmzJjB22+/jb29PXXq1GH06NEAWFlZsXXrVt588026deuGTqejZs2afPPNN8X5dMu1/Reu8+y8g2Tl6WkZ5MZPw5pga1W0nSqDPBxY8XJLZu04x7c7I1l+OIb9Udf5ckB9GlUpmy1wBoPCkoPR/G/TWdJzdGjUKp5vE8ioDsFF8vO2ttDQtU5FutapyLWMXNYei2Xl0RhOXUlj65l4tp6Jp4KdJb3q+fBEw0rUq+R831tVYVfT+HzbObadiQfAQq1iQBM/XnusqqxBJEoVlVIaJhp4AGlpaTg7O5OamoqTU/4RETk5OURFRREQEICNTfm9T19Wlcff797I6zw37yDZWj1tgt35YUjjIg8u/3UgKokxy45xJSUbtQpea1+V1zsEY6kpO0NpIxLSmbDyJAcvJgNQr5Iz056oS02f4h91FR6XzsojMaw6eoWEG5PFAQR52PNEw0r0beCLz3+GXUckZPDl9nOsP2GcQVutgicaVuKNx4Kp7Fa2bpuL0uter9//JeFFlBnl7fe7O+IaI+YfJEdroG2IBz8MaWS2YaxpOVo+WHOaVUevAFDPrwKzysD6SLk6Pd/+Gcm3OyPQ6hXsrDS89Xg1hrX0L7ZRQHejNyjsjrjGiiMxbDkdR47W2GFfpYKWQW480aAStX2d+WHXBVYdjcFw4y99z3o+jO4YTJCHgxmrF+J2El4kvJRL5en3+/f5REbOP0SuzkD7ah58N9h8weVWa4/H8v6qk6Tl6LCz0jCpR00GNPErlWvdHIhKYsLKE0QmZgLwWHVPPupTu8RNJgeQnqNl06k4VhyOYf+Nzrf/9XhNL8Z0CpE5ekSJ9SDhRfq8CFHK/HUukecXHCJPZ6BDdU++HdywxKyV06ueD42ruDD2t2Psu5DE+JU31kfqV7fQRsgUtdRsLf/bdJYlB6IB4yiryb1q0r1OxRIbwhxtLE3Dri8nZbHq6BVWHonh4vUs2oV4MLZTCPX8Kpi7TCEKjbS8iDKjPPx+/wxP4MWFh8nTGehU04vZzzQoMcHlVnqDwo9/X2Dm1nC0egUPR2s+e6oe7UI8zF3aXSmKcWbZm+v3AAxs4seErjVwtit9o6gURSFbqy+SUWdCFAVpeRGiDNoRFs/Li46QpzfQuZYXXz/dsMSuL3Pr+kijlx0jIiGDYT8fYHhLf8Z3LXnrI8WmZDNpzSm2hxlnqQ50t+eTJ+rQPNDNzJU9PJVKJcFFlFkl8y9fEStjjU3ihrL8e916Oo6XFh0mT2+gWx1vZj9TcoPLrWr7OrPutdYMa1EFgHl7LtJr9j+ciU0zc2VGeoPCvN1RdPr8L7aHJWCpUfHGY1XZOKpNqQ4uQpR15SqW35xSPysrS6a1L4Oysowr3/536YTSbvOpOF779Qg6g0KPuhX5YkD9UjUM2dZKw5TetQmt7snbv5/gXHwG3b76G2dbS7ycrPFysrnxYfza0/Hfrz0crYvsuYZdTWP8ypMcvzElfqMqLkx7og4hXkUzDb8QovCUq/Ci0WioUKGCaQFDOzu7EtsBTxScoihkZWWRkJBAhQoVTOsulQWbTl7l9SVH0RkUetf3YeZT9bAoRcHlVu2rebJldBveXXWSLafjSc3Wkpqt5Vx8xl0fo1KBm71VvoBjDDe3hB0na9zsrQs8dDlHq+erHef5YdcFdAYFR2sL3ulanUFNK8tCk0KUEuWqwy4YX+ji4uJISUkp/uJEkapQoQLe3t5lJpCuPxHLqKXH0BsU+jbw5bOn6pl9bpHCkpqtJSEth/i0XOLTcohPzyE+9cb36TkkpOWSkJ6DVl+wP08atQpPR2s8nWzwcrT+N+jc2qrjaMOZq2m8u+okl64bW+m61PJmcq9a5XpxSSFKCpnnpQBPXq/Xo9Vqi7EyUZQsLS3LVIvLmmNXGLPsGAYF+jWsxPQn65aZ4FJQBoNCclaeKdDkDzf/Bp9rGbmmCdgKytvJhim9a9G5lvf9DxZCFAsZbVQAGo2mTL3YibJj1dEY3vztOAYF+jeuxLQnyl9wAVCrVbg5WOPmYE1N7v6HTKc3cC0jz9iCk5ZDfHrujXBzS8tOWg7JWVrUKhjcvApvd65WLhaRFKKsKrfhRYiSaMXhGN5afhxFgaeb+jG1Tx3ph3EfFho13s429731k6vTo9Mr2FvLnz0hSjv5XyxECfHbocuMW3ECRYFBzSrzUe/aElwKkbWFBsktQpQN8l9ZiBJg6YFoxq88CcCQ5lX4sHetMtPxWAghCpuEFyHMRG9QOHkllU2nrvL9XxcAGN7Snw961pTgIoQQ9yDhRYhiFJeaw67ziew6l8g/EddIyfp3xNuI1gG8372GBBchhLgPCS9CFKEcrZ4DUUnsOpfIrvOJt03I5mhjQasgd7rW8aZXPR8JLkIIUQASXoQoRIqiEJGQwV/nEtl1/hr7L1wnV2cw7VepoG6lCrQLdqdtiAf1/SqU2hlzhRDCXCS8CPGIUrO0/BNxzdS6cjU1J99+Lydr2gZ70DbEg9ZV3XGxtzJTpUIIUTZIeBHiAen0Bo7HpJrCyvHLKflmeLWyUNMswJV2IcbAEuzpILeDhBCiEEl4EaIArqRkG8PKuUR2R1wjLUeXb3+wpwNtb4SVZgGu2FjK7M1CCFFUJLwIcQfZeXr2RV03BZbIxMx8+51tLWld1Z22Ie60CfbAp4KtmSoVQojyR8KLKPcMBoX49BwuJ2Vz7HIyu85d48DFJPJu6WirVkF9vwqm1pV6lSqUy/WGhBCiJJDwIso8RVFIyszjcnI2l5OyuJycRcyNr2OSs7mSnE2e3nDb43wr2NI2xJ22wR60rOqOs60s5CeEECWBhBdRJmTk6ozBJCnLFFJikrO4nJRNTHIWmXn6ez7eQq3Cp4ItVT0dbtwO8iDIw1462gohRAlULOHlm2++YcaMGcTFxVGvXj2+/vprmjZtesdj582bx7PPPptvm7W1NTk5OXc8XpQPOVo9V1KyTeEkJjmLmKRsLicbA0vyLTPV3o2XkzV+Lnb4udrh52JLJVe7G9/b4u1kI/OtCCFEKVHk4WXZsmWMHTuWOXPm0KxZM7788ks6d+5MeHg4np6ed3yMk5MT4eHhpu/l3W/5ciAqiX/OJ+a7zROflnvfx1WwszSFET8XuxvhxBY/Vzt8K9jKCCAhhCgjijy8fP755zz//POm1pQ5c+awYcMGfv75Z8aPH3/Hx6hUKry9vYu6NFHCxKfl8PGGMNYdj73jfjsrjSmcVLqlBcXP1Y5KLrY42kifFCGEKA+KNLzk5eVx+PBhJkyYYNqmVqvp2LEje/fuvevjMjIyqFKlCgaDgYYNG/LJJ59Qq1atOx6bm5tLbu6/78rT0tIK7wmIYqHTG1iw9xKfbztHRq4OtQp61PWhekXHfLd5XO2tpBVOCCFE0YaXa9euodfr8fLyyrfdy8uLs2fP3vEx1apV4+eff6Zu3bqkpqby2Wef0bJlS06fPk2lSpVuO37atGlMmTKlSOoXRe/wpWTeX32KsKvG0FnPrwJT+9Smtq+zmSsTQghRUpW40UYtWrSgRYsWpu9btmxJjRo1+P777/noo49uO37ChAmMHTvW9H1aWhp+fn7FUqt4eMmZeXy6+SxLD14GjJO+je9anQGN/VDL/ClCCCHuoUjDi7u7OxqNhvj4+Hzb4+PjC9ynxdLSkgYNGhAREXHH/dbW1lhbWz9yraJ4GAwKvx26zKebz5pGCPVvXIlxXarj5iC/RyGEEPdXpGNDraysaNSoETt27DBtMxgM7NixI1/ryr3o9XpOnjxJxYoVi6pMUUxOx6bSb84exq88SXKWlurejix/qQXTn6wnwUUIIUSBFflto7FjxzJs2DAaN25M06ZN+fLLL8nMzDSNPho6dCi+vr5MmzYNgA8//JDmzZtTtWpVUlJSmDFjBpcuXWLkyJFFXaooIuk5Wj7fdo75ey5iUMDeSsOYTiEMb+kvc6sIUUQupV1ixfkVDK81HFcbV3OXI0ShKvLwMmDAABITE5k0aRJxcXHUr1+fzZs3mzrxRkdHo1b/+wKWnJzM888/T1xcHC4uLjRq1Ig9e/ZQs2bNoi5VFDJFUVh34iofrz9DQrpxRFiPuhV5v3tNvJ1tzFydEGVXpjaTV7a/QnR6NCk5KXzY6kNzlyREoVIpiqKYu4jClJaWhrOzM6mpqTg5OZm7nHIrMjGDSWtOsTviOgAB7vZ82LsWbYI9zFyZEGXfhL8nsP7CegCs1FZse2qbtL6IEu9BXr9L3GgjUbpl5+mZ/ed5fth1Aa1ewdpCzWvtq/JCu0CsLWSGWyGK2trItay/sB61So23nTexmbEsP7ecF+q+YO7ShCg00uFAFJrtZ+Lp+PlffPNnJFq9wmPVPdk2ph2vdwiW4CJEMbiYepGP930MwCv1XuGNhm8AsPTsUrT6+6//JURpIS0v4pFdTspiyrozbA8zDon3rWDLBz1r0qmml8yIK0QxydPn8c6ud8jWZdPEuwkj64zEoBiYeWgmidmJbLm0hR6BPcxdphCFQlpexEPL0xn45s8IOn3xF9vD4rFQq3g5NIhtY9vyeC1vCS5CFKMvDn9BWFIYFawrMK31NDRqDZYaSwZWHwjAojOLKGNdHEU5JuFFPJQ9EdfoOmsXM7aEk6M10DzQlU2j2jCuS3XsrKRBT4jitPPyThaFLQJgauupeNn/uyTLkyFPYqW24vT10xxPPG6mCoUoXPIqIx5IQloOUzeGseaYceVndwdr3u9eg971faSlRQgziM+MZ+LuiQAMrjGYtpXa5tvvauNKj6AerDy/koVnFlLfs74ZqhSicEl4EQWi0xtYuO8Sn289R/qNlZ+HNK/C2Mer4Wxrae7yhCiX9AY94/8eT0puCjVcazCm0Zg7HjeoxiBWnl/JjugdXM24SkUHmbFclG5y20jc15HoZHrN3s2UdWdIz9VRz68Ca19rzZTetSW4CGFGP578kUPxh7CzsGNGuxlYaazueFyISwjNvJuhV/QsCV9SzFUKUfgkvIi7Ss7MY8LKEzzx7R7OXE3D2daST/rWYdXLLant62zu8oQo1w7HH+a7498B8H7z96niVOWexw+uORiA5eeWk6XNKvL6hChKcttI3NHxyymMXHCIxBvT+j/VqBLju8rKz0KUBKm5qYz/ezwGxUDPwJ70DOp538e0rdQWP0c/LqdfZv2F9fSv1r8YKhWiaEjLi7jN1tNxDPhhL4npuQR7OvD7Sy2Y8ZSs/CxESaAoCpN2TyIuM44qTlV4r/l7BXqcWqVmUI1BACwKW4RBMRRlmUIUKQkvIp9fdkfx4qLD5GgNhFbzYNWrrWjiL2uiCFFSLAtfxh+X/8BCbcH0ttOxt7Qv8GP7VO2Dg6UDUalR7IndU4RVClG0JLwIAPQGhSnrTjNl3RkUBZ5pVpm5QxvjYC13FoUoKcKTwplxcAYAYxuNpaZbzQd6vL2lPX2q9gEwzQsjRGkk4UWQnafn5UWH+WX3RQDGd63O1D61sdDIPw8hSoosbRZv73qbPEMebSu1ZXCNwQ91nmdqPIMKFbuv7OZCyoVCrlKI4iGvTuVcYnouA3/Yy9Yz8VhZqJn9TANeahckE84JUcJ8evBTolKj8LT15KNWHz30/1E/Rz/a+7UHYHHY4sIsUYhiI+GlHItISKfvt7s5HpOKi50lv45sRo+6PuYuSwjxH5uiNrHy/EpUqJjWZhquNo/WD+3msOl1F9aRmptaGCUKUawkvJRTeyOv88S3e4hJzsbfzY6Vr7SisXTMFaLEuZx+mSl7pwDwfN3naVqx6SOfs7FXY6q5VCNbl82K8yse+XxCFDcJL+XQqqMxDP15P2k5OhpVcWHlK60IcC/4iAUhRPHQGrSM2zWOTG0mDTwb8HK9lwvlvCqVytT6suTsEnQGXaGcV4jiIuGlHFEUhVnbzzNm2XG0eoXudSqyeGQzXO3vPKW4EMK8vj76NSevncTJyolP23yKhbrwRv91DeiKq40rcZlx7IjeUWjnFaI4SHgpJ/J0Bt5efoIvtp8D4KV2QXz9dANsLDVmrkwIcSe7r+zml1O/APBhyw8LfTFFa421aZbdRWdk2LQoXSS8lAOp2VqenXeA5Ydj0KhVTO1bm/Fdq6NWy4giIUqia9nXePefdwEYUG0AHap0KJLrDKg2AAu1BccSj3Hq2qkiuYYQRUHCSxkXk5zFU3P2sDviOvZWGuYOa8ygZvdewE0IYT4GxcC7f79LUk4SwS7BvNX4rSK7lrutO139uwIyaZ0oXSS8lGEnY1Lp++0ezsVn4OVkzW8vtaB9NU9zlyWEuId5p+ex9+pebDQ2fNb2M2wsbIr0eoNqGtc72hK1hYSshCK9lhCFRcJLGbUjLJ7+3xsXV6zu7cjqV1tRy8fZ3GUJIe7hROIJvj7yNQATmk0gsEJgkV+zllstGno2RKfoWBa+rMivJ0RhkPBSBi3Ye5HnFxwiW6unTbA7v7/UgorOtuYuSwhxD+l56byz6x10io4u/l3oW7VvsV375rDp38N/J0eXU2zXFeJhSXgpQwwGhY/Xn2HSmtMYFBjYxI+fhzfB0cbS3KUJIe5BURSm7J3ClYwr+Dr4MqnFpGJdoqO9X3t87H1Izk1mY9TGYruuEA9LwksZkZ2n55XFR5j7TxQAb3euxrQn6mApiysKUeKtPL+SLRe3YKGyYHrb6ThaORbr9S3UFjxd/WnA2HFXUZRivb4QD0pe2cqAaxm5PP3jPjafjsNKo2bWwPq82r6qLK4oRCkQmRLJ/w78D4DXG75OXY+6j3xORVFIWryYqCefIuvIkQI9pm9wX2wtbDmffJ4DcQceuQYhipKEl1IuMjGDvt/u5tjlFJxtLVk0shm96/uauywhRAHk6HJ4e9fb5OhzaOnTkuG1hj/yORWdjviPPib+o4/JOXWK2HfGYcjOvu/jnK2d6R3UG5BJ60TJJ+GlFNt/wbi44uWkbCq72rHylZY0DZDFFYUoLT479Bnnk8/jauPK1NZTUase7U+yPiOTy6+8QvKvvwKgdnJCGxPDtW++KdDjn6nxDAB/xfxFdFr0I9UiRFGS8FJKrTl2hSE/HSA1W0t9vwqsfKUlQR4O5i5LCFFA2y9tNw1NntZ6Gu627o90Pu3Vq1waNIjMXX+jsrbGd9YsfP5nvB11/Zd55Jw9e99zBDgH0Ma3DQoKv5799ZHqEaIoSXgpZRRF4Zs/Ixi19Bh5egNdanmz9IXmuDtYm7s0IUQBXc24yqQ9kwB4tvaztPRt+Ujnyz51mov9B5AbHo7G3Z0qCxfg1PlxHB9rj2PnzqDXc3XSByh6/X3PNbiGcdj0qvOrSM9Lf6S6hCgqEl5KEa3ewPgVJ5mxJRyA59sE8O2ghrK4ohCliM6gY9zf40jPS6eOex1eb/D6I50vfccOLg0Zgi4xEevgqgQsW4pt3X87/Xq9+y5qBwdyTpwgecnS+56vhU8LAp0DydJlsTpi9SPVJkRRkfBSSqTlaHlu3kGWHbqMWgUf9q7Fe91ryuKKQpQy3x3/jqMJR3GwdODTtp9iqX64eZgUReH6vHnEvPY6SnY29q1aUeXXX7H0zd9h39LLE883xwKQ+PnnaOPi7nlelUplmrRucdhi9Ib7t9YIUdwkvJQCsSnZ9J+zl7/PX8PWUsOPQxsztIW/ucsSQjygA1cP8OOJHwH4oMUH+Dn6PdR5FJ2OuA8/JOF/n4KiUGHAAPzmfIfG8c7zw1QYMADb+vUxZGUR9/HH9z1/j8AeOFs7cyXjCjtjdj5UjUIUJQkvJVjY1TQmrz1Nly93cTYuHQ9Ha357sQUdaniZuzQhxANKykliwt8TUFB4IvgJugR0eajz6DMyuPzyK6QsWQoqFZ7vvIP35A9QWd69BUelVuP94RSwsCBj+w7St2+/5zVsLWx5MvhJwNj6IkRJI+GlhEnN1rJw3yV6fv0PXWf9zbw9F0nL0ZkWV6xTSRZXFKK0URSFibsnkpCdQKBzIOOajHuo82hjY7n0zCAy//4blY0Nvl/Nwu25Zws0IaVNSAhuI0YAEPfRx+gzMu55/MDqA9GoNByMO8jZpPuPVBKiOEl4KQEURWFv5HXGLDtG06nbmbj6FCevpGKpUdG1tje/PNuEDW+0wbeCLK4oRGm0KGwRu2J2YaW2Yka7GdhZ2j3wObJPniJqwAByz51D43FjRFGnTg90DveXX8KySmV08fEkfvHlPY/1tvemUxXj+aX1RZQ0KqWMLWKRlpaGs7MzqampODk5mbuce4pPy2H54Rh+O3SZS9ezTNtDvBzo39iPvg18cZMh0EKUameun2HQxkHoDDrea/YeA6sPfOBzpG/fzpW33kbJycE6OBi/7+dg6ePzUPVk7t1L9LPPgUqF/9Il2Nard9djjyceZ/DGwViqLdn25DbcbN0e6ppCFMSDvH5bFFNN4gat3sCOsAR+O3SZneEJGG5ER3srDb3q+9C/sR/1/SrIukRClBGzj85GZ9DRoXIHBlQb8ECPVRSFpF/mkTBjBigK9q1b4/vlF2gcHn5CSvsWLXDu3YvUNWu5OukDApb/ftf+MvU86lHHvQ4nr53kt3O/8XK9lx/6ukIUJgkvxSQiIYPfDl1m5ZEYrmXkmbY38Xehf2M/utetiJ2V/DqEKEuSc5LZG7sXgFENRz3QmxJFpyPuo49JWWachbfCwAF4v/8+KotH/zvhOW4cGX/tIjc8nKT583EbOfKuxw6uMZhxf4/jt/DfGFF7BFYaq0e+vhCPSl4ti1Bmro4NJ66y7NBlDl9KNm13d7CmXyNf+jf2kyn9hSjDtl3ahk7RUcO1BgHOAQV+nD4jgyujx5D5zz+mEUWuw4cVWoushasrnuPGcXXCBBJnf4Nj585Y+d152HYn/07MPDSThOwEtlzcQs+gnoVSgxCPQsJLIVMUhSPRKfx28DLrT8SSmWec4EmjVtG+mgf9G/vRvronlhrpKy1EWbcxaiMA3QK6Ffgx2itXuPzSy+SeP4/K1hbfGdNx7Nix0Gtz7tOb1DVryNq3j7jJU/Cb++Mdw5Gl2pKB1Qfy1dGvWHhmIT0Ce8htbWF2El4KybWMXFYducKyQ5eJSPh3CGKAuz1PNa7Ekw0r4elkY8YKhRDFKS4zjsPxhwEKPKdL9smTXH75FfTXrqHxcMfvuznY1q5VJPWpVCoqTv6AC716k7l7N2nrN+Dcs8cdj30y5Em+P/E9YUlhHE04SkOvhkVSkxAFJeHlEegNCrvOJbLs4GW2h8Wju9H71sZSTfc6Pgxo4kcTfxd5lyJEObQ5ajMAjbwa4W3vfd/j07ZuJfadccYRRSEh+M357qFHFBWUlb8/7q+8TOKXs4ifNg2HNq3RVKhw23EuNi70COzBivMrWBS2SMKLMDsJLw8h+noWvx26zPLDMcSl5Zi21/OrwIDGfvSsVxFHm4dbr0QIUTYU9JaRoigk/fwLCZ99ZhxR1KYNvl98/kgjih6E23PPkbZhA7nnI4ifMQOfqVPveNygGoNYcX4FO6J3cCXjCr4Ovnc8TojiIOGlgHK0ejafimPZwcvsvXDdtN3FzpK+DSoxoIkf1bzvvK6IEKJ8iUqNIiwpDAuVhWmitztRtFrjiKLffgPA5Zmn8Xr33UIZUVRQKisrvKdM4dIzg0hdsRLn3r2xb9r0tuOCXYJpXrE5+67uY+nZpbzZ+M1iq1GI/5JeowV08GISo5cdY++F66hU0DbEg2+eaci+dzswqWdNCS5CCJNNUZsAaOHTAhcblzseo09P5/KLLxmDi0qF17sT8Jo4sViDy012DRtSYYBxDpq4DyZjyMu743GDaxhXm15xbgVZ2qw7HiNEcZCWlwJqFeROswBXWga582TjSjJVvxDijhRFMd0y6hrQ9Y7H5MVcIebll8g9H2EcUTTzMxwfe6w4y7yN55tjSf9jB3lRUVz//gc8Xn/ttmPaVGpDZcfKRKdHsyZyDU9Xf9oMlQohLS8FplarWPZiC0Z1DJbgIoS4qzNJZ7iUdgkbjQ2PVb49kGSfOMHFAQPIPR+BhacnVRYtNHtwAdA4OeH93nsAXPvhB3IjI287Rq1SM6jGIAB+DfsVg2Io1hqFuEnCixDlkKLTkfHPblJWr0aXlGTucsqUTReMt4za+bXD3tI+3760LVu5NGQo+uvXsa5eHf/flmFbq2iGQj8Mx86dcWjXDrRarn7wAYrh9nDSu2pvHCwduJh2kX+u/GOGKoWQ8CJEuaEoCllHjxL30cecb9uOyyNHcnX8BOPXL71M2saNGHJy7n8icVcGxcCmi8bwcustI0VRuD53LldGjULJzcWhXTuqLFqEpff9h1AXJ5VKhfekiahsbck+dJiUFStuO8be0p4ngp8AYNGZRcVdohCArCpd4ukSE0maPx9Fq8Wxc2ds69dHpZbMKQouNyKC1PXrSVu/AW1MjGm7xsUFC29vcsPCTNvU9vY4du6Mc69e2DVtIv/WHtDBuIM8t+U5HC0d2TlgJ1YaKwy5ucR//DEpvy8HwGXwYLzGjzNLx9yCuj5vHgn/+xS1kxNBGzdg4e6eb39MegzdV3XHoBhY1WsVVV2qmqnS8kvR68n4axeWvj5Yh4SUifnEHuT1W8JLCWXIyyN5wQKuffsdhqx/e/VbeHvj1LUrTt26YlO7dpn4BysKn/bqVdI2bCB1/QZyz541bVfb2eHYqSNOPXpg37w5KktLci9cIHXtWtLWrkMbG2s61sLbG+eePXDq2RObkBBzPI1SZ8reKawI/53h1o8xLKs+mXv2kHX4MEpODqjVeI0fj+vQIeYu874UnY6L/QeQc+YMTt264fv5zNuOGf3naHZE7+DJkCf5oMUHZqiy/NJdv86Vt94ia+8+ACx8KuIY2h6H9qHYNW2K2travAU+JAkvpTi8KIpCxh9/EP/pdLTR0QDY1K2LdYA/6dt3YMjMNB1r6ednCjLW1apJkCnndMnJpG/ZStr69WQdOvTvDktLHNq0wblHdxzat0dte+cO54rBQPaRI6SuXUfa5s0Y0tJM+6xr1MC5Z0+cenTH0tOzqJ9KqaO9coW03f+wftknVL+Qh1N2/v0WXl54T/4Ax/btzVPgQ8g+fZqLT/UHgwG/H3/AoU2bfPsPxR3i2S3PYqOxYduT26hgU8E8hZYzWYcPc2XMWHQJCahsbEBRUHJzTftVdnY4tGqJQ2goDu3a3dZqVpJJeCml4SX3/Hnip00jc89eACw8PPB4cyzOvXqhUqsx5OaSsWsX6Zs2kf7nTpTsf/9CWgUE4NStmzHIBAWZ6ymUSorBgC7xGrqrsSgGBevgqmgcS8e8PYasLNL/+JO09evJ+Ocf0OlM++yaNMGpRw+cOj9+xynf73ne3Fwydv5F6rq1ZPy1C7Ra4w61GvvmzXHu3QvHjh1R29vf+0RllD41lcz9+8ncu5fMPXvQXorOt19la4t906bYt2yBfcuWWFWtWirfXMRP+x9J8+dj6etL4Lq1qO3sTPsURWHA+gGEJYUxquEoRtYZacZKyz5FUUj6ZR4JM2eCXo9lYADHRnXiqpMer7AEPI9G43L4AlZJ6fkep68RiKpVUyzbtsCmWnVsLG2w1lhjY2GDldqqRP27lPBSysKLPiWFxK9nk7x0Kej1qKyscH32WdxfeP6uLw6GrCwydu4kbdMmMv7ahXLLpFLWISE4deuKU9euWFWpUlxPo8QyZGejvXoVbexVtFdj0V29ivZKrHHb1ato4+L+fXG+wdLHB+tq1bCuFoJNtWpYV6uGVZUqqDQaMz2LfylaLZl79pC6fgPpO3ag3HJb0bpGDZx79MCpW1csK1YslOsZW3S2kLpmLdlHj5q2q2xtcezYEedevbBv0bxE9+F4VIa8PLKPHDWFlZzTp+HWkTgaDfH+TuzyTsE7tDPPD/gUlZWV+QouJIbMTCJ79kQXexXXEc/h9fbb+faviVjD+7vfx9POk839NmOplmVRioI+PZ2r775L+rbtADj26M4Xj2WzNXFX/gMVhYB4aHReoVGEgaC4/LuvOcHhIBWHg1WcrqJCZ6HGWmONtYW1MdBobLC2uPH5xnYbjQ02Fja3bXO3dad/tf6F+jwlvJSS8KLodCQvW8a1r75Gn5oKgGOnTni+8zZWfn4FPo8+I4OMHTtI27iJjD178r0Q29SqZQwyXbpg6Vv21iJRFAX99evGEGIKJPkDij45+f4nUqux8PICRUEXF3fHQ1TW1lgHB/8baEKM4cbC5c4zqBYmxWAg+9gxUtetI33TZvQpKaZ9ln5+OPXojnP37lhXLdqOk3nR0aSuW0fq2rX5Whs0Hu44d+uOU6+e2NSsWaLezT0MxWAgNzyczD178/dbuYVVUBD2LVpg37IF6oZ1aL+hO9m6bBZ3W0xdj7pmqrzwpe/cScxLL4NGQ8Dy37GpUcO0L0+fR6flnUjKSWJG2xkFXj1bFFxOWBgxo0ajjY5GZWmJ17sTWBASz9xTP2GptuSJ4CfQGXTk6nPJ1eeSo8sxftbnYHU9g8AzyVQLS6daRA5WulvOawkn/VUcClZxJEhFqsOD/Z/1d/JnXd91hfpcS1x4+eabb5gxYwZxcXHUq1ePr7/+mqZ3WDvjpt9//52JEydy8eJFgoOD+fTTT+nW7d6Lm91UWsJL5p49xE+bRu75CMDYWuI1fhz2TRqALhf0efk/G3SgGG58KKDob/n+xodBjz49g/Q9x0jbdZjME+fzvTu0rVYZp5a1cWxWE0sXh/88Xv+f8xvPh40zuAWBaxDYu0MxvygZcnONQeRmy0nsLQEl1rhductU5rdS29lh6euDRcWKWPr4YFnRB0ufilje+N7C09PUcqBPTSX33Dlyws+RGx5Ozrlwcs+dz3eb7lYWnp5YV6uGTbUQY2tNSDWsA/wL5Z13Tvg50tavJ23DhnydaTVubjh17Ypzj+7Y1KtX7GFBURRyTpwgdc1a0jZuzBemrKoG4dyzF849exT5qsiFSXvliqllJXPvvttCr8bD3RhWWrTEvmULLL28TPs2R23m7V1vU8mhEhuf2Fjqw9t/xYweQ/rmzdjUro3/sqX5WiC/PfYt3x3/jroedVncbbEZqyx7UpYvJ+7Dj1Dy8rD08cF31pdst7vIu/+8C8AnrT+hZ1DPAp3LkJND5r59ZPz5J+k7d6KPT8i3X6lRFV2L+mQ3r0VWFU9yDXnk6HPI1RmD0K3BKFefi4uNC6/Wf7VQn2+JCi/Lli1j6NChzJkzh2bNmvHll1/y+++/Ex4ejucdOv7t2bOHtm3bMm3aNHr06MGvv/7Kp59+ypEjR6hdu/Z9r1dk4SUnFc5tuREockGX95/P/wkc/w0fNz7nXc8mflcGGVF6ADTWCh71cqgQmI4K3X2KeDC6HDXpl21Ii7YlK9EKuPkHVcHOIw+nytk4+uVgYVPAWTKtncA1wBhkbgYa10Dj13ZuDxxsDHl56BIS0MXFoY2LRxd/83O86ZaO/tq1+59IpcLC0/NGEKl4e0Dx8UHt6PhILyiKXo/28uX8gSb8HNrLl+/8AEtLrAMDb2+l8fC4bx15MVdI27iRtPXryT13zrRdbW+PY6dON0YKNSsxt2kUrZaMv/8x9o/Z8Ue+MGnXpImxf0znziWuH9F9+63Y2WHfpEmB+q28/sfr7Ly8k+frPM8bDd8ohuqLlzYhgQvde2BIT8fr3XfzjZi6ln2Nx5c/jtagLXOtTuZiyM4m7qOPSV25EgCHdu3w+fR/nMiLYsSWEWgNWkbWGcmohqMe6vyKopAbFkb6n3+S8edOck6dyrffwtsbh9B2OLZvj12zZqhtbB75ORVEiQovzZo1o0mTJsyePRsAg8GAn58fr7/+OuPHj7/t+AEDBpCZmcn69etN25o3b079+vWZM2fOfa9XZOEl8Rx80+ShH67Xqrh+xoGkcAcUgwpUCi7BmXjUSkdjfZdfgcYKNNZgYQVqS1Cpb/lQgVrzn21qUGmM+27dptagzTCQfi6btLOZZF/5t2c6KrAPcMCptguONVzQ2P3nOpnXIOkCpMYA9/inYu0MboHGMOMahMHeD62+Arpca7QpWcZAEh+PLi4ebXwcurh49AWc2VVla2tqIbktoPj4YOnpabb+BfqMTHLPnyM3/By558JN4caQkXHH4zUuLjdaaaqZ+tRYV62KISuLtE2bSFu/gewjR0zHqywtsW/XFucePXAIDS22PyIPS5+eTvrWraSuWUvWgQOm7SorKxweewznXj1xaN26SH5fiqKAVoui0xk/bn6t1YHO+LXu2jUy9+4jc+9e4x/s//Rbsa1b19i60qoltnXqFKjO1NxUQn8LRWfQlek5T5KXLiVu8hTUdnYEblifr0/Ve/+8x9rItXQN6Mr0ttPNWGXpl3fxIjGjRpMbHg5qNR6jRuH2/Ehis67yzIZnSMpJokPlDnwe+jlqVeHMw6RNSCDjr7/I2PkXmXv25GtlVtnaYt+iBQ7tjaOXinK0YYkJL3l5edjZ2bF8+XL69Olj2j5s2DBSUlJYs2bNbY+pXLkyY8eOZfTo0aZtH3zwAatXr+b48eO3HZ+bm0vuLcPE0tLS8PPzK/zwknYVVr0IFtbGUGFh/W+wyPf51v1WKGorUneHkfDrdvQpxl7g9g1r4vXKEKyDAu752KK6RaO9coW0zVtI27jR2PHwJktLHFq2xKlbVxw6dEDj4HDLg3Ig+SLK9QgMl8PQXjyLLiYK7dU4dElpaLPV6LI06LI1aLM0GLQF+0+lsrbGwtsLS08vLLy9sfT2wsLL+NnSx3ibR1OhQqlqhlcUBe2VWHLPhRtbaW4EmrxLl/K/WN6kvhFG9cbWOFQq7Jo2xalHd5wefxyNs3PxPoFCoo2NJXX9BlLXriEv4t91cjQVKuDQrh0qK6v/BA0taHX/btPpUHRaFO2dt6O9JaDodP/+/B7Av/1WWmLXtEn+f/MFtOLcCibvnUywSzAre6184MeXForBwKVBg8k+ehSHxx6j0jezTf8vw66H0X99fyxUFmzqtwlv+5I1c3BpkbZlK1fffRdDZiYaNzd8Z87EvnkzMvIyGLJpCBEpEdRwrcG8LvOws7S7/wkfgiEnh6z9+42tMjv/uq0PoE3t2sYgExpa6P3bHiS8FGm787Vr19Dr9Xjdcm8YwMvLi7O3TJx1q7i4uDseH3eXTpTTpk1jypQphVPwvThVhGFrH+ghWUePEj/1E1OTnGWVyniNH49DaKhZX4wtfX1xG/EcbiOeI+/SJdI2bSZt40Zyz50zpu+//jK+U27XFiv/AHQJ8cbbOXFxaBMS7tD34863A9SWYGGrxdJWj4WdAUs7PRa2etNnCzs9GidnVO6u4GoNbq7g6me8HeVZHaxK5zBclUqFVSVfrCr55ltwz5CdTW5E5I0WGuNtp9yzZ02dtW1q1sSpZ0/jSKH//B8ojSx9fHB/4Xncnh9JblgYqWvWkrphA/pr10i9wxuXImFhgeqWD7W9PbaNGmLfsiX2LVoUys95U5RxOYBuAQXrl1daqdRqKn44hQtP9CPjjz9I37YNp8cfB6CGWw0aeTXicPxhloUve+jbGeWVotWS8NlMkubPB8C2cSN8Z36OpZcneoOecX+PIyIlAndbd7567KsiCy4AahsbHNq1w6FdO+PtpbNnTUEm58QJck6dIufUKZIWLCRk9z9gptvXJeOm+SOYMGECY8eONX1/s+XFnLRxcSTM/Jy0dcae2Gp7e9xfeQWXIYNRl7Dhk1ZVquD+0ou4v/QiuZGRpG3cRNrGjeRFRZmG5d3JzanlLT0987WYWHh5YuntjYWXl/FdbF4WJF+EpEi4Hmn8nBRl/Do9FnJT4Mph48etrJ2hyydQf1CxdxIuKmpbW2zr1Ma2zr99txRFQZeQADpdmRwNBsYwZ1OzJjY1a+L59ltk7t1L9rHjoFGjsrA0BgtLS1SWxoBhDByW+baZtlta3th3SyixtIQ7brMo8jcJiVmJHIgz3h67dS2jsso6OBi3Ec9xfc73xH88FfsWLUx9mYbUGMLh+MMsP7ecF+q+gK3FnSdDFPlp4+K4MmasaRoCt5Ej8Bg92tSn7fPDn7MrZhfWGmu+av9VsbZqqVQqbGrUwKZGDTxeeQVdYiIZf/1F+s6dWLi4mrXfXZFe2d3dHY1GQ3x8fL7t8fHxeN9lQTJvb+8HOt7a2hrrEjIVsiEnh6RffuHaDz8aWydUKpz7PYHn6NGlYpZD66AgPF5/DffXXiU3PJz0rVvRp6XfHkw8PQve98LKDrxqGj/+Ky/TGGSSLtwSbi7AtXOQmQhrXoUza6HnLGPLVxmkUqnKRCtLQaksLHBo0+a22VpLq80XN6OgUM+jHr4OZTN8/pf7Sy+RtmkT2kvRJH7xJd6TJgIQ6heKr4MvVzKusOHCBp4MedLMlZZ8Gbt3E/vW2+iTk1E7OuLzv2k4duhg2r/i3AoWnFkAwMetPqaORx1zlQoYJ06t8OSTVHjS/L/bIg0vVlZWNGrUiB07dpj6vBgMBnbs2MFrr712x8e0aNGCHTt25Ovzsm3bNlq0aFGUpT4SRVFI37KVhOnTTcNZbRs2xOvdd7GtXXKWuy8olUqFTfXq2FSvXrQXsrIH79rGj1sZ9LDna/hzKpzfAt82g64zoG7/MtMKI8qG8nLL6FZqGxsqTplC9PBnSV6yBKeePbBr0ACNWsPT1Z/ms0OfsThsMf2C+xX77XFdcjIaZ+cSv6CoYjBw7bvvuDb7G1AUrGvWoNKsWfnm9zoYd5CP930MwCv1XpE5dP6jyH/DY8eO5ccff2T+/PmEhYXx8ssvk5mZybPPPgvA0KFDmTBhgun4UaNGsXnzZmbOnMnZs2eZPHkyhw4dumvYMbecsDCihwzlyujRaGNjsfD2xmfmZ1RZvKhUBpcSQa2B1qPhxb/Bp4FxmPqqF2DpIEiPv+/DhSgOl9Muc/LaSdQqNY/7P27ucoqVffPmOPfpA4pC3KQPjJ2qgb7BfbG1sCUiJYItl7YUeR2KTkfm/gPE/+9TIjt34XyLlkSEtifuw4/I3Lff2JG7hNElJ3P5hRe59vVsUBQq9O+P/5Il+YJLdFo0Y3aOQafo6OLfhZfqvWTGikumYpmkbvbs2aZJ6urXr89XX31Fs2bNAAgNDcXf35958+aZjv/99995//33TZPUTZ8+vcRNUqdLSiLxy1mk/P47KAoqa2vcRo7EbeSIuy58Jx6CXge7v4Sd/wODFmxdoNtnULuftMIIs/r++PfMPjabFhVb8MPjP5i7nGKnS07mQtdu6FNS8Bg7FvcXngfgk/2fsOTsEgAaezVmZJ2RtPRpWWitMPq0NDJ2/U3Gn3+S8fff+RYQ/S+NqyuOHTrg+PjjxrmRLM27fEH2sWPEjB6DLi4OlY0N3pM/oMItI3EB0vLSGLxxMFGpUdR2q80vXX7BxqJkT5FQWErMUGlzKOrwouTlkfTrr1z75lsM6cahz07duuL51lulajbRUifuFKx+GeJOGL+v2Ru6f26c9VeIYqYoCn3W9OFC6gU+avURfar2MXdJZpGyejVXx09AZW1N4Lq1WFWuTKY2k08PfMq6C+vQGYwtH9VdqzOi9gg6VemERv3g64PlXbpkmlAt6/DhfAuQalxcjKNj2rfHrmkTso8fJ33rNjJ27DCN5ANQOznh2L49jp07Y9+qJepi7CupKArJCxcRP3066HRY+fvjO2sWNtVC8h2nM+h4dcer7Indg5edF0u6L8HDzqPY6jQ3CS9FFF4ydu0iftr/yIuKAsC6Zg28330Xu8aNC/U64i70Wvj7c9g13bhcgp079PjcGGSEKEbhSeE8ue5JrNRW7BywE0erkjV7cHFRFIXo554ja+8+7Fu2wO+nn0wtLHGZcSw4s4Dl55aTrTNOr+Dn6MeztZ+lV1AvrDV3Dw+KTkf2sWOmwJJ34UK+/VZVg3Bs/xgO7dtjW6/uHRdMVbRasg4eJG3LVtK3b0d//bppn9rODofQUBwffxyHtm3yrZZd2PQZGVx9fyLpmzcD4Ni1CxU/+uiOcwrdbLWytbBlfpf51HCrcdsxZZmElyIIL+nbtxPz2uuAcV0ZzzGjce7bt0SsMlzuXD0Oq16GhBsT7NXuZ7yVZOdq3rpEufHF4S/4+dTPdKzckS/af2Hucswq79IlLvTqjZKbi8/0T3Hu1Svf/pScFJacXcLis4tJzTW2hLjbujO05lCeCnkKByvji7g+I4PMf/4x3g76a1e+9bKwsMCuSWMc27fHoX37B1q4FozLe2QfOULa1m2kb9uWb+I1lY0NDm3aGINM+9CHmqjwbnLCz3Fl1CjyLl4ES0u83nkHl8GD7ngLbenZpUzdPxWAL0O/pEOVDrcdU9ZJeCmC8KJotUT1H4B9ixa4v/xSiVunpdzR5RlbYP7+3LiopL0n9PwSqnc3d2WijDMoBrqu6EpsZiwz280sd5117+TanO9J/PJLNC4uBG7ccMeV1rO0Waw8v5J5p+cRn2XseB+QYcezqXWoE56L9shxuNHxF0Dt7IxDu7Y4tm+PfevWhfY3VzEYyDl50tgis3Ur2pgY0z6VpSX2LVvi2Lkzjo+1R1OhwkNfJ2X1auImT0HJycGiYkUqffE5tvXr3/HYPbF7eGX7K+gVPaMajmJknZEPfd3STMJLEd02UrRas3f4Ev9x5TCsfgUSb8zYXHcgdP2fsWOvEEXgaMJRhm4air2lPTv77yw3nSnvRcnLI6pfP3LPR+D8xBP4fDL1zsfp9aQfPcKJ1T+h/Xsv3vH5V4RXV/GjQodOOLYPxbZBgyKfBO3mAoVpW7eSvnVb/ttTFhbYN22K4+OP49ixQ4Hn6jLk5hL/8VTjYA7AvnVrfGZMv2OgA7iQeoHBGwaTrk2nZ2BPpraeWqqWQylMEl6KYbSRKEG0ObBzGuz5ChQDOHhDr68gpLO5KxNl0NR9U1kavpReQb2Y2vrOL9LlUdbRo1x6ZhAoCpXnzcO+uXFEqSEzk4zdu8n4cycZf/2Vb0FWRa3mUoAdf/lncbiqikQ3C7oEdGFE7REEuwQX+3PIjYggbcsW0rduMy6MeJNajV3DhsYWmcc73XViybzLl4kZNYrcM2GgUuH++mu4v/TSXeedSclJYdDGQUSnR1Pfoz4/df4JK03JmoW9OEl4kfBSPl0+aByRdP288fv6g41LDNiUzoUNRcmjM+jo8HsHknKS+K7jd7T2bW3ukkqUq1OmkLJkKVZVquAydIhxdND+/aZ5YADUjo7GWZbbtzd2lnVy4mDcQeaenMveq3tNx7Wr1I6RdUZS37O+GZ6JsS/PzRaZnJMn8+2zrVfP2CLT+XGsKlUCIH3HDmLHT8CQno7GxQWfz2bg0KrVXc+v1Wt5cfuLHIw7iI+9D792/xU3W7cifU4lnYQXCS/llzYb/vgY9n4DKODkC72+hqrlr/ObKHy7r+zmpe0v4WLtwo7+O7BUy23kW+nT07nQrTu6xMR82y0rVzZ1trVr1PCut99PXz/Nzyd/ZtulbSgYX5oaejZkRJ0RtPFtY7bbKdorV0jbto30rduMaxDd8rJpU7MmVoGBpK1fD4Btgwb4fvE5lndZ0gaMt6um7J3CivMrsLOwY2G3hYS4hNz1+PJCwouEF3Fpr7EVJtk4rJ1Gw+Hxj8FaOlqLh/feP++xNnItA6oN4P3m75u7nBIpY9cuYt8Zh1VQEI7tQ42jgwIDHyh4XEy9yLzT81gTucY0V0yISwgjao/gcf/HsVCbb0FAbUIC6du3k75lK1kHD4LBYNrnOnw4nm+OvW/fyIVnFjL94HRUqJjdYTZtK7Ut6rJLBQkvEl4EGBd+3PEh7J9j/N65MvT+GgJDzVqWKJ1ydDmE/hZKpjaT+V3m09CroblLKvPiM+NZeGYhv5/7nSxdFgCVHCrxbO1n6V219z3niikOuqQk0nfsIGvffpy6dsGxY8f7PmZXzC5e/+N1DIqBtxq/xbBaw4qh0tJBwouEF3GrqL+NK1SnXDJ+32QkdJwC1oU3n4Mo+7Zd2sbYnWOpaF+Rzf02o1aV7MX/ypLU3FSWnl3K4rDFJOcmA+Bm48bgmoMZUG1AqZkk8HzyeYZsGkKmNpMngp9gcovJ5XZk0Z08yOu3/O8rwbR6LZEpkWy/tJ2I5Ahzl1N6BbSBl/dA4xHG7w/OhTmt4OJu89YlSpWbK0h3CegiwaWYOVs782K9F9ny5BbGNx1PRfuKXM+5zqwjs3h8+eN8efhLrmVfM3eZ95SUk8Trf7xOpjaTxl6Neb/Z+xJcHoG0vJQAufpcLqZe5ELqBSJTIk2fo9Oi0SnG+702GhsWdF1Q7qaLLnSRf8La1yH1svH7Zi9Dh0lgVXTTg4vSLz0vndBloeQZ8vi95+9Ud61u7pLKNa1By+aozfx08iciUyMBsFJb0adqHwZUH1DiOr/m6fMYuXUkRxOO4ufox6/dfqWCTQVzl1XiyG2jEhpesnXZRKVG5QsoF1IvcDn9MgbFcMfH2FnY4WDpQEJ2At723izpvgR3W1mM8JHkpMHW9+DIAuP3roHQ5zuo3Ny8dYkSa03EGt7f/T6BzoGs7r1a3jGXEAbFwF+X/2LuqbmcSDxh2h7iEkL3wO50C+iGt/3dR/0UB0VReH/3+6yNXIujpSOLui8i0DnQrDWVVBJezBxeMrWZXEi5QGRqpOlzZEoksRmxpuF//+Vo5UiQcxBBFYIIdA4kqILxay87L9K16QzaMIiLaRdp4NmAuY/PLdcTGRWa89uNrTDpsYAKWrwKj70PlrbmrkyUMC9te4ndsbt5tf6rvFTvJXOXI/5DURQOxx9mUdgi/or5yzRCSYWKJt5N6B7YnY5VOuJkVfyvCT+d/Ikvj3yJRqXh2w7f0tK3ZbHXUFpIeCmm8JKam2pqSbk1qMRlxt31MRWsKxiDiXMQgRUCTV+727rf891cVGoUgzYMIl2bTt+qfZnScoq8+ysM2Smw5V04ttj4vXs1GLwCKjzYwm+i7LqefZ0Ov3dAr+jZ0HcDlZ0qm7skcQ+pualsvbSVDRc2cDj+sGm7ldqKdn7t6B7YnTa+bYrlDeCO6B2M+XMMCgoTmk7gmRrPFPk1SzMJL0UQXq5nX2dH9I58QSUxO/Gux7vbuv8bUG4JKq42D7/y8e4ru3llxysYFAPjm45nUI1BD30u8R/hm2HdG5ARDxUqw7B14OJv7qqKnl4LmjI80VpelnGun+uRkHQBdLnQ8vUH6uO05OwSPtn/CbXdarOkx5IiLFYUttiMWDZGbWR95HpT3xgAJysnHvd/nO4B3Wno1bBIOmCfTTrL0E1DydZly7xABSThpQjCS3hSOE+ue/K27V52Xrfd6gl0DsTZumimpF9wegEzDs1ArVLzXcfvaOkjTZCFJjUG5veCpEjjzLxD14J7VXNXVTSyk2H5cxD5B9i5GwObSxXj5wpVbnxUNrZAlfTbaNpsSL54I6BE/htUki5A2pXbj+84GVqPKfDph2wcwrHEY7zd+G2G1hpaaGWL4qMoCuHJ4Wy4sIGNFzaSkJ1g2lfRviLdA7vTPaA7VV0K5//7texrPL3haeIy42hesTnfdvxWZmMuAAkvRRBecvW5jPlzTL6gEugciINV8c4VoigKE3dPZE3kGhytHFnSfQlVnKoUaw1lWnqcMcBcCwcHL2OA8SxjI0vSrsKiJyDhTMGOd/C6JdT8J+Q4VwKLYpgoTJtjDChJN4KJKajcDCj3+DNm4wyuQaC2gJgDUKkJjNxeoMvGZsTSeUVnVKjY/tR2PO08C+XpCPPRG/Qcij/E+gvr2X5pOxnaDNO+6q7V6R7Qna4BXfGyv/Pii/eTo8thxJYRnLh2An8nfxZ1W1Rkb2bLGgkvJXS0UWHJ0+fx3JbnOJ54nADnABZ3W1xqJmkqFTISYWEfiD8Fdm4wdA141zF3VYXj2nlY+ASkRhtDyYBFYGFjnMAvJdr4kXzz60uQl3GfE6rAseItgeY/IcfJt+C3pXR5/waUm+Ek6YIxoKRe5p4BxdoZ3AKNIcU1ENyCjF+7BYHdjVu1abHweQ1jzW+Gg+P9X5xudrZs6t2Unzr/VLDnIUqNHF0Of8X8xfoL6/nnyj/5Ovo2rdiU7gHGjr4F/fuqKArj/h7HpqhNOFk58Wv3X+XN5QOQ8FLGwwsYmyUHrh9IfFY8rX1bM/ux2WjUGnOXVXZkJcHCvnD1GNhUgCGrwLeUTwd/5TAsfgqyrhtf2IesvHe/HkUx3l5KuXRLoInOH3S0Wfe+pkptDDAVbgk3LlWMrSHJl/IHldQYuMuUAQBYOf4bUG6Gk5tBxc4NCtKB/Yf2EHsEes4yrnd1H/3W9uNc8jk+aPEBT4bcfttYlB0pOSmmjr5HEo6YtltrrGlXqR09AnvQ2rc1lvcI43OOz+GbY99gobLg+07f07Ri0+IovcyQ8FIOwgvAmetnGLZpGDn6HJ6t9SxjG481d0llS3YKLH4SYg6CtZNxFJJfKf1jFLEDlg0BbSb4NIBBy8H+EecLUhTIvHZLoLlD640+98HOaeVgDCT/bT1xDTLW+6gj7HbNMK46Hvw4DPr9nodGpkTSZ00fLNQW7Oy/U5r+y5ErGVfYeGEj6y6sIyo1yrTd2dqZzlU60yOoB/U96ucb8bn54mbe/uttAAm7D0nCSzkJLwCbozbz9i7jf5hPWn9Cz6CeZq6ojMlNh8X9IXqP8YX1md/Av5W5q3owJ36H1S+BQQeB7WHAwuJZXdtggMzEW0LNLS042cnGFpj/3uZx8Hz0gHIv8WfguxagsYZ3Ltxzfauvj37NDyd+ILRSKF93+LroahIllqIohCWFGTv6Rm3MtwSBr4Mv3QK60SOwB1m6LIZvHk6uPpfBNQYzruk4M1Zdekl4KUfhBf79I2ultuKXLr9Q16OuuUsqW/IyYclAiNoFFrbw9BIIal9op49KjSItL416HvUK7Zwm+76DzeONX9fuB33mgEU5nuBQUeCr+sa+Nf0XQM3edzlModvKbsRkxPBpm0/pFtitWMsUJY/eoOdA3AFTR9+bq1yDcQ6ZPEMebXzb8PVjX8st/IckCzOWM6/Wf5X2fu3JM+Qx6s9RxGfGm7ukssXK3tjiUrUj6LLh1wFwbusjnzYqNYp3/nqH3qt7M3jjYHZe3vnI5zRRFNg+5d/g0vRFeGJu+Q4uYGzVqd7D+PXZjXc97NS1U8RkxGBrYUuoX2jx1CZKNI1aQwufFkxtPZWdA3Yyo+0M2lVqh4XKgjxDHlUrVGV62+kSXIqJtLyUEZnaTAZvHExESgS13Goxr8s8bCxszF1W2aLLhd+HQ/hGUFtC//lQvfsDn+Zy+mXmHJ/D+gvr861p5Wnnyereqx995JheB+tHwdFFxu87TILWY4v2dkxpcnE3zOtm7Ij9dsQdR0N9euBTFoUtomtAV6a3nV78NYpSIzknmQNxB2jm3UwWW3xE0vJSDtlb2vP1Y19TwboCp6+fZtKeSZSxXGp+FtY3bjX0AYMWfhsKp1cV+OFXM64yec9keq3qxdrItRgUA6F+oSzqtojKjpVJyErgi8NfPFqN2mz4bYgxuKjU0OtraPOmBJdb+TUDW1fISYHovbft1hv0bL64GYBuAXK7SNybi40Lnf07S3ApZhJeypBKjpX4PPRzLFQWbIraxE+nZF6KQqexhH4/QZ3+xg6wy5+D48vu+ZDErEQ+2f8J3Vd1Z8X5FegUHa18W7Gk+xK+fuxr6nnUY3LLyQD8fu53DsYdfLjaspONw7vDNxrnbhmwCBrKjLC30VhASBfj13e4dXQo/hDXsq/hZOVEK59S1jlbiHJCwksZ08S7CROaTQDgqyNfFW4/CmGksYC+c6DBYOO8JKtehCMLbzssKSeJzw5+RteVXVlydglag5am3k1Z0HUBczrOobZ7bdOxTbyb8FTIUwBM3jOZbF32g9WUFgu/dDO2JFg7G+eleYhbWuVG9RstKuEbjP2DbrExyhhoOlXpdM85PYQQ5iPhpQzqX60/A6oNQEFh3K5xRCRHmLuksketgZ5fQ+MRgAJrX4ODcwHjqrazjsyiy4ouzD8zn1x9LvU96jP38bn81PknGng2uOMpxzQag6edJ9Hp0Xx37LuC15J4Dn563Djdv4M3PLcJqsiaV/cU9NiNmYWjIf60aXOePo9tl7YB0D1Qwp8QJZWElzJqXNNxNPFuQpYui9f/eJ2UnBRzl1T2qNXQfSY0fwWA9I1v8d36Z+myogtzT84lW5dNLbdafNfxOxZ0XUCzis3ueTpHK0cmNp8IwPwz8zl97fQ9jwcg5jD83Nk4fb5bVRixFbxqPfJTK/Os7I1z3gCc3WDa/M+Vf0jPS8fT1pOGnqV8RmUhyjAJL2WUpdqSme1m4uvgS0xGDG/+9SZag9bcZZU9KhVZj73P3Hrd6OLnw7fXD5GhzSDEJYRZ7WexpPsSWvu2zjcT572E+oXSNaArBsXApD2T7v07i9gO83tCdpJx1tznthgnfhMFc+utoxs2RW0CoHNAZxnyKkQJJuGlDHOxceHrx77GzsKOA3EHmH5AhnwWphxdDvNPz6frqm7MSjtFmkZDQJ6WGQnX+N2hEY/5tS9waLnV+KbjqWBdgXPJ5/jl1C93PujEb8b5ZrSZxlsgw9Y/+nT/5U1IF0AFV49DagxZ2ixTH7HuAXLLSIiSTMJLGRfsEsz/2vwPFSqWhi/lt/DfzF1SqZenz+PXsF/ptrIbnx36jKScJPwc/fik9SesqjaCLplZqHd9Cjum3NYZtCBcbVxN04vPOT6HCykX8h+w91tY+bxxtFPtJ+HpZfec5l7chYOncdg0QPgm/rj8Bzn6HCo7VqamW03z1iaEuCcJL+VA+8rteb3B6wBM2z+NQ3GHzFxR6aQ1aFl+bjndV3Vn2oFpJGYnUtG+IlNaTmFNnzX0DOqJpu3b0PkT4wP++QK2vPtQAaZ7QHfa+LZBa9Ayac8k9Ab9jVlzJ8MW42gymr0MT/wos+Y+ipu3js6uN90y6hrQ9aFazIQQxUdm2C0nFMU48mjTxU24WLuwpMcSfB18zV1WqaA36NkQtYHvjn1HTEYMAJ62njxf93meCH4CK80dwsOBH2HjW8avG4+Abp8ZO/g+gLjMOHqv7k2WLovxjd9h0Pl9cOzmrLkfQOsxMvnco7oWAbMbkWJhSfvKldApetb0XkNghUBzVyZEuSMz7IrbqFQqprSaQk23miTnJvP6H6+Tpc26/wPLMYNiYFPUJvqs6cN7/7xHTEYMrjauvNPkHTY8sYGB1QfeObgANH3eOLstKjj0E6x7Awz6B7q+t703YxuNBWDWoRnEnlpyY9bc2dBGpvsvFO5VwT2ErbbW6BQ91V2rS3ARohSQ8FKO2FrYMqv9LNxs3DiffJ53/3k339o6wkhRFHZc2sGT657knV3vcDHtIs7WzoxuOJpNT2xiSM0hBVs3quFQ6Pu9MXAcXQirXzauO/QAnqrckYaKFdkofOjujtJ/ETQc8pDPTNxRtW5ssrcDZDkAIUoLCS/ljLe9N7Mem4Wl2pId0Tv49ti35i6pxFAUhV0xuxiwfgCjd47mfPJ5HCwdeKX+K2x+YjMj6ozAztLuwU5abwA8+TOoLeDEMlgxAvQFHLKeFov6l+5MvnIRK0Vht60166werPVG3F+cf0sO21gD0MXvMTNXI4QoCAkv5VA9j3p80OIDAL4/8b1pEbryLDotmhFbR/DqjlcJSwrD1sKW5+s8z+Z+m3m53ss4WD3CaJ5afY0LOqot4cxq+G2YcYXqe7k5a25iGAE2Hrxc7RnAuNrxtexrD1+LuM2W3FgUlYqGOTlUTDxv7nKEEAUg4aWc6l21N8NqDgNg4j8TCbseZuaKzENv0LPwzEL6re3HwbiD2GhsGF5rOJv7beaNhm/gbO1cOBeq3h2eXgIaa+OkaEsHGVeAvpOYQ7fMmhsMI7YyrNnb1HCtQVpeGtP2TyucmgQAG2+uIJ2RdceFGoUQJY+El3JsTKMxtPJtRY4+hzf+fKPcvaO/mHqRZ7c8y/SD08nR59DMuxmreq/izcZv4mrjWvgXDO4Eg34DC1uI2GacZC4vM/8x52+dNbehcdbcCpWxVFsypeUUNCoNWy9tZUf0jsKvrxy6mHqRM9fPoEFNp8wsCN/0UEPbhRDFS8JLOaZRa5jedjr+Tv7EZcYx5s8x5OnzzF1WkdMb9Mw/PZ8n1z3J0YSj2FnYMbH5RH58/EcqOVYq2osHhsLgFWDlAFF/waInITfduO/Eb7BkAGizbsyauw7s3UwPreFWg2drPwvA1H1TSctLK9pay4Gbc7s0r9gMV40tpMdC7FEzVyWEuB8JL+Wck5UTXz/2NY6WjhxLPMZH+z6ijE39k8+F1AsM3TyUzw59Rq4+lxYVW7Cq9yr6V+tffBOT+beCIavB2hmi98DCvrDrs39nza3z1F1nzX2p3kv4O/mTmJ3I54c+L556yyhFUdgYZbxN1D2oJ1TtYNxxy0KNQoiSScKLwN/Zn8/afYZapWZ1xGoWhS0yd0mFTmfQ8dPJn3hq7VOcSDyBg6UDk1tM5vtO3+Pj4FP8Bfk1gWFrwKYCxByEPz4ybm/+CvT94a6z5lprrJnccjIAK86vYN/VfcVTbxkUlhTGxbSLWGuseazyY8Z+SQDh0u9FiJJOwosAoKVvS95qbJwR9rNDn7Hnyh4zV1R4IpIjGLJxCF8e+ZI8Qx6tfFuxqvcq+oX0M+808D4NYPh6sLtxa6jjZOPSAveZibeRVyMGVBsAwJQ9U8jW3aXjr7inm7eM2lVqh72lPQQ/DioNJJyBpCgzVyeEuBcJL8JkcI3B9KnaB4Ni4K1db3Ex9aK5S3okOoOOH0/8SP/1/Tl1/RSOlo581OojvuvwHd723uYuz8i7Drx6EF7e+0DT/Y9uOBpve29iMmL45ug3RVxk2XNz9mS4ZWI6O1eo0tL4tbS+CFGiSXgRJiqVionNJ1Lfoz7peem8/sfrpbZTaHhSOM9seIavjn6F1qClXaV2rOq9ij5V+5S8Rffs3cDrwVYxdrByYGLziQAsDFvIycSTRVFZmXUk/gjxWfE4WDrQulLrf3fcvHUkQ6aFKNEkvIh8rDRWfNH+C7zsvLiYdpEXt77IpqhNpebWhNagZc7xOQzcMJCwpDCcrJz4pPUnfP3Y13jZe5m7vELVtlJbugd2x6AYmLRnEtqCztwrTK0uHat0xFpj/e+OajdaYaL3QOZ1M1QmhCgICS/iNu627nz92NfYWthy6vop3tn1DqHLQnnvn/fYE7sH/QMuMFhcziad5ZkNz/DNsW/QGXS092vP6t6r6RnUs+S1thSScU3G4WLtQkRKBHNPzTV3OaWC1qBl66WtAHQN6Jp/p0sV8KoNigHObzFDdUKIgpDwIu6ohlsNVvRawfN1nsfXwZcsXRZrI9fy4rYX6bi8I58e+JTT10+XiGHVWr2Wb459w9Prn+Zs0lmcrZ35tM2nzGo/Cw87D3OXV6RcbFyY0GwCAD+c+IGI5AgzV1Ty7Y3dS0puCq42rjT1bnr7ATdbX2TItBAlloQXcVd+jn680fANNj2xiQVdFzCg2gCcrZ25ln2NRWGLGLh+IL1W92LO8TlcTr9slhrPXD/DwA0DmXN8DjpFR8fKHVndezXdAruV2daW/+ri34XQSqHoDDo+2PtBiW0ZKylu3jLq4t8FC7XF7Qfc7PcS+cfdl3AQQpiVSikJb50LUVpaGs7OzqSmpuLk5GTucsocrV7L7tjdbLiwgT8v/0mu/t8FBut51KN7YHc6+3cumun1b5Gnz2PO8Tn8fOpn9IoeF2sX3m3+Lp2rdC43oeVWcZlx9F3TlwxtBuOajGNwzcHmLqlEytZl025ZO7J12SzsupD6nvVvP0hR4IvakBYDTy+Fal1vP0YIUege5PVbwot4aBl5GeyI3sGGCxvYH7cfg2IAwEJlQUvflnQP6E77yu2xtbAt1OueunaKibsnEpFivEXS2b8z7zZ7t8gDU0n3+7nf+XDvh9ha2LKy18qiX+qgFNp8cTNv//U2vg6+bHpi092D7oa34OCP0GAI9J5dvEUKUU49yOv3HdpMhSgYBysHelftTe+qvUnMSmRT1CY2RG3gzPUz7IrZxa6YXdhZ2NGhcgd6BPagacWmd26mL6BcfS7fHvuWeafnYVAMuNq48n7z9+lUpVMhPqvSq19wPzZe2Mih+ENM2TuFHzr9UC5boe5l4wXjEOiuAV3v/bOp3s0YXs5tBoMe1JpiqlAIURDS8iIK3YXUC2y4sIENFzZwJeOKabubjRtdA7rSPbA7tdxqPdAL6/HE40zcPZGoVOPMp90CujG+6XhcbFwKvf7S7FLaJfqt7UeuPpcPW35I3+C+5i7pNjqDjuj0aNSosVBbYKm2NH7WWGKh+vdzYQevtLw0QpeFojVoWdFrBSEuIfcoMg9mVIXcVHhuK1RuVqi1CCFuJ7eNJLyUCIqicDzxOOsvrGfLxS2k5KaY9vk7+dMtsBs9Anrg5+R313Pk6HL45tg3LDizAINiwM3GjYktJtKhcodieAal0y+nfuHzw5/jaOXI2j5rcbd1N3dJgLG/yarzq5h/ej6xmbH3PT5fsLnl892+vuPnWwJRXGYcO6J3ULVCVVb1XnX/gpePgFPLodUo6PRhIfwEhBD3IuFFwkuJozVo2Ru7l/WR6/nz8p/k6HNM++p61KV7QHe6BHTJ12/lWMIxJu6eyMW0iwD0DOzJuKbjcLZ2Lu7ySxWdQcegjYM4c/0Mnap04vNQ864+nZqbytKzS1kctpjk3GQAbDQ2WKot0Sk6tHotOkVXbPWMajiKkXVG3v/AUytg+XPgVhVeP1z0hQlRzpWY8JKUlMTrr7/OunXrUKvV9OvXj1mzZuHg4HDXx4SGhvLXX3/l2/biiy8yZ86cAl1TwkvJl6nNNHX03Xd1n6mjr0aloaVPS7oHduf09dMsOrMIBQVPW08mtZhEO792Zq689AhPCmfg+oHoFB2fh35uln5BCVkJLDyzkN/CfyNLlwWAr4Mvz9Z6lt5Ve2NjYWM6VlEUdAYdWoPW9HHz+3t+vhF88n2+x/G2lrYMrzW8YJ3Ic9JgeiAYtMb1pzzucZtJCPHISkx46dq1K1evXuX7779Hq9Xy7LPP0qRJE3799de7PiY0NJSQkBA+/PDfZlo7O7sCBxEJL6XLtexrxo6+FzZw+vrp2/b3DurN203eltaWh/DVka/48eSPuNm4sabPmmL7GUanRfPzqZ9ZG7kWrcG4ZEGwSzAjao+gs3/nR+q0XewW9jXO99JxsnHhTCFEkSkR4SUsLIyaNWty8OBBGjduDMDmzZvp1q0bMTEx+Pj43PFxoaGh1K9fny+//PKhrivhpfSKSo1iw4UNbLm4BQu1BWMbjaVNpTbmLqvUytXn8tS6p4hKjaJP1T581OqjIr1e2PUwfjr1E9subTO1pjX0bMiIOiNo49umdI58OjgXNrwJlZrCyG3mrkaIMq1EhJeff/6ZN998k+TkZNM2nU6HjY0Nv//+O3373nkURGhoKKdPG6ed9/b2pmfPnkycOBE7O7s7Hp+bm0tu7r8TpaWlpeHn5yfhRQjgaMJRhm0ahoLC952+p6VPy0I9v6IoHIo/xE8nf2J37G7T9raV2jKi9ggaejUs1OsVu7RY+LwGoII3w8GxbC3uKURJUiLmeYmLi8PT0zP/xSwscHV1JS4u7q6Pe+aZZ6hSpQo+Pj6cOHGCcePGER4ezsqVK+94/LRp05gyZUqh1i5EWdHAswFPV3+aX8/+yod7P2Rlr5XYWd75jcCDMCgG/rr8F3NPzeVE4gkA1Co1Xfy78Fzt56jmWu2Rr1EiOPmATwOIPQrnNkGj4eauSAjBQ4SX8ePH8+mnn97zmLCwsIcu6IUXXjB9XadOHSpWrEiHDh2IjIwkKCjotuMnTJjA2LFjTd/fbHkRQhiNajiKPy//yZWMK3x99GvGNR330OfSGrRsjtrMz6d+Ns1wbKW2om9wX4bVGoafYxn8v1etuzG8nN0o4UWIEuKBw8ubb77J8OHD73lMYGAg3t7eJCQk5Nuu0+lISkrC29u7wNdr1sw4OVRERMQdw4u1tTXW1tYFPp8Q5Y2dpR0ftPiAl7a/xOKwxXQJ6EI9j3oPdI47zdHiYOnAgGoDGFxzcImZS6ZIVO8Of34MF3ZCbgZY3320pBCieDxwePHw8MDDw+O+x7Vo0YKUlBQOHz5Mo0aNAPjjjz8wGAymQFIQx44dA6BixYoPWqoQ4oZWvq3oFdSLtZFr+WD3B/zW8zesNFb3fdyd5mhxtXFlSM0hDKg2AEcrx6Iu3fw8a4CLPyRfNI48qtnL3BUJUe6pi+rENWrUoEuXLjz//PMcOHCA3bt389prrzFw4EDTSKMrV65QvXp1Dhw4AEBkZCQfffQRhw8f5uLFi6xdu5ahQ4fStm1b6tatW1SlClEuvN34bVxtXIlMjeTHkz/e89iErARmHprJ48sfZ/ax2STnJuPr4Mv7zd5nS78tjKwzsnwEFwCVynjrCODsBvPWIoQAinhhxsWLF/Paa6/RoUMH0yR1X331lWm/VqslPDycrCzjBFZWVlZs376dL7/8kszMTPz8/OjXrx/vv/9+UZYpRLlQwaYC7zZ7l7f+eou5J+fSqUqn29b3uZR2iV9O/VI25mgpTNW7wb5v4PwW0OtAU05/DkKUELI8gBDliKIojP5zNH9c/oPabrVZ1G0RGrWmbM7RUpj0OvisKmQnw7D1ECDzDwlR2ErEUGkhRMmjUql4r/l7HIw7yKnrp5h2YBox6TFlc46WwqSxgJCucPxXCN8o4UUIMyuyPi9CiJLJ086TNxu/CcCy8GXsjt2NRqWhe2B3lvdczjcdvpHgcifVuxk/n10PZavBWohSR1pehCiHngh+gl0xu9gdu5s+VfuU3TlaClPQY2BhAynREH8avGubuyIhyi0JL0KUQyqVii/af4GiKGjUGnOXUzpY2UNgKJzbbLx1JOFFCLOR20ZClFNqlVqCy4OqLkOmhSgJJLwIIURBhXQBVHD1GKTGmLsaIcotCS9CCFFQDp7g19T4dfgm89YiRDkm4UUIIR5EtZujjuTWkRDmIuFFCCEeRPUexs8X/4GcVPPWIkQ5JeFFCCEehHtVcA8BgxbObzN3NUKUSxJehBDiQcmtIyHMSsKLEEI8qJtDpiO2gy7PvLUIUQ5JeBFCiAfl2xjsPSE3DS7+be5qhCh3JLwIIcSDUquhWlfj1+EbzVuLEOWQhBch/t/enUdFcaZrAH8aGppFaJewKlFQUaNjXK4aE/cNo2M0auIyxmWCJi434yTRyMSROCdOMoa5OZOrudkMuMaIkxj1JBIHUGciMZGI4sKiEJcgGEEaUEDs/u4fBS1oozRUdVnN8zunD71UV73vQYvn1PdVFVFjWK+2+zVv1EjkYAwvRESNEToUcPMGSvOAvGNqV0PUrDC8EBE1hpsH0GmE9JxDR0QOxfBCRNRYNResy2B4IXIkhhciosbqPAbQuQJXTgFFuWpXQ9RsMLwQETWWV2ug/ePScw4dETkMwwsRUVNYr7bL8ELkKAwvRERN0bU6vFw4DNwoUrcWomaC4YWIqCladQACegDCAmQlqF0NUbPA8EJE1FQ1Q0eZvFEjkSMwvBARNVXN0NHZRKCqXN1aiJoBhhcioqYK6gX4tgWqbgA5B9WuhsjpMbwQETWVTsehIyIHYnghIpJDzdBR5jeAxaxuLUROjuGFiEgO7QcBBl/g+q/ApaNqV0Pk1BheiIjkoHcHOo+WnnPoiEhRDC9ERHLh1XaJHILhhYhILp1HAy5uQGE2cDVb7WqInBbDCxGRXDyMQOhg6XkGh46IlMLwQkQkJ+vQEcMLkVIYXoiI5FQTXi79CJRdUbcWIifF8EJEJCdjWyC4NwAhXfOFiGTH8EJEJLcu46WfmTzriEgJDC9ERHKrudruuWSgskzdWoicEMMLEZHc/B8BWrYHzJXAuSS1qyFyOgwvRERy0+mArr+VnnPoiEh2DC9EREqoGTrK2geYb6lbC5GTYXghIlJCyGOAZyug/Bpw8Xu1qyFyKgwvRERKcNUD4WOl57xgHZGs9GoXQETktLqMA45/JoWXiL9Kc2GcTVU5UHAayD9R/UgHinKAwJ5AlyeB8AigVQe1qyQnw/BCRKSUTiMBvQdQfB448iHQtg/QuiPg1VqbQeZG0e2Acrk6rFzNAoTl7mVzkqXHN8sBv25Al7HSkah2/QAXV8fXTk5FJ4QQahchp5KSEhiNRphMJvj6+qpdDhE1d9umA1l3XGnXYARahwJtOgKtw6RA0zpMeng/pH6wEQIwXawOKOm3A4vpou3lvR4CgnpKR1sCfyMdabmQAmTuk34Kc61l2wCdx0hHZDqOBDy4nyaJPX+/GV6IiJRUeA44/L9A4VmgKBcouXTv5Q2+UrC5M9S06Qh4+8kfbMy3pKMn1iMqx6WfFcW2l2/VoTqk9LwdWHwC66+r/BpwNlG6VcLZ/UCF6fZnLm5A+8erh5fGSn1Ts8XwwvBCRA+qqnLg2s9SqCnKqX6ck4KN6RKAe+yS3VtUB5s7Qk3rMKBFwP2Dzc3rQMGp2wEl/4Q0X8VcefeyLnppuCeo+mhKYE8gsAfgYWx87+Zb0plXWfukozKF2XU/f6hL9fDSk9LwkitnNjQnDC8ML0SkRVUVUrCxBprqcFOYUz1kc4/dtZt3daCpNRzl7Qf8mimFlMsnpKM/ttbh3qI6oPzm9hEVv66A3qBQo9UKz1UHmW+k4SVLrevheLaqHl4aK80dakpoIk1geGF4ISJnc6sSuHb+dqipfeTGdNH2pFlbWgTUDSmBPYFWoYCLylfOKC8GziVKR2Syv607bOWil4aXwqsn/bbpqFaVpCCGF4YXImpOblUCxRfuHooquwK06VQdUh6VQotPgNrV3p/5FnDpB+mITNY+aU5ObQ+FSxN+w58EQgZweMlJMLwwvBAROY/Cc0BWghRkzn9Xd3jJoyXQefTt4SXPVqqVSU3D8MLwQkTknCpM0tlLWQnS8FJ50e3PXPTAwMXAiFU8GqNBDC8ML0REzs9iBi7+IB2RydoH/JohvR86FJgaC3i3Ubc+sgvDC8MLEVHzc2oXsGsRUHUdMD4MTN8CBD2qdlXUQPb8/eaNGYmIyDl0nwRE/ks6e8p0AdgwBjixQ+2qSAEML0RE5DwCHgEWJAOdRgO3KoAv5gP7oqQzmMhpMLwQEZFz8WwFzPwcGLJMev39+8DmScD1q6qWRfJRLLysWbMGjz/+OLy8vNCyZcsGfUcIgVWrViEoKAienp4YNWoUsrOz7/9FIiKi2lxcgRErgWlbpCsI//xv4MOhQN4xtSsjGSgWXm7evIlnnnkGCxcubPB31q5di/feew8ffPABjhw5Am9vb0RERKCiokKpMomIyJl1mwBEJkoX6yu5BGyIANK2qV0VNZHiZxvFxcVh6dKlKC4uvudyQggEBwfjlVdewauvvgoAMJlMCAgIQFxcHKZPn96g7fFsIyIiukuFCfjiBSDrG+l1/wVAxF8BVzd16yIrTZ5tlJubi/z8fIwaNcr6ntFoxIABA5CSklLv9yorK1FSUlLnQUREVIeHEZi+DRgWJb3+4SNg41PSLRRIcx6Y8JKfnw8ACAioe9+NgIAA62e2vPXWWzAajdZHSEiIonUSEZFGubgAw1YA0z8DDL7AhcPSPJhLqWpXRnayK7ysWLECOp3uno+MjAylarUpKioKJpPJ+rh48aJDt09ERBrTdRwwP0m6wWNpHhA7Fvhpk9pVkR3suvnDK6+8grlz595zmbCwsEYVEhgYCAAoKChAUFCQ9f2CggL06tWr3u8ZDAYYDIZGbZOIiJqphzpLE3l3LQQy9gK7/1s6E2ns3wC9u9rV0X3YFV78/Pzg5+enSCGhoaEIDAxEYmKiNayUlJTgyJEjdp2xRERE1CAevsCzm4F//x1IXgMc/RQoOAU8uwnwCVS7OroHxea8XLhwAWlpabhw4QLMZjPS0tKQlpaGsrIy6zJdu3bFl19+CQDQ6XRYunQp3nzzTezevRvp6emYPXs2goODMWnSJKXKJCKi5szFBRi6TLqoncEIXDwizYO5+IPaldE9KHbP8FWrVmHjxo3W17179wYAJCcnY9iwYQCAzMxMmEwm6zLLly/H9evXsWDBAhQXF2PQoEHYt28fPDw8lCqTiIgICI+QbiuwfaZ0d+rYccC4d4D/mqd2ZWQD7ypNRERUo7JUujP1md3S6z5zpBCj59xKpWnyOi9ERESqM/hIc15GRgPQAT9tBOLGAyV5aldGtTC8EBER1abTAYNfBn63U7q43aUfpXkw5+u/YCo5FsMLERGRLZ1HAQsOAP7dgetXgI2/BX74GHCu2RaaxPBCRERUn9ZhQOR+oPtkwHIL+PpV4KslQJXKNwy+VQmU5jfbIKXY2UZEREROwd0bmPopENwb+Fc0kLYFuHIamLYZMLaTd1sWC3DjqjTHpvSy9Ci5fPt5ab70WXmRtHzQo8Bv3wXa9pW3jgcczzYiIiJqqHPJwM55QPk1wNsPeGYj0OGJhn23oqSeQFLzOh8oy5eO8NhFB/R7HhjxZ8Czpb0dPTDs+fvN8EJERGSPaz8Dn88C8tMBFz0wZo10vySbgaTWEZObZfddtUQHtPAHfIKkh2/Q7ee1X1tuAd/+GTixXfqatz8w9i2gxxRp0rHGMLwwvBARkZJu3gD2vASkx9v3PYOxOnwEAj7B0k/f4LqvWwQArnbM6sg9BOx9GSjMll6HDQPG/w/QpqN9tamM4YXhhYiIlCYE8P37QNIawFJ1/yMlPoHS/Bkl3KoEDr8HHIoBblUArgbpdO8nlgJu2rhKPcMLwwsRETmK+Rbg4vpgDNUU5QBfLwPO/kt63ToMGP93oOMIdetqAF5hl4iIyFFc9Q9GcAGksPK7ncAzcUCLQCnMbH4a2Pk8UFqgdnWyYXghIiJyJjod0P1pYMmPwIAXAZ0LcHInsK6fdJE9i1ntCpuM4YWIiMgZefgCT/4NmJ8sXaOm0iRdZO+TUUBemtrVNQnDCxERkTML7gVEJgLjYgCDL5D3E/DxcOCb16Rrz2gQwwsREZGzc3EF+s+XhpJ6TAWEBTjygTSUdOpLzd1mgOGFiIioufAJBKZuAJ77UprcW5YPxM8Ftk6VJvdqBMMLERFRc9NxBLAwBRgWBbi6S6dWvz8QOPSOdM2YBxzDCxERUXPk5gEMWyGFmLBh0sXtkt4E/u8J6aq9DzCGFyIioubsoU7Ac7uAKRuk+yMVZgMbJwBfvACU/ap2dTYxvBARETV3Oh3wm6nShN5+kQB00g0f1/UFjsYCFovaFdbB8EJEREQSz5bS7QQiE4HAnkCFCdi7FPh0jHQX7QcEwwsRERHV1a6vdHG7sW8D7j7ApR+BD4cCCa8DlaVqV8fwQkRERDa46oHHFgJLfgAemQQIM5CyDlg/ADizR9VrwzC8EBERUf18g4FnN0o3fGzZHij5BfhqiTSkpBK9alsmIiIi7eg8Glh8BDgUA7QOlebHqIThhYiIiBrGzRMY+We1q+CwEREREWkLwwsRERFpCsMLERERaQrDCxEREWkKwwsRERFpCsMLERERaQrDCxEREWkKwwsRERFpCsMLERERaQrDCxEREWkKwwsRERFpCsMLERERaQrDCxEREWmK091VWggBACgpKVG5EiIiImqomr/bNX/H78XpwktpaSkAICQkROVKiIiIyF6lpaUwGo33XEYnGhJxNMRisSAvLw8+Pj7Q6XQO3XZJSQlCQkJw8eJF+Pr6OnTbamC/zo39Ojf269y02K8QAqWlpQgODoaLy71ntTjdkRcXFxe0a9dO1Rp8fX01849FDuzXubFf58Z+nZvW+r3fEZcanLBLREREmsLwQkRERJrC8CIjg8GA6OhoGAwGtUtxCPbr3Nivc2O/zs3Z+3W6CbtERETk3HjkhYiIiDSF4YWIiIg0heGFiIiINIXhhYiIiDSF4aWWt956C/369YOPjw/8/f0xadIkZGZm1lmmoqICixcvRps2bdCiRQtMmTIFBQUFdZZ56aWX0LdvXxgMBvTq1cvmtk6cOIHBgwfDw8MDISEhWLt2rVJt1ctR/R44cAATJ05EUFAQvL290atXL2zdulXJ1mxy5O+3xtmzZ+Hj44OWLVvK3M39ObJfIQRiYmIQHh4Og8GAtm3bYs2aNUq1ZpMj+01ISMBjjz0GHx8f+Pn5YcqUKfj5558V6qx+cvR8/PhxzJgxAyEhIfD09ES3bt3wj3/8465tHThwAH369IHBYECnTp0QFxendHt3cVS/X3zxBUaPHg0/Pz/4+vpi4MCBSEhIcEiPtTny91vju+++g16vv+++TW0ML7UcPHgQixcvxvfff4/9+/ejqqoKY8aMwfXr163L/PGPf8SePXsQHx+PgwcPIi8vD5MnT75rXb///e8xbdo0m9spKSnBmDFj0L59e6SmpuKdd97BG2+8gY8++kix3mxxVL+HDx9Gz5498c9//hMnTpzAvHnzMHv2bOzdu1ex3mxxVL81qqqqMGPGDAwePFj2XhrCkf3+4Q9/wCeffIKYmBhkZGRg9+7d6N+/vyJ91cdR/ebm5mLixIkYMWIE0tLSkJCQgKtXr9pcj9Lk6Dk1NRX+/v7YsmULTp06hddffx1RUVFYt26ddZnc3FyMHz8ew4cPR1paGpYuXYrIyEiH/0F3VL+HDh3C6NGj8fXXXyM1NRXDhw/HhAkTcOzYMafst0ZxcTFmz56NkSNHOqS/JhFUrytXrggA4uDBg0IIIYqLi4Wbm5uIj4+3LnPmzBkBQKSkpNz1/ejoaPHoo4/e9f77778vWrVqJSorK63vvfbaa6JLly7yN2EHpfq1Zdy4cWLevHmy1N1YSve7fPlyMWvWLBEbGyuMRqPc5dtNqX5Pnz4t9Hq9yMjIUKz2xlCq3/j4eKHX64XZbLa+t3v3bqHT6cTNmzflb8QOTe25xqJFi8Tw4cOtr5cvXy66d+9eZ5lp06aJiIgImTuwj1L92vLII4+I1atXy1N4Iynd77Rp08TKlSvt2perhUde7sFkMgEAWrduDUBKsFVVVRg1apR1ma5du+Lhhx9GSkpKg9ebkpKCIUOGwN3d3fpeREQEMjMzce3aNZmqt59S/da3rZrtqEXJfpOSkhAfH4/169fLV3ATKdXvnj17EBYWhr179yI0NBQdOnRAZGQkioqK5G3ATkr127dvX7i4uCA2NhZmsxkmkwmbN2/GqFGj4ObmJm8TdpKr5zv/f6akpNRZByDts5q6H2gqpfq9k8ViQWlpqdPss2z1Gxsbi5ycHERHRytQufwYXuphsViwdOlSPPHEE+jRowcAID8/H+7u7nfNXwgICEB+fn6D152fn4+AgIC71lHzmRqU7PdOO3bswI8//oh58+Y1peQmUbLfwsJCzJ07F3FxcQ/MDdGU7DcnJwfnz59HfHw8Nm3ahLi4OKSmpmLq1KlytmAXJfsNDQ3Ft99+iz/96U8wGAxo2bIlLl26hB07dsjZgt3k6vnw4cP4/PPPsWDBAut79e2zSkpKUF5eLm8jDaRkv3eKiYlBWVkZnn32Wdnqt5eS/WZnZ2PFihXYsmUL9Hpt3K9ZG1WqYPHixTh58iT+85//qF2KQziq3+TkZMybNw8ff/wxunfvrui27kXJfufPn4+ZM2diyJAhsq+7sZTs12KxoLKyEps2bUJ4eDgAYMOGDejbty8yMzPRpUsX2bd5P0r2m5+fj/nz52POnDmYMWMGSktLsWrVKkydOhX79++HTqeTfZsNIUfPJ0+exMSJExEdHY0xY8bIWJ38HNXvtm3bsHr1anz11Vfw9/dv9LaaSql+zWYzZs6cidWrV1v//2oBj7zYsGTJEuzduxfJyclo166d9f3AwEDcvHkTxcXFdZYvKChAYGBgg9cfGBh41xkONa/tWY9clO63xsGDBzFhwgS8++67mD17dlPLbjSl+01KSkJMTAz0ej30ej2ef/55mEwm6PV6fPrpp3K10WBK9xsUFAS9Xl9nx9etWzcAwIULF5pWfCMo3e/69ethNBqxdu1a9O7dG0OGDMGWLVuQmJiII0eOyNWGXeTo+fTp0xg5ciQWLFiAlStX1vmsvn2Wr68vPD095W2mAZTut8b27dsRGRmJHTt23DVs5khK9ltaWoqjR49iyZIl1n3WX/7yFxw/fhx6vR5JSUmK9tZoak+6eZBYLBaxePFiERwcLLKysu76vGZy1M6dO63vZWRkNHrCbu3JfVFRUQ6fsOuofoUQIjk5WXh7e4t169bJVr+9HNXv6dOnRXp6uvXx5ptvCh8fH5Geni6Kiopk7eleHNVvQkKCACDOnj1rfS8tLU0AEJmZmfI00wCO6vfll18W/fv3r/NeXl6eACC+++67pjdiB7l6PnnypPD39xfLli2zuZ3ly5eLHj161HlvxowZDp+w66h+hRBi27ZtwsPDQ+zatUveJuzgiH7NZnOd/VV6erpYuHCh6NKli0hPTxdlZWXKNNdEDC+1LFy4UBiNRnHgwAFx+fJl6+PGjRvWZV588UXx8MMPi6SkJHH06FExcOBAMXDgwDrryc7OFseOHRMvvPCCCA8PF8eOHRPHjh2znl1UXFwsAgICxHPPPSdOnjwptm/fLry8vMSHH37olP0mJSUJLy8vERUVVWc7hYWFTtnvndQ628hR/ZrNZtGnTx8xZMgQ8dNPP4mjR4+KAQMGiNGjRztlv4mJiUKn04nVq1eLrKwskZqaKiIiIkT79u3rbEsrPaenpws/Pz8xa9asOuu4cuWKdZmcnBzh5eUlli1bJs6cOSPWr18vXF1dxb59+5yy361btwq9Xi/Wr19fZ5ni4mKn7PdOWjjbiOGlFgA2H7GxsdZlysvLxaJFi0SrVq2El5eXePrpp8Xly5frrGfo0KE215Obm2td5vjx42LQoEHCYDCItm3birfffttBXd7mqH7nzJlj8/OhQ4c6rlnh2N9vbWqFF0f2+8svv4jJkyeLFi1aiICAADF37lyHh1NH9vvZZ5+J3r17C29vb+Hn5yeeeuopcebMGQd1epscPUdHR9tcR/v27etsKzk5WfTq1Uu4u7uLsLCwOttwFEf1W9+/gTlz5jiuWeHY329tWggvOiGEsDmeRERERPQA4oRdIiIi0hSGFyIiItIUhhciIiLSFIYXIiIi0hSGFyIiItIUhhciIiLSFIYXIiIi0hSGFyIiItIUhhciIiLSFIYXIiIi0hSGFyIiItIUhhciIiLSlP8HxZB6eUHTQ34AAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "apple_reports_df = cf_standardizer_result_bag.result_df[(cf_standardizer_result_bag.result_df.cik==320193) & mask_FY]\n", "\n", "# Plotting\n", "plt.plot(apple_reports_df['date'], apple_reports_df['NetCashProvidedByUsedInOperatingActivities'], label='Operating', linestyle='-')\n", "plt.plot(apple_reports_df['date'], apple_reports_df['NetCashProvidedByUsedInFinancingActivities'], label='Financing', linestyle='-')\n", "plt.plot(apple_reports_df['date'], apple_reports_df['NetCashProvidedByUsedInInvestingActivities'], label='Investing', linestyle='-')\n", "plt.plot(apple_reports_df['date'], apple_reports_df['CashPeriodIncreaseDecreaseIncludingExRateEffectFinal'], label='IncDec', linestyle='-')\n", "plt.legend()\n"]}, {"cell_type": "markdown", "id": "28b1b97e-11a2-4932-b7a5-f8964c08a180", "metadata": {}, "source": ["### Compare companies"]}, {"cell_type": "markdown", "id": "d24f1215-9440-42ee-8564-1855a646c964", "metadata": {}, "source": ["Let's visualize and compare the history of the NetCashProvidedByUsedInOperatingActivities for a few companies. We just use the yearly reports."]}, {"cell_type": "code", "execution_count": 13, "id": "43924bdd-763c-4061-8543-3a269406195d", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["<matplotlib.legend.Legend at 0x203d5717550>"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAu0AAAHWCAYAAADD67eUAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjguMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8g+/7EAAAACXBIWXMAAA9hAAAPYQGoP6dpAAEAAElEQVR4nOzdd1gTyRsH8O8mgdBDB1EEBBGwYUVRT7A37IoFFVHsFT09xd57+9mlqKgIds87e0M9u4J6iqCCeArY6D3J/P7gkmNJgNAM6nyeJ48yO7v77mazeTM7O8sQQggoiqIoiqIoiqqyOMoOgKIoiqIoiqKo4tGknaIoiqIoiqKqOJq0UxRFURRFUVQVR5N2iqIoiqIoiqriaNJOURRFURRFUVUcTdopiqIoiqIoqoqjSTtFURRFURRFVXE0aacoiqIoiqKoKo4m7RRFURRFURRVxdGkvRiWlpbo0aOHssMoE0tLS3h6epZYb+/evWAYBrGxsRW2bk9PT1haWlbY8r4nP/O2l1VlHIPKxjAMFi1aVOr5Fi1aBIZhFKr7I+43ZSrNvv9WEhMT0b9/fxgYGIBhGGzatEnZIbHExsaCYRjs3buXVX7u3Dk4OjpCTU0NDMMgOTkZABAUFAQ7OzuoqKhAV1f3m8dLfTvK/Dx169YN3t7eSlm3Is6dOwctLS18+vSp1POWKmmXfEmoqanh/fv3MtNdXFxQr169UgcBAIcOHSr2hJSamorFixejYcOG0NLSgrq6OurVq4fZs2fjw4cPZVpnRbl27RoYhpG+VFRUUKtWLQwfPhxv3rxRamxVRcH9wzAMNDU14eDggGXLliEzM7NMyyzuR9WDBw/kfpkoA8MwmDRpUrmWcebMGXTp0gUGBgZQU1ODra0tZs6ciS9fvlRQlJVvxYoVOHnypLLDAAA0b94cDMNgx44dZV7Gn3/+WabEvLSq0n6TuHXrFvr06QMTExPw+XxYWlpi7NixiIuLU3ZoLJaWljLnHnmvqnCekGf69Ok4f/485syZg6CgIHTp0qVS11dwn/B4POjr66NJkyaYOnUqnj9/rtAyvnz5goEDB0JdXR3btm1DUFAQNDU1ERkZCU9PT1hbW2PPnj3YvXt3pW5LeTx//hyLFi0q9Q/i8PBweHh4wNzcHHw+H/r6+ujQoQMCAwMhEokqJ1iK5datW7hw4QJmz54tMy0xMREzZ86EnZ0dNDQ0oKmpiSZNmmDZsmXSH5aA/Fy2qHwjKCgIXC4XXbp0QXZ2tkIxdunSBTY2Nli5cmXpNg4ASCkEBgYSAAQAmTRpksz0tm3bkrp165ZmkVLdu3cnFhYWcqe9fv2aWFlZES6XSwYNGkS2bt1Kdu/eTSZNmkQMDAxI7dq1y7TOklhYWJDu3buXWO/q1asEAJkyZQoJCgoiAQEBZNKkSURVVZXo6+uT9+/fV0p8xcnOzia5ubkl1pO8pzExMRW27hEjRsi8lwBIx44dSVBQEAkKCiI7duwgQ4YMIQBI//79y7Se4t6f+/fvEwAkMDCwTMsuq6K2feLEiWVe5owZMwgA0rBhQ7J69WqyZ88eMn78eMLn80n16tVJZGRkOaP+NjQ1NcmIESNkyoVCIcnKyiJisfibxBEVFUUAEEtLS9KqVasyL2fixImkqFNoVlYWycvLK/Uy8/LySFZWFqusquw3iS1bthCGYYi1tTVZunQp8fPzIzNmzCACgYAIBAJy69atbxpPcU6cOCE95wQFBZHBgwcTAGTjxo2s8tevX8vd98pmYmJChg4d+s3WV/A8vX//fvK///2PjB49mggEAsLj8cj69etZ9cViMcnKyiJCoVBadvbsWQKAXLx4kVV3x44dBACJjo7+JttSHkeOHCEAyNWrVxWeZ8+ePYTL5RIzMzMye/Zs4ufnRzZu3Eh69OhBGIYhy5cvr7yAqyBlfZ569epFOnXqJFN+7949YmhoSNTU1Mjo0aPJjh07yI4dO8ioUaOIpqYm6dixo7SuvFxWXr5x4MABwuFwSKdOnUq9rdu3bycaGhokNTW1VPOVKWl3dHQkfD5fJhmtjKQ9Ly+PNGzYkGhoaJAbN27ITE9JSSFz584t0zpLUtqk/ciRI6zyLVu2EABkxYoVRc6bnp5e7jjL41sm7fIS1/79+xMOh1OmD/fPkLQfOnSIACDu7u6sL0ZCCLl79y7R0NAg9evXL1OCWB4ikajU71lRyee3tmDBAmJsbEyOHTtGGIYp87FfXNJekarKfiOEkJs3bxIOh0PatGlDMjIyWNNevXpFTExMSLVq1cjXr1+/aVyKnkfXrl1b4ee7ysQwTLl+8BeWlZVFRCJRkdOLOld9/vyZtGzZkgAgf/zxR7Hr2LdvHwFA7t+/zypfvHgxAUA+ffpUtuDlqKzvz9Im7bdv3yZcLpe0bt1abhJ2//79b/5d9DNKTEwkPB6P+Pn5scqTkpJI9erViYmJCXnx4oXMfAkJCWTp0qXSvxVJ2oODgwmXyyUdOnQoU/6SmJhIuFwu8ff3L9V8ZUraQ0NDCY/HI5MnT2ZNLyppDwoKIo0bNyZqampET0+PuLu7k7i4ONZ8khZ8yUuS+Bw+fJgAUPhXalhYGOnfvz8xNzcnqqqqpEaNGmTatGkkMzOTVS8+Pp54enqS6tWrE1VVVWJqakp69uzJOplL3qQbN26QZs2aET6fT6ysrMi+fftYyyoqaX/27BkBQLy9vQkhhCxcuJAAIH///TcZPHgw0dXVJY6OjoSQ/B8nS5YsIbVq1SKqqqrEwsKCzJkzh2RnZ0uX1717d2JlZSV3u1u0aEGaNGnCir3wF/2zZ8+Iq6srUVNTI9WrVydLly4l/v7+cr/E/vzzT9K6dWuioaFBtLS0SLdu3cizZ89k1nvixAlSt25dwufzSd26dcnx48dLlbhOmjSJcLlcadK5YMECwuPxyMePH2Xqent7E4FAIP2AlDZpV+Q9/xbbLjleQkJCyLJly0j16tUJn88n7dq1k2mFqlOnDtHT0yMpKSlyt1PyRRgcHCwtk3wOHzx4QFq2bEnU1NSIpaUl2bFjh8z82dnZZMGCBcTa2lr6efn1119Zx13BbThw4ABxcHAgPB6PnDhxghCSnwi1bNmS6OvrEzU1NdK4cWOZz0LhzzcA6fEp74ejop89QgiJiIggv/zyC+u4DggIKDI5s7GxIRMmTCA5OTlEV1e3yHPLnTt3SNeuXYmurq70x9GmTZsIIfk/zuRtU8HtXbhwISHkvwTg2rVrMuvYuXMnAUCePn1KCPnvHFHW/UaIYsevop+Fwjp37ky4XC558+aN3OmShG3lypWEkP+S5NjYWJm6v/32G1FRUWEl+Hfu3CGdO3cmOjo6RF1dnfzyyy/k5s2brPmKO4+WpLikvfC+J+S/4z40NJTY29sTNTU10qJFC/LkyRNCSP77Z21tTfh8Pmnbtq3c5SqyTYUVvKot7/h6/fo16d+/P9HT0yPq6urEycmJnDlzhrUMyXkmODiY+Pr6EjMzM8IwDElKSipyvcU1MLx9+5bweDzi7OwsLYuJiWGdZ+V9l0vOiYXLJZ8PQhQ7ZkeMGEE0NTXJq1evSNeuXYmWlhbp1asXISS/EWHjxo3EwcGB8Pl8YmxsTMaMGSPz41GR80pR+764BL5Lly6Ex+ORt2/fFlmnoPT0dOLj40Nq1KhBVFVVia2tLVm7dq3MVbPyHn+Kfhfk5OSQ+fPnk8aNGxMdHR2ioaFBWrduTa5cucKqJ3m/165dS3bt2iXNV5o2bUru3bvHqivv80RIyfkgIflXQ/v27UtMTEykV5Td3d1JcnJysftVct4vfL5ZtWoVAUAOHjxY7PwSJSXtISEhhMvlkvbt28vkloTkJ/SNGzcmWlpaRFtbm9SrV0/63VFQo0aNSM+ePRWKSaJMSfv9+/eJl5cXUVNTY7W2y9vQZcuWEYZhiLu7O9m+fTtZvHgxMTQ0JJaWltKTx4ULF4ijoyMxNDSUXq6UJASSLhSF39SiTJ48mXTr1o2sWLGC7Nq1i4waNYpwuVyZLhjOzs5EIBCQefPmET8/P7JixQri6upKrl+/Lq1jYWFB6tSpQ0xMTMjcuXPJ1q1bSePGjQnDMKwTSlFJ+6lTpwgA8ttvvxFC/juIHRwcSK9evcj27dvJtm3bCCH/JQH9+/cn27ZtI8OHDycASO/evaXL279/PwEg8+GIjY2VfpAKxl4waY+PjydGRkZET0+PLFq0iKxdu5bUrl2bNGjQQOZLbP/+/YRhGNKlSxfyv//9j6xevZpYWloSXV1dVr3z588TDodD6tWrRzZs2EB8fX2JQCAgdevWlZu4jho1inz69Il8+vSJxMbGkoMHDxJtbW0ybNgwab3o6GgCgPzvf/9jzZ+Tk0P09PSIl5cXaxtLk7Qr8p5X1rbLS9obNWpEmjRpQjZu3EgWLVpENDQ0SPPmzaX1JN04PD095W4jIf+dRAteQm/bti0xMzMjxsbGZNKkSWTLli2kdevWBADrV71IJCKdOnUiGhoaZNq0aWTXrl1k0qRJhMfjSb8MC26Dvb09MTIyIosXLybbtm0jjx8/JoQQUqNGDTJhwgSydetWsmHDBtK8eXMCgJVEBAUFET6fT9q0aSP9jP/111+EkKKTdkU+e//88w/R19cnBgYGZPHixWTdunXEzs6ONGzYUG5ydufOHQJAetXOy8uLODg4yOzXCxcuSH88L1y4kOzYsYNMmTKFdOjQgRBCyF9//UU6duxIALC6WRTcX5KkJDMzk2hpaZEJEybIrMfV1ZV1ziz8RVfa/abo8avIZ6GwjIwMwuPxiIuLS5F1srOzCZ/Pl3Y7evv2LWEYhqxZs0ambq1atVif38uXLxNVVVXSsmVLsn79erJx40bSoEEDoqqqSu7evSuzj+SdR0tSlqS9QYMGxNzcnKxatYqsWrWKCAQCUrNmTbJ161bi4OBA1q9fT+bNm0dUVVWJq6sra35Ft6mw169fk6CgIJluhYTktwqamJgQbW1t4uvrSzZs2EAaNmxIOBwOOX78uHQZkvOMg4MDcXR0JBs2bCArV66UuUJSeHuLa9lv37494XA40kaEwkn7hQsXyJgxYwgAsmTJEunxeuLECdKnTx8CgOzYsYMEBQWRiIgIQojix+yIESMIn88n1tbWZMSIEWTnzp1k//79hBBCRo8eTXg8HvH29iY7d+4ks2fPJpqamqRZs2asbqKKnFdev35NpkyZQgCQuXPnSvd9QkKC3H2SkZFBVFRUSLt27YrcbwWJxWLSrl07wjAMGT16NNm6dStxc3MjAMi0adNk3o/yHH+Kfhd8+vSJVKtWjfj4+JAdO3aQNWvWkDp16hAVFRXpeb7g+92oUSNiY2NDVq9eTdasWUMMDQ1JjRo1WPta3udJkXwwJyeHWFlZETMzM7Js2TLi5+dHFi9eTJo1ayb3x39Bo0ePJgYGBjLlzs7ORF1dneTk5BQ7f8H9VlTSfvToUcLj8Yirq6vchP3ChQsEAGnfvj3Ztm0b2bZtG5k0aRIZMGCA3HgNDQ0VikmizEn769evCY/HI1OmTJFOL7yhsbGxhMvlyrRkPX36lPB4PFZ5Ud1jGjVqRAQCgcIxytuJK1euJAzDSH8FJyUlySS58khaB8LCwqRlHz9+JHw+n8yYMUNaJjk5BgQEkE+fPpEPHz6QP/74g1haWhKGYaSXCSUH8eDBg1nrCQ8PJwDI6NGjWeUzZ84kAKS/dlNSUmTWTQgha9asYW2fJPaCSfu0adMIANYXxcePH4lAIGB9iaWlpRFdXV3p1QGJhIQEIhAIWOWOjo6kWrVqrF+/kgNWXuIq79W7d2+ZVt2WLVsSJycnVtnx48dlWjtKk7Qr8p5X5rbLS9rt7e1ZJ5HNmzezWl1PnjxJgPz+t8XR0dEhjRs3lv4tae0q2P80JyeHODo6EmNjY+mJNSgoiHA4HJluZ5LW34J9kwEQDodD/v77b5n1F/7M5ebmknr16sl8iRXVzaOopF2Rz97kyZMJwzCsL5YvX74QfX19ucnZpEmTiLm5ubRFS/KeFZxfKBQSKysrYmFhIdMqWbAlrLjuMYVbEgcPHkyMjY1ZXZzi4+MJh8MhS5YskZbJ+6JTdL8pevwqev4rTHKemjp1arH1GjRoQPT19aV/t2zZknUVkJD8/qUApEmXWCwmtWvXJp07d2bt48zMTGJlZcXqb1rUeVQRZUna+Xw+q/6uXbsIAGJqasrqCjFnzhzWskuzTUWRl0RLzuUFP7dpaWnEysqKWFpaSru/SM4ztWrVkvu9qOj6Cpo6dSoBIE24CyfthLDzhIIk+7dg95jSnHMlDVuSRjCJGzduyG1FPXfunEy5oueV0nSPiYiIUOhzISE5ry9btoxV3r9/f8IwDHn16pW0rDzHHyGKfxcIhUKZhDYpKYmYmJiwGsok77eBgQHrKoakgfL333+XlhX+PCmaDz5+/FhuI6giWrduLXOuIYQQPT090rBhQ4WXU1TSbmZmJm24KOrH79SpU4mOjo5Md1Z5VqxYQQCQxMREhWMr85CPtWrVwrBhw7B7927Ex8fLrXP8+HGIxWIMHDgQnz9/lr5MTU1Ru3ZtXL16tcT1pKamQltbW+G41NXVpf/PyMjA58+f4ezsDEIIHj9+LK2jqqqKa9euISkpqdjlOTg4oE2bNtK/jYyMUKdOHbmjwnh5ecHIyAhmZmbo3r07MjIysG/fPjRt2pRVb9y4cay///zzTwCAj48Pq3zGjBkAgD/++AMAoKOjg65duyI0NBSEEGm9kJAQtGjRAjVr1ixyO/7880+0aNECzZs3Z23L0KFDWfUuXryI5ORkDB48mPWecblcODk5Sd+z+Ph4hIeHY8SIERAIBNL5O3bsCAcHB7kx9OrVCxcvXsTFixdx6tQpzJkzB+fOncOQIUNY2zN8+HDcvXsXr1+/lpYdPHgQ5ubmaNu2bZHbWBxF3vPK3HZ5Ro4cCVVVVenfkuNMcmylpaUBQInHv7a2NlJTU1llPB4PY8eOlf6tqqqKsWPH4uPHj3j48CEA4MiRI7C3t4ednR1re9u1awcAMp/Ptm3byt2+gp+5pKQkpKSkoE2bNnj06FHxO6AEinz2zp07h5YtW8LR0VFapq+vL3NcA4BQKERISAjc3d2lQ5G1a9cOxsbGOHjwoLTe48ePERMTg2nTpskMS1fWIczc3d3x8eNHXLt2TVp29OhRiMViuLu7l2mZhSl6/Jbm/FdQWY9Hd3d3PHz4kPV5DgkJAZ/PR69evQDkj7oRHR2NIUOG4MuXL9LYMzIy0L59e4SFhUEsFrPWU/g8Wlnat2/PGsbVyckJANCvXz/WvpCUS47PsmyTIv788080b94crVu3lpZpaWlhzJgxiI2NlRnlZcSIEazPaHloaWkB+O9YKC9Fj9mCxo8fz/r7yJEjEAgE6NixI2sZTZo0gZaWlswySvOdrgjJsa5onvLnn3+Cy+ViypQprPIZM2aAEIKzZ8+yyst6/Eko8l3A5XKl30VisRhfv36FUChE06ZN5Z7H3d3doaenJ/278HeXPIrmg5Lv1PPnz5d6ZLkvX76w4pIobR5ZFMl+qVGjRpGfKV1dXWRkZODixYslLk8S6+fPnxWOgadwTTnmzZuHoKAgrFq1Cps3b5aZHh0dDUIIateuLXd+FRWVEteho6NTqg9TXFwcFixYgNOnT8t8IaWkpAAA+Hw+Vq9ejRkzZsDExAQtWrRAjx49MHz4cJiamrLmkZcI6+npyf2yW7BgAdq0aQMulwtDQ0PY29uDx5PdxVZWVqy/3759Cw6HAxsbG1a5qakpdHV18fbtW2mZu7s7Tp48idu3b8PZ2RmvX7/Gw4cPSxy/9+3bt9IPdUF16tRh/R0dHQ0A0sStMB0dHenyAMh9b+vUqSP3g16jRg106NBB+nfPnj1hYGCAmTNn4syZM3Bzc5Nu47Rp03Dw4EEsWLAAKSkpOHPmDKZPn17qpElSX5H3vDK3XZ7Cx5bkAyw5tiQnmZK+INPS0mBsbMwqMzMzg6amJqvM1tYWQP7Yyi1atEB0dDRevHgBIyMjucv9+PEj6+/Cx63EmTNnsGzZMoSHhyMnJ0daXt4xehX57L19+xYtW7aUqVf4swQAFy5cwKdPn9C8eXO8evVKWu7q6org4GCsXr0aHA5HmlyWdfhaebp06QKBQICQkBC0b98eQH7i6ujoKH1fykvR47c057+CSnM8FvyCHDBgAHx8fBASEoK5c+eCEIIjR46ga9eu0pgksY8YMaLI5aakpLC+kIs6Hita4eNQklSYm5vLLZccn2XZJkUUdS63t7eXTi947FbkfkpPTwegeIJaEkWPWQkej4caNWrILCMlJUXmHChR+DxWmu90RUhiVPSHzNu3b2FmZiazDwu+fwWV9fiTUOS7AAD27duH9evXIzIyEnl5edK68o6fkr675FE0H7SysoKPjw82bNiAgwcPok2bNujZsyc8PDxYjWRFKdgAKKGjo1MhPzTbt2+PmjVrYseOHdDX15eb906YMAGhoaHo2rUrqlevjk6dOmHgwIFyh2uVxFqa78pyJe21atWCh4cHdu/ejd9++01mulgsBsMwOHv2LLhcrsx0ya/24tjZ2eHx48d49+6dzEFamEgkQseOHfH161fMnj0bdnZ20NTUxPv37+Hp6clq1Zg2bRrc3Nxw8uRJnD9/HvPnz8fKlStx5coVNGrUSFpPXtyA/AOjfv36rKS0KEX9QlPkjXNzc4OGhgZCQ0Ph7OyM0NBQcDgcDBgwoMR5FSHZR0FBQXK/wOX9CCkPSQITFhYmTdr19PTQo0cPadJ+9OhR5OTkwMPDgzWvmpoasrKy5C5X8gtdTU1NWlbSe/6tt72kY0tyEn/y5EmRy3j79i1SU1NL1cIvIRaLUb9+fWzYsEHu9MKfN3nH7Y0bN9CzZ0/88ssv2L59O6pVqwYVFRUEBgbi0KFDpY6poNJ89hQhaU0fOHCg3OnXr1+Hq6trmZZdEj6fj969e+PEiRPYvn07EhMTcevWLaxYsaLC1lGa41fR819BNjY24PF4xR6POTk5ePnyJevqopmZGdq0aYPQ0FDMnTsXd+7cQVxcHFavXi0T+9q1a1lXTQoq/H1RUa3HJSnqOCzp+CzLNlWGitxPz549A5fLrbAfAqU95/L5fHA47A4CYrFY5mpZQYUbJSr6vCL5XDx9+rRM85ekrMdfaRw4cACenp7o3bs3fv31VxgbG4PL5WLlypWsK2TlWXdp8sH169fD09MTp06dwoULFzBlyhSsXLkSd+7ckfnRVpCBgYHcHw52dnYIDw9Hbm4u6+p2WWzduhVJSUnYsmUL9PT0ZJ7VYWxsjPDwcJw/fx5nz57F2bNnERgYiOHDh2Pfvn2supJYDQ0NFV5/ubOQefPm4cCBA6wTsIS1tTUIIbCysiqxNamohNXNzQ3BwcE4cOAA5syZU+wynj59iqioKOzbtw/Dhw+Xlhd1mcLa2hozZszAjBkzEB0dDUdHR6xfvx4HDhwodj0VzcLCAmKxGNHR0dJEDch/EEBycjIsLCykZZqamujRoweOHDmCDRs2ICQkBG3atIGZmVmJ65C0ahT08uVL1t/W1tYA8g+84n6ASGJSZJnFEQqFAP5rwZEYPnw4evXqhfv37+PgwYNo1KgR6tatKxNDUQ/8kMRQcN8Bxb/n33rbS2JrawtbW1ucPHkSmzdvltu6tX//fgCQeejDhw8fkJGRwWphiYqKAgDppVZra2tERESgffv2ZW4VP3bsGNTU1HD+/Hnw+XxpeWBgoEzdyng6noWFBavVXKJwWUZGBk6dOgV3d3f0799fpv6UKVNw8OBBuLq6So+DZ8+eFXsclHZ73N3dsW/fPly+fBkvXrwAIUShrjGKrkfR47dg/dKc/zQ1NeHq6oorV67g7du3Mp8tAAgNDUVOTo7M8eju7o4JEybg5cuXCAkJgYaGhvRHesHYdXR0FIr9e1BZ22RhYSH3PBMZGSmdXhni4uJw/fp1tGzZssJa2kt7zBa1jEuXLqFVq1YV9gOlNJ9tDQ0NtGvXDleuXFGocdHCwgKXLl2SuSJVWe+fIt8FR48eRa1atXD8+HHWti9cuLDC4ihNPgjkN4LWr18f8+bNw19//YVWrVph586dWLZsWZHz2NnZ4dixYzLlbm5uuH37No4dO4bBgweXazs4HA7279+PlJQULF68GPr6+jJdnVRVVeHm5gY3NzeIxWJMmDABu3btwvz581lXgWNiYmBoaFjk1W656y9X9Mh/Izw8PLBr1y4kJCSwpvXt2xdcLheLFy+W+QVGCGE9zVFTU1PafaWg/v37o379+li+fDlu374tMz0tLQ2+vr4A/vv1V3BdhBCZSxiZmZkyT66ytraGtrY26/L+t9KtWzcAkOniImkB7d69O6vc3d0dHz58gJ+fHyIiIhT64u/WrRvu3LmDe/fuScs+ffok0zrRuXNn6OjoYMWKFaxLZAXnAYBq1arB0dER+/btY71vFy9eVPjJeQDw+++/AwAaNmzIKu/atSsMDQ2xevVqXL9+XaaVXbJN//zzj8zTInNycuDn5wdjY2M0btwYgGLv+bfedkUsWLAASUlJGDdunMwT9R4+fIjVq1ejXr166NevH2uaUCjErl27pH/n5uZi165dMDIyQpMmTQDktzi/f/8ee/bskVlvVlYWMjIySoyPy+WCYRhWbLGxsXKf4Kmpqcl66lxF6Ny5M27fvo3w8HBp2devX2WO6xMnTiAjIwMTJ05E//79ZV49evTAsWPHkJOTg8aNG8PKygqbNm2SibfguUXyJajoNnXo0AH6+voICQlBSEgImjdvrlCLpaL7TdHjtzznv3nz5oEQAk9PT5mrXDExMZg1axaqVavG6kML5Pe/5XK5CA4OxpEjR9CjRw9WEtGkSRNYW1tj3bp1Mj/gC8b+PamsberWrRvu3bvH+j7MyMjA7t27YWlpWaarbiX5+vUrBg8eDJFIJP2+rQiKHrPFGThwIEQiEZYuXSozTSgUlumcU9rP9sKFC0EIwbBhw+S+1w8fPpS2snbr1g0ikQhbt25l1dm4cSMYhkHXrl1LHW9xFPkukJc73b17V27OVVaK5oOpqanSxjyJ+vXrg8PhlHh+atmyJZKSkmS6VI8bNw7VqlXDjBkzpD9YCvr48WOxPwYKU1FRwdGjR9GqVStMmzYNQUFB0mmFn1LO4XDQoEEDAJCJ/+HDh3K7dxanQq73+/r6IigoCC9fvmS1hlpbW2PZsmWYM2cOYmNj0bt3b2hrayMmJgYnTpzAmDFjMHPmTAD5J7iQkBD4+PigWbNm0NLSgpubG1RUVHD8+HF06NABv/zyCwYOHIhWrVpBRUUFf//9Nw4dOgQ9PT0sX74cdnZ2sLa2xsyZM/H+/Xvo6Ojg2LFjMpdLoqKi0L59ewwcOBAODg7g8Xg4ceIEEhMTMWjQoIrYJaXSsGFDjBgxArt370ZycjLatm2Le/fuYd++fejdu7fMJftu3bpBW1sbM2fOBJfLlUnY5Jk1a5b0MdhTp06FpqYmdu/eDQsLC9blbh0dHezYsQPDhg1D48aNMWjQIBgZGSEuLg5//PEHWrVqJT3ZrFy5Et27d0fr1q3h5eWFr1+/4n//+x/q1q0r98QVFRUlbcXLzMzEnTt3sG/fPtjY2GDYsGGsuioqKhg0aBC2bt0KLpcr99fxmDFjEBAQgAEDBsDLywuNGjXCly9fEBISgmfPnmH//v3SS2GKvOeVue1lNXToUNy/fx+bN2/G8+fPMXToUOjp6eHRo0cICAiAgYEBjh49KnN/iJmZGVavXo3Y2FjY2toiJCQE4eHh2L17t7TusGHDEBoainHjxuHq1ato1aoVRCIRIiMjERoaivPnz8vcRF1Y9+7dsWHDBnTp0gVDhgzBx48fsW3bNtjY2Mh0o2jSpAkuXbqEDRs2wMzMDFZWVnL75pbGrFmzcODAAXTs2BGTJ0+GpqYm/Pz8ULNmTXz9+lXaanTw4EEYGBjA2dlZ7nJ69uyJPXv24I8//kDfvn2xY8cOuLm5wdHRESNHjkS1atUQGRmJv//+G+fPn5duD5DfSt+5c2dwudxizx8qKiro27cvDh8+jIyMDKxbt06hbVR0vyl6/Jbn/PfLL79g3bp18PHxQYMGDeDp6SndN3v27IFYLMaff/4p00/b2NgYrq6u2LBhA9LS0mQaGjgcDvz8/NC1a1fUrVsXI0eORPXq1fH+/XtcvXoVOjo60h/434vK2qbffvsNwcHB6Nq1K6ZMmQJ9fX3s27cPMTExOHbsmEz3kdKSnKcJIUhNTUVERASOHDmC9PR06We9opTmnFuUtm3bYuzYsVi5ciXCw8PRqVMnqKioIDo6GkeOHMHmzZvlXl0rjqOjI7hcLlavXo2UlBTw+XzpTevyODs7Y9u2bZgwYQLs7OwwbNgw1K5dG2lpabh27RpOnz4tTQrd3Nzg6uoKX19fxMbGomHDhrhw4QJOnTqFadOmSa8+VBRFvgt69OiB48ePo0+fPujevTtiYmKwc+dOODg4VNj3maL54JUrVzBp0iQMGDAAtra2EAqFCAoKUijX6d69O3g8Hi5duoQxY8ZIy/X09HDixAl069YNjo6O8PDwkJ6/Hz16hODg4FInzxoaGvjjjz/Qtm1beHl5QSAQoGfPnhg9ejS+fv2Kdu3aoUaNGnj79i3+97//wdHRkdWT4uPHj3jy5AkmTpxYqvWWecjHwiTDMcl7uNKxY8dI69atiaamJtHU1CR2dnZk4sSJ5OXLl9I66enpZMiQIURXV1fusHlJSUlkwYIFpH79+kRDQ4OoqamRevXqkTlz5pD4+HhpvefPn5MOHToQLS0tYmhoSLy9vaVDMkmGpfr8+TOZOHEisbOzI5qamkQgEBAnJycSGhrKWmdRQwq2bduWtG3bVvp3UeO0FyZvyCuJvLw8snjxYmJlZUVUVFSIubm5zMOVCho6dCgBIB03ujB5D1d68uQJadu2rUIPV7p69Srp3LkzEQgERE1NjVhbWxNPT0/y4MEDVr1jx44Re3t7wufziYODQ7EPGCr44nK5pEaNGmTMmDFFDnckGRZO3iOJJZKSksj06dOl+01HR4e4urqSs2fPsuop+p5X1rbLG/Kx8PEib/g0iZMnT5KOHTsSPT09wufziY2NDZkxY4bcY0neAzUsLCzI1q1bZerm5uaS1atXSx8SpaenR5o0aUIWL17MeqBT4W0oyN/fn9SuXZvw+XxiZ2dHAgMD5Q6fFxkZSX755Reirq5OAMUeriRv2wp+9gjJHyKsTZs2hM/nkxo1apCVK1dKn0ackJAgfUpewecBFJaZmUk0NDRInz59pGU3b94kHTt2JNra2kRTU5M0aNCA9fwAoVBIJk+eTIyMjAjDMKztRaEhHyUuXrxIABCGYci7d+9kppd3vxFS8vFbms9CUcLCwkivXr2IoaEhUVFRITVr1iTe3t7FjqO8Z88eAoBoa2sX+QTBx48fk759+xIDAwPC5/OJhYUFGThwILl8+bLMPirLkzXL+nClggo+YKagoj7XimxTUYr63EkerqSrq0vU1NRI8+bNi3y4UmmGzit4juZwOERXV5c0atSITJ06Ve5wr+Ud8rFgrCWdcyUPVyrK7t27SZMmTYi6ujrR1tYm9evXJ7NmzSIfPnyQ1inNeWXPnj2kVq1ahMvlKjz848OHD8mQIUOImZkZUVFRIXp6eqR9+/Zk3759rCfRpqWlkenTp0vr1a5du9iHKxVUmuNP0e8CsVhMVqxYQSwsLAifzyeNGjUiZ86ckfk+K2rdklgLnvOKerhSSfngmzdviJeXF7G2tiZqampEX1+fuLq6kkuXLhWx19l69uxJ2rdvL3fahw8fyPTp04mtrS1RU1MjGhoapEmTJmT58uWs7ztFnogqkZCQQGxsbIiamhq5evUqOXr0KOnUqRMxNjYmqqqqpGbNmmTs2LGsPJUQQnbs2EE0NDTkPkG3OAwhZbz7gqIqWUREBBwdHbF//36ZlniqaC4uLvj8+TOePXum7FCUZtq0adi1axfS09OLvGmKoijqR/YzfhfcuHEDLi4uiIyMLHKkmqqgUaNGcHFxwcaNG0s1X7n7tFNUZdmzZw+0tLTQt29fZYdCVWGF+1Z/+fIFQUFBaN26NU3YKYqifiJt2rRBp06dsGbNGmWHUqRz584hOjq6xMFV5KnYMewoqgL8/vvveP78OXbv3o1JkybJjDFLUQW1bNkSLi4usLe3R2JiIvz9/ZGamor58+crOzSKoijqGyv8gKqqpkuXLmW+V4Am7VSVM3nyZCQmJqJbt25YvHixssOhqrhu3brh6NGj2L17NxiGQePGjeHv749ffvlF2aFRFEVRVIWhfdopiqIoiqIoqoqjfdopiqIoiqIoqoqjSTtFURRFURRFVXE0aacoiqIoiqKoKo4m7RRFURRFURRVxdGk/ScXFhYGNzc3mJmZgWEYnDx5slTzZ2dnw9PTE/Xr1wePx0Pv3r1l6sTHx2PIkCGwtbUFh8PBtGnTKiR2iqIoiqKonwVN2n9yGRkZaNiwIbZt21am+UUiEdTV1TFlyhR06NBBbp2cnBwYGRlh3rx5aNiwYXnCpSiKoiiK+inRcdp/cl27dkXXrl2LnJ6TkwNfX18EBwcjOTkZ9erVw+rVq+Hi4gIA0NTUxI4dOwAAt27dQnJysswyLC0tsXnzZgBAQEBAhW8DRVEURVHUj462tFPFmjRpEm7fvo3Dhw/jyZMnGDBgALp06YLo6Ghlh0ZRFEVRFPXToEk7VaS4uDgEBgbiyJEjaNOmDaytrTFz5ky0bt0agYGByg6PoiiKoijqp0G7x1BFevr0KUQiEWxtbVnlOTk5MDAwUFJUFEVRFEVRPx+atFNFSk9PB5fLxcOHD8HlclnTtLS0lBQVRVEURVHUz4cm7VSRGjVqBJFIhI8fP6JNmzbKDoeiKIqiKOqnRZP2n1x6ejpevXol/TsmJgbh4eHQ19eHra0thg4diuHDh2P9+vVo1KgRPn36hMuXL6NBgwbo3r07AOD58+fIzc3F169fkZaWhvDwcACAo6OjdLmSsvT0dHz69Anh4eFQVVWFg4PDt9pUiqIoiqKo7xZDCCHKDoJSnmvXrsHV1VWmfMSIEdi7dy/y8vKwbNky7N+/H+/fv4ehoSFatGiBxYsXo379+gDyh3R8+/atzDIKHloMw8hMt7CwQGxsbMVtDEVRFEVR1A+KJu0URVEURVEUVcXRIR8piqIoiqIoqoqjSTtFURRFURRFVXH0RtSflFgsxocPH6CtrS23vzlFURRFUVUPIQRpaWkwMzMDh0PbXn8mNGn/SX348AHm5ubKDoOiKIqiqDJ49+4datSooewwqG+IJu0/KW1tbQD5H3odHR0lR0NRFEVRlCJSU1Nhbm4u/R6nfh40af9JSbrE6Ojo0KSdoiiKor4ztGvrz4d2hqIoiqIoiqKoKo4m7RRFURRFURRVxdGknaIoiqIoiqKqONqnnSqWSCRCXl6essOgKKqKU1FRAZfLVXYYFEVRPyyatFNyEUKQkJCA5ORkZYdCUdR3QldXF6ampvQGOYqiqEpAk3ZKLknCbmxsDA0NDfolTFFUkQghyMzMxMePHwEA1apVU3JEFEVRPx6atFMyRCKRNGE3MDBQdjgURX0H1NXVAQAfP36EsbEx7SpDURRVweiNqJQMSR92DQ0NJUdCUdT3RHLOoPfBUBRFVTyatFNFol1iKIoqDXrOoCiKqjw0aacoiqIoiqKoKo4m7RRFURRFURRVxdGknfrh3L59G1wuF927d5eZFhsbC4ZhpC9tbW3UrVsXEydORHR0tLSem5sbunTpInf5N27cAMMwePLkibRs7Nix4HK5OHLkiEz9RYsWgWEYjBs3jlUeHh4OhmEQGxvLKj927BhcXFwgEAigpaWFBg0aYMmSJfj69SsAYO/evaxtkLzU1NSky/D09JSWq6iowMTEBB07dkRAQADEYnGx+08Sr7ztX7t2LRiGgYuLC6u+o6Mjq15qaip8fX1hZ2cHNTU1mJqaokOHDjh+/DgIIQAAFxcXVuy2trZYuXKldHpB+/btQ7NmzaChoQFtbW20bdsWZ86cKXY7AMDS0lK6DnV1dVhaWmLgwIG4cuUKq17h46Lg686dO1i/fj309PSQnZ0ts47MzEzo6Ohgy5Yt0nVu2rSJVefx48cYMGAATExMoKamhtq1a8Pb2xtRUVEKrR/Iv0F81apVsLOzg7q6OvT19eHk5AQ/P78it//atWtgGEY6dKvk77p160IkErHq6urqYu/evaWKm6Ioivp2aNJO/XD8/f0xefJkhIWF4cOHD3LrXLp0CfHx8YiIiMCKFSvw4sULNGzYEJcvXwYAjBo1ChcvXsQ///wjM29gYCCaNm2KBg0aAMhP2g4fPoxZs2YhICBA7vrU1NTg7+/P+mEgj6+vL9zd3dGsWTOcPXsWz549w/r16xEREYGgoCBpPR0dHcTHx7Neb9++ZS2rS5cuiI+PR2xsLM6ePQtXV1dMnToVPXr0gFAoLDaOatWq4erVqzLbHxAQgJo1axY7b3JyMpydnbF//37MmTMHjx49QlhYGNzd3TFr1iykpKRI63p7eyM+Ph4vX77EnDlzsGDBAuzcuZO1vJkzZ2Ls2LFwd3fHkydPcO/ePbRu3Rq9evXC1q1bi40FAJYsWSJdx/79+6Grq4sOHTpg+fLlMnUlx0XBV5MmTTBs2DBkZGTg+PHjMvMcPXoUubm58PDwkLv+M2fOoEWLFsjJycHBgwfx4sULHDhwAAKBAPPnz1do/QCwePFibNy4EUuXLsXz589x9epVjBkzpkzPUnjz5g32799fbJ3SxE1RJSEiEQi9QZmiyodQP6WUlBQCgKSkpMhMy8rKIs+fPydZWVlKiKx80tLSiJaWFomMjCTu7u5k+fLlrOkxMTEEAHn8+DGrXCQSERcXF2JhYUGEQiHJy8sjJiYmZOnSpXKXv2PHDmnZ3r17SYsWLUhycjLR0NAgcXFxrHkWLlxIGjZsSDp27EgGDBggLX/8+DEBQGJiYgghhNy9e5cAIJs2bZK7bUlJSYQQQgIDA4lAICh2P4wYMYL06tVLpvzy5csEANmzZ0+R80ri7dGjB1m2bJm0/NatW8TQ0JCMHz+etG3bVqa+xPjx44mmpiZ5//69zLLT0tJIXl4eIYSQtm3bkqlTp7KmN27cmPTp00f69+3btwkAsmXLFpll+fj4EBUVFZn9XZCFhQXZuHGjTPmCBQsIh8MhkZGRhJCij4uC+vbtS9q3by9T3rZtW+Lu7i53nRkZGcTQ0JD07t1b7jIl76ki62/YsCFZtGhRkdPluXr1KgEgXY/k719//ZWYm5uT7OxsaV2BQEACAwNLFXdh3/O5g6o84pwcEjN4CHnZqjXJfPpM2eF894r7/qZ+bLSlnSoRIQSZuUKlvIicrhLFCQ0NhZ2dHerUqQMPDw8EBAQotAwOh4OpU6fi7du3ePjwIXg8HoYPH469e/ey5j9y5AhEIhEGDx4sLfP394eHhwcEAgG6du0q08VAYtWqVTh27BgePHggd/rBgwehpaWFCRMmyJ2uq6tb4naUpF27dmjYsKHcFuPCvLy8WNsSEBCAoUOHQlVVtch5xGIxDh8+jKFDh8LMzExmupaWFng82cdDEEJw48YNREZGspYfHBwMLS0tjB07VmaeGTNmIC8vD8eOHStxWwqbOnUqCCE4deqUwvOMGjUKV65cYV3RePPmDcLCwjBq1Ci585w/fx6fP3/GrFmz5E4vzXtqamqKK1eu4NOnTwrPU5Rp06ZBKBTif//7n9zpFRk3RX3Ztw9Zjx5B9Pkz3o0ejZxXr5QdEkV9l+jDlagSZeWJ4LDgvFLW/XxJZ2ioKn6YShJoIL97SEpKCq5fv87qg10UOzs7APn9i5s3bw4vLy+sXbuWNX9gYCD69esHgUAAAIiOjsadO3ekSbCHhwd8fHwwb948meHvGjdujIEDB2L27NnSbjgFRUdHo1atWlBRUSkx1pSUFGhpabHK2rRpg7Nnzyq0nQX74xelR48eGDduHMLCwtCkSROEhobi5s2bRXYBAoDPnz8jKSlJui9Lsn37dvj5+SE3Nxd5eXlQU1PDlClTpNOjoqJgbW0t94eCmZkZdHR0ytS/Wl9fH8bGxjL3Ezg7O4PDYbdlpKenAwA6d+4MMzMzBAYGYtGiRQDy7y8wNzdH+/bt5a5H0h1K0f1R3Po3bNiA/v37w9TUFHXr1oWzszN69eqFrl27KrTsgjQ0NLBw4ULMnTsX3t7e0uO5rHFTVFHyPnzA5+07AAA8IyMIP31C3EgvWBw6CFVzcyVHR1HfF9rSXgWEhYXBzc0NZmZmYBgGJ0+eLLb+8ePH0bFjRxgZGUFHRwctW7bE+fPKSaqrkpcvX+LevXvSVnAejwd3d3f4+/srNL+kRV2SbNvZ2cHZ2VmapL569Qo3btxgtaoGBASgc+fOMDQ0BAB069YNKSkpMjc6Sixbtgw3btzAhQsXily/IrS1tREeHs56FXdDYuH1KDKetoqKCjw8PBAYGIgjR47A1tZW2o+/uGWXxtChQxEeHo5bt26ha9eu8PX1hbOzc7mWqSh5+yEkJERmv0pwuVyMGDFCevVFLBZj3759GDlypEyiXdbYi1u/g4MDnj17hjt37sDLywsfP36Em5sbRo8eXap1SIwaNQoGBgZYvXp1ueOmqKIkrlwFkpUF9SZNYHX6FPi1a0sT97zERGWHR1HfFdrSXgVkZGSgYcOG8PLyQt++fUusHxYWho4dO2LFihXQ1dVFYGAg3NzccPfuXTRq1KjC41NX4eL5ks4VvlxF160of39/CIVCVrcMQgj4fD62bt0q05pY2IsXLwAAVlZW0rJRo0Zh8uTJ2LZtGwIDA2FtbY22bdsCyB/NY9++fUhISGB1+RCJRAgICJDb+mptbQ1vb2/89ttvMj8mbG1tcfPmTeTl5ZXY2s7hcGBjY1NsneK2s+A2FsfLywtOTk549uwZvLy8SqxvZGQEXV1dREZGKrR8gUAg3Y7Q0FDY2NigRYsW6NChA4D/9klubq5Ma/uHDx+QmpoKW1tbhdZV0JcvX/Dp0yeZ/WBubl7sfvXy8sLKlStx5coViMVivHv3DiNHjiyyviS2yMhItGzZssS4Slo/h8NBs2bN0KxZM0ybNg0HDhzAsGHD4Ovrq/B7KsHj8bB8+XJ4enpi0qRJ5YqbouRJv3EDaRcvAlwuTBcsAE9PD+b+fnjrMQx5cXGI8xoFi6D94OnrKztUivou0Jb2KqBr165YtmwZ+vTpo1D9TZs2YdasWWjWrBlq166NFStWoHbt2vj9998rJT6GYaChylPKS9EnLAqFQuzfvx/r169ntVJGRETAzMwMwcHBxc4vFouxZcsWWFlZsX74DBw4EBwOB4cOHcL+/fvh5eUljenPP/9EWloaHj9+zFpncHAwjh8/XuSoHgsWLEBUVBQOHz7MKh8yZAjS09Oxfft2ufOVZZSQwq5cuYKnT5+iX79+CtWvW7cu6tati2fPnmHIkCEl1udwOBg0aBAOHjwod+Se9PT0Ikeu0dLSwtSpUzFz5kxpS++gQYOQnp6OXbt2ydRft24dVFRUFN6WgjZv3gwOh4PevXuXaj7Jj7aAgAAEBgaiQ4cOsLCwKLJ+p06dYGhoiDVr1sidXt731MHBAUD+D/+yGDBgAOrWrYvFixezyis7burHJ87JQcLSZQAAfQ8PqNXJ/yGoYmyMmgEB4JmaIvf1a8SNHg1RWpoyQ6Wo7wZtaf8BiMVipKWlQb+Y1oqcnBzk5ORI/05NTf0WoX0zZ86cQVJSEkaNGiXTot6vXz/4+/uzxkn/8uULEhISkJmZiWfPnmHTpk24d+8e/vjjD3C5/7Xua2lpwd3dHXPmzEFqaio8PT2l0/z9/dG9e3c0bNiQtT4HBwdMnz4dBw8exMSJE2ViNTExgY+PD9auXcsqd3JywqxZszBjxgy8f/8effr0gZmZGV69eoWdO3eidevWmDp1KoD8KwgJCQkyyzY2NpZ21cjJyUFCQgJEIhESExNx7tw5rFy5Ej169MDw4cMV3LP5iX5eXp7CNx8uX74c165dg5OTE5YvX46mTZtCRUUFN27cwMqVK3H//v0ilzV27FgsXboUx44dQ//+/dGyZUtMnToVv/76K3Jzc9G7d2/k5eXhwIED2Lx5MzZt2gTzEvrFpqWlISEhAXl5eYiJicGBAwfg5+eHlStXyrRqS46LgnR1dVlj4I8aNQre3t4AUORNxxKamprw8/PDgAED0LNnT0yZMgU2Njb4/PkzQkNDERcXx/rxVtz6+/fvj1atWsHZ2RmmpqaIiYnBnDlzYGtrW66+56tWrULnzuwraaWNm6IK++Lvj7y4OPCMjGA4mX0lR7VGddQMCMBbDw/kPH+Bd2PHoabfHnA0NJQULUV9J771cDVU8QCQEydOlGqe1atXEz09PZKYmFhknYULFxIAMq8fZcjHHj16kG7dusmdJhlKMSIiQjq0nuSloaFB7O3tyYQJE0h0dLTc+f/66y8CgLX8hIQEwuPxSGhoqNx5xo8fTxo1akQIkR0SkZD8IbsMDQ1ZQz5KhISEkF9++YVoa2sTTU1N0qBBA7JkyRLWkI/y3ksAJD4+nhCSP+SjpIzH4xEjIyPSoUMHEhAQQEQiUbH7Ul68BU2dOrXYIR8JISQ5OZn89ttvpHbt2kRVVZWYmJiQDh06kBMnThCxWEwIkT/kIyGEjB07ltStW5cVp7+/P2nSpAlRU1MjmpqapE2bNuT06dPFbgch+cMvSvaDqqoqqVmzJhk4cCC5cuUKq17h46LgKzg4mFU3MzOTCAQCoq+vzxoyseA6Cw8zef/+fdK3b19iZGRE+Hw+sbGxIWPGjJEec4qsf/fu3cTV1ZUYGRlJt8XT05PExsYWuf1FDflYeMjGTp06EQDSIR8Vjbuw7/HcQVW8nHfvyIsGDcnzOnYk+cyZIutlPX9OIps2I8/r2JG3XqOIKCfnG0b5/aJDPv68GELoHUdVCcMwOHHihMKX7Q8dOgRvb2+cOnVK2g9YHnkt7ebm5khJSYGOjg6rbnZ2NmJiYmBlZcVqYaQoiioOPXdQAPBu3HikX7sGjRYtUDMwoNhujpmPHyNu1GiQzExod+yA6hs3gpEzLCz1n9TUVAgEArnf39SPjfZp/44dPnwYo0ePRmhoaLEJOwDw+Xzo6OiwXhRFURRVkdKuXEH6tWuAigpMF8wv8b4kjUaNYL5tKxgVFaRdvIR433kgYvG3CbYcRMI8/H39MnIyy3Y/CUWVBU3av1PBwcEYOXIkgoOD0b17d2WHQ1EURf3kxFlZSFy2HABg4DkC/Fq1FJpPs2VLVN+0EeBykXLqFBKXLa/yw47GPH6Ic9s3Iui3qXJjzYmORvyiRSAikRKio35UNGmvAtLT01ljMsfExCA8PBxxcXEAgDlz5rBuHDx06BCGDx+O9evXw8nJCQkJCUhISEBKSooywqcoiqIofN61C3kfPoBXrRoMx48v1bza7dvDbNVKgGGQdOgQPm3cVDlBVpAXt64DAGyaOrGuJhCxGF/3ByGmX38kHw7B1/1BygqR+gHRpL0KePDgARo1aiQdatDHxweNGjXCggULAADx8fHSBB4Adu/eDaFQiIkTJ6JatWrSl2RkEYqiKIr6lnJiYvDVP/9BdCZzfivTSDACNzeYLlwIAPiyezc+795ToTFWlJzMTLx5cBcAYN/aVVqel/gR70Z7I3HFCpDcXGj+0gaCHvRKOFVx6N0eVYCLi0uxlwILDyt37dq1yg2IoiiKohRECMnv0pKXB802baDdsWOZl6U3yB3ijHR8XLsOnzZsAEdLE/oKPCPiW3p1/zaEebnQM6sBYytrAEDqufNIWLgQopQUMHw+jGfPgt7gwQo/a4SiFEFb2svh0aNHePr0qfTvU6dOoXfv3pg7dy5yc3OVGBlFURRFfRtp5y8g49YtMKqqMJ3nW+5E1WDUKBiMz3+uRuKSpUg5daoiwqwwL25eAwDYt24LcUYGPsyZi/fTpkGUkgI1BwdYnTgO/SFDaMJOVTiatJfD2LFjERUVBQB48+YNBg0aBA0NDRw5cgSzZs1ScnQURVEUVbnEGRlIXLkSAGAwejRUi3lCcGkYTZkCPQ8PAMCHub5IvXixQpZbXhnJSYh7GgEAsBQYIKZXb6ScOAFwODAYOxaWh4MVvgGXokqLJu3lEBUVBUdHRwDAkSNH8Msvv+DQoUPYu3cvjh07ptzgKIqiKKqSfdq+HcLERKjUqAGDMd4VtlyGYWAydw4EffoAIhE++MxA+s1bFbb8soq8FQZCxDDU0kHylOnIe/8eKtWrwyJoP4ynTwOjqqrsEKkfGE3ay4EQAvG/48leunQJ3bp1AwCYm5vj8+fPygyNoiiKoipVTnQ0vu7bDwAw8Z0LTgU/UIvhcFBt6RJod+4MkpeHfyZNQuajRxW6jtJ6fuU8AMD45WtALIagd29YnToJjSZNlBoX9XOgSXs5NG3aFMuWLUNQUBCuX78uHS89JiYGJiYmSo6OoiiKoioHIQQJS5YCQiG02rWDtqtryTOVAcPjofraNdBs0wYkOxvvxoxF1t9/V8q6ikMIQeyeXfj4TxwYQlBdxEH1TZtgtmoluFpa3zwe6udEk/Zy2LRpEx49eoRJkybB19cXNjY2AICjR4/C2dlZydH9vG7fvg0ulyv3oVOxsbFgGEY6Jn5he/fuBcMwYBgGHA4HNWrUwMiRI/Hx40dpHYZhcPLkSZl5PT090bt37zLFI3mpqqrCxsYGy5YtY40otGjRIlY9ycvOzk5mGfJehUcgkrC0tMSmTZtYfzMMgzt37rDqTZs2DS4uLqyy1NRU+Pr6ws7ODmpqajA1NUWHDh1w/PjxKv9gFIqiyif1zBlk3r8PRk0NJnPnVuq6GFVV1NiyGepNm0Ccno53o72R8/p1pa6zIOGnT3g3bhwiDuwDAJjw+LA/eRI6XTp/sxgoCqBDPpZLgwYNWKPHSKxduxZcLlcJEVEA4O/vj8mTJ8Pf3x8fPnyAmZlZqebX0dHBy5cvIRaLERERgZEjR+LDhw84f/58pcVz6dIl1K1bFzk5Obh58yZGjx6NatWqYdSoUdI6devWxaVLl1jz8Xg86OnpIT4+Xlq2bt06nDt3jlVXIBAoHK+amhpmz56N69evF1knOTkZrVu3RkpKCpYtW4ZmzZqBx+Ph+vXrmDVrFtq1awddXV2F10lR1PdDlJaGxDVrAACG48ZCtUb1Sl8nR10d5jt3Is5zJLKfPUPcSC9YHDoI1Ro1KnW9aZcvI37efAiTkvDBviYAwHHsRKjQq+mUEtCkvZySk5Nx9OhRvH79Gr/++iv09fXx/PlzmJiYoHr1yj+RUWzp6ekICQnBgwcPkJCQgL1792JuKVuBGIaBqakpAMDMzAxTpkzB/PnzkZWVBXV19UqJx8DAQLpOCwsLBAYG4tGjR6ykncfjSesUVrBcS0ur2LolGTNmDHbu3Ik///xTep9GYXPnzkVsbCyioqJYP0JsbW0xePBgqFVw31aKoqqOT//7H0SfPkPV0hL6Xl7fbL1cLS2Y79mNt8OGIffV6/zE/cABqJgYV/i6xBkZSFy1CslHjgIAMu1tkakqAk+Vj9pO9Eo6pRy0e0w5PHnyBLVr18bq1auxbt06JCcnAwCOHz+OOXPmKDe4ikQIkJuhnFcpu1mEhobCzs4OderUgYeHBwICAsrdVUNdXR1isRhCobDU85YlngcPHuDhw4dwcnIqa8jlYmVlhXHjxmHOnDnSG60LEovFOHz4MIYOHSr3qoHkRwNFUT+e7MhIJB04CAAwmT8PnG88WgpPTw81/QOgYm6OvHfvEDfKC8KkpApdR1Z4ON706ZufsDMM9Ed5IaVHFwCAdVMnqKqVrvGGoioK/WYtBx8fH4wcORJr1qyBtra2tLxbt24YUsWe4FYueZnAitJ1Makwcz8AqpoKV/f394fHv2P7dunSBSkpKbh+/bpMf2xFRUdHY+fOnWjatCnrPR48eLBMF6icnByZfuuKxuPs7AwOh4Pc3Fzk5eVhzJgxGD58OKvO06dPoVXohicPDw/s3LmzTNtWnHnz5iEwMBAHDx7EsGHDWNM+f/6MpKQk2NnZVfh6KYqquohYjITFSwCxGNpdukCrVSulxKFiYoyagQF4O9QDua9e491ob9TcGwhugXN0WRChEJ937MTnnTsBkQi8atVgtmoV1Js2QdT4EQAA+9YuFbAFFFU2NGkvh/v372PXrl0y5dWrV0dCQoISIvq5vXz5Evfu3cOJEycA5HcncXd3h7+/f6mS9pSUFGhpaUEsFiM7OxutW7eGn58fq87GjRvRoUMHVtns2bMhEonKFE9ISAjs7e2Rl5eHZ8+eYfLkydDT08OqVaukderUqYPTp0+z5tPR0VF4u0rDyMgIM2fOxIIFC+Du7s6aRm8ypaifU8qJk8h6/BiMhgZMfput1FhUa9RAzQB/vPUYhuy//8a78eNRc88ecErZhVEiNzYW72fPRnbEEwCATo8eMF0wH1wdHcRGPEJmSjLUtHVg2bBxRW4GRZUKTdrLgc/nIzU1VaY8KioKRkZGSoiokqho5Ld4K2vdCvL394dQKGR12SCEgM/nY+vWrQrfjKmtrY1Hjx6Bw+GgWrVqcvuxm5qaSkcLKjifpItUaeMxNzeXLs/e3h6vX7/G/PnzsWjRImn/cMnIMt+Kj48Ptm/fju3bt7PKjYyMoKuri8jIyG8WC0VRyiVKTsbHdesAAEYTJ0CljPfMVCS+tTVq+vvh7QhPZD14iH8mT0GN7dtK1WWHEILkI0eQuHIVSFYWONraMF24EIIe/101fXHzGgCgTovW4NKuf5QS0T7t5dCzZ08sWbIEeXl5APJvYIyLi8Ps2bPRr18/JUdXgRgmv4uKMl4Mo1CIQqEQ+/fvx/r16xEeHi59RUREwMzMDMHBwQpvLofDgY2NDWrVqlXqG08rKh4ulwuhUIjc3Nwyrb8iaGlpYf78+Vi+fDnS0tKk5RwOB4MGDcLBgwfx4YPsj7n09PQy9f+nKKrq+rhpE0RJSVC1sYZ+oa57yqTm4ADzXTvBqKsj4+ZNfJj5K4iC5x/hly/4Z+IkJCxYCJKVBY3mzVHr1ElWwp6Xk43oe7cB0K4xlPLRpL0c1q9fj/T0dBgbGyMrKwtt27aFjY0NtLW1sXz5cmWH91M5c+YMkpKSMGrUKNSrV4/16tevH/z9/Vn1X758yUqmw8PDpT++lBHPly9fkJCQgH/++Qdnz57F5s2b4erqyur+IhQKkZCQwHolJiZWWMzyjBkzBgKBAIcOHWKVL1++HObm5nBycsL+/fvx/PlzREdHIyAgAI0aNUJ6ejoAYM6cOTJ98ymK+r5kPX2G5JBQAIDp/AVgVFSUHBGbRuPGqLH1f2BUVJB24QLi5y8AkXMTfUFp167hTc9eSL9yBYyKCoxnzULNvYFQKXRz/euH95CXnQUdIxOY1bGvzM2gqBLR6zzlIBAIcPHiRdy8eRNPnjxBeno6GjduLNPXmap8/v7+6NChg9wuMP369cOaNWvw5MkTaRI8aNAgmXrv3r1TWjySY4bL5aJatWro1q2bzA+/v//+G9WqVWOV8fl8ZGdnV1jchamoqGDp0qUyN1br6+vjzp07WLVqFZYtW4a3b99CT08P9evXx9q1a6XbHR8fj7i4uEqLj6KoykVEIiQsWQIQAh03N2g6NVd2SHJptWoFsw3r8X7adKScOAGOpiZMfOeCKXS1VpyVhcQ1a5AcfBgAwK9tA7O1a6FWxI31kq4xdq1+kVkWRX1rDKF3lf2UUlNTIRAIkJKSInMzY3Z2NmJiYmBlZUXH26YoSmH03PHjSTocgoRFi8DR0oL12T/Bq+L3a6WcOoUPs38DABiMGwvjadOk07KePsOHX39FbmwsAEB/xAgY+UwHh8+Xu6ystFTsHDsMYpEII9Ztg6G5RWWHr5Divr+pHxttaS+lLVu2YMyYMVBTU8OWLVuKrTtlypRvFBVFURRFVSzh16/4uHEjAMBoypQqn7ADgKBXL4gyMpC4ZCm+7NwFrpYW9D098cXPD5+2bgOEQvCMjWG2aiU0nYt/SFLUnVsQi0QwsrCqMgk79XOjSXspbdy4EUOHDoWamho2/nsyk4dhGJq0UxRFUd+tj+vXQ5ySAr6dHfSGDFZ2OArTHzIE4vQMfNqwAR/XrUfy8RPIffMGAKDdpQuqLVoIrq5uicuRdI2hN6BSVQVN2kspJiZG7v8piqIo6keR+egxUo4dBwCYLlgA5jsb6tBwjDfE6en4sns3ct+8AUdTE6YL5kOnZ0+F+qanfvqI95F/AwwDu1Ztv0HEFFUyOnpMOSxZsgSZmZky5VlZWViyZIkSIqIoiqKo8iFCYf7NpwAEfftCo3EjJUdUNkbTp8Fo2jTodO8Oq1OnIOjVS+GbSV/cug4AMLevB20Dw8oMk6IURpP2cli8eLF0aLuCMjMzsXjxYiVERFEURVHlkxR8GDmRkeAIBDCeOUPZ4ZQZwzAwHDcW1devg2qN6qWaN1IyagztGkNVITRpLwdCiNxf7REREdDX11dCRBRFURRVdsJPn/Bp82YAgPH0aeD9hN9ln+Ji8fndW3B5PNg6tVJ2OBQl9X11Uqsi9PT0wDAMGIaBra0tK3EXiURIT0/HuHHjlBghRVEURZVe4tq1EKenQ61ePegOGKDscJRCcgOqVaOmUNPSUm4wFFUATdrLYNOmTSCEwMvLC4sXL2Y9QEdVVRWWlpZo2bKlEiOkKIqiqNLJuHcPqad/BxgGpgsXgOFylR3SN0fEYkTezO/PTkeNoaoamrSXwYgRIwAAVlZWcHZ2hkoVe6QzRVEURZUGyctD4tKlAADdgQOhXr++kiNSjveRz5H25RNU1TVQq3HVfPor9fOiSXsppaamSp9A1qhRI2RlZSErK0tuXfqkMoqiKOp78HV/EHKiX4Grpwfj6dOUHY7SSLrG1HZyBk9VVbnBUFQh9EbUUtLT08PHjx8BALq6utDT05N5Scop5bh9+za4XC66d+8uMy02NhYMw4DL5eL9+/esafHx8eDxeGAYBrH/Pua6oM6dO4PL5eL+/fus8mvXrknvcZD3cnV1ZdXft28fmjVrBg0NDWhra6Nt27Y4c+aM3GXWrVsXIpGINU1XVxd79+4tdh+kpqbC19cXdnZ2UFNTg6mpKTp06IDjx4+DECKt9/fff2PgwIEwMjICn8+Hra0tFixYIDOUqaWlJRiGweHDh2XWVbduXTAMU2xMixYtgqOjI+tvhmFk7v0IDw+Xu/+PHTsGFxcXCAQCaGlpoUGDBliyZAm+fv1a7H6gKKpkeQkJ+LRtGwDAeOYMhR489CMSCfMQdecmANo1hqqaaNJeSleuXJGODHPlyhW5r6tXr+LKlStKjvTn5e/vj8mTJyMsLAwfPnyQW6d69erYv38/q2zfvn2oXl3+sGBxcXH466+/MGnSJAQEBLCmOTs7Iz4+Xua1a9cuMAyDCRMmSOvOnDkTY8eOhbu7O548eYJ79+6hdevW6NWrF7Zu3Sqz3jdv3sjEWZLk5GQ4Oztj//79mDNnDh49eoSwsDC4u7tj1qxZSElJAQDcuXMHTk5OyM3NxR9//IGoqCgsX74ce/fuRceOHZGbm8tarrm5OQIDA1lld+7cQUJCAjQ1NUsVIwCoqanB398f0dHRxdbz9fWFu7s7mjVrhrNnz+LZs2dYv349IiIiEBQUVOr1UhTFlrhqNUhmJtQdHSHo00fZ4ShNTPgjZGekQ1NPH+Z1f87uQVQVR6ifUkpKCgFAUlJSZKZlZWWR58+fk6ysLCVEVj5paWlES0uLREZGEnd3d7J8+XLW9JiYGAKAzJs3j9SuXZs1zdbWlsyfP58AIDExMaxpixYtIoMGDSIvXrwgAoGAZGZmFhvH8+fPiba2NvH19ZWW3b59mwAgW7Zskanv4+NDVFRUSFxcHCGEkKtXrxIA5NdffyXm5uYkOztbWlcgEJDAwMAi1z1+/HiiqalJ3r9/LzMtLS2N5OXlEbFYTBwcHEjTpk2JSCRi1QkPDycMw5BVq1ZJyywsLMhvv/1G+Hy+NEZCCPH29iaTJ08uMaaFCxeShg0byvzdsWNHMmDAAGn548ePWfv/7t27BADZtGmT3OUmJSUVuU7q2/uezx0/q7SbN8nzOnbkub0DyXr+XNnhKNXpjavIuoHdydV9u5UdSrGK+/6mfmy0pb0cateujUWLFpXYUvi9I4QgMy9TKS9SoCuHIkJDQ2FnZ4c6derAw8MDAQEBcpfRs2dPJCUl4ebN/EuhN2/eRFJSEtzc3ORuf2BgIDw8PGBnZwcbGxscPXq0yBiSk5PRq1cvuLi4YOm/N3YBQHBwMLS0tDB27FiZeWbMmIG8vDwcO3aMVT5t2jQIhUL873//U2j7xWIxDh8+jKFDh8LMzExmupaWFng8HsLDw/H8+XP4+PiAw2GfBho2bIgOHTogODiYVW5iYoLOnTtj3759APIfIhYSEgIvLy+FYpNn1apVOHbsGB48eCB3+sGDB6GlpcW6WlGQ7k96GZ+iKoI4NxeJS5cBAPSGDoWavb2SI1KenMxMvHlwFwBg39q1hNoUpRz0RtRymDBhAg4dOoSlS5eicePG8PDwgLu7O0xNTZUdWoXKEmbB6ZCTUtZ9d8hdaKhoKFzf398fHh4eAIAuXbogJSUF169fh4uLC6ueioqKNKlv3bo1AgIC4OHhIXckoEuXLiEzMxOdO3cGAHh4eMDf3x/Dhg2TqSsWizFkyBDweDwcPHiQNYZ/VFQUrK2toSrn5iYzMzPo6OggKiqKVa6hoYGFCxdi7ty58Pb2Zg0vKs/nz5+RlJQEOzu7YutJ1mNfxJe0vb299AdNQV5eXpgxYwZ8fX1x9OhRWFtbs/qql1bjxo0xcOBAzJ49G5cvX5aZHh0djVq1atERmiiqEnwNCERubCy4hoYwmjJZ2eEo1av7tyHMy4WeWQ0YW1krOxyKkou2tJfD9OnTcf/+fbx48QLdunXDtm3bYG5ujk6dOpW6HzJVfi9fvsS9e/cwePBgAACPx4O7uzv8/f3l1vfy8sKRI0eQkJCAI0eOFNliHBAQAHd3d/B4+b9xBw8ejFu3buH169cydefOnYvbt2/j1KlT0NbWlple2isHADBq1CgYGBhg9erVJdYt7fJLW7979+5IT09HWFgYAgICytXKLrFs2TLcuHEDFy5cKHd8FEUpJvef9/i8cycAwGTWr+DKOV/9TCSjxti3biv3SecUVRXQlvYKYGtri8WLF2Px4sW4c+cOxo8fj5EjR2L48OHKDq1CqPPUcXfIXaWtW1H+/v4QCoWsbiGEEPD5fGzdulWmlbp+/fqws7PD4MGDYW9vj3r16iE8PJxV5+vXrzhx4gTy8vKwY8cOablIJEJAQACWL18uLTt8+DDWrVuHP/74A7Vr15aJz9bWFjdv3kRubq5Ma/uHDx+QmpoKW1tbmfl4PB6WL18OT09PTJo0qdh9YGRkBF1dXURGRhZbT7KeFy9eoFGjRjLTX7x4UWQsw4YNw8KFC3H37l2cOHGi2PUowtraGt7e3vjtt99kfmBJ9lleXh5tbaeoCpS4ciVIdjY0mjWDjpxugT+TjOQkxD2NAADYt3JRbjAUVQza0l5B7t27h2nTpqFPnz6IiorCgB/o8c8Mw0BDRUMpL0VbPIRCIfbv34/169cjPDxc+oqIiICZmZlM/2wJLy8vXLt2rcgW44MHD6JGjRqIiIhgLXf9+vXYu3evdDjG8PBwjBo1CqtWrZJ2oyls0KBBSE9Px65du2SmrVu3DioqKujXr5/ceQcMGIC6deti8eLFxe4HDoeDQYMG4eDBg3JHzklPT4dQKISjoyPs7OywceNGiMViVp2IiAhcunRJesWiMC8vL1y/fh29evWqsKFNFyxYgKioKJkhJYcMGYL09HRs375d7nzJyckVsn6K+pmkXbuG9MuXAR4Ppgvm//Qtyy//CgMhYlSrXQe6ptWUHQ5FFYm2tJdDVFQUDh48iODgYMTExKBdu3ZYvXo1+vbtCy0tLWWH91M5c+YMkpKSMGrUKJkW9X79+sHf319mTHAA8Pb2xoABA4q8odHf3x/9+/dHvXr1WOXm5uaYM2cOzp07BycnJ/Tu3RsuLi7w8PBAQkICqy6Xy4WRkRFatmyJqVOn4tdff0Vubi569+6NvLw8HDhwAJs3b8amTZtgbm5e5DYW94OgoOXLl+PatWtwcnLC8uXL0bRpU6ioqODGjRtYuXIl7t+/D11dXfj7+6Njx47o168f5syZA1NTU9y9exczZsxAy5YtMW3aNLnLt7e3x+fPn6Ghofi9BiUxMTGBj48P1q5dyyp3cnLCrFmzMGPGDLx//x59+vSBmZkZXr16hZ07d6J169aYOnVqhcVBUT86cXY2EpflXyHUHz4cfDlXBX82kq4xdrSVnariaNJeDnZ2dmjWrBkmTpyIQYMGwcTERNkh/bT8/f3RoUMHuTdq9uvXD2vWrMGTJ09knlLL4/FgaGgod5kPHz5EREQE9uzZIzNNIBCgffv28Pf3x+fPn/H27Vu8ffsW1arJttJYWFhIHxa0adMmNGjQANu3b8e8efPA5XLRuHFjnDx5Uu7INQW1a9cO7dq1k9v3uyB9fX3cuXMHq1atwrJly/D27Vvo6emhfv36WLt2rXQfOTs7486dO1i8eDG6du2KtLQ01KxZEyNGjMCcOXPA5/OLXIeBgUGxMZTFzJkzsWPHDmRnZ7PKV69ejSZNmmDbtm3YuXMnxGIxrK2t0b9/f4wYMaLC46CoH9mXPX7I++cf8ExMYDRR/qhMP5Ok+PdIeB0NhsOBnXMbZYdDUcViCL3Tq8yio6Pl9l3+HqSmpkIgECAlJUUmkc3OzkZMTAysrKygpqampAgpivre0HNH1Sb88gWv2rUHyclB9U0bodOli7JDUrq/jhzE7aPBsHRsgn5ziu9+WFUU9/1N/dhon/Zy+F4TdoqiKOrn8zUoCCQnB2r160Nbga52PzpCSIFRY1yUGgtFKYJ2jyklfX19REVFwdDQEHp6esXewPP169dvGBlFURRFySdKT0fSofwb8g3GeP/0N58CQMLrKCQnxIPH58OmWQtlh0NRJaJJeylt3LhROv72xo0b6YmPoiiKqvKSQ0IhTk2FqpUVtNu3V3Y4VULkzesAAJumLaCqpvjwwhSlLDRpL6WCN755enoqLxCKoiiKUoA4Nxdf9+4FABiMHg2GQ3vGikUiRP4VBgCwa9VWydFQlGLoJ7ccuFwuPn78KFP+5csXcLlcJUREURRFUWwpp05B+OkTeCYmELj1UHY4VULcswhkpiRDTV0Nlra1lB0ORSmEJu3lUNTAOzk5OTJPvKQoiqKob42IRPjql/+kYf2RnmDodxOA/8Zmr8N/A27oECAvS7kBUZQCaPeYMtiyZQuA/CeF+vn5sR6kJBKJEBYWBjs7O2WFR1EURVEAgLSLF5H79i04AgH0fqAndZdHXnYWom/n92e3F3wCqncBuEU/l4KiqgqatJfBxo0bAeS3tO/cuZPVFUZVVRWWlpbYuXOnwssLCwvD2rVr8fDhQ8THx+PEiRPo3bt3sfNcu3YNPj4++Pvvv2Fubo558+bRPvYURVGUFCEEX3bnPxxOf+hQcDQ1lRxRFSAW43WAD/LyRNBRyYZZt0lA21kAHVSC+g7QpL0MYmJiAACurq44fvw49PT0yrW8jIwMNGzYEF5eXujbt69C6+/evTvGjRuHgwcP4vLlyxg9ejSqVaum0GPuKYqiqB9fxl9/Ifv5czDq6tAb5qHscJRPJAR+n4LIh+EADGDfqB4Yl9lKDoqiFEeT9nK4evVqhSyna9eu6Nq1q8L1d+7cCSsrK6xfvx4AYG9vj5s3b2Ljxo00aacoiqIAAF/2+AEAdAf0B6+cjUvfPWEOcGwUsp6dRUy6EwDA3t1HyUFRVOnQG1HLoV+/fli9erVM+Zo1azCgEvsO3r59Gx06dGCVde7cGbdv3y5ynpycHKSmprJeP6rbt2+Dy+Wie/fuMtNiY2PBMIz0ZWBggE6dOuHx48fSOi4uLtLpampqcHBwwPbt26XT9+7dC11d3SLX7+npyVqH5NWlmEeGL1q0CI6Ojqy/GYbBuHHjWPXCw8PBMAxiY2NZ5ceOHYOLiwsEAgG0tLTQoEEDLFmyhD7gi6J+UllPniDzzh2Ax4PBz951MjcDOOQOvPgdUemmEIMDIwsrGNSoqezIKKpUaNJeDmFhYejWrZtMedeuXREWFlZp601ISICJiQmrzMTEBKmpqcjKkn8H/MqVKyEQCKQvc3PzSotP2fz9/TF58mSEhYXhw4cPcutcunQJ8fHxOH/+PNLT09G1a1ckJydLp3t7eyM+Ph7Pnz/HwIEDMXHiRAQHByscQ5cuXRAfH896lWZ+AFBTU4O/vz+io6OLrefr6wt3d3c0a9YMZ8+exbNnz7B+/XpEREQgKCioVOukKOrHIGllF/ToARUzMyVHo0RZScD+3sCbq4CKJl6otgYA2Ld2UWpYFFUWNGkvh/T0dLlDO6qoqFS5luw5c+YgJSVF+nr37p2yQ6oU6enpCAkJwfjx49G9e3fs/feBIoUZGBjA1NQUTZs2xbp165CYmIi7d+9Kp2toaMDU1BS1atXCokWLULt2bZw+fVrhOPh8PkxNTVmv0t77UKdOHbi6usLX17fIOvfu3cOKFSuwfv16rF27Fs7OzrC0tETHjh1x7Ngx1sPAKIr6OeS8eYO0S5cAAAajRyk5GiVKSwT29gD+uQeoCZDaYx/ex74HGIY+UIn6LtGkvRzq16+PkJAQmfLDhw/DwcGh0tZramqKxMREVlliYiJ0dHSgri7/Ucx8Ph86Ojqsl6IIIRBnZirlVdRY+EUJDQ2FnZ0d6tSpAw8PDwQEBJS4DMk+y83NLbZOcdMry6pVq3Ds2DE8ePBA7vSDBw9CS0sLEyZMkDu9uG48FEX9mL74+wOEQKt9e/BtbJQdjnIkxwGBXYDEZ4CWCTDyLF7EJAMAzO3rQdvAULnxUVQZ0BtRy2H+/Pno27cvXr9+jXbt2gEALl++jODgYBw5cqTS1tuyZUv8+eefrLKLFy+iZcuWlbI+kpWFl42bVMqyS1Ln0UMwGhoK1/f394eHR/4oCV26dEFKSgquX78OFxcXufWTk5OxdOlSaGlpoXnz5jLTRSIRgoOD8eTJE4wZM0bhOM6cOcMavx8A5s6di7lz5yq8DABo3LgxBg4ciNmzZ+Py5csy06Ojo1GrVi2oqKiUarkURf2Y8hISkHL6dwCAofdoJUejJJ9e5neJSfsA6NYEhp0EDKwReWsHAMCOdo2hvlM0aS8HNzc3nDx5EitWrMDRo0ehrq6OBg0a4NKlS2jbVvFLb+np6Xj16pX075iYGISHh0NfXx81a9bEnDlz8P79e+zfvx8AMG7cOGzduhWzZs2Cl5cXrly5gtDQUPzxxx8Vvo3fk5cvX+LevXs4ceIEAIDH48Hd3R3+/v4ySbuzszM4HA4yMjJQq1YthISEsO4T2L59O/z8/JCbmwsul4vp06dj/PjxCsfi6uqKHTt2sMr09fXLtF3Lli2Dvb09Lly4AGNjY9a00l6JoCjqx/Z17z4gLw8azZpBvcDN7T+ND+HAgb5A5hfAsA4w/CSgY4ZPcbH4HBcLLo8HW6dWyo6SosqEJu3l1L17d7mjlDx79gz16tVTaBkPHjyAq6ur9G8fn/xhqEaMGIG9e/ciPj4ecXFx0ulWVlb4448/MH36dGzevBk1atSAn59fpQ33yKiro86jh5WybEXWrSh/f38IhUKYFbjpihACPp+PrVu3QiAQSMtDQkLg4OAAAwMDuV1Ihg4dCl9fX6irq6NatWrgcErXk0xTUxM2FXRZ2traGt7e3vjtt9/g7+/PmmZra4ubN28iLy+PtrZT1E9OlJyMpNBQAIDBGG8lR6MEsbeA4EFATipQzRHwOA5oGgAAXty8BgCwatQUaoWuglLU94Im7RUoLS0NwcHB8PPzw8OHDyESiRSaz8XFpdgWU3k3U7q4uLCGKaxMDMOUqouKMgiFQuzfvx/r169Hp06dWNN69+6N4OBg1vCJ5ubmsLa2LnJ5AoGgwpLuirBgwQJYW1vj8OHDrPIhQ4Zgy5Yt2L59O6ZOnSozX3JyMu3XTlE/ia+HDoFkZoJvZwfN1q2VHc63FXUBCB0GCLMBi9bA4GBALf/eLSIWI/LmdQB01Bjq+0aT9goQFhYGPz8/HD9+HGZmZujbty+2bdum7LB+KmfOnEFSUhJGjRrFalEH8sfT9/f3lxnzvDxEIhHCw8NZZXw+H/b29gDyx8VPSEhgTefxeDA0LNvNTyYmJvDx8cHatWtZ5U5OTpg1axZmzJiB9+/fo0+fPjAzM8OrV6+wc+dOtG7dWm4yT1HUj0WcmYmk/flDvBp4jwbDMEqO6Bt6dgw4PgYQCwHbLsCAvYDKf1dp30c+R9qXT1BV10CtxrL3LlHU94Im7WWUkJCAvXv3wt/fH6mpqRg4cCBycnJw8uTJSh05hpLP398fHTp0kEnYgfykfc2aNXjy5EmpRs0pTnp6Oho1asQqs7a2lt6bcO7cOVSrVo01vU6dOoiMjCzzOmfOnIkdO3YgOzubVb569Wo0adIE27Ztw86dOyEWi2FtbY3+/fvTIR8p6ieRfOw4RMnJUDE3h87P9GTsB4HAmekACFB/ANB7B8BldxWUdI2p7eQMnpxhminqe8EQeidbqbm5uSEsLAzdu3fH0KFD0aVLF3C5XKioqCAiIuK7SNpTU1MhEAiQkpIik8hmZ2cjJiYGVlZWUFNTU1KEFEV9b+i5QzlIXh5ede4M4Yd4mC5aCL1Bg5Qd0rdxcxNwaWH+/5uOArqtAwrdfyQS5mHnmGHIzkhH/3nLYFHf8ZuHWdGK+/6mfmy0pb0Mzp49iylTpmD8+PGoXbu2ssOhKIqifmIpf/wB4Yd4cA0NIejTR9nhVD5CgMuLgZsb8/9u7QO0XwDI6RIUE/4I2Rnp0NTTh3nd+t84UIqqWPThSmVw8+ZNpKWloUmTJnBycsLWrVvx+fNnZYdFURRF/WSIWIwvfn4AAP3hw8Hh85UcUSUTi4E/fP5L2DssBjoslJuwA/91jbFz/gUcDvcbBUlRlYMm7WXQokUL7NmzB/Hx8Rg7diwOHz4MMzMziMViXLx4EWlpacoOkaIoivoJpF+7htxXr8HR0oLe4B+8W4woDzgxBngQAIABemwCWk8rsnpOZibePLgLgI4aQ/0YaNJeDpqamvDy8sLNmzfx9OlTzJgxA6tWrYKxsTF69uyp7PAoiqKoHxghBF927QYA6A0eDK62tpIjqkR5WUCIB/D0CMDhAf38gKYji53l1f3bEOblQs+sBoytih7il6K+FzRpryB16tTBmjVr8M8//yA4OFjZ4VAURVE/uKwHD5AVEQFGVRX6w4cpO5zKk50KHOgPRJ0DeGrAoGCgfv8SZ5N0jbFv3fbnGgKT+mHRG1ErGJfLRe/evdG7d29lh0JRFEX9wD7v2QMAEPTtA56RkZKjqSQZX4ADfYH4cEBVGxgSAli2Knm25CTEPY0AANi3cqncGCnqG6FJezn06dNH7q93hmGgpqYGGxsbDBkyBHXq1FFCdBRFUdSPKjsyEhlhNwAOBwZeXsoOp3KkfgD29wY+vwQ0DACPY4BZoxJnA4CXf4WBEDGq1a4DXdNqJc9AUd8B2j2mHAQCAa5cuYJHjx6BYRgwDIPHjx/jypUrEAqFCAkJQcOGDXHr1i1lh0pRFEX9QL7syR8xRqdLF6jWrKnkaCrBl9dAQOf8hF2nOjDynMIJO1Cwa4xL5cRHUUpAW9rLwdTUFEOGDMHWrVvB+feBDmKxGFOnToW2tjYOHz6McePGYfbs2bh586aSo6UoiqJ+BLnv3iH17FkAgIH3aCVHUwkS/waC+gDpiYB+LWD4KUBX8R8mSfHvkfA6GgyHgzot21RioBT1bdGW9nLw9/fHtGnTpAk7AHA4HEyePBm7d+8GwzCYNGkSnj17psQoKYqiqB/Jl4AAQCyGZps2ULO3V3Y4FevdfSCwW37CblIvv4W9FAk78F8ru0WDRtAQ6FZ8jBSlJDRpLwehUIjIyEiZ8sjISIhEIgCAmpoavWv9G/H09GTdAOzp6QmGYbBq1SpWvZMnT0rfE0mdol6WlpYAABcXF7nTx40bJ10uwzA4efJkqWJ+/PgxBgwYABMTE6ipqaF27drw9vZGVFQUq96+ffvQrFkzaGhoQFtbG23btsWZM2dYda5du8aKzcjICN26dcPTp09l9pOkjqqqKmxsbLBkyRIIhcJSxU5R1Lcn/PwZKceOA/gBW9lfXwX29wKyk4EazQHPM4C2SakWQQihXWOoHxbtHlMOw4YNw6hRozB37lw0a9YMAHD//n2sWLECw4cPBwBcv34ddevWVWaYPzU1NTWsXr0aY8eOhZ6ensz0zZs3s5L6atWqITAwEF26dAGQPxqQhLe3N5YsWcKaX0NDo8yxnTlzBv369UPnzp1x8OBBWFtb4+PHjzhy5Ajmz5+PkJAQAMDMmTOxdetWLFu2DL1790ZeXh4OHDiAXr16YfPmzZg0aRJruS9fvoSOjg4+fPiAX3/9Fd27d8erV6+gqqoqrdOlSxcEBgYiJycHf/75JyZOnAgVFRXMmTOnzNtDUVTl+7o/CCQ3F+oNG0Lj3++dCpOXDdz3A3LS8sdC53D/ffH++5sp9LdkOquc89//peWFlsVw2MuJuw0cHwOIcoFarsCgg4CqZqk3IfF1NJIT4sHj82HTrEXF7h+KUjKatJfDxo0bYWJigjVr1iAxMREAYGJigunTp2P27NkAgE6dOkkTQOrb69ChA169eoWVK1dizZo1MtMFAgEEAgGrTFdXF6ampjJ1NTQ05JaXRWZmJkaOHIlu3brhxIkT0nIrKys4OTkhOTkZAHDnzh2sX78eW7ZsweTJk6X1li9fjuzsbPj4+KBXr14wNzeXTjM2NpZuw7Rp09CzZ09ERkaiQYMG0jp8Pl+6LePHj8eJEydw+vRpmrRTVBUmSktD0qFDAACDMd4VfxX3zjbg8pKS61Umezegnz/A45dpdkkru03TFlBVU6/AwChK+WjSXg5cLhe+vr7w9fVFamoqAEBHR4dVp+YPcFc/IQTCXLFS1s1T5ZTri4nL5WLFihUYMmQIpkyZgho1alRgdGV3/vx5fP78GbNmzZI7XVdXFwAQHBwMLS0tjB07VqbOjBkzsGHDBhw7dgzTpk2TmZ6SkoLDhw8DAKuVXR51dXV8+fKldBtBUdQ3lRwSAnF6OlStraHl6lqxCxeLgPsB+f+v0w3QNMovIyJALPz3JSq6TCwsUF6gTFpecJoQIGL2MhgGaOIJdFkNcMuWmohFIkT+FQaAdo2hfkw0aa8ghZP1H4kwV4zdU68rZd1jNreFCp9bcsVi9OnTB46Ojli4cCH8/f3LvJzt27fDz8+PVbZr1y4MHTq01MuKjo4GANjZ2RVbLyoqCtbW1nKTbjMzM+jo6Mj0f5f8MMnIyAAA9OzZs8j1EEJw+fJlnD9/ntWST1FU1SLOycGXffsAAAajR4PhVPAtaVHngdR/AHU9oH8goKJWscsvCSH5iXs5xD2LQGZKMtS0dWDRQPHhISnqe0GT9nJITEzEzJkzcfnyZXz8+BGEENZ0yc2olPKtXr0a7dq1w8yZM8u8jKFDh8LX15dVZmJSupukJAofKxVVFwBu3LgBDQ0N3LlzBytWrMDOnTtl6pw5cwZaWlrIy8uDWCzGkCFDsGjRolKth6KobyflxEmIPn0Gr1o1CLp3q/gVPPi3QaPRsG+fsAPlTtiB/7rG1GnRGlweTW+oHw89qsvB09MTcXFxmD9/PqpVq/bDjhLDU+VgzOa2Slt3Rfjll1/QuXNnzJkzB56enmVahkAggI2NTYXEY2trCyB/pKGWLVsWW+/mzZvIzc2VaW3/8OEDUlNTpcuSsLKygq6uLurUqYOPHz/C3d0dYWFhrDqurq7YsWMHVFVVYWZmBh79gqOoKosIhfjy71VCg5GeYEro7lZqX98Ary4BYICmIyt22d9IXk42ou/dBkC7xlA/LvpNXQ43b97EjRs34OjoqOxQKhXDMOXuolIVrFq1Co6OjqhTp46yQ0GnTp1gaGiINWvWsG5ElUhOToauri4GDRqELVu2YNeuXTLdV9atWwcVFRX069evyPVMnDgRK1euxIkTJ9CnTx9puaamZoX9AKEoqnKlXbiAvHfvwBUIoNu/f8Wv4MG/fdlt2uc/zOg79ObRfeRlZ0HHyARmdX6wsesp6l80aS8Hc3PzUnddoJSnfv36GDp0KLZs2VKm+TMzM5GQkMAq4/P5rKEkY2JiEB4ezqpTu3ZtaGqyhy7T1NSEn58fBgwYgJ49e2LKlCmwsbHB58+fERoairi4OBw+fBgtW7bE1KlT8euvvyI3N5c15OPmzZuxadMm1sgxhWloaMDb2xsLFy5E7969f9irQRT1oyKE4POe/Htp9IYNA6ccw8zKlZcFPD6Q//9mZR/3XSQUIu5pONS0tCEwMYW6ts43Pd/8NzZ7W3qeo35YNGkvh02bNuG3337Drl27pA/hoaq2JUuWSMc/L609e/Zgz549rLLOnTvj3Llz0r99fHxk5rtx4wZat24tU96rVy/89ddfWLlyJYYMGYLU1FSYm5ujXbt2WLZsmbTepk2b0KBBA2zfvh3z5s0Dl8tF48aNcfLkSbi5uZUY96RJk7BhwwYcOXIEAwcOLM0mUxSlZBk3byHnxQswGhrQGzqk4lfw90kgKwkQmAO1O5VpEWKxCH9uWYuou7ekZSp8NQhMTCEwNoWuiQkExqbSvwVGJuBVYBefrPQ0xDx+CIB2jaF+bAyhTcVlpqenh8zMTAiFQmhoaEBFRYU1/evXr0qKrGSpqakQCARISUmRGfkmOzsbMTExsLKygpqaEm5Ioijqu0TPHRXv7fARyLx3D/ojRsBkzm8Vv4I97YH3D4B284FfSn+jPiEEV/fuxuNzv4PD5UFTVw9pXz/njwZTDC19g38TelNWQq9rYgoNgW6pWssjLp7FJb9tMLKsheGry3Yl9XtS3Pc39WOjLe3lsGnTJmWHQFEURf2gssLDkXnvHqCiAv2RnhW/gg/h+Qk7RwVoPLxMi3jw+3E8Pvc7AKDrJB/YOf8CYV4eUj99RMrHBKQkJiD5339TPiYgOTEBedlZSP/6Belfv+B95N8yy+Sp8iEwNsl/mZhCt2ArvbEJVPjsH4TSrjGtlDNgAkV9KzRpL4cRI0YoOwSKoijqB/X53+dCCNzcoFJBT2NmkQzz6NAL0DIu9ezPb1xF2MFAAIDL8NGwc/4FAMBTUYG+WXXom1WXmYcQgqy0VGlCn/IxEcn/JvQpHxOQ9vkzhLk5+PJPHL78Eyd3vZq6etLWeW19g/zEn2FgR5N26gdHk/ZSSk1NlV6OkjwFtSj0shVFURRVFjmvXyP90mWAYWAwyqviV5CVDDw5kv//MtyAGvvkMc7v2AQAaNKjD5p0763QfAzDQENHAA0dAarZyI7kJRLmIe3zZ1brfMHW+pzMDGQkJyEjOQkfol5I5zO3rwdtA8NSbwdFfU9o0l5Kenp6iI+Ph7GxMXR15fe7I4SAYRj6cCWKoiiqTL745beCa3doD761dcWvICIYEGYBxg5AzRalmjUx5jVOr18BsUiEOs6/oO3QihvbnctTga5pNeiaVpM7PTs9XdrNRpLQZ6WlonmvShgKk6KqGJq0l9KVK1egr68PALh69aqSo6EoiqJ+NHnx8Uj5Pb+fuMHosg/DWCRCgPv/do1pNqpUTyNN+ZiIE6sWIS87C+Z1G6DLhOlgOBXzEDxFqGlpQU3LBia16HMmqJ8PTdpLqW3b//rMWVlZwdzcXKa1nRCCd+/efevQKIqiqB/A1717AaEQGk5OUG/YsOJXEBMGfIkGVLWABu4Kz5aVlopjKxciIzkJhjUt0WumL3iFRk2jKKryfLufxz8gKysrfPr0Sab869evsLKyUkJEFEVR1PdMmJSEpND8vuYG3t6Vs5L7+Te4ouEggK+t0Cx5uTk4sWYJkj78A20DI/Sdswh8Dc2SZ6QoqsLQpL0cJH3XC0tPT6djFFMURVGllnTwEEhWFvgO9tBs5VzxK0j9AET+kf//pqMUmkUsFuGPzWsRHxUJvqYm+s1dDG19etMnRX1rtHtMGUieeskwDObPnw+NAo+VFolEuHv3LhwdHZUUHUVRFPU9EmdmIikoCABg6O1dqgcMKezRfoCIgJrOgIlDidUJIbgSsBOvH9wBV0UFvX+dD4MaNSs+LiXIE+fh2ednqG9YHzwOTYeoqo8epWXw+PFjAPkns6dPn0K1wOOYVVVV0bBhQ8ycWfony1EURVE/r+QjRyBKSYFKzZrQ7tSp4lcgygMe7s3/fzPFWtnvnghFxMWzAMOg2+SZqGFfr+LjUpLwj+HwOu8FSx1LnO59unJ+JFFUBaLdY8rg6tWruHr1KkaMGIGzZ89K/7569SrOnz+PXbt2oXbt2soO86fj6ekJhmEwbtw4mWkTJ04EwzDw9PRk1e/duzerXkJCAiZPnoxatWqBz+fD3Nwcbm5uuHz5srSOpaUlGIbJH29YQwP169eH378PQSlIJBJh48aNqF+/PtTU1KCnp4euXbvi1q1bMvVWrVoFOzs7qKurQ19fH05OTjLLfPfuHby8vGBmZgZVVVVYWFhg6tSp+PLlC6uei4uLNL6CLz8/P7nlBV+xsbFF7t9jx47BxcUFAoEAWlpaaNCgAZYsWYKvX79K62RlZWHhwoWwtbUFn8+HoaEhBgwYgL//Zj/1cNGiRdJ1crlcmJubY8yYMaxlFd7XmpqaaNy4MY4cOVJkjBT1vSK5ufgSuBcAYDBqFBgut+JX8vJPIC0e0DQC7HuWWP3ZtUu4FZLf8t/OcwxsnVpVfExKdOP9DQBAPcN6NGGnvgs0aS+HwMBA+gClKsbc3ByHDx9GVlaWtCw7OxuHDh1CzZrFX9KNjY1FkyZNcOXKFaxduxZPnz7FuXPn4OrqiokTJ7LqLlmyBPHx8Xj27Bk8PDzg7e2Ns2fPSqcTQjBo0CAsWbIEU6dOxYsXL3Dt2jWYm5vDxcUFJ0+elNZdvHgxNm7ciKVLl+L58+e4evUqxowZg+TkZGmdN2/eoGnTpoiOjkZwcDBevXqFnTt34vLly2jZsqVMsuvt7Y34+HjWa+jQoay/W7ZsKVPP3Nxc7r7x9fWFu7s7mjVrhrNnz+LZs2dYv349IiIiEPTv5fycnBx06NABAQEBWLZsGaKiovDnn39CKBTCyckJd+7cYS2zbt26iI+PR1xcHAIDA3Hu3DmMHz9eZt2Sff348WM0a9YM7u7u+Ouvv4p9Lynqe5Ny5g8IExLANTKEoHevylmJ5AbUxsMBnmqxVWMeP8CFXVsAAM179UejLm6VE5MS3fgnP2lvU72NkiOhKMXQ7jHl9ODBA4SGhiIuLg65ubmsacePH1dSVBWLEAJhTo5S1s3j80vVAtK4cWO8fv0ax48fx9ChQwHkvw81a9YscUSfCRMmgGEY3Lt3D5qa/42KULduXXh5sZ9IqK2tDdN/Hys+e/ZsrFmzBhcvXkTXrl0BAKGhoTh69ChOnz4NN7f/vux2796NL1++YPTo0ejYsSM0NTVx+vRpTJgwAQMGDJDWa1homLeJEydCVVUVFy5cgLq6OgCgZs2aaNSoEaytreHr64sdO3ZI62toaEjjK0gyL5DflauoegXdu3cPK1aswKZNmzB16lRpuaWlJTp27Cj9cbFp0ybcvn0bjx8/lsZvYWGBY8eOwcnJCaNGjcKzZ8+k7yePx5Ouu3r16hgwYAACAwNl1i/Z16ampti2bRsOHDiA33//Hc7OlXCTHkUpARGL8eXfK2sGI0aAw+dX/Eo+ReUP9chwgCaexVZNeB2N3zeuAhGL4dDGFa0Hj6j4eJQsISMBr5JfgcNw4GxGzyXU94Em7eVw+PBhDB8+HJ07d8aFCxfQqVMnREVFITExEX369FF2eBVGmJODLSOU87S5KfuOQqWUI/F4eXkhMDBQmrQHBARg5MiRuHbtWpHzfP36FefOncPy5ctZCbuErq6u3PnEYjFOnDiBpKQk1r0Nhw4dgq2tLSthl5gxYwaOHz+Oixcvonfv3jA1NcWVK1cwYcIEGBkZyY3t/PnzWL58OSvpBgBTU1MMHToUISEh2L59e6Vc4j148CC0tLQwYcIEudMl++bQoUPo2LGjzA8ODoeD6dOnY+jQoYiIiJB7k3ZsbCzOnz/P2ofy8Hg8qKioyPxApqjvWfqVK8h98wYcbW3oDhpUOSt5EJD/r20XQLfoq47JCfE4sXox8nKyYdGgETqNm/JDdh2RdI2pb1gfumq6yg2GohREu8eUw4oVK7Bx40b8/vvvUFVVxebNmxEZGYmBAweW2BWDqjweHh64efMm3r59i7dv3+LWrVvw8PAodp5Xr16BEAI7OzuF1jF79mxoaWmBz+ejf//+0NPTw+gCTy6MioqCvb293Hkl5VFRUQCADRs24NOnTzA1NUWDBg0wbtw4Vleb6OhoEEKKXV5SUhLrmQHbt2+HlpaW9DVjxgyFtkue6Oho1KpVCyolPESlNNsMAE+fPoWWlhbU1dVhZWWFv//+G7Nnzy5y+bm5uVi5ciVSUlLQrl27MmwJRVU9hBB83r0HAKA3ZAi4WloVv5LcDCD8UP7/i7kBNTM1BcdWLkBmSjKMLa3R02cOuLwf8+FJN/+5CQBoXb21kiOhKMXRlvZyeP36Nbp37w4gv6tBRkYGGIbB9OnT0a5dOyxevFjJEVYMHp+PKfuOKm3dpWVkZITu3btj7969IISge/fuMDQsfkxhQkip1vHrr7/C09MT8fHx+PXXXzFhwgTY2LAfq63oMh0cHPDs2TM8fPgQt27dQlhYGNzc3ODp6cm6GbU0MQ4dOhS+vr7Sv4u6UqCI0qy3NHXr1KmD06dPIzs7GwcOHEB4eDgmT54sU2/27NmYN28esrOzoaWlhVWrVkk/dxT1vcu8dx/ZT56A4fOhP6z4xoUye3oUyEkB9KyAWvJ/8OZlZ+PE6sVIToiHjpEJ+s5ZBFV1Dbl1v3d5ojzcic+/x6ZNDdqfnfp+0KS9HPT09JCWlgYgv0/us2fPUL9+fSQnJyMzM1PJ0VUchmFK3UVF2by8vDBp0iQAwLZt20qsX7t2bTAMg8jISIWWb2hoCBsbG9jY2ODIkSOoX78+mjZtCgeH/HGPbW1t8eLFC7nzSsptbW2lZRwOB82aNUOzZs0wbdo0HDhwAMOGDYOvry9sbGzAMAxevHght9vVixcvoKenx+paIxAIZH5ElJWtrS1u3ryJvLy8YlvbS7vNqqqq0hglifjixYuxdOlS1rySH0haWlowMTH5IS/VUz+vL3vyW9l1+/UFr4TGhTIh5L8bUJt6ARzZC+xikQhnNq9GwqsoqGlpo9/cxdDU1av4WKqIRx8fIVOYCX01fdjry786SFFVEe0eUw6//PILLl68CAAYMGAApk6dCm9vbwwePBjt27dXcnQ/ty5duiA3Nxd5eXno3LlzifX19fXRuXNnbNu2DRkZGTLTC47kUpi5uTnc3d0xZ84cadmgQYMQHR2N33//Xab++vXrYWBggI4dOxa5TEnyn5GRIa27fft21qg4QP4QlQcPHoS7u3ulJbNDhgxBeno6tm/fLne6ZN8MGjQIly5dQkREBGu6WCzGxo0b4eDgINPfvaB58+Zh3bp1+PDhA6tc8gPJ1NSUJuzUDyX7+XNk3LwJcLnQL3Sze4V5/xBIeAJw+UAj2ZZ8Qggu7tmGN4/ug6eiij6zF0DfrEblxFJF3Hz/X9cYDkPTIOr7QY/Wcti6dSsG/XvTkK+vL3x8fJCYmIh+/frB399fydH93LhcLl68eIHnz5+Dq+B4x9u2bYNIJELz5s1x7NgxREdH48WLF9iyZQtatmxZ7LxTp07F77//jgcPHgDIT2D79OmDESNGwN/fH7GxsXjy5AnGjh2L06dPw8/PT3rDa//+/bFx40bcvXsXb9++xbVr1zBx4kTY2tpK+9hv3boVOTk56Ny5M8LCwvDu3TucO3cOHTt2RPXq1bF8+fJy7K3iOTk5YdasWZgxYwZmzZqF27dv4+3bt7h8+TIGDBiAffv2AQCmT5+O5s2bw83NDUeOHEFcXBzu37+Pfv364cWLF/D39y826W7ZsiUaNGiAFStWVNq2UFRVIhkxRqdrV6jWqKREWdLKXq8foKEvM/n20UN4dvUCGIaD7lNnwcz2x295pkM9Ut8rmrSXkVAoxJkzZ6QJIYfDwW+//YbTp09j/fr10NMr3aXFbdu2wdLSEmpqanBycsK9e/eKrb9p0ybUqVMH6urqMDc3x/Tp05GdnV3m7fkR6ejolGoc/Vq1auHRo0dwdXXFjBkzUK9ePXTs2BGXL19mDacoj4ODAzp16oQFCxYAyO9SFBoairlz52Ljxo2oU6cO2rRpI03KCz7UqXPnzvj999/h5uYGW1tbjBgxAnZ2drhw4QJ4vPwebLVr18aDBw9Qq1YtDBw4ENbW1hgzZgxcXV1x+/Zt6OvLfhlXpNWrV+PQoUO4e/cuOnfujLp168LHxwcNGjTAiBH5w8GpqanhypUrGD58OObOnQsbGxt06dIFXC4Xd+7cQYsWLUpcz/Tp0+Hn54d3795V6vYoSpyZieznL5CXkKDsUKgfTO7bt0g9dx4AYOA9uoTaZZT5FXj279DDcm5AfXL5HG4fDQYAtB81HjbNSv6Mfu8+pH/A65TX4DActDQrvjGGoqoahpT2DjxKSkNDAy9evICFhUW5lhMSEoLhw4dj586dcHJywqZNm3DkyBG8fPkSxsbGMvUPHToELy8vBAQEwNnZGVFRUfD09MSgQYOwYcMGhdaZmpoKgUCAlJQUmcQ2OzsbMTExsLKygtp31pedoioKIQS5MTEQ/3t/imrNmuDSh6kVi547FBe/cBGSQ0Kg2fYX1Ny1q3JWcmsLcHE+UK0hMOY6UOBK1+uH93Bq7TIQIkaLfoPQamAl3QRbxYS+DMXSO0vRyLgR9nfdr+xwyqS472/qx0Zb2suhefPmCA8PL/dyNmzYAG9vb4wcORIODg7YuXMnNDQ0EBAQILf+X3/9hVatWmHIkCGwtLREp06dMHjw4BJb5ymKUpw4I0OasANA3vv3EOflKTEi6keReuECko/mj8hl6O1dOSsRi4EH/3bTbDaalbDHR7/EmU2rQYgYdV06wHnA0MqJoQqSjM9Oh3qkvkd09JhymDBhAnx8fPDu3Ts0adJE5qE8DRo0KHEZubm5ePjwIesmRg6Hgw4dOuD27dty53F2dsaBAwdw7949NG/eHG/evMGff/6JYcOGFbmenJwc5BR4qmlqamqJsVHUz4oQAuHHjwAArp4eSFYWxNnZyHv/HqoWFvSGWKrM0q5cxXufGYBIBEGfPlBv0qRyVvT6CpAUC/AFQL3/Ho739cN7nFi9GMLcHFg5NkFH70k/zfGcK8rF3fi7AGh/dur7RJP2cpDchDplyhRpGcMwIISAYRiIRKISl/H582eIRCKYmJiwyk1MTIocfnDIkCH4/PkzWrdunZ9cCIUYN24c5s6dW+R6Vq5c+cOMG09RlU2cnp7fys4w4BkbAyIRcl6/hjg9HaIvXypnaD7qh5d+4wbeT50KCIXQ6dYN1ZYtrbyEWdLK7jgEUM0fbz0jOQnHVy5AVloqTGrVRo/pv4HL+3nSgIeJD5ElzIKhuiHs9BV7kB5FVSU/z6e1EsTExChlvdeuXcOKFSuwfft2ODk54dWrV5g6dSqWLl2K+fPny51nzpw58PHxkf6dmpoKc3PzbxUyRX03Cray8/T1wVFRAVRUoGJqirz4eOQlJoKjpQUO7bNNlULG7dv4Z9JkkLw8aHfqBLPVq8AoOLJVqSXHAVHn8v//7w2ouVmZOL5qEVI+JkLXpBr6/rYQqmrqlbP+KqrgUI8/y9UF6sdCk/ZyKO8NqED+GNRcLheJiYms8sTERJiamsqdZ/78+Rg2bBhGj84fcaB+/frIyMjAmDFj4OvrC46ch2fw+Xzwy/B0UYr62YjT0yHOygI4HFaLOldfP7+lPS0Nee/eQdXaGoyczxpFFZZ5/z7ejZ8AkpMDLVdXVF+3FkwxDyort4d7ASIGrNoChrUhEgpxesNKfIx5DXUdAfrOXQwNgW7lrb+Kov3Zqe8d/cYpp6CgILRq1QpmZmZ4+/YtgPzhGE+dOqXQ/KqqqmjSpAkuX74sLROLxbh8+XKRY4NnZmbKJOaSoSfpYEAUVXaEEAj//QHN09dnJVYMw0ClenUwPB7EOTnSehRVnMzHj/Fu7DiQ7GxotmmD6ps3gVFVrbwVCnOBR/+OitJsNAghuLBrC94+eQwen48+sxdAz9Ss8tZfRf2T9g9iUmLAZbh0qEfqu0WT9nLYsWMHfHx80K1bNyQnJ0v7sOvq6mLTpk0KL8fHxwd79uzBvn378OLFC4wfPx4ZGRkYOXIkAGD48OGsG1Xd3NywY8cOHD58GDExMbh48SLmz58PNzc3hR8kRFGULHFaGsTZ2WAKtbJLMDweVKpXBwAIv3yBKC3tW4dIfUeynj7FO+8xEGdmQqNlC9T43xZwKjNhB4AXp4GMT4B2NaBON9wKCcLzsCtgOBy4Tf8N1WzqVO76qyhJ15iGRg2ho0qHSaS+T7R7TDn873//w549e9C7d2+sWrVKWt60aVPMnDlT4eW4u7vj06dPWLBgARISEuDo6Ihz585Jb06Ni4tjtazPmzcPDMNg3rx5eP/+PYyMjODm5lapT8WkqB8da8QYAwMwRdygx9XWBs/AAMIvX5D3/j04NjZF1qV+XtnPnyNu1GiI09Oh0bQpzLdv/zb3Qdz/9wbUJp4Iv3Qed0+EAgA6jpmEWo2aVf76qyhJ0t6mBh01hvp+0W+acoiJiUGjRo1kyvl8PjIyMkq1rEmTJmHSpElyp127do31N4/Hw8KFC7Fw4cJSrYOiqKKJU1P/a2U3MCi2Ls/EJL/ve04O8t6/h0rNmvTGtnIghCD199/BMzSEprOzssMpt+yXUYjzGgVxairUHR1RY+dOcNS/wU2fiX8DcX8BDBfRqI/LgTsBAM4Dh6K+a6f/s3ff4VFUXwPHv1uzm957I6RACL2DUhQBFeyACoJg+6mIiAVRrK8NC4IVC0UFFMFKkSIiSgm9hZIQSCG997Jt3j82CUQChLTdJPfzPHlIZmdmz4bsztm7557b/PdvpSqNlaLVo9AmiPKYRujQoUOdiytt3LiRzp07t3xAgiA0SH1H2avJ5HJUAQEgk2EsLsaYl9cSYbZZeUuWkvbcbJKnPUD6a69hqqiwdEgNVnn2LMnTpmEsKEDTtSsBX32Jwt7uygc2hapR9lSPUWz4ejFIEt2uH82AO+5umfu3UgcyDlBhrMBT60m4S7ilwxGEBhNJeyPMmjWLxx9/nFWrViFJEnv37uXNN99kzpw5PPfcc5YOr925//77kclktUqVAH799deaUdCffvoJhUJBampqnecICwuraY05bNgwZs6cWXPbsGHDkMlkyGQybGxs8PPzY+zYsfz8888XnUcmk/Hrr79etP2RRx5BoVCwevXqej+un376iWHDhuHk5IS9vT3dunXj9ddfJ++CRLG8vJxXXnmF8PBwbGxscHd3Z9y4cRw/frzWuV599dWax6BQKAgICODhhx+udS6A4ODgmv3s7Ozo1avXVcXc2hgLCzFVViKTK+rdg12u0aDyMnd40mdktOpE05JKd+8m64MPan4u+P4HEsdPoPLMGQtG1TC6xESSp9yPMTcXm86dCfz6KxQODi1z55XFcHQVeZVaft2rx6DXEdK7H9c/8Gi7/xSopmuMv2j1KLRuImlvhAcffJB58+Yxd+5cysrKuPfee/n8889ZuHBhzcJLQsvSaDTMmzeP/Pz8Om+/5ZZbcHNz45tvvrnotn/++Yf4+HgeeOCBS57/oYceIj09nTNnzvDTTz8RGRnJ3XffzcMPP3zF2MrKyvjhhx947rnnWLJkSb0ez4svvsiECRPo27cvf/zxBzExMXzwwQccOXKE7777DjCvdjtixAiWLFnCG2+8QVxcHBs2bMBgMNC/f3+io6NrnbNLly6kp6eTnJzM0qVL2bhxI48++uhF9/3666+Tnp7OoUOH6Nu3LxMmTGDXrl31irs1MY+yZwOgcHe7qt7ZCjdX5Pb2IEnoU1KQTKbmCrNN0qemkvrULDCZcLr9dgK+/hqFmxuVcXEk3HkXBT/91Go6YulSUki6fyqG7GxswsMJXLIYhZNTywVwdBXGylLWZ3anorwCn9AIxjz5HHLRnKBWf3ZBaM1ETXsjTZw4kYkTJ1JWVkZJSQmenp6WDqnJSZKEpLdMMiJTya9qZGTEiBHEx8fz9ttv8+677150u0ql4r777mPZsmUXrSC7ZMkS+vfvT5cuXS55fltb25r++f7+/gwYMIBOnToxbdo0xo8fz4gRIy557OrVq4mMjOT555/H19eXc+fOXXaBq7179/LWW2+xYMECnnzyyZrtwcHB3HDDDRQUFADmFqO7d+/m0KFDdO/eHTCvIfDTTz/Rv39/HnjgAWJiYmp+j0qlsuYx+Pn5MW7cOJYuXXrR/Ts4OODt7Y23tzeffvopy5cvZ+3atQxqAzXHFzIWFiLpKpEpFFesZf8vmUyG2s+Pyvh4TBUVGDKzUPnUvb6CUJupooKUGU+ay0giI/F+5WXkGg0hv/5C2uzZlO7aTfqLcyndtRvv115FYW9v6ZAvSZ+WRvLkKRgyMlCHhBC4dAlKF5eWC0CSYN9ionMCySpVobF34JZnXkRlIxYAO1d0jsSiRJQyJQN8Blg6HEFoFJG0N4GsrCxiY2MB80Xcw8PDwhE1LUlvIu1ly4yw+r4+CJn6KkY+FQreeust7r33XmbMmIG/v/9F+zzwwAPMnz+ff/75hyFDhgBQUlLCmjVr+PDDD686xilTpvD000/z888/XzZpX7x4MZMmTcLJyYkbb7yRZcuWXXIFW4AVK1Zgb2/PY489Vuftzs7OAKxcuZIbbrihJmGvJpfLeeqpp5g4cSJHjhyhR48eF50jMTGRTZs2ob5CGzqlUolKpUKn0112v9amVi27u3uDVqiUqVSo/PzQJSdjyM1B7mBv1QmmNZAkiYzXXqfi+HEULi7mVohVnVWUHh4EfP01uV8vJnvhQorWr6f86FH85n+AtmtXC0d+MX1mJkn3T0WfloYqKJDApUuv+s1foyVHk5GUzJ6cHgCMePAx7F1cWzYGK1VdGtPDswcO6hYqVRKEZiLKYxqhuLiY++67D19fX4YOHcrQoUPx9fVl0qRJFBYWWjq8duv222+nR48el+yuExkZyYABA2qVqPz4449IktSgsia5XE54eDiJiYmX3Of06dNER0czYcIEACZNmsTSpUsv+9H/6dOnCQkJQXWFlRPj4uIuOfG5entcXFzNtmPHjmFvb49Wq6VDhw4cP36c2bNnX/L8Op2Ot99+m8LCQq677rrLxtLaGAsKkHQ68yi7a8OTHIWjI4qq4/UpKUgGQ1OF2Cblf/89hb/8AnI5fvM/qOl9X00ml+P+8EMELf8Ola8v+nPnSLznXnKXLLWqEiRDdjbJ909Fn5yMyt+foGXLUHm1/Ket+uiv+CMtAgkZEQOvJWKg6JBSTbR6FNoSMdLeCA8++CCHDh1i/fr1NauX7t69myeffJJHHnmEH374wcIRNg2ZSo7v65YpiZCpGva+ct68eVx33XWX7Jc/bdo0nnrqKT7++GMcHBxYsmQJ48aNw6GBk8YkSbpsGc+SJUsYNWoU7lWTHG+66SYeeOAB/vrrL66//vpLnvNq7r++IiIi+P3336moqGD58uUcPnyYJ5544qL9Zs+ezdy5c6moqMDe3p533nmHm2++ud73Y+0kk6mmll3p7tGgUfYLqby9MZWWIlVWok9NQxUYICa91aHs4CEy33obAM+nZ2F3iZWfAWx79qTDr7+QPvclijdvJuvddymN3o3vO+806k1WUzDk5ZE0dSq6hASUvj4ELluGysen5QMpyWLn9iPk6Xywc7Dn+gcunp/SXlUYKtibsRcQ9exC2yBG2hth3bp1NcmYo6Mjjo6OjBo1iq+++oq1a9daOrwmI5PJkKsVFvlqaNIzZMgQRo0aVWsl2QtVj6j/+OOPnD59mp07d152AurlGI1GTp8+TYcOHS55+zfffMP69etRKpUolUpsbW3Jy8u77ITU8PBwzp49i16vv+z9h4eHc/LkyTpvq94eHn6+zZlarSY0NJSoqCjeeecdFAoFr7322kXHPvvssxw+fJiUlBTy8/MvOxrfGhkLCpD0OmRKJQq3xieAMrkctb9/VRvIIoyXmAzdnumzskh5cgYYDDjcOBrXadOueIzC0RG/hQvwfvUVZGo1pf/8S8Ktt1H6nwnWLclYUEDytAfQxZ9B6elJ0LJlqP39rnxgMzi39iMO5JrnUYx87Gm0DmK1z2r7M/dTaazEy9aLMOcwS4cjCI0mkvZGcHNzw6mO7gBOTk64tOQkJKFO77zzDmvXrmX37t0X3ebg4MC4ceNYsmQJS5cuJTw8nGuvbdjHp9988w35+fnceeeddd6+YcMGiouLOXToEIcPH675+v777/n5559rJpT+17333ktJSQmfffZZnbdXH3f33Xfz559/cuTIkVq3m0wmPvzwQyIjIy+qd7/Q3Llzef/990lLS6u13d3dndDQULy9vdvciLFkMmHIrh5ld0cmb5qXQrlWi6pqJWN9RgamysomOW9bIOl0pD45E2N2DjZhYfi+8Ua9/65kMhkud99N8OrVqDt2NJelTJ1G1sKFLV6KZCwqIvmBB6k8dQqFuzuBy5ahDgxs0Riq6UqL2fjHPkBGVPeOhPRqvyue1uXCrjFt7TVMaJ9E0t4Ic+fOZdasWWRkZNRsy8jI4Nlnn73sBEOhZXTt2pWJEyfy0Ucf1Xn7Aw88wK5du1i0aBHT6jHiB+a2jRkZGaSkpBAdHc3s2bP53//+x6OPPsrw4cPrPGbx4sXcfPPNdO/enaioqJqv8ePH4+zszIoVK+o8rn///jz33HM8/fTTPPfcc+zevZukpCS2bt3KuHHjatpWPvXUU/Tr14+xY8eyevVqkpOT2bdvH3feeScnT55k8eLFl71gDRw4kG7duvHWW2/V63fQFhjz85H0evMoexOXWSjc3JDb2YHJhP6caANZLfOdeZQfOoTcwcE88dTu6hcc0kSE02H1jziPuwskidzPF5E0eQr6/7zhbC7GkhKSH3qoZgJt0NIl2ITU/QlbS9j+2f9RVKnCUa1j2ONihez/+jfFPAlVrIIqtBUiaW+Ezz//nOjoaAIDAwkNDSU0NJTAwEB27drFF198Qa9evWq+BMt4/fXXMV0iabrmmmuIiIigqKiIyZMn1+t8X331FT4+PnTs2JE77riDEydOsGrVqkuOhmdmZrJ+/fo6R+Hlcjm33347ixcvvuT9zZs3j5UrV7Jnzx5GjRpFly5dmDVrFt26dWPKlCmAuTf9X3/9xeTJk3nhhRcIDQ1l9OjRKBQKoqOjGTDgym3OnnrqKb7++mvOnTtXr99Da2YeZc8BzJ1KmmqUvZpMJkPl749MocBUUV7TnaY9K/jlV/JXrgTA9915qIODG3wuua0tPv/3f/h+8D5yOzvKDx7k7O13ULRlSxNFWzdTaSnnHvkfFUeOIndyInDpEmzCLFdykXBoP0f3nwBg9IhIbJxEt5gLJRUlkVycjFKupL9Pf0uHIwhNQia1lpUrrFBddcCXcqlOJpZSVFSEk5MThYWFODrWroGsqKggISGBDh06oNGIPr9C22LIzUWfno5MpcImLKzJk/ZqxqIidMnJAKiDg9tFG8i6XjvKY46TdO+9SDod7tOn4zH98Sa7P11yMqlPP0PFsWMAuNx7L56zn0NuY9Nk9wFgKi/n3P8epWzPHuQODgQuXYo26tLrOTS38pJivpn1CKWFRfRySWX4Wz+Ba4jF4rFGK06u4J2979DPux+LR116YKQ1utz1W2jbRPeYRrC2RFwQhMurVcveDKPsF1I4OqJwccGYn48+JRV5aEdkyvb1kmvIyyNlxhNIOh32w4fj/ljTdjZRBwYSvGI5WQsWkrdkCfkrV1J28CB+8z/AJqRpklhTZSUp058wJ+x2dgR+/ZVFE3aAv5YsorSwCBd1GdcM7CAS9jpU92cXpTFCW9K+riBNrLy8nC1bttT0wI6IiGDEiBFotVoLRyYIQl2MeXlIBgMylQpF1eJUzammDaROZ158J6D9tIGUDAZSn34aQ1o66qAgfN+d1yxvkmRqNV7PPYvdgP6kzX6eylOnSLjzLrxfegmn229r1O9b0ulInfEkpTt3IrO1JeDLL9BeZlJ3S4iL3sGpnduRIXGjbxyqAV9ZNB5rVG4oZ1/6PkC0ehTaFpG0N9Dvv//Ogw8+SE5OTq3t7u7uLF68mLFjx1ooMkEQ6iIZjRhymq+WvS4yhQK1fwCVCWcxFhUhLyho2eXtLSjrww8p2x2NzNYW/08+RtHANRDqy37IEDr8+itps2dTFh1N+gsvULp7N96vvILC/uonvUp6PSmzZlGyfTsyGxsCPvsM2969myHy+istyGfL1+b5M/3czuHj7QxhIy0akzXal7EPnUmHj50PHZ07WjocQWgyYiJqA+zatYu77rqLIUOGsHPnTvLy8sjLy2PHjh1ce+213HXXXURbsIewIAgXqxllV6tbZJS9mtxWi9LTvEqmIT29XbSBLPnnH/IWm9cg8H3rzRabsKny8iRw8dd4zHwSFAqK1q4l4c47KI85flXnkQwGUp97jpI/tyJTq/H/9FPsBlh2MqMkSWz56hMqiovwsDcx0CMZ+kwFeeMWBWuLqrvGiFaPQlsjkvYGeOONN5g6dSpr1qxh4MCBODs74+zszKBBg/jpp5+4//77ef311y0dZqOJOcpCW2GJUfYLKd3dkdvaIZlM6FPabhtISZKQ9HqyPvwQALcHH8Bx9OgWjUGmUOD+v/8R9N23KH180Cclk3jPPeR98029XtMko5G0F16g+I+NoFLh//FH2F8zuAUiv7zj27dyZv8e5AoFN3ocQqFQQs/6db1qTyRJqunPLurZhbZGJO0NEB0dzfTp0y95++OPP17ngj6thUqlAsw9yQWhLTDk5iEZjS0+yl7N3AbSz9wGsry8ZjJsW1NaUoI+MxPSM7AdOACPmTMtFottr16E/PIz9iOuB72ezLffIeXRxzBcZqVayWQi/aWXKfp9LSiV+H84H/uhQ1sw6roV5WSxbdmXAAzq6oiHpgwibwV7DwtHZn2SipJIKUlBJVeJVo9CmyNq2hugvLz8sm2WnJycqKioaMGImpZCocDZ2Zmsqv7Stra24iNGodWSjEZ02VlIJhNKZ2ewYHmK0d0dfXo6lZmZqFQqFLa2FoulKUmSRGlpKRnx8cj+3Ira1RW/+fMt3i1H4eyM/8cfk79yJVnz3qXk779JuPU2fN97D7v+/WrtK0kSGa+/TuHPP4Ncjt/77+EwYoSFIr8gLpOJTZ8vRFdehk/HUPoafjDf0PdBywZmpaq7xvT26o2tqm08vwShmkjaGyAsLIy//vqLqVOn1nn71q1bCbPgohtNwdvbG6AmcReE1spYXIypuBiUSlRKJeTmWjaekhJM5eWQl2eRUp3mYiwowPTrb6g2bcJvxXKrmXArk8lwnTgR2969SX1qFrqEBJLvvx/3Rx/F/bFHkSmVSJJE5ltvU/DDKpDJ8J33TouX9VzK4c3rSY45glJtw43X+iPfUw6eXSDwyoumtUfVpTGia4zQFomkvQGmTp3KM888g5eXFzfddFOt29avX89zzz3HCy+8YKHomoZMJsPHxwdPT0/0er2lwxGEBjEWF5P87HPISkvxmvM89k3Uu7tRMXl6kfLEExjS07EbNgyv2c9ZOqRGK92zh8wnZ6KoqMDn7bfRdrFsH/O6aDp1osNPa8h4400Kf/6ZnM8+o3TvHvzee4+875aT/913APi88QZOVtL9Kz89lX9WLANgyL334xJftaBf32kgPv28SJm+jH0Z5laPop5daItE0t4ATz75JLt27WLMmDFERETQuXNnJEni5MmTnD59mttuu42ZFqzlbEoKhQKFQnQnEFqn7C+/hPh4tGFhuI0YYR2j2hoNgc89S+LESZR9/z2VvXpaTZLYELrERHKeeRZZRQUu996L8+23WTqkS5Lb2uL71pvYDRxAxiuvUr7/AGduvAmpqpzR+9VXcb7zDgtHaWYyGfnjsw8x6CoJjOpGjzAH2HMa1PbQbYKlw7NK+zL2oTfp8bP3o4NTB0uHIwhNzgquYK2PXC5n9erVfP/990RERHDq1CliY2Pp1KkTK1as4KeffkJuDcmBILRjhvx88r75FgD3J6ZbR8JeRdujB+6PPwZAxmuvo0tJsXBEDWMqLeXc9OmYiovR9uqF1/OzLR1SvTiNHUuHn39CExlZk7B7vfgiLndbTzK8f+0vpMedQq3VMurRmcgOLDbf0P1usGnenvetVXU9u2j1KLRVYqS9ESZMmMCECdbzIi8Iwnl5S5ZiKi3FpnNnq5hQ+F/uDz9M6Y6dlB88SNpzswn69huLT9y8GpIkkfbCi+jiz6D08MBvwYfI1GpLh1Vv6uBggn74nvyVK1F5e1tNDTtAdnIiu35cDsDwKQ/jqDbCqfXmG/s8YMHIrJdo9Si0B63nCmGlTCYT8fHxZGVlYfpP7+UhQ4ZYKCpBaN8MubnkrVgBgIeVjbJXkymV+L77Lgm33Ub5wYPkfPklHo89Zumw6i1vyRKKN20ClQq/hQtRVS0g1ZrI1Wrc7r/f0mHUYjTo+eOTDzAaDIT07keXYSNg+zyQjBA4CLwiLR2iVUooTCC1JBWVXEVf776WDkcQmoVI2hshOjqae++9l6SkpIsW7ZDJZBiNRgtFJgjtW+7iJUhlZWiiorAfPtzS4VyS2t8P71deJu3Z58j59DPsBg7EtmdPS4d1RaW7dpH1wXwAvF+Yg20v64+5tYj+6QeykxLQODgy8uEnkJkMcGCZ+ca+YpT9UqpLY/p69xWtHoU2y/qGn1qR//3vf/Tp04eYmBjy8vLIz8+v+crLy7N0eILQLhmys8lfuRIAjxlPWH1tq9PYsTiOGQNGI2nPPoexpMTSIV2WLiWV1FlPg8mE0x134Hz33ZYOqc1Ij49lz6+rARjxwGPYObtA7AYoTgc7D+h8i4UjtF6i1aPQHoiR9kY4ffo0a9asITQ01NKhCIJQJffrr5EqKtB2747dta2jttX7lZcpP3gQfUoKmW+8ie87b1s6pDqZKipImfEExoICNFFReL/ystW/KWot9LpK/vj0QySTiU6DhxIxsCr53Fc1AbXXFFC2njkDLalMX8aBzAOAqGcX2jYx0t4I/fv3Jz4+3tJhCIJQRZ+ZRf735hUj3VvBKHs1hYMDvu+9C3I5hb/+StGGDZYO6SKSJJHxyqtUnjiJwsUF/48WIrexsXRYbcaO778lPy0FOxdXrpv2P/PG7DhI2A4yOfS+36LxWbM96XvQm/T42/sT5Bhk6XAEodmIkfZGeOKJJ3j66afJyMiga9euqFSqWrd369bNQpEJQvuU++WXSDod2t69sRs0yNLhXBXb3r1x/98j5Hz2Oemvvoa2Rw9Uvr6WDqtG/sqVFP72G8jl+H0436pia+3OHT/KwQ2/ATDykSfQ2le1dNy/xPxv+GhwDrBQdNZPtHoU2guRtDfCnXfeCcC0adNqtslkMiRJEhNRBaGF6dPTKfjxRwA8nmg9o+wXcn/0UUp27qTiyFHSZj9P4LKlyKxgcbOyAwfIfPsdADyfeQa7AQMsHFHboSsvY+PnCwDoev0oQnr2xSSZiE7+m55HVqIFMQH1Mmq1evQXpTFC2yaS9kZISEiwdAiCIFTJWfQFkl6Pbb9+2A3ob+lwGkSmUuH33nsk3HY7Zfv2kTrraeyHDkUT1QWbkBCL9HHXZ2aRMnMmGAw43nQjrlPvb/EY2rK/v/2aouwsHD28GHafOTn//tT3vLP3Ha5zVLFQ2wFCrrNwlNbrTMEZ0kvTUcvVotWj0OaJpL0RgoJE7ZwgWANdSioFP/0EmDvGtGbqwEC85s4l/YUXKN60ydwLHZBptWg6d0YT1QVtVBSaLl1QBwc360i8pNOROnMmxuwcbMLC8HnjjVb5CYa1OntwH8f+2gzA6MdmotbaIkkSP5z6HoC/7GzZ5zuavla4zoC1qB5l7+vTF61Sa+FoBKF5iaS9AX7//fd67XfLLaI9lyC0hJxFn4PBgN2gQdj26WPpcBrN+Y7bUXp5UrpzFxUxMVQcP46ptJTygwcpP3iQ/Kr95La2aCIj0URFoYmKQhvVBVVgYJMtJpXx9tuUHzqE3NER/08+Rm4r+l83lfLiIjZ/8REAvW66lYDIrgDsz9xPYlFSzX7vl8fzvWRCLhOJe13EKqhCeyKS9ga47bbbrriPqGkXhJahS06m8JdfAXB/Yrplg2lC9oMHYz94MACSyYQuMYmK4zFUxMRQHnOcihMnMJWVUbZ/P2X799ccJ3dwQNOlC9qoLjXJvMrP76pHyAt++pmC738AmQy/995FLT5ZbFJblyyitCAfV19/rrlncs321Ye/AGBEaRm77Rw4kR/LhoQNjAkZY6lQrVapvpQDWaLVo9B+iKS9AUwmk6VDEAShSs5nn4PRiN2Qa1vFaqINIZPLsQnpgE1IB5zGjgVAMhrRnT1L+bGqRP54DJUnT2EqLqYsOpqy6Oia4xVOTjUJfHV5jdLb+5KJfPmxGDJeew0A9+mPYz90aPM/yHYkdve/xO76B5lczujHn0KlNrfOzD+9iT8zokEm40FNEF263cnCo4v46OBHjAgcgUapsXDk1iU6PRqDyUCgQyCBjoGWDkcQmp1I2gVBaLUqExIorCpX83iiddeyXy2ZQoFNWBg2YWFwx+0ASHo9lfHxlMfEUBFz3FxaExeHsbCQ0p07Kd25s+Z4hZubOYHvcj6ZV3l6YsjLI2XGDCSdDvvrrsP90Uct9RDbpNKCfP5c/DkA/W8bh09ohPmGM9v4ff3/0LvY0xk1XSatpaNcwaozv5Jems6Kkyt4oKvoInOhf1PMrR5F1xihvRBJ+1Wqbz07iJp2QWhuOZ99DiYT9sOHo+3a1dLhWJxMpTJPVu3cGcaNA8Ck01EZG0fF8ZiaZL7y9GmMubmUbv+H0u3/1Byv9PREZmODIT0ddXAwvvPeabL6eMHcnnDzFx9RUVyEZ3BHBtx5t/mGuE1Iq+5jjbcrAHf1nQVqOzTAjJ4zeGHHC3x97GtuD7sdV42r5R6AFbmw1eM1ftdYOBpBaBkiab9K/61nr+7LfuHP1URNuyA0n8r4eIrWrQPAow3Vsjc1uVqNtmsU2q5RuFRtM1VUUHnqlLk2PiaGiuMxVJ45iyErCwCZrS3+n3yMwsHBcoG3Qcf//pOzB/ehUCoZ/fhTKJQqOPE7rJnGAbWcRLUKrVLLTaG31hxzc8jNfHfiO07mnWTRkUW80P8FCz4C63G64DSZZZnYKGzo49X6J58LQn2IIZSrZDKZar42b95Mjx49+OOPPygoKKCgoIANGzbQq1cvNm7caOlQBaFNy/70U5AkHG4YgSYy0tLhtCpyjQZtjx64TpqI7ztvE7J2LRH79hK0Yjlec+cS9M032ISGWjrMNqUoO4tt33wJwKDxk/AIDIajq2H1/WDSs8a/MwA3dbgJe7V9zXFymZxn+jwDwOrY1SQUivVB4HzXmH7e/UStv9BuiJH2Rpg5cyaLFi3immvOfzQ3atQobG1tefjhhzl58qQFoxOEtqsiNo7iP8xvjN2nt69a9uYit7PDtndvbHv3tnQobY5kMrFp0QJ05eX4hHeiz9jb4dBy+G06IFHQbTxbSg8CcFf4XRcd38+nH8P8h/F3yt98eOBDPrruoxZ+BNanup5dlMYI7YkYaW+EM2fO4OzsfNF2JycnEhMTWzweQWgvcj75BACHG0ejiQi3cDSCcHmHNq0nOeYoShsbbnzsKeT7l8BvjwMS9JnG2ohr0Zl0dHLtRBe3LnWe46neT6GQKdh2bhv7Mva17AOwMiW6Eg5nHQbEJFShfRFJeyP07duXWbNmkZmZWbMtMzOTZ599ln79+lkwMkFouypOnKB4yxaQyfB4/HFLhyMIl5WXlsq/K5cBMGTiVFwSfoEN5nIXBjyGdNMHrDltXs13XPi4S7bhDHEOqRmF/2D/B5ik9tt6ODo9GoNkINgxmACHAEuHIwgtRiTtjbBkyRLS09MJDAwkNDSU0NBQAgMDSU1NZfHixZYOTxDapOxPPgXA8eabRd21YNVMRiMbP5uPQVdJYNce9NCcgM0vmm+8ZhaMeotD2Yc5W3jWPAG1w02XPd+j3R/FTmXH8dzj/JHwRws8Auv0b6oojRHaJ5G0N0JoaChHjx5l7dq1zJgxgxkzZrBu3TqOHTtG6FUmE59++inBwcFoNBr69+/P3r17L7t/QUEBjz/+OD4+PtjY2BAeHs6GDRsa83AEweqVH4uh5K+/QC7H/bHHLB2OIFzWvt9/Iv10LGqtLaO6yZBte9N8w/C5MOIVkMlYHbcagBs73FhrAmpd3LRuPNj1QQAWHlxIpbGyWeO3RpIksSPFPAlVrIIqtDdiImojyWQyRo4cyZAhQ7CxsbnqpcIBVq1axaxZs1i0aBH9+/dnwYIFjBo1itjYWDw9PS/aX6fTccMNN+Dp6cmaNWvw8/MjKSmpzvp6QWhLsj82T8BzGjsWm5AOFo5GEC4t48xpdq1eCcB1fdxwPFg1efSG/4PBMwAorCxkc+JmAO4Ku3gCal0mdZ7ED6d+qFlwaVrUtKYP3orF5ceRVZ6FVqmlt7eYNC20L2KkvRFMJhP/93//h5+fH/b29iQkmFtxvfTSS1dVHjN//nweeughpk6dSmRkJIsWLcLW1pYlS5bUuf+SJUvIy8vj119/ZfDgwQQHBzN06FC6d+/eJI9LEKxR2aFDlP7zLygUuD8mVukUrNfJf7ex6rXnMRkNdPS3JzLbnLxz43s1CTvA2jNr0Zl0RLhEEOUeVa9za5Qanuz1JABfHf2K/Ir8Jo/fmlWXxvTz7oeNwsbC0QhCyxJJeyO88cYbLFu2jHfffRe1Wl2zPSoqiq+//rpe59DpdBw4cIARI0bUbJPL5YwYMYLdu3fXeczvv//OwIEDefzxx/Hy8iIqKoq33nrrsos5VVZWUlRUVOtLEFqTnI/NHWOcbr8NdVCQhaMRhIsZ9Hq2LvmcDZ98gKGykkBvDaNst5g/gR37EfR/uGZfSZJYE7cGMLd5vJpPaW8OuZnOrp0p0Zfw+ZHPm/xxWLPqVo+iNEZoj0TS3gjffvstX375JRMnTkShUNRs7969O6dOnarXOXJycjAajXh5edXa7uXlRUZGRp3HnD17ljVr1mA0GtmwYQMvvfQSH3zwAW+88cYl7+ftt9/Gycmp5isgQMy4F1oHU3k52Z99RumuXaBU4v4/McouWJ+inCxWvTqbw5vWAzAg0o47nbegVZrg9i+g95Ra+x/OPsyZwjNoFBpuDrn5qu5LLpPzdJ+ngfa14FKRrogj2UcAuMZfTEIV2h+RtDdCampqnRNOTSYTer2+2e7XZDLh6enJl19+Se/evZkwYQIvvvgiixYtuuQxc+bMobCwsObr3LlzzRafIDQFyWSi4JdfOXPjTeR89DEArhMnovb3s3BkglBb4pGDfPf8TDLi49DY2XP7QDsGSxuRK5Rw11LoPuGiY6pH2Ud3GI2D2uGq77O/T3+G+g/FIBlYcGBBYx9Cq7A7bTdGyUgHpw742YvXAaH9ERNRGyEyMpJ///2XoP98VL9mzRp69uxZr3O4u7ujUChq9XoHc793b2/vOo/x8fFBpVLVGt3v3LkzGRkZ6HS6WqU61WxsbLCxEfV/QutQumsXme+9T2XVqsJKXx88n3oKx5uvbkRSEJqTZDIR/csq84RTScIzOIRbOqbhlLYFFGoY/y1E3HjRcYWVhWxK3ATUvQJqfc3qPYsdqTv469xf7M/YTx/vPg0+V2uwI1V0jRHaN5G0N8LLL7/MlClTSE1NxWQy8fPPPxMbG8u3337LunXr6nUOtVpN79692bp1K7fddhtgHknfunUr06dPr/OYwYMHs3LlSkwmE3K5+cOSuLg4fHx86kzYBaG1qIiNI+v99yn911y3KndwwP1/j+AyaRJy8aZTsCLlxUX88ckHJBw+AEDXYddzneZvlEl/g1ILd6+A0OvrPHbd2XVUGisJcwmjm3u3BscQ4hzCnWF38mPcj3yw/wNW3LwCuaxtfoAuSVJN0i76swvtVdt8dreQW2+9lbVr1/Lnn39iZ2fHyy+/zMmTJ1m7di033HBDvc8za9YsvvrqK7755htOnjzJo48+SmlpKVOnTgVg8uTJzJkzp2b/Rx99lLy8PJ588kni4uJYv349b731Fo+L1SGFVkqfmUXa3Lkk3H67OWFXqXCZfB8dN2/C7YEHRMIuWJWMM6dZPmcmCYcPoFSpGfXQ/xip2WJO2FV2MGnNJRP2WhNQw65uAmpdHuvxGHYqO2JyY9iYsLFR57Jmp/JOkVOeY2716CVaPQrtkxhpb6Rrr72WLVu2NOocEyZMIDs7m5dffpmMjAx69OjBxo0bayanJicn14yoAwQEBLBp0yaeeuopunXrhp+fH08++SSzZ89uVByC0NKMJaXkLVlM7tJlSOXlADiMHo3nrKdQBwZaODpBqE2SJI5t3cRfSxdhNBhw9vJh7GPT8dzxNKTsAxtHmLgGAvtf8hxHso8QXxCPRqFhTMcxjY7JTevGA1EP8NGhj1h4cCHXB13fJlshVo+y9/fpj1ohPlEW2ieRtDfCuXPnkMlk+Pv7A7B3715WrlxJZGQkDz/88BWOrm369OmXLIf5+++/L9o2cOBAoqOjrzpmQbAGksFAwZqfyP7kE4w5OQBoe/bE87lnsa3nfBBBaEn6ygq2Lv6c49u3AtCxT39GT70fzU8TIf0IaJzhvl/Ar9dlz1O9Auqo4FE4qh2bJLZJkZNYFbuKtNI0Vp5cydSoqU1yXmtS3Z9d1LML7Zkoj2mEe++9l23btgGQkZHBiBEj2Lt3Ly+++CKvv/66haMTBOsjSRLFf23j7K23kfHqqxhzclAFBeL30UKCVq4QCbtglfLTU/l+7jMc374VmUzOtffez62PPITmx/HmhN3WHe5ff8WEvakmoP6XVqllRi/zok1tccGlwsrCmlaPImkX2jORtDdCTEwM/fr1A+DHH3+ka9eu7Nq1ixUrVrBs2TLLBicIVqb8WAzJk6eQ8thj6M6cQeHsjNeLL9Jx7VocR45sdG2vIDSH0/t2s3zOU2QnJ2Lr5Mxdc9+g3/BByL4ZA1knwN4bpm4A7yuvaLr+7HoqjZWEOofS3aNpV7AeEzKGTq6dKNYXs+jIpdv/tka703ZjkkyEOofiY+9j6XAEwWJEeUwj6PX6mjaKf/75J7fccgsAnTp1Ij093ZKhCYLV0KWkkr1gAUVVHZVkNja4Tp6M28MPoXC4+v7UgtASTEYjO374ln2//wSAb3hnxjw1Gwd5GSy9EfITwSkAJv8Gbh2veD5JklhzumEroNaHXCbnmT7P8ODmB/kx9kfu6XQPwU7BTXofllJdGiO6xgjtnRhpb4QuXbqwaNEi/v33X7Zs2cLo0aMBSEtLw83NzcLRCYJlGYuKyHzvPc7eeGNNwu506y10/GMDnk/PEgm7YLVKC/JZ88bcmoS99823Mv6Vt3GQCmFJVcLuEmweYa9Hwg5wNOcop/NPY6OwYUxI4yeg1qW/T3+G+A8xL7h0cEGz3EdLM0kmdqbuBETSLghipL0R5s2bx+233857773HlClT6N7d/HHn77//XlM2IwjtjaTTkf/99+R89jnGwkIAbAcMwPPZZ9B26WLh6ATh8lJOHWfdgnmU5ueh0mgZ9b8niRh4DWTHwje3QEkGuIebR9gdfet93uo2j6OCR+Fk49Rc4dcsuLQ1eSsHMg+0+vaIJ/NOkluRi63Sll6el58zIAhtnUjaG2HYsGHk5ORQVFSEi4tLzfaHH34YW1tbC0YmCC1PkiSKN20i64P56M+dA8AmLBTPZ57BbsgQUbMuWDVJkji44Te2L1+CZDLh5h/I2FlzcPMLgIxj8O1tUJYDnl1g8q9g71nvcxfrimt6qDflBNS6dHTuyJ1hd7I6bjXv73u/1S+4tCPF3OpxgM8AVAqVhaMRBMsSSXsjKRSKWgk7QHBwsGWCEQQLKTt4kKx571J+xNzhQeHhjseMGTjffjsypXiZEaxbZVkZmxctJG6PuQyj0+Ch3PDwdNQaLaQehO9uh4oC8OkO9/0Ktq5Xdf71Z9dTYaygo1NHenj0aPL4/+uxHo+x/ux6YnJj2JS4iRs73Njs99lcalo9+ouuMYIgrqYN4OLiUueooZOTE+Hh4TzzzDNXtSKqILRWlQkJZM+fT/GWPwGQ2driNm0ablPvR25nZ+HoBOHKcpIT+X3+2+SnpyJXKBk2+QF6jBpjfo0/tQF+eQQqi8C/r3nhJK3zVZ1fkqSa3uzNMQG1Lu5adx7o+gAfH/qYBQcWcF3gda1ywaWCigKO5RwDRD27IIBI2htkwYIFdW4vKCjgwIEDjBkzhjVr1jB27NiWDUwQWoghL4+cTz8jf9UqMBhALsf5rrtwn/44Ks/6lw0IgiWd/Hcbm7/6BENlJfZu7oyd+Ty+4Z0g9wxsnAOnzT3VCRoM964Cm6ufPB2TE0Ncfhw2ChvGdmy5a8J9kffVLLj0/cnvuT/q/ha776ayK20XJslEmEsY3nbelg5HECxOJO0NMGXKlMve3qNHD95++22RtAttji4llaJ168j9+mtMJSUA2A8diuczT2MTFmbh6AShfgx6PX9/+zVHNq8HILBrD26e8Sy2GhX89QbsXAhGHchVMPBxGPY8qLQNuq/qNo8jg0Y26wTU/9IqtczoOYO5O+fy5dEvuTX0Vlw0Llc+0IrsSDXXs4tRdkEwE0l7MxgzZgxvvPGGpcMQhEbTZ2ZRtncPpdHRlEXvQZ+aWnObJjISz+eew25AfwtGKAhXpygni7UfvkNGfBwAA+6YwMC77kEe9wdsfAEKk807hgyHm94D94a/GS3WFfNHwh9A809ArcuYkDEsP7mcU3mn+OLoFzzf7/kWj6GhTJKJnWnmOQZiFVRBMBNJezOorKxErVZbOgxBuGqGvDzK9u41J+l79qJLSKi9g1KJtmtXXO65G8cxY5DJW29XCqH9STxykPUfv09FcREaO3tunP40IYEusHI8nNlq3snRH0a/DZ3HQiPrzzec3UC5oZwQpxB6evZsgkdwdRRyBU/3eZqHNj/EqlOruKfTPQQ5BrV4HA1xIvcEeRV52Kvs6eHZw9LhCIJVEEl7M1i8eDE9evSwdBiCcEXGoiLK9u+vGUmvjIurvYNMhqZLF2z798NuwABse/USE0yFVkcymYj+eRW71qwEScKzQ0dumT4Tp1PfwvpPzKUwCjUMegKufRrUjf8bt8QE1LoM8BnAtX7X8m/qvyw4sIAPh39okTiuVnXXmAE+A1DJRatHQQCRtDfIrFmz6txeWFjIwYMHiYuL459//mnhqAThykylpZQdPFgzkl5x4gSYTLX2sQkPx3ZAf+z698e2Tx8UTi1XhysITa28pJg/Pn6fhMMHAOh63SiuG+CD8scxUJRi3il0BNz4br1XN62P47nHic2PRS1XMzbEsvObZvWexc60nfyZ/CcHMw/Sy8v6Fymq7s8uWj0KwnkiaW+AQ4cO1bnd0dGRG264gZ9//pkOHTq0cFSCcDFTRQXlhw9TumcPZdF7KD92zNzt5QLqDh3Oj6T364fS9ep6UAuCtUo/HcvaBe9QnJONUqXm+vF3EFWwGn7527yDU6C5FKbTzY0uhfmv6hVQbwi+AWeNc5Oe+2qFuoRyR9gdrIlbw/v732fFTSuserGz/Ir8mlaPg30HWzgaQbAeImlvgG3btlk6BEGok6TTUR4TUzOSXn7oEJJOV2sfla8vtgMHmEfS+/dH5eVloWgFoXlIksShjWvZ/t0STEYDzp5ejB3sgufhmWDSg8IGBj8J1zwF6qZfvbpEV8KGhA0A3BXW8hNQ6/J4j8fZcHYDx3KOsSlxE6M7jLZ0SJe0M20nEhIRLhF42YnXJ0GoJpL2Rli6dCl33303Wm3DWoEJQmNJRiMVJ05Stiea0ug9lB08iFRWVmsfpYcHtgMGYDfAnKSr/f0tFK0gNL/KslI2LVrI6T27AAjrFMQo27+xOVVVChM2Cm58B1xDmi2GDQnmCajBjsH09urdbPdzNdy17kyLmsYnhz9hwUHzgktqhXU2TBCtHgWhbiJpb4Tnn3+eJ598knHjxvHAAw8waNAgS4cktBO6lBSy5r1LaXQ0puLiWrcpXFyw7d8fu/79sO0/AHWHYKv+KFwQmkpmwhnWffgOBZnpyBUKhkYY6WlajqwccA6CG+dBxI3NHkd1aYwlJ6DWZXKXyfwY+yOpJal8f+p7pnS5/JojlmA0GdmZWtXqUdSzC0ItImlvhNTUVNauXcuyZcsYNmwYISEhTJ06lSlTpuDtLVZvE5qHsaiIcw89XNOOUe7ggG3fvjUj6TZhYaIVo9CuSJLE0T83su2bLzHq9TjaqxjjcRAfqQBUGnMZzOAnG7xA0tU4nnuck3knUclV3Nrx1ma/v6uhVWp5otcTvLTzJb44+gW3drzV4vX2/3U89zgFlQU4qBzo7tHd0uEIglURV/ZGUCqV3H777fz222+cO3eOhx56iBUrVhAYGMgtt9zCb7/9huk/nTkEoTEko5HUp59Bl5CA0tub4FU/EB69m4DPPsV18mQ0EREiYRfaFV1FORs+fp8/v/4Uo15PiFMJk3z/wUdTABE3weN7GrWi6dWqmYAaZPkJqHUZGzKWCJcIinXFfHH0C0uHc5HqVo8DfQeilItxRUG4kLi6NxEvLy+uueYaBg4ciFwu59ixY0yZMoWOHTvy999/Wzo8oY3Ieu99Sv/9F5lGQ8Bnn6Lt3h2ZQmHpsATBInKSE1kx5ylO7dyOTCZxrWcCt/kcQuseAPeuhnu+B5fgFounVF/KhrNVE1AtsAJqfVQvuATww6kfSCpKsnBEtVW3ehT17IJwMZG0N1JmZibvv/8+Xbp0YdiwYRQVFbFu3ToSEhJITU1l/PjxTJlifXWDQutT8PMv5C1bBoDvO2+jiYy0bECCYEHHt29lxYuzyEtLwV5ZyfjAo/TzykV23Vx4LBrCR7Z4TBsSNlBmKCPYMZg+Xn1a/P7ra6DvQK7xuwaDZGDhwYWWDqdGbnkux3OPAyJpF4S6iM+eGmHs2LFs2rSJ8PBwHnroISZPnozrBT2u7ezsePrpp3nvvfcsGKXQFpQdPETGK68A4P7YYziOtt52bYLQnPSVFfy15Ati/t4CQJBdPjf5xmIbNcrcc9050GKxWesE1LrM6j2LXWm72JK0hUNZh+jp2dPSIbErbRcSEp1dO+Nh62HpcATB6oikvRE8PT3Zvn07AwcOvOQ+Hh4eJFRNGBSEhtCnpZHyxBNIej0OI0fiPv1xS4ckCBaRl5bC2ndfJSc9A5AY5J5E/3A18pt/MK9qakHHc49zIvcEKrmKWzreYtFY6iPMJYzbQ2/np9M/8f6+91l+03KLv9GormcXo+yCUDeRtDfC4sWLr7iPTCYjKCioBaIR2iJTWRnnHp+OMTcXm06d8H3nbTHRVGiXTm37g81ff47eYMJWoeOmwESCbn4EBk4HpY2lw+OnuJ8AGBE4AheNi4WjqZ/pPaezIWEDR3OOsilpE6ODLfcJntFkZFeaube+aPUoCHUTV/8GKi4u5sCBA5SUlABw8OBBJk+ezLhx41ixYoWFoxPaAslkIm3OC1SePInC1ZWATz9Bbtv0qzcKgjUz6HT8+cELrF/0KXqDCX/bAu4b5UnQ81vh2qetImEv05ex/ux6wHonoNalesElgAUHFqAz6q5wRPM5lnOMwspCHNQOdHXvarE4BMGaiaS9Af755x/8/Pzo27cvQUFBbN68mWHDhrFv3z5OnjzJ5MmT+eqrrywdptDK5Xz2OcWbNoFKhf8nH6Py87N0SILQogoyM/h+9iMc2XsUgP7+JYyb8yr2U5aDc4CFozvvj4Q/KDOUEegQSF/vvpYO56pMjpyMp9azZsElS6kujRnsO1i0ehSESxBJewPMnTuXcePGce7cOWbOnMmECROYPn06J0+eJCYmhtdee41PP/3U0mEKrVjRps3kfPIJAD6vvoJtr14WjkgQWtbpfbtZ/txjZKVlo1HouWOQHdfM24Q83LK163VpTRNQ/8tWZcv0ntMB+OLoFxRUFFgkjh2potWjIFyJTJIkydJBtDbOzs5ER0fTqVMndDodWq2WgwcP0r27efW2+Ph4evbsSfF/lpe3JkVFRTg5OVFYWIijo6OlwxEuUHHyJIn3TkQqL8d1ymS85syxdEiC0GKMBgP/rlzGgfW/AuCjLWLM0AAcJy8DhcqisdXlZO5Jxq8bj1KuZOu4rbhqXK98kJUxmoyMXzeeuPw4JnWexOx+s5vkvJIkoTfpqTBWUGEwf5Ubymv/bCynWFfM67tfB2Db+G24a92b5P7bKnH9br/EZ1ANUFRUVNPaUa1WY2tri4ODQ83tDg4OlJWVWSo8oRUz5ORw7rHHkcrLsbvmGjyffdbSIQlCiynKyWLdgnmkn44FoLdrCtcO7YZi3GKrTNgBfjp9fgJqa0zY4fyCS49seYQfYn+gm0c3FDJFTXJdbig3J9nVyfZ/ku7q76tvu3B/k1T/VcEj3SJFwi4IlyGS9gaQyWS1PgL978+C0BAmnY6UJ2ZgSE9H3aEDfvM/QKYUT1GhfTh7aB9/fDKfipJibOQGRvnGETZgKNzxFSis83lQpi9j3dl1QOuagFqXQb6DGOw3mJ2pO3nun+ea/PwKmQKtUotGqUGj0NT+V6nBTmXHxM4Tm/x+BaEtsc5XQisnSRLXX389yqqEqqysjLFjx6JWqwEwGAyWDE9ohSRJIuPV1yg/dAi5oyP+n32KQnzsKbQDJqORnT8uZ++vqwHw0pQwxu8kzr1uhtu/tNqEHWBj4kZK9aWtcgJqXeb0m8PcHXPRmXRoFJqaJNtGYXNRwq1Vamsl3Rf9rNDWfK9RalDJrfOTEkFoTaz31dCKvVK1MmW1W2+99aJ97rzzzpYKR2gD8r75hsKffwa5HL/587Hp0MHSIQlCsyvJy2X9R++RcjIGgB6uGQz1iEfZ/S64bZFVJ+xwfgLqneF3Ipe1/r4OQY5BfHfTd5YOQxCES7DuV0Qr9d+kXRAao+TfHWS9+x4AXs/Pxv6awRaOSBCaX9Kxw2z4+H3KCgtQqVWM9Iihk2MWdB0Pt31u9Qn7qbxTHMs5hlKu5NaOFw/cCIIgNDXrflUUhDau8mwCqbNmgcmE01134nLffZYOSRCalclkZM/PP7JrzUqQJNw9XRjrsBVXdSl0uxtu+wzkCkuHeUXVo+zXBVyHm9bNwtEIgtAeiKT9KvXs2bPek04PHjzYzNEIrZmxsJCURx/FVFyMtndvfF5+WUxoFtq0ssICNnzyAUlHDwEQ1SOM6yq+QyXTQ/d74NZPW0XC3lpXQBUEoXUTSftVuu2222q+r6io4LPPPiMyMpKBAwcCEB0dzfHjx3nssccsFKHQGkgGA6lPPYUuKQmlrw/+Hy1EVjWRWRDaGqPBwJn90Wxb9iUl+XkobWwYMaovXeLngcwEPSbCLR+3ioQdYFPiJkr0Jfjb+9Pfp7+lwxEEoZ0QSftVurCe/cEHH2TGjBn83//930X7nDt3rqVDE1qRzHnvUrprNzJbWwI++wylm/h4XWh7CrMyOLp1EzHbtlBWWACAq18AY2/uifuO2SCZoOckGPsxyFvPRM41p9vWBFRBEFoHkbQ3wurVq9m/f/9F2ydNmkSfPn1YsmSJBaISrF3+6tXkf2fu0OA77x00nTpZOCJBaDpGg4EzB/Zw9M+NNWUwALZOznS9biT9OspRb3iiKmG/D8Z+1KoS9ti8WI5mH0UpU3Jb6G2WDkcQhHZEJO2NoNVq2blzJ2FhYbW279y5E41GY6GoBGtWtn8/Ga+bP5lxn/EEjjfcYOGIBKFpFGRmcOyv2qPqAEHdetJtxGg69u6P4vga+OV/gAS9JsOYha0qYYfzK6AODxwuVu8UBKFFiaS9EWbOnMmjjz7KwYMH6devHwB79uxhyZIlvPTSSxaOTrA2upRUUp6YAXo9DjeOxv3RRy0dkiA0yuVG1aOG30DX60bh7OVt3nh4Jfz6GCBB7/vh5g9bXcJebihn3Zm2sQKqIAitj0jaG+H5558nJCSEhQsXsnz5cgA6d+7M0qVLGT9+vIWjE6yJqbSUlMcfx5ifjyYyEt+33hKdYoRWqyAzg2NbNxLz958Xjap3H3EjIb37oVBecHk5tAJ+exyQoM80uOmDVpewg3kCarG+GD97Pwb4DLB0OIIgtDMiaW+k8ePHiwRduCzJZCJ19mwqY2NRuLvj/+knyLVaS4clCFelugPM0a2bao2q2zm7VI2qj8TJ0/viAw9+B78/AUjQ90G46X1opW9Yq3uz3xV+l5iAKghCixNJeyMVFBSwZs0azp49yzPPPIOrqysHDx7Ey8sLPz8/S4cnWIHsjz+m5M+tyFQqAj75GJWPj6VDEoR6K8hIN9eqXziqLpMR3K0n3a4fffGo+oUOfANrZ5i/7/cw3Phuq03Y4/LjOJJ9RExAFQTBYkTS3ghHjx5lxIgRODk5kZiYyIMPPoirqys///wzycnJfPvtt/U+16effsp7771HRkYG3bt35+OPP66pk7+cH374gXvuuYdbb72VX3/9tRGPRmgORRs2kPv5IgC8/+91tD16WDYgQagHo0HPmf17OPLnRpKPHa7ZfsVR9QsdWAZrnzR/3/9/MPqdVpuwA/wUZ56AOixgmJiAKgiCRYikvRFmzZrF/fffz7vvvouDg0PN9ptuuol777233udZtWoVs2bNYtGiRfTv358FCxYwatQoYmNj8fT0vORxiYmJPPPMM1x77bWNehxC8yiPOU7anBcAcH1gGs4XLMwlCNaoICOdo39t4vh/R9W796Lb9aMI6XWZUfUL7V8K62aav+//KIx+u1Un7OWGctaeXQuICaiCIFiOSNobYd++fXzxxRcXbffz8yMjI6Pe55k/fz4PPfQQU6dOBWDRokWsX7+eJUuW8Pzzz9d5jNFoZOLEibz22mv8+++/FBQUNOgxCM1Dn5VFyuOPI1VWYj90KJ6zZlk6JEGok9GgJ37fHo5urWtUfSRdr7vhyqPqF9q3GNZX/b0PeAxGvdWqE3aALUlbKNYV42vny0DfgZYORxCEdkok7Y1gY2NDUVHRRdvj4uLw8PCo1zl0Oh0HDhxgzpw5NdvkcjkjRoxg9+7dlzzu9ddfx9PTkwceeIB///33ivdTWVlJZWVlzc91xS00DVNlJSlPPIEhMxN1x474fvA+MkXrWJ5daD/yM9I49tfmxo+qX2jvV7DhGfP3A6fDyDdafcIO5yegihVQBUGwJJG0N8Itt9zC66+/zo8//giATCYjOTmZ2bNnc+edd9brHDk5ORiNRry8vGpt9/Ly4tSpU3Ues2PHDhYvXszhw4frHevbb7/Na6+9Vu/9hYaRJImMl1+m4shR5E5OBHz2KQp7e0uHJQg1Eg4fYP/an0mOOVKzzc7Fla7DbyBq+EicPL0uc/Rl7PkS/njW/P2gJ+CG/2sTCXt8fjyHsg6hkCnEBFRBECxKJO2N8MEHH3DXXXfh6elJeXk5Q4cOJSMjg4EDB/Lmm282y30WFxdz33338dVXX+HuXv/JUHPmzGHWBSUaRUVFBAQENEeI7VrekiUU/vY7KBT4L1yAOijI0iEJAgA5yYlsX76ExCMHzRuqR9VHjCakZ9+rH1W/0J4v4I/nzN8PfhJGvNYmEnY4vwLqsIBheNpeeo6RIAhCcxNJeyM4OTmxZcsWduzYwdGjRykpKaFXr16MGDGi3udwd3dHoVCQmZlZa3tmZibe3hfXkZ45c4bExETGjh1bs81kMgGgVCqJjY2lY8eOFx1nY2ODjY1NveMSrl7x33+T9f4HAHi9MAe7AWLxFcHyygoL2Pnjco5t3YwkmZArlPQcfTM9R9/S8FH1C0V/Dhur5t5c8xRc/0qbSdgrDBX8fuZ3QExAFQTB8kTS3gSuueYarrnmmgYdq1ar6d27N1u3buW2qu4iJpOJrVu3Mn369Iv279SpE8eOHau1be7cuRQXF7Nw4UIxem4hlfHxpD39DEgSzhMm4HIV3YMEoTkYdDoO/vE7e35Zha68HICwfoMYMnEqzt5NtFbA7k9hk7lDEtc+Dde91GYSdkmSWH5yOUW6IvMEVB8xAVUQBMsSSftV+uijj+q974wZM+q136xZs5gyZQp9+vShX79+LFiwgNLS0ppuMpMnT8bPz4+3334bjUZDVFRUreOdnZ0BLtoutAxDfj7nHnscU2kptn374j33RWRtJHERWh9JkoiL3sE/K5ZRlG3+BM8rJJRh9z2If2QTvkbs+gQ2v2j+fsizMPzFNpOwF1YW8vru19mctBmAuzvdjUIuJpMLgmBZImm/Sh9++GGtn7OzsykrK6tJnAsKCrC1tcXT07PeSfuECRPIzs7m5ZdfJiMjgx49erBx48aayanJycnI5aJjgTWS9HpSZz6FPjkZlb8/fh8tRKZSWTosoZ1Kj4/l72++Ji3uJAD2Lq5cc88UIq8djqwpX0N2fgRbXjJ/P+Q5GP5Cm0nY92XsY86/c8gsy0QpU/J4z8eZHDnZ0mEJgiAgkyRJsnQQrdXKlSv57LPPWLx4MREREQDExsby0EMP8cgjjzBx4kQLR3hpRUVFODk5UVhYiKOjo6XDabWyPlxA7hdfILe1JeiH79GEh1s6JKEdKsrJ4t+V33Bq53YAlDY29B17J33H3oFKo2naO9uxAP58xfz90Odh+JzL7t5a6I16Pjn8CUtjliIhEeQYxDvXvkOUu/gEU7Au4vrdfomkvRE6duzImjVr6NmzZ63tBw4c4K677iIhIcFCkV2ZeNI3ni4pibNjxiLp9fgtWIDj6FGWDkloZ3QV5ez7bQ371/6CQa8DoMvQ6xl89304uNa/uxQAJhOU50FxBpRkmr+KM6AkC0qq/i3OgLwz5v2HzYFhdS/+1tokFCYw+5/ZnMwzf0JxZ9idPNf3OWxVthaOTBAuJq7f7Zcoj2mE9PR0DAbDRduNRuNF3WCEtidz3rtIej12116Lw6iRlg5HaEdMJiPH/97KzlXfUVqQD4B/5yiGTX4Qr5DQ2jsbKquS8Kqk+8IEvDohL86E0iwwXfx6djGZuX596LNN/8BamCRJrDm9hnf3vkuFsQInGydeHfgqI4Lq3wFMEAShpYikvRGuv/56HnnkEb7++mt69eoFmEfZH3300atq+yi0PiU7dlLy11+gVOL1/Gwx8VRoMckxR/j726/JTjJ/kufs5syQYT0I9bNBFr8MDmfVHi0vz7+6O7B1A3tvsPcEB2+w9zJ/OVT96xIMTv5N/rhaWn5FPq/seoVt57YB0N+nP28OfhMvuyZogykIgtAMRNLeCEuWLKnp+qKqmnxoMBgYNWoUX3/9tYWjE5qLpNeT+c7bALhOvBebOvriC0JTy0tLZfvyxZw9sBcAG7mBAe7J9HRNQ3FiLZy4zMEK9fnkuyYBvzAx9zz/s6LtT6TelbqLF3e+SE55Dkq5kpm9ZnJf5H3IZWLCvyAI1ksk7Y3g4eHBhg0biIuL49SpU4C5j3q4mIzYpuX/sApd/BkULi64P/64pcMR2rjykmKi13zP4c3rMRmNyGTQwzmVAe7J2NrZgUPYBcn4BQm4wwVJutalzXR3aYxKYyULDy7kuxPfARDiFMK8IfPo5NrJwpHVVlluoCi7HBcfW5Qq0WpSEAQzkbQ3gfDwcJGotxOG/HyyP/4YAI+ZM1GISUBCMzEa9BzetIHon76norQEgBB3E0McD+JmJ8G45RBxo4WjbD3i8+OZ/e9s4vLjAJgQMYGn+zyNVqm1aFy6CgM5KSVkJRaRlVRMdnIxBZllANg6qek1MojIa31RqUXyLgjtnUjar9KsWbP4v//7P+zs7Jg1a9Zl950/f34LRSW0lJyPP8ZUVIRNp04433WnpcMR2iBJkjizfw//rFhCfnoaAO5+fgx1PUmw4QhonOCeVRAkVuisD0mS+P7U98w/MJ9KYyWuGldeH/Q6QwOGtngsBp3RnKAnFZOdVERWcjH56aXU1cNNaaOgrFDHjtWnObAxkZ43BNFliC9qjbhsC0J7JZ79V+nQoUPo9fqa7y9FTExseypiY8n/YRUAXi/MQaYQI19C08pKPMvf337NueNHAbB1cmbwzSOJOvMe8qJkcPCBST+BVxcLR9o65JTn8PLOl/k39V8ABvsN5o3Bb+Cuvcp2mA1gNJjITa2doOellmIyXZyh2znb4BnkgGeQAx5BjngGOqDWKjm1O50DG5Mozq1g18/xHNycRI8RAXQd5i+Sd0Foh0Sf9nZK9Hm9OpIkkXz/VMr27MFh9Gj8F3x45YMEoZ5K8vPYueo7Yv7+EyQJhUpFnzG3069vKOqfJkFZLriFwqSfwSXI0uG2Cv+k/MNLO18iryIPtVzNrD6zuLfTvc0yoGIymshLLyMrqYjspGKykorISS3BZLj48qp1UOEZ5FiVpDviEeSAnZPNJc9tNJqI25PB/j+SKMouB8DGTkmP6wPoOjwAG61I3tsbcf1uv0TS3gjLly/njjvuwNa29S3AIZ70V6do82ZSZzyJzMaGkPXrUfv7WTokoQ3QV1awf90v7PvtJ/SVFQB0GjyUa++ZgmNRDPwwCfSl4NsTJq4Bu+YfIW7tKgwVfLD/A36I/QGAMJcw5l07jzCXsCY5v8kkUZBZRnZSEZlVo+g550ow6E0X7Wtjq8Qz2DxyXp2g27vYNOiNg8lo4vS+TPb/kVRT867WKul+nT/drgtAY9f2u/4IZuL63X6JpL0RPDw8KC8v55ZbbmHSpEmMGjUKRSspmRBP+vozVVZy9qab0aem4v7Yo3jMmGHpkIRWrqK0hFM7trPnt9WU5OYA4BMWwbDJD+Eb3gmOrYFf/gcmPYQMgwnLwcbBskG3ArF5scz+ZzZnCs2rtk7qPImZvWdio7j0SPblSJJEYXY5WUlVk0SrJorqK40X7avWKPAIcsAz0JycewY54uiuafKRfZNJIv5AJvvXJ5KfYU7eVRoF3Yb70+P6QDT2Inlv68T1u/0SSXsjGAwGNm7cyPfff89vv/2Gra0t48aNY+LEiQwaNMjS4V2WeNLXX86iL8hesACltzcdN6xH3go/WREsT5IkUk4c49i2LZyO3olBrwPAwd2DIffeT8SgIeYEb88X8MdsQIIud8DtX4BSbdngrZxJMvHdie9YeHAhepMed607bwx+g8F+gxt0vty0EuIPZBG/P6tmVPtCSrUcj8DzCbpXsCNOHlpk8pabyySZJM4cymb/hgRyU0sBUNko6DrMjx4jAtE6iL+Ztkpcv9svkbQ3kbKyMn755RdWrlzJn3/+ib+/P2fOnLF0WJcknvT1o8/M5MyNNyGVleH7/vs4jbnZ0iEJrUxxXg4ntv9FzLYtFGSm12x38w+k2/Wj6DpiNCq1DUgSbHsT/nnPvEO/h2H0PJCLBX8uJ6ssi7k75rI7fTcAwwKG8dqg13DVuF7VeQoyyzi9P5P4A1nkpZXWbJcrZXgEOJhLXILNSbqLtx3yFkzQL0cySSQcyWHfhgRyzplbgyrVcqKG+NHjhsDL1ssLrZO4frdfYgZLE7G1tWXUqFHk5+eTlJTEyZMnLR2S0ASy589HKitD26sXjjffZOlw2iRJksg5l8TZA3sx6HUEde2Bb3hn5K2k1KwuRoOeswf2EfP3FhIOHUCSzPXOaq2WiEFD6Dp8JN6h4edLJ0xGWD8LDiwz/zx8Lgx5RiyIdAVbk7fy6q5XKagsQKPQ8GzfZxkXPq7eJSmF2eXEHzAn6tUJL4BcISOwixuhvT3p0M0dtRVP9pTJZYT09KBDD3eSjuWyb30CWUnFHP7zHMe2p9LlWl96jQzCzlkk74LQ2omR9kaqHmFfsWIFW7duJSAggHvuuYeJEyfSqZN1rbJ3IfFO/crKDx8m8e57QCYjePVqtFGizV5TMRmNpMaeIH5fNGf2R1OYlVnrdhtbO4K69aRDzz506NEbO2cXC0V6dXJTznFs22ZO/PMX5UWFNdv9OkUSNXwkEQOuQaXR1D5IXwE/PQCn1oFMDjd/AH2mtXDkrUuZvoz39r/Hmrg1AHR27cw7Q94hxCnkiscW5ZYTfyCLMweyyEoqrtkul8vw7+xCaG8vQnq4Y2PbOmvDJUki+UQe+9YlkJlQBIBCKSdysA89RwXh4Kq5whkEayeu3+2XSNob4e6772bdunXY2toyfvx4Jk6cyMCBrWPBE/GkvzzJZCJxwt1UHDuG05134Pvmmy1237ryMk78s41jf21Gr6vEv3MXArt0I6BLt1aTvNZFX1FB4tGDnNm/hzMH91FRXFRzm1KlJrBbD9QaLYlHD9W6DcCzQ0dCevYhuEcffMLCkcutZxReV15G7O4dHNu2mfS4UzXbbZ2c6TL0eqKG34Crr3/dB1cUwvf3QtIOUKjhzsUQeUsLRd46Hc89zvP/PE9iUSIyZNwfdT9P9HgCleLSSXZJfiVnDmZxen9mTSIL5g8y/CJcCOvjRUgPjzY1iVOSJFJO5bNvfQLp8eY3kHKFjM6DfOg1KghHd8uuBCs0nLh+t18iaW+EiRMnMnHixFbVNaaaeNJfXsEvv5I+Zw5yOzs6bvwDpYdHs99nbuo5jmzewPHtf6IrL69zH1e/AAKjzAm8f+cobB2dmj2uxigrLODMgb3E748m+ejhmsmXABp7Bzr27kfHPv0J7tarZgTaZDKSeSaes4f2k3BoP5lnT9c6p8begaBuPc1JfPde2Do5t+RDAswJUVrcKWK2bSZ217817RplcjkhvfoSNXwkHXr0RqG8TFlFcSasuBMyjoHaAe5ZCR2GtNAjaH2MJiPLji/jk0OfYJAMeNp68tY1b9Hfp3+d+5cWVnLmYDbxBzJrklYAZOAX5kxoVaJu69i2J2xKkkRqXAH71yeQGlcAmD9ViBjoTe/RQTh5iIn1rY24frdfImlvp8ST/tKMJaWcuXE0xuwcPJ99BrcHHmi2+zKZjJw9sI9Dm9aRfOxwzXYXX396jLwZJ09Pzh0/RvLxo2QnJfDf9c49AoMJiOpelcR3QWNn32yx1ld+eirx+6KJ37+HtLiTtWJ28vSiY58BhPYdgF9EZL3q1ksL8kk8cpCEQ/tJOnqIitLztcfIZHiHhJrLaHr2wTskDFkzTtwsLcjnxL/biPlrM3lpKTXbXXx8iRo+ksgh12HvUo8JkHln4bvbIT8R7Dxh0hrw6d5scbd22WXZzP53Nvsy9gFwQ9ANvDLwFZxsar9pLS/WceaQOVFPiyuo9XTx6ehEaB9POvbybLeTM9NOF7B/QwLnTuYD5nr4iH5e9L4xGGcvkby3FuL63X6JpL2Rtm/fzvvvv18z8TQyMpJnn32Wa6+91sKRXZ540l9a1gfzyf3qK9RBQYSs/R2ZuulH4sqKConZtoUjWzZQlJ0FgEwmJ6R3P3qOGkNg1+4XTaYrLykm5cQxcxIfc4TclORat8tkcjw7hBDQpRuBXbrh1ykStbb5L8SSyUTGmdPE748mfl80eannat3uFRJKxz79Ce07EPeAoEb1rTYZjaSfjiXh8H4SDh0gK7F2hyatgyPBPXrToUdvgrv3QuvQ+L9tk9FIwuEDxGzbzNmD+zAZzT26lTY2RAy4hqjhN+DXqUv9H1f6EVh+F5RmgUsw3PcLuF65Fru9KqwsZMofUzhTeAatUsucfnO4LfS2mt93Rames4ezid+fSUpsAZLp/CXNq4Mjob09Ce3tib2LqOWulnG2kH3rE0k+nguYy4TC+pqTd1cfOwtHJ1yJuH63XyJpb4Tly5czdepU7rjjDgYPNvcD3rlzJ7/88gvLli3j3nvvtXCElyae9HXTJSdz9uYxSHo9/p9/hsPw4U16/syz8RzauI5Tu7Zj1OsBc7lH1+tH0X3EjTh5etX7XGWFBZw7YU7gzx0/Rn56aq3bZXI53h3DCIzqTkBkN3wjOqGyaZrExaDXcy7mCPH7ozlzYC+l+Xk1t8kVCgK6dKNjn/507N0fR/fmKy0qyc8j8fABEg7tJ/HoIXTl53tqy2RyvEPD6NCzDyE9++IZHHJVo/D5GWnEbNvCie1bKbng8XmHhtN1+EgiBg3B5mp79if8C9/fA7pi8O4KE38Ch/r/n7c3lcZKHt78MAezDuJp68nikYsJdgqmstxAwuFsTu/PIuVkHqYLEnWPQIeaRF3UbV9eZmIR+zckknjUvMAXMgjt7cmgO0LFhFUrJq7f7ZdI2huhc+fOPPzwwzz11FO1ts+fP5+vvvrKqts+iid93c49Pp2SrVuxu+YaAr76sklWMzTo9ZyO3sGhTetIPx1bs92zQ0d6jh5LxKBrzX26G6k4L4eUqlKac8ePXtSRRaFU4h0aUVMT7xPWCaWq/hPvKkpLSDi0n/h90SQcPoC+4nzdvVqrpUOPPnTsO4AOPXpbpEzHaDCQHneKs4fNtfA5yYm1brd1cqZDj9506NmHoK490dhfHKO+soLTe3ZxbNtmUk7E1GzXOjgSOWQ4UcNuwD0wuGEBnvjd3CXGqIPga+HuFaCx7jkJlmQ0GXn2n2fZkrQFB5UDX1+3BEWSM/EHskg+kYvJcP7S5eZvX5OoO3uKMo+rlZ1czP4NiZw9nA2Ai7ctdz3fB7XGeltdtmfi+t1+iaS9EWxsbDh+/DihoaG1tsfHxxMVFUVFRYWFIrsy8aS/WMnOnZx74EFQKAj5/TdsOnZs1PmKc3M4+ucfHN26ibLCAgDkCiURA6+hx6gx+IRFNPkS5xcqys4yJ/AxR0g+cYyS3JxatytVanwjOhHQxVwT790x7KKJk0U52ZypKntJORlTUxoCYO/iaq5P79Mf/y7druoNQEsozs2pKqPZT9KxI7XeZMjkcnzDO9Ghh7kW3mQ0ErNtMyd3bD8/Wi+TEdy9F12H30DHPv1RKBvx+PYvNfdhl0zQeSzc8TWoxEjmpUiSxNt73+b7U99jg4YXlR+SGW3AqDfV7OPiY0dYH3Oi7uItSjqaQk5KMes+OUppQSXh/bwYMTWyWV+jhIYR1+/2SyTtjRAaGsqzzz7LI488Umv7okWL+OCDDzh9+vQljrQ88aSvTTIYOHvbbejiz+Ay+T68X3ihYeepWqr+0KZ1xO+LRjKZkwx7Vze6j7iRrtePskjbRkmSKMhM59zxoyTHmEfiq99IVFPZaPDr3IWAyK4Y9Xri90eTlVC7ZtzNP5DQvgMI7TMAr5DQZp302ZSMBj2pp06QUFVK89/5ABdy9PAiavgIugwd0fjSHkkyr3C6raplaO/74eb5YEUtK63RkpglfHjgQ9xL/Lk342l02ea/M2cv25oRdTc/y0+6bovS4gv4df4hJJPE8EmdiLzG19IhCf8hrt/tl0jaG+Hzzz9n5syZTJs2jUGDBgHmmvZly5axcOHCi5J5ayKe9LXlLV9B5htvoHB2puOmjSicrq5sQVdRzsl/t3Fo47paCWFAZFd6jB5Dx979L9/+r4VJkkReaoo5iT9+hHMnYi7qjQ7munDfiM6E9ulPx74DcPFuGxfwouwsEg7v5+yh/STHHEEymQjrN4io4TcQ2KVb07wZMZngj+dg31fmn4c8B8NfEKucXsHaM2uZ+89L9E4ZRe+0kSDJ0DqoGHpPBCE9PcTIbws4uCmJ3b+cQaGSc9fsPrj7izdI1kRcv9svkbQ30i+//MIHH3xQU7/euXNnnn32WW699VYLR3Z54kl/niE/nzOjb8RUWIj3q6/gcvfd9T42Ly2VI5vXE/P3nzVlFUobG7oMuY4eI29ueP1zC5NMJnLOJXHu+FHOnThm7mTTqy8hvftZfS/4xjIa9EgmCWVTdgkyVMIv/4PjPwMyuHEe9LfeN/HWYlfaLl7+9S2GnL4bt3LzG8TQPp4MuTscrX3b7qduTSSTxPrPjpIUk4uzly3j5rT9+nbduWJMFQZsQp2t/o2huH63XyJpbyCDwcBbb73FtGnT8Pe/xGqHVkw86c/LeP3/yF+5EpuICDr8/BOyK/QON5mMJBw6wOFN60g8crBmu4uPLz1G3kzk0Outol+6YCGVxbBqEpz9G+QquH0RdL3L0lFZveOZJ/jo65VEnRuKHAUae/PoemhvT0uH1i6Vl+j48c19lORXEtbXixumte369qxFR9AlFuF0Uwcchlj3NV1cv9uvtv3WuRkplUreffddJk+ebOlQhEaoiI0j/4cfAPB64YXLJuzlJcXm3uqb15/vzCKTEdKzDz1HjSGoW89WU+MtNJPSHFhxF6QdApUd3L0cOl5n6aisXszJeH77+gjdSs2/q5Be7gy7pxNaBzG6bilaezUjH4zi1w8OcnpfJr5hzkQN8bN0WM2i8mwBusQiUMiw7d78q18LQkOJpL0Rrr/+erZv305wcLClQxEaQJIkMt9+G0wmHEaNwq5/v4v3qVo46OjWjZzasR2DXgeAxs6eqOtG0v2Gm3D28m7p0AVrlJ8Ey++A3HiwdYOJq8Gvt6WjsmpGg4kdv5/i6JY0nCUvdOpybpgYRVT/IEuHJmBeRXbAbR3Z9XM8O348jVewIx6BDpYOq8kV/WVeEM6ujxeKdrpartA6iKS9EW688Uaef/55jh07Ru/evbGzq9127JZbbrFQZEJ9FP/5J2XR0cjUajyffbZme2FWBknHDpN07AjJMUdqTdD0CA6h56gxdBo8pMkWKhLagMzjsPxOKE4Hp0C472dwD7N0VFYtO7mYLd8cJz+1DDlyUj1P8uj02wnwbBuTnduKHjcEkBZfQOLRHDZ9FcP4F/qi1rad1KEyuYjK+AKQy3AYFmDpcAThskRNeyPIL1MKIZPJMF7Q09ratPeaOFNlJWdvHoM+JQX7B6dRfs3AqkT9MIWZGbX2VWm0hPTqS89RY/CN6Nym6zqFBkjaDd9PgIpC8IyEST+Bo0g8L8VoMLH/j0QO/pGEySRRrizhQNh63pwymxDnEEuHJ9SholTPqjf3UpJXSWhvT0Y+2KXNvA7mLI2hIjYf2z5euN4Vbulw6qW9X7/bs7bzdtkCTCbTlXcSrI5Bp+Pk/PeJN5SRGxlM4f5/YN/2mtvlCgU+YREERvUgqGsPvEPDrapdo2BFYv+A1feDoQICBsC9P4C25fvwtxbZ54rZ+s1JclNKADjjepi9ob/x8ZgFImG3Yho7FaMejOKX9w8SfyAL3zBnug6z7sma9aFLKaYiNh9k4ChG2YVWQGQiDZSYmMiWLVvQ6/UMHTqULl26WDok4RIkk4msxLM1I+mpp45j1OvBqyq5kiTc/AMJ6tqDwK49CIiMQq0VS6ELV1BRBL8+ak7Yw0fDXUtBLf5u6mI0mDiwMYkDGxIxmSTQGNkS8B0J7keYP2w+PT17WjpE4Qq8Q5wYeEdHdq6JZ8ea03h1cMQzqHWP8lbXstv28ETprrVwNIJwZSJpb4Bt27YxZswYysvNy6IrlUqWLFnCpEmTLByZUK2mLv3oYZKPH71o4SAbvQEvlZbIx6YT1LUH9q5uFopUaLU0jjBhORxbAze9DwrxclqXnJRi/lx2fnRdE6bnC8fXKFcXM7f/XK4Put7CEQr11f36ANJOF5BwpKq+/cV+2LTS+nZdeikVJ3JBBg7DxSi70DqImvYGuOaaa3B3d+fzzz9Ho9Ewd+5cfvnlF9LS0iwdWr21tZq48uIikmOOknzsMEkxddelB3Tpiq+rB7KPP8e+Uk+H1avRdo2yUMSC0LYZjSYO/HF+dN3GTonnSBMvZ8zEiJGHuj7EjF4zLB2mcJUqSvX8+NY+inMr6NjTg1EPR7XK+vbclScpP5qDtps7bvd2tnQ4V6WtXb+F+mudb5EtLCYmhl27duHj4wPAe++9xxdffEFubi5ubmLEtiUYdDpST50gKeYwyccOk5lwBi54/1lXXbpcLifx7nuoqNTjdMcdImEXhGaSk1LC1m9OkHPOPLoe0sMDz1EmHt31EEaM3NLxFp7o+YSFoxQaorq+/ef3D3DmUDbH/k6hWysbqdZnlVF+LAcAx+sCLRyNINSfSNoboKioCHd395qfbW1t0Wq1FBYWiqS9mfy3Lj3t1ImanunVrlSXXvDrr1QcPYrc1hbPp2a2YPSC0D4YjSYObkxi/4ZETEbz6PqQu8OxCa/kvj/uo9xQzmDfwbw66NVWOTormHl1cGTQHaHsWH2anWvi8Q5xalX17cXbzoEEmkg3VN52Vz5AEKyESNobaNOmTTg5OdX8bDKZ2Lp1KzExMTXbRJ/2pnN67y7WfvhOrW32Lq4EdjWPpAdGdb9sXbqxpJTsD+YD4P7Yoyg9xKp3gtCUclNL2PrNSbKTiwHo0N2dofdGUK4u5r4NU8mvzCfSLZL5w+ajkqssHK3QWN2u8yctvoCzh7Jr+rfb2Fr//6shp5yyw1kAOF7Xuj4hEASRtDfQlClTLtr2yCOP1Hxv7X3aWxv/yK6otbb4R0YRVJWou/oF1Hu0LvfLLzFkZ6MKCsRl8uRmjlYQ2g+j0cShTUnsW181um5rHl0P6+tFuaGc6Zumk1KSgp+9H59e/ym2KtFhpy2QyWRcd18ncs4VU5RTwV/fnmL0I9Zf3170d9Uoe4QLav+2t7qr0LaJpL0BRH/2lmfr6MTjS75HLldc9bG6c+fIW7oUAK/ZzyNXq5s6PEFoly41um7nZIPepGfW9lkczz2Oi40LX9zwBe5a9yucUWhNbGxVjHooip/eO8DZw9kc/SuF7tdb7+i1Ia+CsoPmUXaH60Utu9D6iKRdaDUakrADZL37LpJej93gwdgPH9akMQlCe2Qymji4KZl96xNqRtevnRBOeD8vZDIZkiTx2q7X2Jm6E61SyyfXf0KQY5ClwxaagWeQI4PvDOPfVXHs+tlc3+7VwTrr24u3nwOThE2oMzaB1hmjIFyOSNob6fTp02zbto2srKyLRuBffvllC0UlVCvdvZviLX+CQoHXnOet/qNbQbB2/x1dD+7mzrCJ5tH1ap8c/oTfzvyGQqbg/aHv082jm6XCFVpA12F+pJ0u4MzBrKr+7X3R2FlXfbuxsJLS/ZmAqGUXWi+RtDfCV199xaOPPoq7uzve3t61EkKZTCaSdguTDAYy33oLAJd778UmNNTCEQlC0zHojBj0JowGE8bqfw3Sf36+4Esv1frZVL3/RfvWsU1//txFOeXnR9fHhxHev/Zr34+xP/Ll0S8BeGnASwzxH2KpX5HQQmQyGcPv60T2uWKKssvZ+s1Jbnq0q1UNkhRvTwGjhDrYEZsQZ0uHIwgNIpL2RnjjjTd48803mT17tqVDEeqQv2oVlafjUTg74zH9cUuHIwiNUlpYSVpcASlx+aTG5lOYVW6xWIK7ujFsYifsnG1qbd+avJU397wJwGPdH+PO8DstEZ5gATZaJaMfimLNu/tJPJrDka3n6DHCOurGjcU6SvaaF9xzFLXsQismkvZGyM/PZ9y4cZYOQ6iDIT+f7I8+BsDjyRkoLmjPKQitQXmJjtTYAlKrkvT8jLK6d5SBQimv+pKhUMkv+LnqSyW7eJuyalv1/qrat8lrHX9+f42dCldfu4tGUQ9nHWb2P7MxSSbuDLuT/3X/Xwv8lgRr4hHowLXjwtj+fRy7fz6Dd4gT3iGWf+0t/jcFDCbUAQ7YhDpbOhxBaDCRtDfCuHHj2Lx5M//7X+MvTp9++invvfceGRkZdO/enY8//ph+/frVue9XX33Ft99+W9MTvnfv3rz11luX3L89yvnkU0yFhdhEROA8fnyL378kSWQlFnMqOh1duQE3f3s8AhxwD7BHa982u9dIkkRpQSXlxXoUSjlK9flEUKk2/2tNH5dbm4pSPWmnC0iNzSc1Lp/c1NKL9nHzt8c/3AW/CGe8Ozqh1iqRy2UW/72eLTzL9L+mU2msZKj/UOYOmGvxmATL6DLEj9TTBcTvN9e3T5jbz6L17cZSPaXR6YC5Y4z4uxRaM5G0X6WPPvqo5vvQ0FBeeukloqOj6dq1KypV7RemGTNm1Oucq1atYtasWSxatIj+/fuzYMECRo0aRWxsLJ6enhft//fff3PPPfcwaNAgNBoN8+bNY+TIkRw/fhw/P7/GPcA2oCIujvwffgDAa84cZIqGdZ1p0H2X6Indk8HJXWm1k669mTXf2rvY4O5vj3tVEu/u74Cju6ZVXUwqSvTkppWQl1ZKblopeakl5KaVois3XPa4CxN4hUqOUnXhvwqUF2wzb1dcvN8FbwiUSgUKtRylUo5aq8TBTYONrbJV/C515QbSTp8vd8lJKQGp9j6uvnb4VSXpfmEuaOyta3IfQHZZNo9ueZTCykK6uXfj3SHvopSLS0t7JZPJGD6xE9nJxRRmlbN12QluerQbMrllnpMlO1KRdCZUfvZoIlwsEoMgNBWZJEnSlXcTqnXo0KFe+8lkMs6ePVuvffv370/fvn355JNPAHMf+ICAAJ544gmef/75Kx5vNBpxcXHhk08+YXI9Fw4qKirCycmJwsJCHB3bTusrSZJInjaNst3ROIwcif9HC5v/Pk0SKXH5nNyRxpnD2ZgM5qeUQiWnYy8PXLzsyEkpIedcMYXZddchqzWKWkm8R6A9Lt52KJTyZo//cvSVxqrE3Jyg56WVkJtaSlmRrs79ZXIZWgcVJoOEQW+eKPnfRLS5qTUKHNy1OLppcHTT4uCuwbHqZwc3DWqNZRJKXYWB9DOF5pH02Hyyk4v576uvs5ctfhEu+IU74xfugq2jdX8qU6Ir4f6N9xObH0uQYxDf3vgtrhpXS4clWIHsc8X8NO8ARoOJgXd0pNfIlm/5aSrTkz5vH1KlEbdJndFGtY11Atrq9Vu4MjEccpUSEhKa9Hw6nY4DBw4wZ86cmm1yuZwRI0awe/fuep2jrKwMvV6Pq+ulL5aVlZVUVlbW/FxUVNTwoK1YydatlO2ORqZW4/ncs817X/mVnNqdzsldaRTlVNRsdw+wJ3KwL+H9vC5a1ltXYahK4M1JfE5KCblpJegqjKSdLiDtdEHNvnKFDFdfO3MyX11e42+PWtv0T1ujwURBZpk5OU+tGj1PK6n1uP7LwU2Dm68drr72uPra4eZnj4uXLQrV+TcakiRhMkkYdaaaTicGndH8r95k3l7VncSgN5q7lOjNtxku+N683Xh+W/UxOlPNuSpK9ZQX6dBVGMlNKSE3paTOuDX2qqoEXovjfxJ6BzcNSlXTfDJj0BlJP1udpBeQlViEyVQ7S3f00OIf7lyVqLtcNLHTmumNemb+PZPY/FjcNG58PuJzkbALNTwCHLh2Qhh/r4gl+tez+IQ44dPC9eQlu9KQKo0ovWzRRLq16H0LQnMQSXsTMhqNHDt2jKCgIFxc6vcxXE5ODkajES8vr1rbvby8OHXqVL3OMXv2bHx9fRkxYsQl93n77bd57bXX6nW+1spUWUnmvHcBcJ02FbW/f5Pfh9FoIulYLid2ppEck1szUqrWKgnv60XkNb54BF56aWy1RolvqDO+F1y8jEYT+ell5KQUk5NcYv43pYTKMkNVcl87+XT00OLhb28elQ9wwN3fATtndb1KQiSTRFFuObmpVaPmaaXkpZVSkFF2UUJZTeuorkrO7XCrStBdfe3qNWItk8lQKGQotHLU2ivu3mgGnZGi3AqKcsopzq2gKLeC4pxy87bccipLDVSU6Kko0ZOVVFznOeyc1Di6a3Fw09T619FNg72LDXJF3Z9+GPUmMhKqkvS4AjISCms+danm4Koxl7pUJekOrpom/x20BJNk4qVdL7EnfQ+2Sls+G/EZAQ6i97VQW+Q1vqTGFXB6XyabFx9n/It9W2xOj6nCQPHONAAcrwu0WHmOIDQlkbQ3wsyZM+natSsPPPAARqORIUOGsHv3bmxtbVm3bh3Dhg1r9hjeeecdfvjhB/7++280mksnAHPmzGHWrFk1PxcVFREQ0LYusnnffIv+3DmUnp64P/RQk567ILOMEzvTOBWdQfkFpSG+Yc5EDvYhpJcnKnXDRmgVCrm5xt3fHgaYt0mSRHFuBTnnSshOKa4ZmS/Jr6Qou5yi7HLOHMquOYfWQXVRnbxaoyQvvXbdeV56KQadqc441BqFedTcz65mBN3N1w6tg3WXaFxIqVbg6mOHq49dnbdXlhsozi2nKKfCnNRXJfTV2/SVRkoLdZQW6kg/U3jR8TK5DHsXG/PIfFUiD5iT9LOFGPW1f7d2zjbmJD3cBf8IFxzdW+CdSwtYcHAB68+uRylT8uGwD4l0i7R0SIIVkslkDJsYQXZyMQWZZfy59CRjHm+Z+vaS6HSkcgNKDy3arm2jLEYQRNLeCGvWrGHSpEkArF27lsTERE6dOsV3333Hiy++yM6dO694Dnd3dxQKBZmZmbW2Z2Zm4u3tfdlj33//fd555x3+/PNPunW7/IqDNjY22Ni0no/er5Y+M4ucRYsA8HzmaeR2dSdtV3VOnZGzB7M4sTO9VtmK1kFFp4E+dB7kg4t34++nLjKZzDy6664lpKdHzfaKEn2tJD4npYT89FLKi/WcO5nPuZP5Vzy3QinHxce21qi5m5899i42rWICZ2PYaJXY+Js/nfgvSZKoKNVfnNDXJPYVGA0miqu+J67gonNoHdW1yl2cPLVt7ne64uQKlsYsBeC1wa8xyG+QhSMSrJlao2TUQ1Gsmbef5OO5HNycRO/Rwc16nyadkZJ/UwBwGB4gRtmFNkMk7Y2Qk5NTk1hv2LCBcePGER4ezrRp01i4sH4TINVqNb1792br1q3cdtttgHki6tatW5k+ffolj3v33Xd588032bRpE3369Gn0Y2nN9KmppDw5E6msDG2PHjiOHduo82UnF3NiZxpxezNruqHIZBAY5UbkYF+CurqhuESJRHPT2KsI6ORKQKfztcMGnZHctFJzEn/ufHmNUW/CydPWPGruZ19T4uLkob1kiUd7JpPJ0Nqr0dqr8Qq+eHKXZJIoK9JdNDpv0Jvw6eiEX4QLLt62bSpJlySJ3IpcYvNiicuP42TeSTYmbATgyV5PckvHWywcodAauPvbM2RCONuWn2LP7wn4dHTGN8y52e6vdE86plIDCjcNtt0v7sAmCK2VSNobwcvLixMnTuDj48PGjRv5/PPPAfPEUMVVtBmcNWsWU6ZMoU+fPvTr148FCxZQWlrK1KlTAZg8eTJ+fn68/fbbAMybN4+XX36ZlStXEhwcTEaGeaU3e3t77O3tm/hRWreSf/8l7ZlnMRYWIndywvvVVxqUNFWW6Ynbm8mJnWm1asgd3TV0HuRDp4E+2LtYZ/2xUq3AK9ixVqJpMklIJsni3WfaEplchp2zDXbONviEWjqapqcz6jhbeJa4/LiaJD0uP468iryL9r074m4eiHrAAlEKrVXnwT6kns4nbk8mm782929vjtI7SW+k+B/zKLvjsABkirbzJloQRNLeCFOnTmX8+PH4+Pggk8lqJoLu2bOHTp061fs8EyZMIDs7m5dffpmMjAx69OjBxo0bayanJicnI5efT74+//xzdDodd911V63zvPLKK7z66quNf2CtgGQ0kvPpZ+R8/jlIEpqoKPwXLkB1FX3qJUki7XQBJ3emE38wq6YeWa6U0bGHB52v8cU/3KVVfrQql8ugFcYtND9JksgpzzEn5/mxNUl6YmEiBuniPvtymZwgxyDCXcKJcIkgyj2KAT4D2tQnCkLzk8lkDL0nguykYvIzyvhz6QnGTO/e5K+vpfsyMRXrUTjbYNtTjLILbYvo095Ia9as4dy5c4wbNw7/qm4l33zzDc7Oztx6660Wju7SWnOfV0N+PmnPPEtp1ZwB53vuxmvOHOTq+o3alBZWEhudwYmdaRRmne+b7uprR+Q1vkT087bKRWwE4WpVj55Xj5zH5sdyOv90naPnAA5qByJcIohwjahJ0kOcQ9Aq28YEWsHyclNLWPPOfgx6E/1vDaHPjcFNdm7JYCLjvX0YC3U439YR+wG+TXZua9Kar99C44ikvZ1qrU/68sOHSZn5FIaMDGRaLT6vvYrTLVeuqzUZTSSfyOPEjjQSj+UiVbU3VNkoCOvrReRgXzyDHcToodAqNXb0PNwlnAjXCLxsvcRzQGh2J3el8de3p5DJ4NaneuIX3jQrlZbsSafgl3jkDmp8nuuLTNU2ywNb6/VbaDxRHtNIpaWlbN++neTkZHS62qtEzpgxw0JRtT2SJJG/YiWZ8+aBXo86OBi/jxaiCQ+/7HFGg4lTu9M58EcSxXnnFwryDnEi8hofOvbytNgKmYLQULnluexM20lsXuwVR88d1Y41I+di9FywBp0H+ZIWV8Cp6Aw2Lz7OhBf7NXr1X8loovjvcwA4DPVvswm70L6JbKURDh06xE033URZWRmlpaW4urqSk5ODra0tnp6eImlvIqbSUtJfepmiDRsAcBg1Cp8330BxmUm3RqOJ2N0Z7N+QWJOsa+xVRAzwJnKQL66+zdOqURCaiyRJHMw6yKrYVWxJ2oLBVHsEvXr0/MLylnCXcDF6LlilIfdEkJlUTH56KVuWHGfsjB7muTgNVHYoG2N+JXJ7FXb9Lt8uWRBaK5G0N8JTTz3F2LFjWbRoEU5OTkRHR6NSqZg0aRJPPvmkpcNrEyrPnCFlxpPozpwBpRKvZ5/BZfLkSyYhRqOJ2OgMDvyRSFGOOVm3dVTTa1QQXa71RdnABZAEwVJKdCWsO7uOVbGriC+Ir9ne2bUzvbx61ZS3dHTuiEZpnR2OBOG/VDYKRj8Uxep39pFyKp8DfyTS9+YODTqXZJLOj7Jf64dcvM4LbZRI2hvh8OHDfPHFF8jlchQKBZWVlYSEhPDuu+8yZcoU7rjjDkuH2KoVrl9P+ksvI5WVofT0xG/Bh9j26lXnviajidg9mezfkFCTrGsd1fQaGUjUED+RrAutTmxeLD/G/si6s+soM5QBoFFouDnkZsZHjBerkAqtnquvHUPvjWDrspPsW5eAvYsNYX29UKqu7vW6/Gg2hpxy5LZK7Ab4NFO0gmB5ImlvBJVKVdOK0dPTk+TkZDp37oyTkxPnzp2zcHStl6TTkTnvXfJXrADAdsAA/D54H6Wb20X7mowm4vZlsn99IoXZ5k4wWgcVPUcGETXUD5VI1oVWRGfUsTlpMz/G/sihrEM12zs4dWBCxATGdhyLo1pMPBPajk4DfEiLK+DkF2q0QAAALSNJREFUrnT++vYUO1bHE9rLg/D+3viGOl+xJaRkkij6y3y9tR/sh9xGpDVC2yX+uhuhZ8+e7Nu3j7CwMIYOHcrLL79MTk4O3333HVFRUZYOr1XSp6eTMnMmFUeOAuD2v0fweOIJZP9ZrMpkkji9L5P9GxIpyKwahbRX0XNkIF2H+qOyEcm60HqkFKewOm41v5z+hfzKfACUMiXXBV7HhIgJ9PXuK+rShTZryD3h2DnbcGp3OiX5lZzYmc6Jnek4uGoI7+dFeH9vXH3qnodUfjwHQ1YZMo0C+8Fts8WjIFQTLR8bYf/+/RQXFzN8+HCysrKYPHkyu3btIiwsjCVLltC9e3dLh3hJ1tgyqmTHTtKeeQZjQQFyJyd8572Dw7BhtfYxmSTi92eyb/0FybqdOVmPGuonOsEIrYbRZGRH6g5Wxa5iR+oOJMwvxV62XtwVfhd3ht2Jh62HhaMUhJYjmcwL3sXuyeDMwSx0Fcaa2zwCHYjo701YX6+aTjOSJJH10SH06aU4XBeA08hgC0Xesqzx+i20DJG0t1PW9KSXTCZyPv+cnE8+Na9uGhmJ30cLUVctVmXeRyL+QBb71ieQn2FO1m3slPS8IZCuw/xFsi60GrnlufwS/wurY1eTVppWs32Q7yDGR4xnqP9QlHLx9yy0bwadkYSjOcTtzSQ5JhdT1doaMrmMgM4uRPT3xsdGQeH3p5CpFfg83xe5bftYFM+art9CyxJXhgYoLy9ny5YtDB8+HAcHh1q3FRUV8ffffzNq1ChsbGwsFGHrYcjPJ+252ZT++y8AzhMm4PXCHORVvzvJJBF/MIt96xPJTy8FwMZWSY8RgXQb7o9aK/6EBetX067x1Cq2JJ9v1+ioduT20NsZFzGOIMcgC0cpCNZDqVYQ1seLsD5elBfrOL0/i7i9GWQmFJF8PI/k43kMdVTiLJdhCncBMXAjtANipL0BFi5cyO+//87WrVvrvH3EiBHcfvvtPP744y0cWf1Zwzv18qNHSZk5E0NaOjKNBu9XX8H5ttsAc7J+5lA2+9YnkJd2Plnvfn0A3a4LwEYk60IrcKl2jd3cuzE+YjyjgkeJNo2CcBUKMsuI3ZtBzu50uptMGCSJLUUGVI5qwvp5E9HfC3d/hyufqBWzhuu3YBkiaW+Afv368dJLLzF27Ng6b1+3bh2vv/46e/fubeHI6s+ST3pJksj//nsy337HvLppUBB+H32EJiIcySRx9kg2+9YlkptaAoBaa07Wu1/nj007+fhTaN3qateoVWq5qcNNol2jIDSSJElkLzqCLqmYAnct0WllVJadX2zMzc+O8H7ehPfzwt6l7b0pFkl7+yWGKxvg9OnTl51k2q1bN06fPt2CEbUeprIy0l95laK1awFwuOEGfN56E7m9PWcPm0fWc86Zk3WVRkH36wLofn0AGjuRrAvWrbpd46pTqzicfbhmu2jXKAhNq/JMIbqkYlDK6PxwNzprlSQdzyV2TwaJx3LITS1l9y9n2P3rGfwjXAjv503HXh5i7pPQ6om/4AYwGAxkZ2cTGBhY5+3Z2dkYDIY6b2vPKs+eJWXGDHTxZ0ChwPOZZ3CZMpmkY7nsXXfyfLJuo6Dbdf70GBEoknXB6p0rPsfquNX8evpX0a5REFpA8V/JANj19UZR1UkmpIcHIT08qCjVc+ZgFrF7MkiPLyTlVD4pp/L55/tYOnR3J7y/NwGRrigUcks+BEFoEJG0N0CXLl34888/6d27d523b968mS5durRwVNat6I8/SH9xLqayMpQeHvh+OJ9sdTBb3zlAdnIxYE7Wuw73p+eIQDT2IlkXrNuhrEMsiVnC9nPbRbtGQWghlYmFVJ4tBIUMh6EBF92usVPR5Vo/ulzrR1FOOXF7M4ndk0FBZhmn92dxen8WWgcVYX3M/d89gxzEm2qh1RBJewNMmzaNWbNm0aVLF8aMGVPrtrVr1/Lmm28yf/58C0VnXSSdjsz33yf/2+8A0Pbvj+mRl9mwJZesJPMCSkobBd2G+dHjhkC09mpLhisIl2WSTGw/t52lx5fWWrFUtGsUhJZRvfqpXW8vlM6X79Dm6K6lz03B9L4xiKykYuL2ZHB6fyblxXqObkvh6LYUnL1siejvTe/RQVdcfVUQLE1cXRrg4Ycf5p9//uGWW26hU6dOREREAHDq1Cni4uIYP348Dz/8sIWjtDx9RgapM5+i/PBhAJRTZrBf1ofMbxPNP6vldB3qT8+RgWgdRLIuWC+9Uc/6hPUsi1nGmcIzAKjkKm7peAuTu0wmxCnEwhEKQtunO1dMZVw+yMFhqP+VD6gik8nwCnbEK9iRQXeFcu5EHnF7Mjh7JIeCzDKSYnLpc1Nw8wUuCE1EJO0NtHz5cm655RZWrlxJXFwckiQRERHBa6+9xvjx4y0dnsWV7tpF6tPPYMzPR+7ggO+8ech6DmLrS7tRquREDfWj58igmpXtBMEalepLWRO3hm9PfEtWWRYA9ip7xkWMY1LnSXjaelo4QkFoP4qqatlte3iidNM26BwKhZzgru4Ed3VHV27gzKFstKIcU2glRMvHdqq5WkZJJhO5X3xB9kcfgyRhE9kZ/4ULUQeYaw/PHsrGK8QROyex8JRgvXLKc1hxcgWrTq2iWG+ec+Gh9WBS5CTGhY/DQd22+0ALgrXRpZWQ9dEhkIHXrN6oPGwtHZLFiJaP7ZcYaW8EhUJBeno6np61R9tyc3Px9PTEaDRaKDLLkIxGUh57nJLt2wFwHncXXnPn1qxuChDSU0zOE6xXUlESy44v4/f439GZdAAEOwYzNWoqY0LGoFaIT4YEwRKqO8Zou3m064RdaN9E0t4Il/qQorKyErW6/V3cZQoFNuFhlEZH4/3KKzjfcbulQxKEejmec5zFMYv5M+nPmk4w3Ty6MS1qGsMDhiOXifZwgmAp+sxSymNyAXAcfnHHGEFoL0TS3gAfffQRYJ7c8vXXX2Nvb19zm9Fo5J9//qFTp06WCs+iPJ58Eqfbb8cmREzME6ybJEnsStvFkpgl7M04v3rxEP8hTO0yld5evUUrOKHdMpbqkSoMKFw1Fn8eVHeM0XZxQ+VtZ9FYBMGSRNLeAB9++CFgvugvWrQIhUJRc5tarSY4OJhFixZZKjyLkimVImEXrJrBZGBT4iaWxiwlNj8WMC+GdGOHG7k/6n7CXcItHKEgtDxJkjBklVF+Mo+KU3nokopAAqWbBk0Xd7Rd3FAHOLR4W0R9dhnlR7MBcLiu7gUNBaG9EEl7AyQkJAAwfPhwfv75Z1xcXCwckSAIV1JuKOeX07/w7YlvSS1JBUCr1HJn2J1MjpyMj72PhSMUhJYlGUxUJhZScTKP8pN5GPMqau8gl2HIraDknxRK/klB7qBGG+mKtos7NiFOyJTNXzZWvO0cSKDp5Iraz/7KBwhCGyaS9kbYtm0bADqdjoSEBDp27IhSKX6lgmBN8ivy+eHUD6w8tZKCygIAXDWu3NvpXu7udDdONk6WDVAQWpCxVE9FbB4VJ/OoiMtHqrygYYJChk1HZ7SdXdF0ckVuq6QiNp/y47lUnMrDVKyjdE8GpXsykGkUaDu5ounijibcBbmN4tJ32kCG3HLKDptbrTpeL0bZBUFkmI1QXl7O9OnT+eabbwCIi4sjJCSEJ554Aj8/P55//nkLRygI7VdqSSrfHv+WX+J/odxQDoC/vT/3d7mfW0NvRaPUWDhCQWh+lyp7qSa3V6GJcEXb2RWbsIuTb9tuHth28zCPyp8poPx4LuUncjGV6Ck7nE3Z4WxQytGEOaPt4oamsxsKu6bpe168PQVMYBPmjDpAtFkVBJG0N8Lzzz/PkSNH+Pvvvxk9enTN9hEjRvDqq6+KpF0QLCA2L5YlMUvYlLgJo2QeRezs2plpUdMYETQCpVy87Altm2QwUZlQSMWpusteVN52aDq7ounsitq/fnXqMqUcTYQrmghXnG8LRZdcZE7gj+dizKswj9yfzAP5aWyCndB0cUPbxR2lc8PW5DAUVFB6IBMQo+yCUE1cvRrh119/ZdWqVQwYMKDW7PouXbpw5swZC0YmCO2LJEnsy9jHkuNL2Jm6s2b7AJ8BTIuaxgCfARbvgCEIzelqyl6ULo37lEkml2ET7IRNsBNON3VAn1FGxfEcyo/nok8vpfJsIZVnCylcexaVnz3aLm5ou7ih9LSt9/OweHsKGCVsQsz3IwiCSNobJTs7+6KFlQBKS0tFgiAITaxEV0JqSSppJWmklaaRWpJKekk6qSWppJakUqQrAkAukzMyaCRTo6YS6RZp4agFoXnUKns5mYcu+erKXpqKTCZD7WOH2scOxxFBGPIqKK9K4HVJRehTS9CnllC0OQmlu9ZcQtPF7bIj/MYiHaX7MgDRMUYQLiSS9kbo06cP69ev54knngCoSdS//vprBg4caMnQBKHVKdYVmxPyC5Ly6p8vTMovxUZhw22htzElcgoBjmIBFqHtaY6yl6amdNXgcK0/Dtf6YyzWUX4yl4rjuVTEF2DIKad4ewrF21NQOKrRRJpH4G1CnJApzneiKf4nBQwS6iBHbDqKUXZBqCaS9kZ46623uPHGGzlx4gQGg4GFCxdy4sQJdu3axfbt2y0dniBYleqkvDoZv3DUPO3/27vz6KjK+3/g77vMTGYmk4UtG2H7lqAVJUAFsQUPCuJRLFZtKWoD1F1sVaoo1op4qhFEa1V6rG0FrSKCtSicCqUJ0J8YeyRsQSCgiLRmY0sy2Wa59/n9MUtmCyRhtpD365w5997nPvfe55OBmc8888xzm6rOmpQDQIYpA7mpuchLzUOuNbd9PTUXA20DYVbNcYiEKPaEEBAODVqjE87/2c8+7OXCPlAzkuvH1YrNiNRxOUgdlwO9zY22ylPemWhOe3rTP6tG82fVkMwqzBf08dw8KTcVzf+pBgCkXZnPb62JAkhCCHH2atSRr776Cs899xz27NmDpqYmjBkzBo8++iguvvjiRDftjBobG5Geno6GhgakpaUlujnUwwkh0OhsRHVzdVDveGCvud1pP+t5Mk2ZyE0NTsYDE3SLwRKHaIhiR+gCerMLWqMTWpMTeqMTmt3z0O1OaHaXf1249LDj5VQDUi7wDnv5TuyGvcSScOlo+6oerftOoO3AKejNrrA6hoGpGDCvkEl7BHz/7r2YtPdS/E9PXeHSXahrqUN1UzWqm0Me3jLftIpn0ieljz8BD0rOmZRTDydcekjy7fQk5oHbdhf0JmfQ2POzkUwK1H5mpBRkJnTYS6wIXcB5tNE/Dl6rdwAA+s7+LswX9k1w65IT3797Lw6P6QZZls/66V+SJLjd7ji1iKj7hBCwu+yobqpGTXMNqpqrUN1cjZqmGk/PeXMVjrcch+hEptEnpQ9yrDnIS83z95T7EvMcaw6TcupRhBDQW9zQm7wJt729V1yze3vJm5zQGl0QbV14vZcA2WqAYjNCSTNCTvUsFZsRss0IxWbwr8vGnteT3hWSLHlmiBmWjvTpw+CqaoZwaZwxhigCJu3d8Pe//73DfWVlZXj55Zeh6+FfaxIlglt343jL8Yi9475Hs6v5rOcxyAbkWHOQk5rjWfoe3u0sSxZvWERJTwgB4dTak/AmJ3TvkBTN7oTe5PKWOaE1uQCtC93iquRJxP3Jt7E9MQ/Ylq0GSMr501seLZIkwZiXmuhmECUtJu3dMGPGjLCyyspKPPbYY1i/fj1uvfVWPP300wloGSUrIQQ0ocGlu+DUnHDpLrg0V9C2U3fCpXmWbt3tXw+qE3JsxH3e/b4x5nUtdf6bDJ1JpikT2dZs5KbmIseag2xrNnKsOchNzUW2NRt9UvpAluSznocoEYRL9/R6271JeEDi7R+i0uTqcKz4mcgWFXKqAUqqEXJaQDLuS869ZVKKwjHYRBQzTNrPUVVVFRYtWoQ333wT06ZNw+7duzFy5MhEN4s6QRc6HJoDDrcDbVobWt2tcGgOtLnb0Ka1BS3PVMehOYLKQ+s4NSecmrNTw0tiRZVVZFuyw3vJvT3l2dZszrxCUSOEAHRAaDqgiYBlx+vQdG9Z+7rv2NB10eYOGiOu2bs4PAWAZFSg2Az+HnBfUu5JxL0Jus0IJdUASeWH1WgRQkAIz7B+IYR3CUgSYFD4dyY6Eybt3dTQ0IBnn30Wr7zyCgoLC1FSUoKJEycmullJrc3dhp21O+EWbrj19ocmNE/Psu7yr/sfAXU1XQs71ret6d5zCJd/PfTcLt0VlFA7NEdC/x5G2QiDYvAsZQMMigEG2QCj4t0OXA/Z5zvWVy/wPL46VqPVn5j3M/djLzl1m9AFtHoHXHUtcNe2wFXbDFddC/QWd0iy3Z6IJ4QqBSXb/p7wkPXeMFa8s9pcGr6oasDOb+pR/s1p7KtqQJtLi5hY+9YRaR/ak3GEbOveRP1sBthMGJFtw4gsGwq8y+FZqbAYmaoQAUzau2Xp0qVYsmQJsrOz8e6770YcLkPhTrWdwt3/ujvRzYjIIBuQoqYgRUlBipoCk2KCWTXDpJiCyjtTx1fuWzcppqBE3KAYoEoqv0anpCN0Aa3BAVdtcHLurmuBcJ7j73QUyTPriSJDUr3rquwZ2y1LkELWIUuQlMjrskkJ7iG3cXhKZ1U3tPoT9J3HTuOLqga4EvUhK0Sd3YE6uwP/7/AJf5kkAYP6WFCQZcMF2Tb/ckg/K3vmqdfhlI/dIMsyzGYzpkyZAkXpuLfmgw8+iGOruiYRU0adbD2JuzffDVVWocgKVEmFQTZ41mUVqqT69xlkg2dd8u4L2B9WHrrPez6DFHBuWYVRNnaYdCsye92o9xAitOfck6C761ohnB38/kGRoPYzw5BlgSHLCkOWBbLN2J5MK8GJeFhSzmQ67pxu3dOLfqweO71JenVDW1i9fqkmjBmUgTGDM1GYn4EMiwESJEgSIMGTOCNoW/KX++rBtx1hn/fwkHMG19OEwNGTzThUY8fBGjsO1XoeJ5qcEWMzKBL+r38qRngT+RFZNozItiEvwwz5PJoSMxJO+dh7sae9G4qKivgG1A19zX3x/g/fT3QziHoNIUJ7zlv8iXqnkvMBFqjeBF3tmxJ0q3lKPnWNbdh57LS3F70eFd82wOkO/oZEkSVcmGPDmEGZ/kd+H3NSvKf1sRoxZlBmUNmJJgcO1dhRWWtHpXd5qMaOZqeGg94EP5DVqGB4SK98QbYN/VJN8QyFKCbY095L8ZM60fnDk5w74a5t9vaadyc59/SgMznvGVyajgPVjdj5zWmUe3vSv60Pv8FZpsWAsYMzMdqboI/KT+/xY8R1XeDb+lYcqg1I5mvs+Op4U4dDffpaje298tk2/3qqqef9Lfj+3Xsxae+l+J+eKLGELiBcOoRTg3Bq0B2ad91TpnvLhUNvX/fub9/nWbpPOyAcHSTncuCwFm9yPsACtZ+ZyXkPcqLJ4U3QT2PXN/XY+2092kKmrpQloCDLhjGDMzF2UCbGDM7EkL6WpOhFjweXpuPoiWZ/b7xvmM03p1o6/CFsXoYZl/9fXzz/41Hxbew54Pt379XzPmISEUWJECJ4ykF34BSD7dsILHfrnnV3wDSEvv0u3ZNIuzwJte7UA5Jt77YvOe/iXOFnJUtQ+6V4essHWNqT9L5mTlnYQzjcGpodGpra3Djd4sTe/9X7h7ocO9USVj/dbMDoQRkYMygTYwdnYlR+Ro/sOY4WgyJjeJYNw7NswCXt5a1ODYfrPL3xh2rbk/naRge+rW9FTWP4OH+iZNR7/3cnmeXLl+P5559HTU0NRo0ahVdeeQXjxo3rsP7atWvxm9/8BkePHsXw4cOxZMkSXHvttXFscfLzzBXteQjdm2DpIniOaF14ki89fD5ofz3dm6gFLCPW68T5An55BUjeH3CFbstSUD1J6mi7gzpyhHMH/WHO9Ec7S8GZNiMdK4CAOeHan5fAMhFShvZjRGiZ3j7lHEKOEQFlQfN+u73PkTtCMp4Ms2ZIgGRQIJlkSEYFslGBZFQgGeX2dVPItrdMNniOUdKNTM4TJDDRbnJ4Hs0ON+zepa+82dG+31+nzY1mp6dOs0ODUzvzB7mCrNT2seiDMzGsn/W8/9FlNJiNCi4ZmIFLBmYElde3OHGotgn8E1JPwaQ9Cbz33nuYP38+XnvtNYwfPx4vvfQSpk2bhsrKSgwYMCCs/qeffopZs2ahuLgY06dPx6pVq3DDDTdg586dPebGTi17j8NxpCEgkfYmxb6EODTRjpR4h9YPPE7XgSh3ZFIvIKF9SkLFO/OJbzYURQYilftmTlFkSAa5PZk2KWFJuGQK2TYqnmN6yfCFRHJpOlqcGtpcGlqcGlqc7oB1LWi91elGq8u3rp1Tot0dZoOC1BQVF2Tb/Al6YX4G0s2GqF+rN8uwGDFuaJ9EN4Oo0zimPQmMHz8el156KV599VUAgK7ryM/Pxy9+8Qs89thjYfVnzpyJ5uZmbNiwwV922WWXobCwEK+99lqnrpnoMXH1679C0/aquF8XgH/OZ0+i5Z03OmRuaCgShCxBA+CCgEsATiHgFAIOXUebLtCq6WjVdLRoOlrcOprdGppcGhxCwA3A7T3WDUAD4PZ2NUsALKoCi1GB1aDCYpBhMXi2LaqCFO+22bueoihIUWXvQ4EiIbwnWnRU5vkQc9ak8Ey7u3JsaFXvtG4I/XbAtwj7FiHgegHHhH27gMjfOPiv50uiVSkoCYcSMB94YDLuK49Tl5tb0+HUdDjdOhzu9qXDrQWV+dc1LayuKyBZ9EYd9MVK+58xOCZ/ecAxnu32/aH72o+V/HUDj5MkKfhLnYDp/HzXCm2bBO9zHnDu8Ke/fTpBIeBPrFtdnmTas+72J9eBibZn3e1fj/Vc5L5EO9XkeVhNSsC66tln9CytJhU2b7nVpMLmLUs1qbAaFaj8rQGdQaLfvylx2NOeYE6nE+Xl5Vi4cKG/TJZlTJkyBWVlZRGPKSsrw/z584PKpk2bhnXr1nV4HYfDAYej/Q6gjY2N59bwc5RSkAnZrLYnybLvxiu+9YB5nwP2BS0Dj1Ui1PNu65KEk21ONLa5cbrVhfpWF+pbXKhvdeJ0i3e9xYnTLU7PerNnX+iPvLrCqMjIsBiQaTFBkoCGVhcaWl1o8c3k4XZ7svru/O0MMtLNBv8jLcW7NAcujTAbFLh1HW5NeJa6gKYLuDQBTde9SwG3FmGfLqBpAi5d99Tx1muvI+DybeueYzzXEdC9Q1h8d0H03xnRmzOFlSP4bothd2Ds4O6Lum9oDdrvtijLEhRJgiJL8NzHx7cevAzcH1zmXZckyDIilAWcX5Y8H6AAODUdDpcnEXcEJd1a+3pAHU1nX0miKLIEi0FBitHzQdns/cBsNiowG1TPh2ejghTfB2mj4k+oU03tSXdgQs5Em4jigUl7gp04cQKapiErKyuoPCsrCwcPHox4TE1NTcT6NTU1HV6nuLgYixcvPvcGR0nKiD5IGRGfryVrG1oxYcmWbh2ryBIyLZ5kONNiRIbFgAyLERlmAzKtRn95psWAdEt7HbMh8p0ZXZqORm8C73s0trk9S19ZiwuNbSF1Wl2wO9ze3kYdbS4HahsdEVrcy/XAZFiRJRgVGSaDDKMiw6jKMKkyjKriXXq2fetGRYZJVaAqnl7owCH/gG87YJx/4DK0HBH2h+0TYXVFaHnIh6ew29oHtCu4LPCDWMj1RHh7zQZPcu1JptWgxDpw3WxUg5JxX3JuNiowKhyOREQ9E5P2XmLhwoVBvfONjY3Iz89PYIviJ9NihCTBn2B7lt7k22JAhtmITGtwYu5bpprUqL7BGxQZfVNN6NuNG31oukCTN8H3JPvhiX3gtsOtw6BIUGQZqix5HooE1bft3eepI8GgyFB89WTZWzd4nyHkGF9dRZFgkOWgXuxId1GUQ4Y8+IZAyHLo3RI9x8pSB3dYjFAOAJrw9PRrumhfF55tXUf7ur9MhJQheL93GbQ/4Bq6N5s0BiXVwcm20b8eoVyR2UNLRESdwqQ9wfr16wdFUVBbWxtUXltbi+zs7IjHZGdnd6k+AJhMJphMvfOOcCkGBV8+cy2UHj5FgCJLSPf26BMREVHvwi6eBDMajRg7dixKSkr8Zbquo6SkBBMmTIh4zIQJE4LqA8DmzZs7rE/o8Qk7ERER9W7saU8C8+fPx+zZs/G9730P48aNw0svvYTm5mbMnTsXAFBUVIS8vDwUFxcDAB544AFcccUVeOGFF3Dddddh9erV2LFjB15//fVEhkFEREREMcKkPQnMnDkTx48fx5NPPomamhoUFhZi48aN/h+bHjt2DLLc/qXI5ZdfjlWrVuGJJ57A448/juHDh2PdunU9Zo52IiIiIuoaztPeS3GeVyIiop6H79+9F8e0ExERERElOSbtRERERERJjkk7EREREVGSY9JORERERJTkmLQTERERESU5Ju1EREREREmOSTsRERERUZJj0k5ERERElOSYtBMRERERJTkm7URERERESU5NdAMoMYQQADy3QyYiIqKewfe+7Xsfp96DSXsvZbfbAQD5+fkJbgkRERF1ld1uR3p6eqKbQXEkCX5U65V0XUdVVRVsNhskSYrbdRsbG5Gfn4///ve/SEtLi9t1E4Xxnt8Y7/mvt8XMeJOfEAJ2ux25ubmQZY5y7k3Y095LybKMgQMHJuz6aWlpPeYFMhoY7/mN8Z7/elvMjDe5sYe9d+JHNCIiIiKiJMeknYiIiIgoyTFpp7gymUxYtGgRTCZTopsSF4z3/MZ4z3+9LWbGS5S8+ENUIiIiIqIkx552IiIiIqIkx6SdiIiIiCjJMWknIiIiIkpyTNqJiIiIiJIck3bqkuLiYlx66aWw2WwYMGAAbrjhBlRWVgbVaWtrw7x589C3b1+kpqbipptuQm1tbVCdX/7ylxg7dixMJhMKCwsjXmvv3r2YOHEiUlJSkJ+fj6VLl8YqrDOKV8xbt27FjBkzkJOTA6vVisLCQrzzzjuxDC2ieD7HPl9++SVsNhsyMjKiHM3ZxTNeIQSWLVuGgoICmEwm5OXl4ZlnnolVaBHFM95Nmzbhsssug81mQ//+/XHTTTfh6NGjMYossmjEu2fPHsyaNQv5+fkwm8248MIL8fvf/z7sWlu3bsWYMWNgMpnwne98BytXrox1eGHiFe8HH3yAqVOnon///khLS8OECROwadOmuMQYKJ7Pr8/27duhqupZX9eIoo1JO3XJtm3bMG/ePHz22WfYvHkzXC4Xrr76ajQ3N/vrPPTQQ1i/fj3Wrl2Lbdu2oaqqCjfeeGPYuX7+859j5syZEa/T2NiIq6++GoMHD0Z5eTmef/55PPXUU3j99ddjFltH4hXzp59+iksuuQR/+9vfsHfvXsydOxdFRUXYsGFDzGKLJF7x+rhcLsyaNQsTJ06MeiydEc94H3jgAfz5z3/GsmXLcPDgQXz00UcYN25cTOLqSLzi/frrrzFjxgxceeWV2L17NzZt2oQTJ05EPE8sRSPe8vJyDBgwAG+//Ta++OIL/PrXv8bChQvx6quvBsV73XXXYfLkydi9ezcefPBB3HHHHXFPZOMV77///W9MnToV//jHP1BeXo7Jkyfj+uuvx65du87LeH3q6+tRVFSEq666Ki7xEQURROegrq5OABDbtm0TQghRX18vDAaDWLt2rb/OgQMHBABRVlYWdvyiRYvEqFGjwsr/8Ic/iMzMTOFwOPxljz76qBgxYkT0g+iiWMUcybXXXivmzp0blXZ3V6zjXbBggbjtttvEihUrRHp6erSb32Wxinf//v1CVVVx8ODBmLW9O2IV79q1a4WqqkLTNH/ZRx99JCRJEk6nM/qBdNK5xutz3333icmTJ/u3FyxYIC666KKgOjNnzhTTpk2LcgRdE6t4I/nud78rFi9eHJ2Gd1Os4505c6Z44oknuvQ6ThQt7Gmnc9LQ0AAA6NOnDwBPj4XL5cKUKVP8dS644AIMGjQIZWVlnT5vWVkZJk2aBKPR6C+bNm0aKisrcfr06Si1vntiFXNH1/JdJ1FiGW9paSnWrl2L5cuXR6/B5yhW8a5fvx7Dhg3Dhg0bMHToUAwZMgR33HEHTp06Fd0AuihW8Y4dOxayLGPFihXQNA0NDQ3461//iilTpsBgMEQ3iC6IVryh/zfLysqCzgF4XrPO9TXgXMUq3lC6rsNut583r1eR4l2xYgWOHDmCRYsWxaDlRGfHpJ26Tdd1PPjgg/j+97+PkSNHAgBqampgNBrDxiZnZWWhpqam0+euqalBVlZW2Dl8+xIlljGHWrNmDT7//HPMnTv3XJp8TmIZ78mTJzFnzhysXLkSaWlp0Wx2t8Uy3iNHjuCbb77B2rVr8dZbb2HlypUoLy/HzTffHM0QuiSW8Q4dOhT//Oc/8fjjj8NkMiEjIwP/+9//sGbNmmiG0CXRivfTTz/Fe++9h7vuustf1tFrVmNjI1pbW6MbSCfFMt5Qy5YtQ1NTE37yk59Erf1dFct4Dx8+jMceewxvv/02VFWNWQxEZ8J/edRt8+bNw759+/DJJ58kuilxE6+Yt2zZgrlz5+JPf/oTLrroophe60xiGe+dd96JW265BZMmTYr6ubsrlvHqug6Hw4G33noLBQUFAIC//OUvGDt2LCorKzFixIioX/NsYhlvTU0N7rzzTsyePRuzZs2C3W7Hk08+iZtvvhmbN2+GJElRv+bZRCPeffv2YcaMGVi0aBGuvvrqKLYu+uIV76pVq7B48WJ8+OGHGDBgQLevda5iFa+mabjllluwePFi//9dokRgTzt1y/33348NGzZgy5YtGDhwoL88OzsbTqcT9fX1QfVra2uRnZ3d6fNnZ2eHzVbh2+7KeaIp1jH7bNu2Dddffz1+97vfoaio6Fyb3W2xjre0tBTLli2DqqpQVRW33347GhoaoKoq3njjjWiF0WmxjjcnJweqqga96V944YUAgGPHjp1b47sh1vEuX74c6enpWLp0KUaPHo1Jkybh7bffRklJCf7zn/9EK4xOi0a8+/fvx1VXXYW77roLTzzxRNC+jl6z0tLSYDaboxtMJ8Q6Xp/Vq1fjjjvuwJo1a8KGB8VTLOO12+3YsWMH7r//fv/r1dNPP409e/ZAVVWUlpbGNDYiv0QPqqeeRdd1MW/ePJGbmysOHToUtt/3o5/333/fX3bw4MFu/xA18AdrCxcuTMgPUeMVsxBCbNmyRVitVvHqq69Grf1dFa949+/fLyoqKvyP3/72t8Jms4mKigpx6tSpqMZ0JvGKd9OmTQKA+PLLL/1lu3fvFgBEZWVldILphHjFO3/+fDFu3LigsqqqKgFAbN++/dwD6aRoxbtv3z4xYMAA8cgjj0S8zoIFC8TIkSODymbNmhX3H6LGK14hhFi1apVISUkR69ati24QXRCPeDVNC3qtqqioEPfee68YMWKEqKioEE1NTbEJjigEk3bqknvvvVekp6eLrVu3iurqav+jpaXFX+eee+4RgwYNEqWlpWLHjh1iwoQJYsKECUHnOXz4sNi1a5e4++67RUFBgdi1a5fYtWuXf7aY+vp6kZWVJX72s5+Jffv2idWrVwuLxSL++Mc/xjVeIeIXc2lpqbBYLGLhwoVB1zl58uR5GW+oRM0eE694NU0TY8aMEZMmTRI7d+4UO3bsEOPHjxdTp049L+MtKSkRkiSJxYsXi0OHDony8nIxbdo0MXjw4KBr9YR4KyoqRP/+/cVtt90WdI66ujp/nSNHjgiLxSIeeeQRceDAAbF8+XKhKIrYuHFj3GKNZ7zvvPOOUFVVLF++PKhOfX39eRlvKM4eQ4nApJ26BEDEx4oVK/x1WltbxX333ScyMzOFxWIRP/rRj0R1dXXQea644oqI5/n666/9dfbs2SN+8IMfCJPJJPLy8sRzzz0XpyiDxSvm2bNnR9x/xRVXxC9YEd/nOFCikvZ4xvvtt9+KG2+8UaSmpoqsrCwxZ86cuH8oi2e87777rhg9erSwWq2if//+4oc//KE4cOBAnCL1iEa8ixYtiniOwYMHB11ry5YtorCwUBiNRjFs2LCga8RLvOLt6PmfPXt2/IIV8X1+AzFpp0SQhBAi4rgZIiIiIiJKCvwhKhERERFRkmPSTkRERESU5Ji0ExERERElOSbtRERERERJjkk7EREREVGSY9JORERERJTkmLQTERERESU5Ju1EREREREmOSTsRUZKaM2cOJEmCJEkwGAzIysrC1KlT8cYbb0DX9U6fZ+XKlcjIyIhdQ4mIKOaYtBMRJbFrrrkG1dXVOHr0KD7++GNMnjwZDzzwAKZPnw63253o5hERUZwwaSciSmImkwnZ2dnIy8vDmDFj8Pjjj+PDDz/Exx9/jJUrVwIAXnzxRVx88cWwWq3Iz8/Hfffdh6amJgDA1q1bMXfuXDQ0NPh77Z966ikAgMPhwMMPP4y8vDxYrVaMHz8eW7duTUygRER0RkzaiYh6mCuvvBKjRo3CBx98AACQZRkvv/wyvvjiC7z55psoLS3FggULAACXX345XnrpJaSlpaG6uhrV1dV4+OGHAQD3338/ysrKsHr1auzduxc//vGPcc011+Dw4cMJi42IiCKThBAi0Y0gIqJwc+bMQX19PdatWxe276c//Sn27t2L/fv3h+17//33cc899+DEiRMAPGPaH3zwQdTX1/vrHDt2DMOGDcOxY8eQm5vrL58yZQrGjRuHZ599NurxEBFR96mJbgAREXWdEAKSJAEA/vWvf6G4uBgHDx5EY2Mj3G432tra0NLSAovFEvH4iooKaJqGgoKCoHKHw4G+ffvGvP1ERNQ1TNqJiHqgAwcOYOjQoTh69CimT5+Oe++9F8888wz69OmDTz75BLfffjucTmeHSXtTUxMURUF5eTkURQnal5qaGo8QiIioC5i0ExH1MKWlpaioqMBDDz2E8vJy6LqOF154AbLs+ZnSmjVrguobjUZomhZUNnr0aGiahrq6OkycODFubSciou5h0k5ElMQcDgdqamqgaRpqa2uxceNGFBcXY/r06SgqKsK+ffvgcrnwyiuv4Prrr8f27dvx2muvBZ1jyJAhaGpqQklJCUaNGgWLxYKCggLceuutKCoqwgsvvIDRo0fj+PHjKCkpwSWXXILrrrsuQRETEVEknD2GiCiJbdy4ETk5ORgyZAiuueYabNmyBS+//DI+/PBDKIqCUaNG4cUXX8SSJUswcuRIvPPOOyguLg46x+WXX4577rkHM2fORP/+/bF06VIAwIoVK1BUVIRf/epXGDFiBG644QZ8/vnnGDRoUCJCJSKiM+DsMURERERESY497URERERESY5JOxERERFRkmPSTkRERESU5Ji0ExERERElOSbtRERERERJjkk7EREREVGSY9JORERERJTkmLQTERERESU5Ju1EREREREmOSTsRERERUZJj0k5ERERElOSYtBMRERERJbn/D2Y7xZ0MOIjYAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "ciks_to_consider = [320193, 789019, 1652044, 1045810, 1018724, 2488, 50863] # Apple, Microsoft, Alphabet, nvidia, Amazon, AMD, intel\n", "df = cf_standardizer_result_bag.result_df[cf_standardizer_result_bag.result_df.cik.isin(ciks_to_consider) & mask_FY].copy()\n", "\n", "# Group by 'name' and plot GrossProfit for each group\n", "# Note: using the `present` method ensured that the same cik has always the same name even if the company name did change in the past\n", "for name, group in df.groupby('name'):\n", "    plt.plot(group['date'], group['NetCashProvidedByUsedInOperatingActivities'], label=name, linestyle='-')\n", "\n", "# Add labels and title\n", "plt.xlabel('Date')\n", "plt.ylabel('NetCashProvidedByUsedInOperatingActivities')\n", "plt.title('NetCashProvidedByUsedInOperatingActivities Over Time for Different Companies (CIKs)')\n", "\n", "# Display legend\n", "plt.legend()"]}, {"cell_type": "markdown", "id": "0d58090a-fcdd-416c-ae0f-281424e9a731", "metadata": {}, "source": ["### Conclusion\n", "\n", "With the Cash Flow Standardizer, we have the possibility to actually compare data between companies and also to create input for ML models. \n", "\n", "The great thing is, that we can do this with official and free data of over 300'000 reports filed by about 14'000 companies since 2010 (by end of 2023).\n", "\n", "Thanks to secfsdstools package, we have the possibility to gather and filter the data in a simple and efficient way, which otherwise would only be possible if you pay for the data. And you have all the data on your computer, no need for using slow api calls.\n", "\n", "The Standardizer framework is simple and can be extended with additional rules to make other data points available. With the validation rules we also have a way to assess the quality of single rows in the dataset.\n", "\n", "Of course, calculating financial ratios based on the standardized dataset is really simple now.\n", "\n", "Also the size of the standardized dataset (about 30MB) is really easy to handle."]}, {"cell_type": "markdown", "id": "a587afb4-6b4b-4b7b-93a4-ece413eea9fc", "metadata": {"tags": []}, "source": ["## Rules"]}, {"cell_type": "markdown", "id": "9507d543-f2a6-498c-bad9-2a5894573491", "metadata": {"tags": []}, "source": ["**Note:** \n", "\n", "**The following section tries to explain how the results are calculated and what kind of rules are applied. It isn't really necessary to understand this section in detail, but it gives you an idea what happens under the hood.**"]}, {"cell_type": "markdown", "id": "ac1eef96-98f8-4969-81db-a0864d89189a", "metadata": {}, "source": ["Let us see how often which rule was applied. This gives an idea about how much \"calculation\" had to be done in order to create a standardized dataset. We can to this by looking at the `applied_rules_sum_s` pandas Series object.\n", "\n", "(If you compare it to the 'IncomeStatementStandardizer, you see that there are less rules in the 'CashFlowStandardizer'. Moreover, the MAIN rules are also only executed once, since the main datapoints are almost always reported and don't need to be calculated)"]}, {"cell_type": "markdown", "id": "961b029e-23de-4a66-8eba-341f9daa12a1", "metadata": {}, "source": ["### How often was a rule applied"]}, {"cell_type": "code", "execution_count": 14, "id": "5c627784-4d98-4b43-b10d-115d1586a0bc", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["0\n", "NaN                                                                                                                                                                                                                                                                                                                                                0\n", "PREPIVOT_CF_PREPIV_#2_MaxQtr                                                                                                                                                                                                                                                                                                                 3890617\n", "PREPIVOT_CF_PREPIV_#5_CashEndOfPeriod                                                                                                                                                                                                                                                                                                         358974\n", "PREPIVOT_CF_PREPIV_#1_DeDup                                                                                                                                                                                                                                                                                                                   349919\n", "PREPIVOT_CF_PREPIV_#4_CorSign                                                                                                                                                                                                                                                                                                                   6723\n", "PREPIVOT_CF_PREPIV_#3_CorSign                                                                                                                                                                                                                                                                                                                   3630\n", "PRE_CF_PRE_#1_NetCashProvidedByUsedInOperatingActivities                                                                                                                                                                                                                                                                                         701\n", "MAIN_1_CF_#1_NETCASH_#1_CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations<-CashProvidedByUsedInDiscontinuedOperationsOperatingActivities                                                                                                                                                                                              45\n", "MAIN_1_CF_#1_NETCASH_#2_CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations<-CashProvidedByUsedInDiscontinuedOperationsInvestingActivities                                                                                                                                                                                              37\n", "MAIN_1_CF_#1_NETCASH_#3_CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations<-CashProvidedByUsedInDiscontinuedOperationsFinancingActivities                                                                                                                                                                                              40\n", "MAIN_1_CF_#1_NETCASH_#4_NetCashProvidedByUsedInOperatingActivities                                                                                                                                                                                                                                                                             52322\n", "MAIN_1_CF_#1_NETCASH_#5_NetCashProvidedByUsedInFinancingActivities                                                                                                                                                                                                                                                                             49888\n", "MAIN_1_CF_#1_NETCASH_#6_NetCashProvidedByUsedInInvestingActivities                                                                                                                                                                                                                                                                             47479\n", "MAIN_1_CF_#2_EFF_EXRATE_#1_EffectOfExchangeRateOnCashAndCashEquivalents<-EffectOfExchangeRateOnCash                                                                                                                                                                                                                                             1447\n", "MAIN_1_CF_#2_EFF_EXRATE_#2_EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalents<-EffectOfExchangeRateOnCashAndCashEquivalents                                                                                                                                                                                   68394\n", "MAIN_1_CF_#2_EFF_EXRATE_#3_EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperations                                                                                                                                                                                  99866\n", "MAIN_1_CF_#2_EFF_EXRATE_#4_EffectOfExchangeRateFinal<-EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperations                                                                                                                                                      111417\n", "MAIN_1_CF_#2_EFF_EXRATE_#5_EffectOfExchangeRateOnCashAndCashEquivalentsContinuingOperations<-EffectOfExchangeRateOnCashContinuingOperations                                                                                                                                                                                                      262\n", "MAIN_1_CF_#2_EFF_EXRATE_#6_EffectOfExchangeRateOnCashAndCashEquivalentsDiscontinuedOperations<-EffectOfExchangeRateOnCashDiscontinuedOperations                                                                                                                                                                                                   12\n", "MAIN_1_CF_#2_EFF_EXRATE_#7_EffectOfExchangeRateFinal                                                                                                                                                                                                                                                                                            9018\n", "MAIN_1_CF_#3_INC_DEC_#1_CashAndCashEquivalentsPeriodIncreaseDecrease<-CashPeriodIncreaseDecrease                                                                                                                                                                                                                                               12988\n", "MAIN_1_CF_#3_INC_DEC_#2_CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsPeriodIncreaseDecreaseIncludingExchangeRateEffect<-CashAndCashEquivalentsPeriodIncreaseDecrease                                                                                                                                                          197142\n", "MAIN_1_CF_#3_INC_DEC_#3_CashPeriodIncreaseDecreaseIncludingExRateEffectFinal<-CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsPeriodIncreaseDecreaseIncludingExchangeRateEffect                                                                                                                                                  301338\n", "MAIN_1_CF_#4_EOP_#1_CashAndDueFromBanksEndOfPeriod<-CashEndOfPeriod                                                                                                                                                                                                                                                                            18102\n", "MAIN_1_CF_#4_EOP_#2_CashAndCashEquivalentsAtCarryingValueEndOfPeriod<-CashAndDueFromBanksEndOfPeriod                                                                                                                                                                                                                                           19014\n", "MAIN_1_CF_#4_EOP_#3_CashAndCashEquivalentsAtCarryingValueIncludingDiscontinuedOperationsEndOfPeriod<-CashAndCashEquivalentsAtCarryingValueEndOfPeriod                                                                                                                                                                                         222162\n", "MAIN_1_CF_#4_EOP_#4_CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsEndOfPeriod<-CashAndCashEquivalentsAtCarryingValueIncludingDiscontinuedOperationsEndOfPeriod                                                                                                                                                                 212471\n", "MAIN_1_CF_#4_EOP_#5_CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsDisposalGroupIncludingDiscontinuedOperationsEndOfPeriod<-CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsEndOfPeriod                                                                                                                            307447\n", "MAIN_1_CF_#4_EOP_#6_CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperationsEndOfPeriod<-CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsDisposalGroupIncludingDiscontinuedOperationsEndOfPeriod                                                                             304464\n", "MAIN_1_CF_#4_EOP_#7_CashAndCashEquivalentsEndOfPeriod<-CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperationsEndOfPeriod                                                                                                                                                                333744\n", "MAIN_1_CF_#5_DeprDeplAmort_#1_Amortization                                                                                                                                                                                                                                                                                                     75390\n", "MAIN_1_CF_#5_DeprDeplAmort_#2_DepreciationAndAmortization                                                                                                                                                                                                                                                                                     103234\n", "MAIN_1_CF_#5_DeprDeplAmort_#3_DepreciationDepletionAndAmortization                                                                                                                                                                                                                                                                            134926\n", "MAIN_1_CF_#6_ProSalesInvest_#1_ProceedsFromSaleOfInvestments                                                                                                                                                                                                                                                                                   45182\n", "MAIN_1_CF_#7_PayDividends_#1_PaymentsOfDividends                                                                                                                                                                                                                                                                                               71931\n", "POST_CF_#1_CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations                                                                                                                                                                                                                                                                       51476\n", "POST_CF_#2_NetCashProvidedByUsedInOperatingActivitiesContinuingOperations                                                                                                                                                                                                                                                                       2465\n", "POST_CF_#3_NetCashProvidedByUsedInOperatingActivitiesContinuingOperations/CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations                                                                                                                                                                                                       278902\n", "POST_CF_#4_CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations                                                                                                                                                                                                                                                                       50248\n", "POST_CF_#5_NetCashProvidedByUsedInFinancingActivitiesContinuingOperations                                                                                                                                                                                                                                                                        932\n", "POST_CF_#6_NetCashProvidedByUsedInFinancingActivitiesContinuingOperations/CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations                                                                                                                                                                                                       275398\n", "POST_CF_#7_CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations                                                                                                                                                                                                                                                                       47136\n", "POST_CF_#8_NetCashProvidedByUsedInInvestingActivitiesContinuingOperations                                                                                                                                                                                                                                                                       1868\n", "POST_CF_#9_NetCashProvidedByUsedInInvestingActivitiesContinuingOperations/CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations                                                                                                                                                                                                       246676\n", "POST_CF_#10_NetCashProvidedByUsedInOperatingActivitiesContinuingOperations                                                                                                                                                                                                                                                                      1002\n", "POST_CF_#11_NetCashProvidedByUsedInInvestingActivitiesContinuingOperations                                                                                                                                                                                                                                                                     42663\n", "POST_CF_#12_NetCashProvidedByUsedInFinancingActivitiesContinuingOperations                                                                                                                                                                                                                                                                     14789\n", "POST_CF_#13_NetCashProvidedByUsedInOperatingActivities                                                                                                                                                                                                                                                                                          1002\n", "POST_CF_#14_NetCashProvidedByUsedInInvestingActivities                                                                                                                                                                                                                                                                                         42663\n", "POST_CF_#15_NetCashProvidedByUsedInFinancingActivities                                                                                                                                                                                                                                                                                         14789\n", "POST_CF_#16_CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations                                                                                                                                                                                                                                                                       1002\n", "POST_CF_#17_CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations                                                                                                                                                                                                                                                                      42663\n", "POST_CF_#18_CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations                                                                                                                                                                                                                                                                      14789\n", "POST_CF_#19_EffectOfExchangeRateFinal                                                                                                                                                                                                                                                                                                         225671\n", "POST_CF_#20_CashPeriodIncreaseDecreaseIncludingExRateEffectFinal                                                                                                                                                                                                                                                                               44768\n", "POST_CF_#21_NetCashProvidedByUsedInOperatingActivities/NetCashProvidedByUsedInFinancingActivities/NetCashProvidedByUsedInInvestingActivities/NetCashProvidedByUsedInOperatingActivitiesContinuingOperations/NetCashProvidedByUsedInFinancingActivitiesContinuingOperations/NetCashProvidedByUsedInInvestingActivitiesContinuingOperations        506\n", "Name: 1, dtype: int64"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["cf_standardizer_result_bag.applied_rules_sum_s"]}, {"cell_type": "markdown", "id": "c4bb3a15-4b18-46fe-92b4-c0b5f95d24d4", "metadata": {}, "source": ["### Applied Rules\n", "To be able to assess the content of `applied_rules_sum_s`  we need to understand the rules that are applied. The simplest way to do this is to print the description of them:"]}, {"cell_type": "code", "execution_count": 15, "id": "b13d5ad1-96de-488b-ad09-4564560d74f7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>part</th>\n", "      <th>type</th>\n", "      <th>ruleclass</th>\n", "      <th>identifier</th>\n", "      <th>description</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>PREPIVOT</td>\n", "      <td>Group</td>\n", "      <td></td>\n", "      <td>PREPIVOT_CF_PREPIV</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>PREPIVOT</td>\n", "      <td>Rule</td>\n", "      <td>PrePivotDeduplicate</td>\n", "      <td>PREPIVOT_CF_PREPIV_#1_DeDup</td>\n", "      <td>Deduplicates the dataframe based on the columns ['adsh', 'coreg', 'report', 'ddate', 'qtrs', 'tag', 'version', 'value']</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>PREPIVOT</td>\n", "      <td>Rule</td>\n", "      <td>PrePivotMaxQtrs</td>\n", "      <td>PREPIVOT_CF_PREPIV_#2_MaxQtr</td>\n", "      <td>Removes the entries that have a bigger qtrs value than 4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>PREPIVOT</td>\n", "      <td>Rule</td>\n", "      <td>PrePivotCorrectSign</td>\n", "      <td>PREPIVOT_CF_PREPIV_#3_CorSign</td>\n", "      <td>Ensures that the tags ['ProceedsFromDivestitureOfBusinessesNetOfCashDivested', 'ProceedsFromIssuanceOfCommonStock', 'ProceedsFromIssuanceOfDebt', 'ProceedsFromMaturitiesPrepaymentsAndCallsOfAvailableForSaleSecurities', 'ProceedsFromSaleAndMaturityOfOtherInvestments', 'ProceedsFromSaleOfAvailableForSaleSecurities', 'ProceedsFromSaleOfHeldToMaturitySecurities', 'ProceedsFromSaleOfIntangibleAssets', 'ProceedsFromSaleOfPropertyPlantAndEquipment', 'ProceedsFromStockOptionsExercised', 'AmortizationOfDeferredCharges', 'AmortizationOfFinancingCosts', 'AmortizationOfIntangibleAssets', 'Depletion', 'Depreciation', 'DepreciationAndAmortization', 'DepreciationDepletionAndAmortization'] have a positive value. Applied when the expectation of having a negative or positive value is not met</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>PREPIVOT</td>\n", "      <td>Rule</td>\n", "      <td>PrePivotCorrectSign</td>\n", "      <td>PREPIVOT_CF_PREPIV_#4_CorSign</td>\n", "      <td>Ensures that the tags ['PaymentsForRepurchaseOfCommonStock', 'PaymentsOfDividends', 'PaymentsOfDividendsCommonStock', 'PaymentsOfDividendsMinorityInterest', 'PaymentsOfDividendsPreferredStockAndPreferenceStock', 'PaymentsToAcquireBusinessesNetOfCashAcquired', 'PaymentsToAcquireIntangibleAssets', 'PaymentsToAcquireInvestments', 'PaymentsToAcquirePropertyPlantAndEquipment', 'RepaymentsOfDebt'] have a negative value. Applied when the expectation of having a negative or positive value is not met</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>PREPIVOT</td>\n", "      <td>Rule</td>\n", "      <td>PrePivotCashAtEndOfPeriod</td>\n", "      <td>PREPIVOT_CF_PREPIV_#5_CashEndOfPeriod</td>\n", "      <td>Adds copies of rows for ['Cash', 'CashAndDueFromBanks', 'CashAndCashEquivalentsAtCarryingValue', 'CashAndCashEquivalentsAtCarryingValueIncludingDiscontinuedOperations', 'CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalents', 'CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsDisposalGroupIncludingDiscontinuedOperations', 'CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperations'] with the 'qtrs' set to the values thatare present for the corresponding 'adsh' and extending the tag name with  'EndOfPeriod'.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>PRE</td>\n", "      <td>Group</td>\n", "      <td></td>\n", "      <td>PRE_CF_PRE</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>PRE</td>\n", "      <td>Rule</td>\n", "      <td>PreCorrectMixUpContinuingOperations</td>\n", "      <td>PRE_CF_PRE_#1_NetCashProvidedByUsedInOperatingActivities</td>\n", "      <td>Checks for reports where 'NetCashProvidedByUsedInContinuingOperations' was used instead of NetCashProvidedByUsedInOperatingActivities.Looks where NetCashProvidedByUsedInContinuingOperations and NetCashProvidedByUsedInFinancingActivities were set, but $NetCashProvidedByUsedInOperatingActivities is nan.In this cases, the value from NetCashProvidedByUsedInContinuingOperations is copied to NetCashProvidedByUsedInOperatingActivities and NetCashProvidedByUsedInContinuingOperations is set to nan afterwards.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>MAIN</td>\n", "      <td>Group</td>\n", "      <td></td>\n", "      <td>MAIN_CF</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>MAIN</td>\n", "      <td>Group</td>\n", "      <td></td>\n", "      <td>MAIN_CF_#1_NETCASH</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_CF_#1_NETCASH_#1_CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations&lt;-CashProvidedByUsedInDiscontinuedOperationsOperatingActivities</td>\n", "      <td>Copies the values from CashProvidedByUsedInDiscontinuedOperationsOperatingActivities to CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations if CashProvidedByUsedInDiscontinuedOperationsOperatingActivities is not null and CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_CF_#1_NETCASH_#2_CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations&lt;-CashProvidedByUsedInDiscontinuedOperationsInvestingActivities</td>\n", "      <td>Copies the values from CashProvidedByUsedInDiscontinuedOperationsInvestingActivities to CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations if CashProvidedByUsedInDiscontinuedOperationsInvestingActivities is not null and CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_CF_#1_NETCASH_#3_CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations&lt;-CashProvidedByUsedInDiscontinuedOperationsFinancingActivities</td>\n", "      <td>Copies the values from CashProvidedByUsedInDiscontinuedOperationsFinancingActivities to CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations if CashProvidedByUsedInDiscontinuedOperationsFinancingActivities is not null and CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SumUpRule</td>\n", "      <td>MAIN_CF_#1_NETCASH_#4_NetCashProvidedByUsedInOperatingActivities</td>\n", "      <td>Sums up the availalbe values in the columns ['NetCashProvidedByUsedInOperatingActivitiesContinuingOperations', 'CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations'] into the column 'NetCashProvidedByUsedInOperatingActivities', if the column 'NetCashProvidedByUsedInOperatingActivities' is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SumUpRule</td>\n", "      <td>MAIN_CF_#1_NETCASH_#5_NetCashProvidedByUsedInFinancingActivities</td>\n", "      <td>Sums up the availalbe values in the columns ['NetCashProvidedByUsedInFinancingActivitiesContinuingOperations', 'CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations'] into the column 'NetCashProvidedByUsedInFinancingActivities', if the column 'NetCashProvidedByUsedInFinancingActivities' is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SumUpRule</td>\n", "      <td>MAIN_CF_#1_NETCASH_#6_NetCashProvidedByUsedInInvestingActivities</td>\n", "      <td>Sums up the availalbe values in the columns ['NetCashProvidedByUsedInInvestingActivitiesContinuingOperations', 'CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations'] into the column 'NetCashProvidedByUsedInInvestingActivities', if the column 'NetCashProvidedByUsedInInvestingActivities' is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>MAIN</td>\n", "      <td>Group</td>\n", "      <td></td>\n", "      <td>MAIN_CF_#2_EFF_EXRATE</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_CF_#2_EFF_EXRATE_#1_EffectOfExchangeRateOnCashAndCashEquivalents&lt;-EffectOfExchangeRateOnCash</td>\n", "      <td>Copies the values from EffectOfExchangeRateOnCash to EffectOfExchangeRateOnCashAndCashEquivalents if EffectOfExchangeRateOnCash is not null and EffectOfExchangeRateOnCashAndCashEquivalents is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_CF_#2_EFF_EXRATE_#2_EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalents&lt;-EffectOfExchangeRateOnCashAndCashEquivalents</td>\n", "      <td>Copies the values from EffectOfExchangeRateOnCashAndCashEquivalents to EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalents if EffectOfExchangeRateOnCashAndCashEquivalents is not null and EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalents is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SumUpRule</td>\n", "      <td>MAIN_CF_#2_EFF_EXRATE_#3_EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperations</td>\n", "      <td>Sums up the availalbe values in the columns ['EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalents', 'EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsDisposalGroupIncludingDiscontinuedOperations'] into the column 'EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperations', if the column 'EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperations' is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_CF_#2_EFF_EXRATE_#4_EffectOfExchangeRateFinal&lt;-EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperations</td>\n", "      <td>Copies the values from EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperations to EffectOfExchangeRateFinal if EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperations is not null and EffectOfExchangeRateFinal is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_CF_#2_EFF_EXRATE_#5_EffectOfExchangeRateOnCashAndCashEquivalentsContinuingOperations&lt;-EffectOfExchangeRateOnCashContinuingOperations</td>\n", "      <td>Copies the values from EffectOfExchangeRateOnCashContinuingOperations to EffectOfExchangeRateOnCashAndCashEquivalentsContinuingOperations if EffectOfExchangeRateOnCashContinuingOperations is not null and EffectOfExchangeRateOnCashAndCashEquivalentsContinuingOperations is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_CF_#2_EFF_EXRATE_#6_EffectOfExchangeRateOnCashAndCashEquivalentsDiscontinuedOperations&lt;-EffectOfExchangeRateOnCashDiscontinuedOperations</td>\n", "      <td>Copies the values from EffectOfExchangeRateOnCashDiscontinuedOperations to EffectOfExchangeRateOnCashAndCashEquivalentsDiscontinuedOperations if EffectOfExchangeRateOnCashDiscontinuedOperations is not null and EffectOfExchangeRateOnCashAndCashEquivalentsDiscontinuedOperations is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SumUpRule</td>\n", "      <td>MAIN_CF_#2_EFF_EXRATE_#7_EffectOfExchangeRateFinal</td>\n", "      <td>Sums up the availalbe values in the columns ['EffectOfExchangeRateOnCashAndCashEquivalentsContinuingOperations', 'EffectOfExchangeRateOnCashAndCashEquivalentsDiscontinuedOperations'] into the column 'EffectOfExchangeRateFinal', if the column 'EffectOfExchangeRateFinal' is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>MAIN</td>\n", "      <td>Group</td>\n", "      <td></td>\n", "      <td>MAIN_CF_#3_INC_DEC</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_CF_#3_INC_DEC_#1_CashAndCashEquivalentsPeriodIncreaseDecrease&lt;-CashPeriodIncreaseDecrease</td>\n", "      <td>Copies the values from CashPeriodIncreaseDecrease to CashAndCashEquivalentsPeriodIncreaseDecrease if CashPeriodIncreaseDecrease is not null and CashAndCashEquivalentsPeriodIncreaseDecrease is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_CF_#3_INC_DEC_#2_CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsPeriodIncreaseDecreaseIncludingExchangeRateEffect&lt;-CashAndCashEquivalentsPeriodIncreaseDecrease</td>\n", "      <td>Copies the values from CashAndCashEquivalentsPeriodIncreaseDecrease to CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsPeriodIncreaseDecreaseIncludingExchangeRateEffect if CashAndCashEquivalentsPeriodIncreaseDecrease is not null and CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsPeriodIncreaseDecreaseIncludingExchangeRateEffect is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_CF_#3_INC_DEC_#3_CashPeriodIncreaseDecreaseIncludingExRateEffectFinal&lt;-CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsPeriodIncreaseDecreaseIncludingExchangeRateEffect</td>\n", "      <td>Copies the values from CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsPeriodIncreaseDecreaseIncludingExchangeRateEffect to CashPeriodIncreaseDecreaseIncludingExRateEffectFinal if CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsPeriodIncreaseDecreaseIncludingExchangeRateEffect is not null and CashPeriodIncreaseDecreaseIncludingExRateEffectFinal is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>MAIN</td>\n", "      <td>Group</td>\n", "      <td></td>\n", "      <td>MAIN_CF_#4_EOP</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_CF_#4_EOP_#1_CashAndDueFromBanksEndOfPeriod&lt;-CashEndOfPeriod</td>\n", "      <td>Copies the values from CashEndOfPeriod to CashAndDueFromBanksEndOfPeriod if CashEndOfPeriod is not null and CashAndDueFromBanksEndOfPeriod is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_CF_#4_EOP_#2_CashAndCashEquivalentsAtCarryingValueEndOfPeriod&lt;-CashAndDueFromBanksEndOfPeriod</td>\n", "      <td>Copies the values from CashAndDueFromBanksEndOfPeriod to CashAndCashEquivalentsAtCarryingValueEndOfPeriod if CashAndDueFromBanksEndOfPeriod is not null and CashAndCashEquivalentsAtCarryingValueEndOfPeriod is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_CF_#4_EOP_#3_CashAndCashEquivalentsAtCarryingValueIncludingDiscontinuedOperationsEndOfPeriod&lt;-CashAndCashEquivalentsAtCarryingValueEndOfPeriod</td>\n", "      <td>Copies the values from CashAndCashEquivalentsAtCarryingValueEndOfPeriod to CashAndCashEquivalentsAtCarryingValueIncludingDiscontinuedOperationsEndOfPeriod if CashAndCashEquivalentsAtCarryingValueEndOfPeriod is not null and CashAndCashEquivalentsAtCarryingValueIncludingDiscontinuedOperationsEndOfPeriod is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_CF_#4_EOP_#4_CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsEndOfPeriod&lt;-CashAndCashEquivalentsAtCarryingValueIncludingDiscontinuedOperationsEndOfPeriod</td>\n", "      <td>Copies the values from CashAndCashEquivalentsAtCarryingValueIncludingDiscontinuedOperationsEndOfPeriod to CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsEndOfPeriod if CashAndCashEquivalentsAtCarryingValueIncludingDiscontinuedOperationsEndOfPeriod is not null and CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsEndOfPeriod is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_CF_#4_EOP_#5_CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsDisposalGroupIncludingDiscontinuedOperationsEndOfPeriod&lt;-CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsEndOfPeriod</td>\n", "      <td>Copies the values from CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsEndOfPeriod to CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsDisposalGroupIncludingDiscontinuedOperationsEndOfPeriod if CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsEndOfPeriod is not null and CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsDisposalGroupIncludingDiscontinuedOperationsEndOfPeriod is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_CF_#4_EOP_#6_CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperationsEndOfPeriod&lt;-CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsDisposalGroupIncludingDiscontinuedOperationsEndOfPeriod</td>\n", "      <td>Copies the values from CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsDisposalGroupIncludingDiscontinuedOperationsEndOfPeriod to CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperationsEndOfPeriod if CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsDisposalGroupIncludingDiscontinuedOperationsEndOfPeriod is not null and CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperationsEndOfPeriod is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_CF_#4_EOP_#7_CashAndCashEquivalentsEndOfPeriod&lt;-CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperationsEndOfPeriod</td>\n", "      <td>Copies the values from CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperationsEndOfPeriod to CashAndCashEquivalentsEndOfPeriod if CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperationsEndOfPeriod is not null and CashAndCashEquivalentsEndOfPeriod is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>MAIN</td>\n", "      <td>Group</td>\n", "      <td></td>\n", "      <td>MAIN_CF_#5_DeprDeplAmort</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SumUpRule</td>\n", "      <td>MAIN_CF_#5_DeprDeplAmort_#1_Amortization</td>\n", "      <td>Sums up the availalbe values in the columns ['AmortizationOfIntangibleAssets', 'AmortizationOfDeferredCharges', 'AmortizationOfFinancingCosts'] into the column 'Amortization', if the column 'Amortization' is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SumUpRule</td>\n", "      <td>MAIN_CF_#5_DeprDeplAmort_#2_DepreciationAndAmortization</td>\n", "      <td>Sums up the availalbe values in the columns ['Amortization', 'Depreciation'] into the column 'DepreciationAndAmortization', if the column 'DepreciationAndAmortization' is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SumUpRule</td>\n", "      <td>MAIN_CF_#5_DeprDeplAmort_#3_DepreciationDepletionAndAmortization</td>\n", "      <td>Sums up the availalbe values in the columns ['DepreciationAndAmortization', 'Depletion'] into the column 'DepreciationDepletionAndAmortization', if the column 'DepreciationDepletionAndAmortization' is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>MAIN</td>\n", "      <td>Group</td>\n", "      <td></td>\n", "      <td>MAIN_CF_#6_ProSalesInvest</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SumUpRule</td>\n", "      <td>MAIN_CF_#6_ProSalesInvest_#1_ProceedsFromSaleOfInvestments</td>\n", "      <td>Sums up the availalbe values in the columns ['ProceedsFromSaleOfAvailableForSaleSecurities', 'ProceedsFromSaleOfTradingSecurities', 'ProceedsFromSaleOfEquitySecurities', 'ProceedsFromSaleOfDebtSecurities', 'ProceedsFromSaleAndMaturityOfOtherInvestments', 'ProceedsFromMaturitiesPrepaymentsAndCallsOfAvailableForSaleSecurities', 'ProceedsFromSaleOfInvestmentsInAffiliates', 'ProceedsFromSaleOfHeldToMaturitySecurities'] into the column 'ProceedsFromSaleOfInvestments', if the column 'ProceedsFromSaleOfInvestments' is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>MAIN</td>\n", "      <td>Group</td>\n", "      <td></td>\n", "      <td>MAIN_CF_#7_PayDividends</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SumUpRule</td>\n", "      <td>MAIN_CF_#7_PayDividends_#1_PaymentsOfDividends</td>\n", "      <td>Sums up the availalbe values in the columns ['PaymentsOfDividendsCommonStock', 'PaymentsOfDividendsPreferredStockAndPreferenceStock', 'PaymentsOfDividendsMinorityInterest'] into the column 'PaymentsOfDividends', if the column 'PaymentsOfDividends' is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>POST</td>\n", "      <td>Group</td>\n", "      <td></td>\n", "      <td>POST_CF</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>MissingSummandRule</td>\n", "      <td>POST_CF_#1_CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations</td>\n", "      <td>Calculates the value for the missing column 'CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations' by subtracting the values of the columns '['NetCashProvidedByUsedInOperatingActivitiesContinuingOperations']' from the column 'NetCashProvidedByUsedInOperatingActivities' if all of the columns ['NetCashProvidedByUsedInOperatingActivities', 'NetCashProvidedByUsedInOperatingActivitiesContinuingOperations'] are set.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>MissingSummandRule</td>\n", "      <td>POST_CF_#2_NetCashProvidedByUsedInOperatingActivitiesContinuingOperations</td>\n", "      <td>Calculates the value for the missing column 'NetCashProvidedByUsedInOperatingActivitiesContinuingOperations' by subtracting the values of the columns '['CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations']' from the column 'NetCashProvidedByUsedInOperatingActivities' if all of the columns ['NetCashProvidedByUsedInOperatingActivities', 'CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations'] are set.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostCopyToFirstSummand</td>\n", "      <td>POST_CF_#3_NetCashProvidedByUsedInOperatingActivitiesContinuingOperations/CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations</td>\n", "      <td>Copies the value of the 'NetCashProvidedByUsedInOperatingActivities' to the first summand 'NetCashProvidedByUsedInOperatingActivitiesContinuingOperations' and set the other summands ['CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations'] to 0.0 if 'NetCashProvidedByUsedInOperatingActivities is set and the summands ['NetCashProvidedByUsedInOperatingActivitiesContinuingOperations', 'CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>MissingSummandRule</td>\n", "      <td>POST_CF_#4_CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations</td>\n", "      <td>Calculates the value for the missing column 'CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations' by subtracting the values of the columns '['NetCashProvidedByUsedInFinancingActivitiesContinuingOperations']' from the column 'NetCashProvidedByUsedInFinancingActivities' if all of the columns ['NetCashProvidedByUsedInFinancingActivities', 'NetCashProvidedByUsedInFinancingActivitiesContinuingOperations'] are set.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>MissingSummandRule</td>\n", "      <td>POST_CF_#5_NetCashProvidedByUsedInFinancingActivitiesContinuingOperations</td>\n", "      <td>Calculates the value for the missing column 'NetCashProvidedByUsedInFinancingActivitiesContinuingOperations' by subtracting the values of the columns '['CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations']' from the column 'NetCashProvidedByUsedInFinancingActivities' if all of the columns ['NetCashProvidedByUsedInFinancingActivities', 'CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations'] are set.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostCopyToFirstSummand</td>\n", "      <td>POST_CF_#6_NetCashProvidedByUsedInFinancingActivitiesContinuingOperations/CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations</td>\n", "      <td>Copies the value of the 'NetCashProvidedByUsedInFinancingActivities' to the first summand 'NetCashProvidedByUsedInFinancingActivitiesContinuingOperations' and set the other summands ['CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations'] to 0.0 if 'NetCashProvidedByUsedInFinancingActivities is set and the summands ['NetCashProvidedByUsedInFinancingActivitiesContinuingOperations', 'CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>MissingSummandRule</td>\n", "      <td>POST_CF_#7_CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations</td>\n", "      <td>Calculates the value for the missing column 'CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations' by subtracting the values of the columns '['NetCashProvidedByUsedInInvestingActivitiesContinuingOperations']' from the column 'NetCashProvidedByUsedInInvestingActivities' if all of the columns ['NetCashProvidedByUsedInInvestingActivities', 'NetCashProvidedByUsedInInvestingActivitiesContinuingOperations'] are set.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>MissingSummandRule</td>\n", "      <td>POST_CF_#8_NetCashProvidedByUsedInInvestingActivitiesContinuingOperations</td>\n", "      <td>Calculates the value for the missing column 'NetCashProvidedByUsedInInvestingActivitiesContinuingOperations' by subtracting the values of the columns '['CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations']' from the column 'NetCashProvidedByUsedInInvestingActivities' if all of the columns ['NetCashProvidedByUsedInInvestingActivities', 'CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations'] are set.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostCopyToFirstSummand</td>\n", "      <td>POST_CF_#9_NetCashProvidedByUsedInInvestingActivitiesContinuingOperations/CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations</td>\n", "      <td>Copies the value of the 'NetCashProvidedByUsedInInvestingActivities' to the first summand 'NetCashProvidedByUsedInInvestingActivitiesContinuingOperations' and set the other summands ['CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations'] to 0.0 if 'NetCashProvidedByUsedInInvestingActivities is set and the summands ['NetCashProvidedByUsedInInvestingActivitiesContinuingOperations', 'CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostSetToZero</td>\n", "      <td>POST_CF_#10_NetCashProvidedByUsedInOperatingActivitiesContinuingOperations</td>\n", "      <td>Set the value of the ['NetCashProvidedByUsedInOperatingActivitiesContinuingOperations'] to 0.0 if all ['NetCashProvidedByUsedInOperatingActivitiesContinuingOperations'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostSetToZero</td>\n", "      <td>POST_CF_#11_NetCashProvidedByUsedInInvestingActivitiesContinuingOperations</td>\n", "      <td>Set the value of the ['NetCashProvidedByUsedInInvestingActivitiesContinuingOperations'] to 0.0 if all ['NetCashProvidedByUsedInInvestingActivitiesContinuingOperations'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>56</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostSetToZero</td>\n", "      <td>POST_CF_#12_NetCashProvidedByUsedInFinancingActivitiesContinuingOperations</td>\n", "      <td>Set the value of the ['NetCashProvidedByUsedInFinancingActivitiesContinuingOperations'] to 0.0 if all ['NetCashProvidedByUsedInFinancingActivitiesContinuingOperations'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>57</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostSetToZero</td>\n", "      <td>POST_CF_#13_NetCashProvidedByUsedInOperatingActivities</td>\n", "      <td>Set the value of the ['NetCashProvidedByUsedInOperatingActivities'] to 0.0 if all ['NetCashProvidedByUsedInOperatingActivities'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>58</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostSetToZero</td>\n", "      <td>POST_CF_#14_NetCashProvidedByUsedInInvestingActivities</td>\n", "      <td>Set the value of the ['NetCashProvidedByUsedInInvestingActivities'] to 0.0 if all ['NetCashProvidedByUsedInInvestingActivities'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>59</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostSetToZero</td>\n", "      <td>POST_CF_#15_NetCashProvidedByUsedInFinancingActivities</td>\n", "      <td>Set the value of the ['NetCashProvidedByUsedInFinancingActivities'] to 0.0 if all ['NetCashProvidedByUsedInFinancingActivities'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>60</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostSetToZero</td>\n", "      <td>POST_CF_#16_CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations</td>\n", "      <td>Set the value of the ['CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations'] to 0.0 if all ['CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostSetToZero</td>\n", "      <td>POST_CF_#17_CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations</td>\n", "      <td>Set the value of the ['CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations'] to 0.0 if all ['CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>62</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostSetToZero</td>\n", "      <td>POST_CF_#18_CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations</td>\n", "      <td>Set the value of the ['CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations'] to 0.0 if all ['CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostSetToZero</td>\n", "      <td>POST_CF_#19_EffectOfExchangeRateFinal</td>\n", "      <td>Set the value of the ['EffectOfExchangeRateFinal'] to 0.0 if all ['EffectOfExchangeRateFinal'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>MissingSumRule</td>\n", "      <td>POST_CF_#20_CashPeriodIncreaseDecreaseIncludingExRateEffectFinal</td>\n", "      <td>Sums up the values in the columns ['NetCashProvidedByUsedInOperatingActivities', 'NetCashProvidedByUsedInInvestingActivities', 'NetCashProvidedByUsedInFinancingActivities', 'EffectOfExchangeRateFinal'] into the column 'CashPeriodIncreaseDecreaseIncludingExRateEffectFinal', if the column 'CashPeriodIncreaseDecreaseIncludingExRateEffectFinal' is nan and if all columns ['NetCashProvidedByUsedInOperatingActivities', 'NetCashProvidedByUsedInInvestingActivities', 'NetCashProvidedByUsedInFinancingActivities', 'EffectOfExchangeRateFinal'] have a value</td>\n", "    </tr>\n", "    <tr>\n", "      <th>65</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostFixMixedContinuingWithSum</td>\n", "      <td>POST_CF_#21_NetCashProvidedByUsedInOperatingActivities/NetCashProvidedByUsedInFinancingActivities/NetCashProvidedByUsedInInvestingActivities/NetCashProvidedByUsedInOperatingActivitiesContinuingOperations/NetCashProvidedByUsedInFinancingActivitiesContinuingOperations/NetCashProvidedByUsedInInvestingActivitiesContinuingOperations</td>\n", "      <td>Tries to find and correct cases when discontinued operations are reported and the 'Sum' tags (e.g.'NetCashProvidedByUsedIn...Activities') were used to report the continuing operation instead using the of the  '...ContinuingOperations' tags. (e.g. 'NetCashProvidedByUsedInOperatingActivitiesContinuingOperations').</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66</th>\n", "      <td>VALID</td>\n", "      <td>Validation</td>\n", "      <td>SumValidationRule</td>\n", "      <td>BaseOpAct</td>\n", "      <td>Checks whether the sum of ['NetCashProvidedByUsedInOperatingActivitiesContinuingOperations', 'CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations'] equals the value in 'NetCashProvidedByUsedInOperatingActivities'</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67</th>\n", "      <td>VALID</td>\n", "      <td>Validation</td>\n", "      <td>SumValidationRule</td>\n", "      <td>BaseFinAct</td>\n", "      <td>Checks whether the sum of ['NetCashProvidedByUsedInFinancingActivitiesContinuingOperations', 'CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations'] equals the value in 'NetCashProvidedByUsedInFinancingActivities'</td>\n", "    </tr>\n", "    <tr>\n", "      <th>68</th>\n", "      <td>VALID</td>\n", "      <td>Validation</td>\n", "      <td>SumValidationRule</td>\n", "      <td>BaseInvAct</td>\n", "      <td>Checks whether the sum of ['NetCashProvidedByUsedInInvestingActivitiesContinuingOperations', 'CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations'] equals the value in 'NetCashProvidedByUsedInInvestingActivities'</td>\n", "    </tr>\n", "    <tr>\n", "      <th>69</th>\n", "      <td>VALID</td>\n", "      <td>Validation</td>\n", "      <td>SumValidationRule</td>\n", "      <td>NetCashContOp</td>\n", "      <td>Checks whether the sum of ['NetCashProvidedByUsedInOperatingActivities', 'NetCashProvidedByUsedInFinancingActivities', 'NetCashProvidedByUsedInInvestingActivities', 'EffectOfExchangeRateFinal'] equals the value in 'CashPeriodIncreaseDecreaseIncludingExRateEffectFinal'</td>\n", "    </tr>\n", "    <tr>\n", "      <th>70</th>\n", "      <td>VALID</td>\n", "      <td>Validation</td>\n", "      <td>IsSetValidationRule</td>\n", "      <td>CashEoP</td>\n", "      <td>Checks whether the CashAndCashEquivalentsEndOfPeriod is set.</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        part        type                            ruleclass                                                                                                                                                                                                                                                                                                                                 identifier  \\\n", "0   PREPIVOT       Group                                                                                                                                                                                                                                                                                                                                                              PREPIVOT_CF_PREPIV   \n", "1   PREPIVOT        Rule                  PrePivotDeduplicate                                                                                                                                                                                                                                                                                                                PREPIVOT_CF_PREPIV_#1_DeDup   \n", "2   PREPIVOT        Rule                      PrePivotMaxQtrs                                                                                                                                                                                                                                                                                                               PREPIVOT_CF_PREPIV_#2_MaxQtr   \n", "3   PREPIVOT        Rule                  PrePivotCorrectSign                                                                                                                                                                                                                                                                                                              PREPIVOT_CF_PREPIV_#3_CorSign   \n", "4   PREPIVOT        Rule                  PrePivotCorrectSign                                                                                                                                                                                                                                                                                                              PREPIVOT_CF_PREPIV_#4_CorSign   \n", "5   PREPIVOT        Rule            PrePivotCashAtEndOfPeriod                                                                                                                                                                                                                                                                                                      PREPIVOT_CF_PREPIV_#5_CashEndOfPeriod   \n", "6        PRE       Group                                                                                                                                                                                                                                                                                                                                                                      PRE_CF_PRE   \n", "7        PRE        Rule  PreCorrectMixUpContinuingOperations                                                                                                                                                                                                                                                                                   PRE_CF_PRE_#1_NetCashProvidedByUsedInOperatingActivities   \n", "8       MAIN       Group                                                                                                                                                                                                                                                                                                                                                                         MAIN_CF   \n", "9       MAIN       Group                                                                                                                                                                                                                                                                                                                                                              MAIN_CF_#1_NETCASH   \n", "10      MAIN        Rule                          CopyTagRule                                                                                                                                                                                         MAIN_CF_#1_NETCASH_#1_CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations<-CashProvidedByUsedInDiscontinuedOperationsOperatingActivities   \n", "11      MAIN        Rule                          CopyTagRule                                                                                                                                                                                         MAIN_CF_#1_NETCASH_#2_CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations<-CashProvidedByUsedInDiscontinuedOperationsInvestingActivities   \n", "12      MAIN        Rule                          CopyTagRule                                                                                                                                                                                         MAIN_CF_#1_NETCASH_#3_CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations<-CashProvidedByUsedInDiscontinuedOperationsFinancingActivities   \n", "13      MAIN        Rule                            SumUpRule                                                                                                                                                                                                                                                                           MAIN_CF_#1_NETCASH_#4_NetCashProvidedByUsedInOperatingActivities   \n", "14      MAIN        Rule                            SumUpRule                                                                                                                                                                                                                                                                           MAIN_CF_#1_NETCASH_#5_NetCashProvidedByUsedInFinancingActivities   \n", "15      MAIN        Rule                            SumUpRule                                                                                                                                                                                                                                                                           MAIN_CF_#1_NETCASH_#6_NetCashProvidedByUsedInInvestingActivities   \n", "16      MAIN       Group                                                                                                                                                                                                                                                                                                                                                           MAIN_CF_#2_EFF_EXRATE   \n", "17      MAIN        Rule                          CopyTagRule                                                                                                                                                                                                                                          MAIN_CF_#2_EFF_EXRATE_#1_EffectOfExchangeRateOnCashAndCashEquivalents<-EffectOfExchangeRateOnCash   \n", "18      MAIN        Rule                          CopyTagRule                                                                                                                                                                                 MAIN_CF_#2_EFF_EXRATE_#2_EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalents<-EffectOfExchangeRateOnCashAndCashEquivalents   \n", "19      MAIN        Rule                            SumUpRule                                                                                                                                                                                MAIN_CF_#2_EFF_EXRATE_#3_EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperations   \n", "20      MAIN        Rule                          CopyTagRule                                                                                                                                                     MAIN_CF_#2_EFF_EXRATE_#4_EffectOfExchangeRateFinal<-EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperations   \n", "21      MAIN        Rule                          CopyTagRule                                                                                                                                                                                                  MAIN_CF_#2_EFF_EXRATE_#5_EffectOfExchangeRateOnCashAndCashEquivalentsContinuingOperations<-EffectOfExchangeRateOnCashContinuingOperations   \n", "22      MAIN        Rule                          CopyTagRule                                                                                                                                                                                              MAIN_CF_#2_EFF_EXRATE_#6_EffectOfExchangeRateOnCashAndCashEquivalentsDiscontinuedOperations<-EffectOfExchangeRateOnCashDiscontinuedOperations   \n", "23      MAIN        Rule                            SumUpRule                                                                                                                                                                                                                                                                                         MAIN_CF_#2_EFF_EXRATE_#7_EffectOfExchangeRateFinal   \n", "24      MAIN       Group                                                                                                                                                                                                                                                                                                                                                              MAIN_CF_#3_INC_DEC   \n", "25      MAIN        Rule                          CopyTagRule                                                                                                                                                                                                                                             MAIN_CF_#3_INC_DEC_#1_CashAndCashEquivalentsPeriodIncreaseDecrease<-CashPeriodIncreaseDecrease   \n", "26      MAIN        Rule                          CopyTagRule                                                                                                                                                         MAIN_CF_#3_INC_DEC_#2_CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsPeriodIncreaseDecreaseIncludingExchangeRateEffect<-CashAndCashEquivalentsPeriodIncreaseDecrease   \n", "27      MAIN        Rule                          CopyTagRule                                                                                                                                                 MAIN_CF_#3_INC_DEC_#3_CashPeriodIncreaseDecreaseIncludingExRateEffectFinal<-CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsPeriodIncreaseDecreaseIncludingExchangeRateEffect   \n", "28      MAIN       Group                                                                                                                                                                                                                                                                                                                                                                  MAIN_CF_#4_EOP   \n", "29      MAIN        Rule                          CopyTagRule                                                                                                                                                                                                                                                                          MAIN_CF_#4_EOP_#1_CashAndDueFromBanksEndOfPeriod<-CashEndOfPeriod   \n", "30      MAIN        Rule                          CopyTagRule                                                                                                                                                                                                                                         MAIN_CF_#4_EOP_#2_CashAndCashEquivalentsAtCarryingValueEndOfPeriod<-CashAndDueFromBanksEndOfPeriod   \n", "31      MAIN        Rule                          CopyTagRule                                                                                                                                                                                        MAIN_CF_#4_EOP_#3_CashAndCashEquivalentsAtCarryingValueIncludingDiscontinuedOperationsEndOfPeriod<-CashAndCashEquivalentsAtCarryingValueEndOfPeriod   \n", "32      MAIN        Rule                          CopyTagRule                                                                                                                                                                MAIN_CF_#4_EOP_#4_CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsEndOfPeriod<-CashAndCashEquivalentsAtCarryingValueIncludingDiscontinuedOperationsEndOfPeriod   \n", "33      MAIN        Rule                          CopyTagRule                                                                                                                           MAIN_CF_#4_EOP_#5_CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsDisposalGroupIncludingDiscontinuedOperationsEndOfPeriod<-CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsEndOfPeriod   \n", "34      MAIN        Rule                          CopyTagRule                                                                            MAIN_CF_#4_EOP_#6_CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperationsEndOfPeriod<-CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsDisposalGroupIncludingDiscontinuedOperationsEndOfPeriod   \n", "35      MAIN        Rule                          CopyTagRule                                                                                                                                                               MAIN_CF_#4_EOP_#7_CashAndCashEquivalentsEndOfPeriod<-CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperationsEndOfPeriod   \n", "36      MAIN       Group                                                                                                                                                                                                                                                                                                                                                        MAIN_CF_#5_DeprDeplAmort   \n", "37      MAIN        Rule                            SumUpRule                                                                                                                                                                                                                                                                                                   MAIN_CF_#5_DeprDeplAmort_#1_Amortization   \n", "38      MAIN        Rule                            SumUpRule                                                                                                                                                                                                                                                                                    MAIN_CF_#5_DeprDeplAmort_#2_DepreciationAndAmortization   \n", "39      MAIN        Rule                            SumUpRule                                                                                                                                                                                                                                                                           MAIN_CF_#5_DeprDeplAmort_#3_DepreciationDepletionAndAmortization   \n", "40      MAIN       Group                                                                                                                                                                                                                                                                                                                                                       MAIN_CF_#6_ProSalesInvest   \n", "41      MAIN        Rule                            SumUpRule                                                                                                                                                                                                                                                                                 MAIN_CF_#6_ProSalesInvest_#1_ProceedsFromSaleOfInvestments   \n", "42      MAIN       Group                                                                                                                                                                                                                                                                                                                                                         MAIN_CF_#7_PayDividends   \n", "43      MAIN        Rule                            SumUpRule                                                                                                                                                                                                                                                                                             MAIN_CF_#7_PayDividends_#1_PaymentsOfDividends   \n", "44      POST       Group                                                                                                                                                                                                                                                                                                                                                                         POST_CF   \n", "45      POST        Rule                   MissingSummandRule                                                                                                                                                                                                                                                                   POST_CF_#1_CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations   \n", "46      POST        Rule                   MissingSummandRule                                                                                                                                                                                                                                                                  POST_CF_#2_NetCashProvidedByUsedInOperatingActivitiesContinuingOperations   \n", "47      POST        Rule               PostCopyToFirstSummand                                                                                                                                                                                                    POST_CF_#3_NetCashProvidedByUsedInOperatingActivitiesContinuingOperations/CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations   \n", "48      POST        Rule                   MissingSummandRule                                                                                                                                                                                                                                                                   POST_CF_#4_CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations   \n", "49      POST        Rule                   MissingSummandRule                                                                                                                                                                                                                                                                  POST_CF_#5_NetCashProvidedByUsedInFinancingActivitiesContinuingOperations   \n", "50      POST        Rule               PostCopyToFirstSummand                                                                                                                                                                                                    POST_CF_#6_NetCashProvidedByUsedInFinancingActivitiesContinuingOperations/CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations   \n", "51      POST        Rule                   MissingSummandRule                                                                                                                                                                                                                                                                   POST_CF_#7_CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations   \n", "52      POST        Rule                   MissingSummandRule                                                                                                                                                                                                                                                                  POST_CF_#8_NetCashProvidedByUsedInInvestingActivitiesContinuingOperations   \n", "53      POST        Rule               PostCopyToFirstSummand                                                                                                                                                                                                    POST_CF_#9_NetCashProvidedByUsedInInvestingActivitiesContinuingOperations/CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations   \n", "54      POST        Rule                        PostSetToZero                                                                                                                                                                                                                                                                 POST_CF_#10_NetCashProvidedByUsedInOperatingActivitiesContinuingOperations   \n", "55      POST        Rule                        PostSetToZero                                                                                                                                                                                                                                                                 POST_CF_#11_NetCashProvidedByUsedInInvestingActivitiesContinuingOperations   \n", "56      POST        Rule                        PostSetToZero                                                                                                                                                                                                                                                                 POST_CF_#12_NetCashProvidedByUsedInFinancingActivitiesContinuingOperations   \n", "57      POST        Rule                        PostSetToZero                                                                                                                                                                                                                                                                                     POST_CF_#13_NetCashProvidedByUsedInOperatingActivities   \n", "58      POST        Rule                        PostSetToZero                                                                                                                                                                                                                                                                                     POST_CF_#14_NetCashProvidedByUsedInInvestingActivities   \n", "59      POST        Rule                        PostSetToZero                                                                                                                                                                                                                                                                                     POST_CF_#15_NetCashProvidedByUsedInFinancingActivities   \n", "60      POST        Rule                        PostSetToZero                                                                                                                                                                                                                                                                  POST_CF_#16_CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations   \n", "61      POST        Rule                        PostSetToZero                                                                                                                                                                                                                                                                  POST_CF_#17_CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations   \n", "62      POST        Rule                        PostSetToZero                                                                                                                                                                                                                                                                  POST_CF_#18_CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations   \n", "63      POST        Rule                        PostSetToZero                                                                                                                                                                                                                                                                                                      POST_CF_#19_EffectOfExchangeRateFinal   \n", "64      POST        Rule                       MissingSumRule                                                                                                                                                                                                                                                                           POST_CF_#20_CashPeriodIncreaseDecreaseIncludingExRateEffectFinal   \n", "65      POST        Rule        PostFixMixedContinuingWithSum  POST_CF_#21_NetCashProvidedByUsedInOperatingActivities/NetCashProvidedByUsedInFinancingActivities/NetCashProvidedByUsedInInvestingActivities/NetCashProvidedByUsedInOperatingActivitiesContinuingOperations/NetCashProvidedByUsedInFinancingActivitiesContinuingOperations/NetCashProvidedByUsedInInvestingActivitiesContinuingOperations   \n", "66     VALID  Validation                    SumValidationRule                                                                                                                                                                                                                                                                                                                                  BaseOpAct   \n", "67     VALID  Validation                    SumValidationRule                                                                                                                                                                                                                                                                                                                                 BaseFinAct   \n", "68     VALID  Validation                    SumValidationRule                                                                                                                                                                                                                                                                                                                                 BaseInvAct   \n", "69     VALID  Validation                    SumValidationRule                                                                                                                                                                                                                                                                                                                              NetCashContOp   \n", "70     VALID  Validation                  IsSetValidationRule                                                                                                                                                                                                                                                                                                                                    CashEoP   \n", "\n", "                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         description  \n", "0                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     \n", "1                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            Deduplicates the dataframe based on the columns ['adsh', 'coreg', 'report', 'ddate', 'qtrs', 'tag', 'version', 'value']  \n", "2                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           Removes the entries that have a bigger qtrs value than 4  \n", "3   Ensures that the tags ['ProceedsFromDivestitureOfBusinessesNetOfCashDivested', 'ProceedsFromIssuanceOfCommonStock', 'ProceedsFromIssuanceOfDebt', 'ProceedsFromMaturitiesPrepaymentsAndCallsOfAvailableForSaleSecurities', 'ProceedsFromSaleAndMaturityOfOtherInvestments', 'ProceedsFromSaleOfAvailableForSaleSecurities', 'ProceedsFromSaleOfHeldToMaturitySecurities', 'ProceedsFromSaleOfIntangibleAssets', 'ProceedsFromSaleOfPropertyPlantAndEquipment', 'ProceedsFromStockOptionsExercised', 'AmortizationOfDeferredCharges', 'AmortizationOfFinancingCosts', 'AmortizationOfIntangibleAssets', 'Depletion', 'Depreciation', 'DepreciationAndAmortization', 'DepreciationDepletionAndAmortization'] have a positive value. Applied when the expectation of having a negative or positive value is not met  \n", "4                                                                                                                                                                                                                                                                                                   Ensures that the tags ['PaymentsForRepurchaseOfCommonStock', 'PaymentsOfDividends', 'PaymentsOfDividendsCommonStock', 'PaymentsOfDividendsMinorityInterest', 'PaymentsOfDividendsPreferredStockAndPreferenceStock', 'PaymentsToAcquireBusinessesNetOfCashAcquired', 'PaymentsToAcquireIntangibleAssets', 'PaymentsToAcquireInvestments', 'PaymentsToAcquirePropertyPlantAndEquipment', 'RepaymentsOfDebt'] have a negative value. Applied when the expectation of having a negative or positive value is not met  \n", "5                                                                                                                                                                                                             Adds copies of rows for ['Cash', 'CashAndDueFromBanks', 'CashAndCashEquivalentsAtCarryingValue', 'CashAndCashEquivalentsAtCarryingValueIncludingDiscontinuedOperations', 'CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalents', 'CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsDisposalGroupIncludingDiscontinuedOperations', 'CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperations'] with the 'qtrs' set to the values thatare present for the corresponding 'adsh' and extending the tag name with  'EndOfPeriod'.   \n", "6                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     \n", "7                                                                                                                                                                                                                                                                                          Checks for reports where 'NetCashProvidedByUsedInContinuingOperations' was used instead of NetCashProvidedByUsedInOperatingActivities.Looks where NetCashProvidedByUsedInContinuingOperations and NetCashProvidedByUsedInFinancingActivities were set, but $NetCashProvidedByUsedInOperatingActivities is nan.In this cases, the value from NetCashProvidedByUsedInContinuingOperations is copied to NetCashProvidedByUsedInOperatingActivities and NetCashProvidedByUsedInContinuingOperations is set to nan afterwards.  \n", "8                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     \n", "9                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     \n", "10                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       Copies the values from CashProvidedByUsedInDiscontinuedOperationsOperatingActivities to CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations if CashProvidedByUsedInDiscontinuedOperationsOperatingActivities is not null and CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations is nan  \n", "11                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       Copies the values from CashProvidedByUsedInDiscontinuedOperationsInvestingActivities to CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations if CashProvidedByUsedInDiscontinuedOperationsInvestingActivities is not null and CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations is nan  \n", "12                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       Copies the values from CashProvidedByUsedInDiscontinuedOperationsFinancingActivities to CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations if CashProvidedByUsedInDiscontinuedOperationsFinancingActivities is not null and CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations is nan  \n", "13                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   Sums up the availalbe values in the columns ['NetCashProvidedByUsedInOperatingActivitiesContinuingOperations', 'CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations'] into the column 'NetCashProvidedByUsedInOperatingActivities', if the column 'NetCashProvidedByUsedInOperatingActivities' is nan  \n", "14                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   Sums up the availalbe values in the columns ['NetCashProvidedByUsedInFinancingActivitiesContinuingOperations', 'CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations'] into the column 'NetCashProvidedByUsedInFinancingActivities', if the column 'NetCashProvidedByUsedInFinancingActivities' is nan  \n", "15                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   Sums up the availalbe values in the columns ['NetCashProvidedByUsedInInvestingActivitiesContinuingOperations', 'CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations'] into the column 'NetCashProvidedByUsedInInvestingActivities', if the column 'NetCashProvidedByUsedInInvestingActivities' is nan  \n", "16                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    \n", "17                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               Copies the values from EffectOfExchangeRateOnCash to EffectOfExchangeRateOnCashAndCashEquivalents if EffectOfExchangeRateOnCash is not null and EffectOfExchangeRateOnCashAndCashEquivalents is nan  \n", "18                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             Copies the values from EffectOfExchangeRateOnCashAndCashEquivalents to EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalents if EffectOfExchangeRateOnCashAndCashEquivalents is not null and EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalents is nan  \n", "19                                                                                                                                                                                                                            Sums up the availalbe values in the columns ['EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalents', 'EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsDisposalGroupIncludingDiscontinuedOperations'] into the column 'EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperations', if the column 'EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperations' is nan  \n", "20                                                                                                                                                                                                                                                                                                                                                                                                                                     Copies the values from EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperations to EffectOfExchangeRateFinal if EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperations is not null and EffectOfExchangeRateFinal is nan  \n", "21                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               Copies the values from EffectOfExchangeRateOnCashContinuingOperations to EffectOfExchangeRateOnCashAndCashEquivalentsContinuingOperations if EffectOfExchangeRateOnCashContinuingOperations is not null and EffectOfExchangeRateOnCashAndCashEquivalentsContinuingOperations is nan  \n", "22                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       Copies the values from EffectOfExchangeRateOnCashDiscontinuedOperations to EffectOfExchangeRateOnCashAndCashEquivalentsDiscontinuedOperations if EffectOfExchangeRateOnCashDiscontinuedOperations is not null and EffectOfExchangeRateOnCashAndCashEquivalentsDiscontinuedOperations is nan  \n", "23                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              Sums up the availalbe values in the columns ['EffectOfExchangeRateOnCashAndCashEquivalentsContinuingOperations', 'EffectOfExchangeRateOnCashAndCashEquivalentsDiscontinuedOperations'] into the column 'EffectOfExchangeRateFinal', if the column 'EffectOfExchangeRateFinal' is nan  \n", "24                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    \n", "25                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               Copies the values from CashPeriodIncreaseDecrease to CashAndCashEquivalentsPeriodIncreaseDecrease if CashPeriodIncreaseDecrease is not null and CashAndCashEquivalentsPeriodIncreaseDecrease is nan  \n", "26                                                                                                                                                                                                                                                                                                                                                                                                                                       Copies the values from CashAndCashEquivalentsPeriodIncreaseDecrease to CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsPeriodIncreaseDecreaseIncludingExchangeRateEffect if CashAndCashEquivalentsPeriodIncreaseDecrease is not null and CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsPeriodIncreaseDecreaseIncludingExchangeRateEffect is nan  \n", "27                                                                                                                                                                                                                                                                                                                                                                                                                       Copies the values from CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsPeriodIncreaseDecreaseIncludingExchangeRateEffect to CashPeriodIncreaseDecreaseIncludingExRateEffectFinal if CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsPeriodIncreaseDecreaseIncludingExchangeRateEffect is not null and CashPeriodIncreaseDecreaseIncludingExRateEffectFinal is nan  \n", "28                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    \n", "29                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 Copies the values from CashEndOfPeriod to CashAndDueFromBanksEndOfPeriod if CashEndOfPeriod is not null and CashAndDueFromBanksEndOfPeriod is nan  \n", "30                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               Copies the values from CashAndDueFromBanksEndOfPeriod to CashAndCashEquivalentsAtCarryingValueEndOfPeriod if CashAndDueFromBanksEndOfPeriod is not null and CashAndCashEquivalentsAtCarryingValueEndOfPeriod is nan  \n", "31                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             Copies the values from CashAndCashEquivalentsAtCarryingValueEndOfPeriod to CashAndCashEquivalentsAtCarryingValueIncludingDiscontinuedOperationsEndOfPeriod if CashAndCashEquivalentsAtCarryingValueEndOfPeriod is not null and CashAndCashEquivalentsAtCarryingValueIncludingDiscontinuedOperationsEndOfPeriod is nan  \n", "32                                                                                                                                                                                                                                                                                                                                                                                                                                             Copies the values from CashAndCashEquivalentsAtCarryingValueIncludingDiscontinuedOperationsEndOfPeriod to CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsEndOfPeriod if CashAndCashEquivalentsAtCarryingValueIncludingDiscontinuedOperationsEndOfPeriod is not null and CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsEndOfPeriod is nan  \n", "33                                                                                                                                                                                                                                                                                                                                                                   Copies the values from CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsEndOfPeriod to CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsDisposalGroupIncludingDiscontinuedOperationsEndOfPeriod if CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsEndOfPeriod is not null and CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsDisposalGroupIncludingDiscontinuedOperationsEndOfPeriod is nan  \n", "34                                                                                                                                                                                                                                                                     Copies the values from CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsDisposalGroupIncludingDiscontinuedOperationsEndOfPeriod to CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperationsEndOfPeriod if CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsDisposalGroupIncludingDiscontinuedOperationsEndOfPeriod is not null and CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperationsEndOfPeriod is nan  \n", "35                                                                                                                                                                                                                                                                                                                                                                                                                                           Copies the values from CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperationsEndOfPeriod to CashAndCashEquivalentsEndOfPeriod if CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperationsEndOfPeriod is not null and CashAndCashEquivalentsEndOfPeriod is nan  \n", "36                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    \n", "37                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               Sums up the availalbe values in the columns ['AmortizationOfIntangibleAssets', 'AmortizationOfDeferredCharges', 'AmortizationOfFinancingCosts'] into the column 'Amortization', if the column 'Amortization' is nan  \n", "38                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    Sums up the availalbe values in the columns ['Amortization', 'Depreciation'] into the column 'DepreciationAndAmortization', if the column 'DepreciationAndAmortization' is nan  \n", "39                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      Sums up the availalbe values in the columns ['DepreciationAndAmortization', 'Depletion'] into the column 'DepreciationDepletionAndAmortization', if the column 'DepreciationDepletionAndAmortization' is nan  \n", "40                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    \n", "41                                                                                                                                                                                                                                                                          Sums up the availalbe values in the columns ['ProceedsFromSaleOfAvailableForSaleSecurities', 'ProceedsFromSaleOfTradingSecurities', 'ProceedsFromSaleOfEquitySecurities', 'ProceedsFromSaleOfDebtSecurities', 'ProceedsFromSaleAndMaturityOfOtherInvestments', 'ProceedsFromMaturitiesPrepaymentsAndCallsOfAvailableForSaleSecurities', 'ProceedsFromSaleOfInvestmentsInAffiliates', 'ProceedsFromSaleOfHeldToMaturitySecurities'] into the column 'ProceedsFromSaleOfInvestments', if the column 'ProceedsFromSaleOfInvestments' is nan  \n", "42                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    \n", "43                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    Sums up the availalbe values in the columns ['PaymentsOfDividendsCommonStock', 'PaymentsOfDividendsPreferredStockAndPreferenceStock', 'PaymentsOfDividendsMinorityInterest'] into the column 'PaymentsOfDividends', if the column 'PaymentsOfDividends' is nan  \n", "44                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    \n", "45                                                                                                                                                                                                                                                                                                                                                                            Calculates the value for the missing column 'CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations' by subtracting the values of the columns '['NetCashProvidedByUsedInOperatingActivitiesContinuingOperations']' from the column 'NetCashProvidedByUsedInOperatingActivities' if all of the columns ['NetCashProvidedByUsedInOperatingActivities', 'NetCashProvidedByUsedInOperatingActivitiesContinuingOperations'] are set.  \n", "46                                                                                                                                                                                                                                                                                                                                                                             Calculates the value for the missing column 'NetCashProvidedByUsedInOperatingActivitiesContinuingOperations' by subtracting the values of the columns '['CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations']' from the column 'NetCashProvidedByUsedInOperatingActivities' if all of the columns ['NetCashProvidedByUsedInOperatingActivities', 'CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations'] are set.  \n", "47                                                                                                                                                                                                                                                                                                                                Copies the value of the 'NetCashProvidedByUsedInOperatingActivities' to the first summand 'NetCashProvidedByUsedInOperatingActivitiesContinuingOperations' and set the other summands ['CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations'] to 0.0 if 'NetCashProvidedByUsedInOperatingActivities is set and the summands ['NetCashProvidedByUsedInOperatingActivitiesContinuingOperations', 'CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations'] are nan.  \n", "48                                                                                                                                                                                                                                                                                                                                                                            Calculates the value for the missing column 'CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations' by subtracting the values of the columns '['NetCashProvidedByUsedInFinancingActivitiesContinuingOperations']' from the column 'NetCashProvidedByUsedInFinancingActivities' if all of the columns ['NetCashProvidedByUsedInFinancingActivities', 'NetCashProvidedByUsedInFinancingActivitiesContinuingOperations'] are set.  \n", "49                                                                                                                                                                                                                                                                                                                                                                             Calculates the value for the missing column 'NetCashProvidedByUsedInFinancingActivitiesContinuingOperations' by subtracting the values of the columns '['CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations']' from the column 'NetCashProvidedByUsedInFinancingActivities' if all of the columns ['NetCashProvidedByUsedInFinancingActivities', 'CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations'] are set.  \n", "50                                                                                                                                                                                                                                                                                                                                Copies the value of the 'NetCashProvidedByUsedInFinancingActivities' to the first summand 'NetCashProvidedByUsedInFinancingActivitiesContinuingOperations' and set the other summands ['CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations'] to 0.0 if 'NetCashProvidedByUsedInFinancingActivities is set and the summands ['NetCashProvidedByUsedInFinancingActivitiesContinuingOperations', 'CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations'] are nan.  \n", "51                                                                                                                                                                                                                                                                                                                                                                            Calculates the value for the missing column 'CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations' by subtracting the values of the columns '['NetCashProvidedByUsedInInvestingActivitiesContinuingOperations']' from the column 'NetCashProvidedByUsedInInvestingActivities' if all of the columns ['NetCashProvidedByUsedInInvestingActivities', 'NetCashProvidedByUsedInInvestingActivitiesContinuingOperations'] are set.  \n", "52                                                                                                                                                                                                                                                                                                                                                                             Calculates the value for the missing column 'NetCashProvidedByUsedInInvestingActivitiesContinuingOperations' by subtracting the values of the columns '['CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations']' from the column 'NetCashProvidedByUsedInInvestingActivities' if all of the columns ['NetCashProvidedByUsedInInvestingActivities', 'CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations'] are set.  \n", "53                                                                                                                                                                                                                                                                                                                                Copies the value of the 'NetCashProvidedByUsedInInvestingActivities' to the first summand 'NetCashProvidedByUsedInInvestingActivitiesContinuingOperations' and set the other summands ['CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations'] to 0.0 if 'NetCashProvidedByUsedInInvestingActivities is set and the summands ['NetCashProvidedByUsedInInvestingActivitiesContinuingOperations', 'CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations'] are nan.  \n", "54                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 Set the value of the ['NetCashProvidedByUsedInOperatingActivitiesContinuingOperations'] to 0.0 if all ['NetCashProvidedByUsedInOperatingActivitiesContinuingOperations'] are nan.  \n", "55                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 Set the value of the ['NetCashProvidedByUsedInInvestingActivitiesContinuingOperations'] to 0.0 if all ['NetCashProvidedByUsedInInvestingActivitiesContinuingOperations'] are nan.  \n", "56                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 Set the value of the ['NetCashProvidedByUsedInFinancingActivitiesContinuingOperations'] to 0.0 if all ['NetCashProvidedByUsedInFinancingActivitiesContinuingOperations'] are nan.  \n", "57                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         Set the value of the ['NetCashProvidedByUsedInOperatingActivities'] to 0.0 if all ['NetCashProvidedByUsedInOperatingActivities'] are nan.  \n", "58                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         Set the value of the ['NetCashProvidedByUsedInInvestingActivities'] to 0.0 if all ['NetCashProvidedByUsedInInvestingActivities'] are nan.  \n", "59                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         Set the value of the ['NetCashProvidedByUsedInFinancingActivities'] to 0.0 if all ['NetCashProvidedByUsedInFinancingActivities'] are nan.  \n", "60                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   Set the value of the ['CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations'] to 0.0 if all ['CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations'] are nan.  \n", "61                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   Set the value of the ['CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations'] to 0.0 if all ['CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations'] are nan.  \n", "62                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   Set the value of the ['CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations'] to 0.0 if all ['CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations'] are nan.  \n", "63                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           Set the value of the ['EffectOfExchangeRateFinal'] to 0.0 if all ['EffectOfExchangeRateFinal'] are nan.  \n", "64                                                                                                                                                                                                                                             Sums up the values in the columns ['NetCashProvidedByUsedInOperatingActivities', 'NetCashProvidedByUsedInInvestingActivities', 'NetCashProvidedByUsedInFinancingActivities', 'EffectOfExchangeRateFinal'] into the column 'CashPeriodIncreaseDecreaseIncludingExRateEffectFinal', if the column 'CashPeriodIncreaseDecreaseIncludingExRateEffectFinal' is nan and if all columns ['NetCashProvidedByUsedInOperatingActivities', 'NetCashProvidedByUsedInInvestingActivities', 'NetCashProvidedByUsedInFinancingActivities', 'EffectOfExchangeRateFinal'] have a value  \n", "65                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         Tries to find and correct cases when discontinued operations are reported and the 'Sum' tags (e.g.'NetCashProvidedByUsedIn...Activities') were used to report the continuing operation instead using the of the  '...ContinuingOperations' tags. (e.g. 'NetCashProvidedByUsedInOperatingActivitiesContinuingOperations').  \n", "66                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    Checks whether the sum of ['NetCashProvidedByUsedInOperatingActivitiesContinuingOperations', 'CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations'] equals the value in 'NetCashProvidedByUsedInOperatingActivities'  \n", "67                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    Checks whether the sum of ['NetCashProvidedByUsedInFinancingActivitiesContinuingOperations', 'CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations'] equals the value in 'NetCashProvidedByUsedInFinancingActivities'  \n", "68                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    Checks whether the sum of ['NetCashProvidedByUsedInInvestingActivitiesContinuingOperations', 'CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations'] equals the value in 'NetCashProvidedByUsedInInvestingActivities'  \n", "69                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      Checks whether the sum of ['NetCashProvidedByUsedInOperatingActivities', 'NetCashProvidedByUsedInFinancingActivities', 'NetCashProvidedByUsedInInvestingActivities', 'EffectOfExchangeRateFinal'] equals the value in 'CashPeriodIncreaseDecreaseIncludingExRateEffectFinal'  \n", "70                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      Checks whether the CashAndCashEquivalentsEndOfPeriod is set.  "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["cf_standardizer_result_bag.process_description_df"]}, {"cell_type": "markdown", "id": "a4690046-7c6e-4806-8397-b0bc75a0169a", "metadata": {}, "source": ["Let's discuss them in more detail.\n", "\n", "**PrePivotRules**\n", "- `PrePivotDeduplicate`: Some data points appear more than once, so we have to deduplicate them to be able to pivot the data\n", "- `PrePivotMaxQtrs`: We are only interested in data points with qtrs <= 4\n", "- `PrePivotCorrectSign`: There are two rules to correct the sign of values. There are data points, which always should be a positive number, for instance values for tags with the prefix 'Proceeds' should be positive and values for tags with the prefix 'Payments' should be negative. However, sometimes data is entered the wrong way, or the 'inverted' flag was set falsely.\n", "- `PrePivotCashAtEndOfPeriod`: An important part of the Cash Flow statement is the 'Cash' at end of period. One problem is, that the cash at end of period is a data point that indicates a point in time, and therefore has 'qtrs' set to 0. However, all other datapoints of the Cash Flow statements, are value that cover a period of time, and therefore have values from 1-4 in the 'qtrs' column. Since we have to use the 'qtrs' column when we pivot, and therefore also group the datapoints, we would lose 'Cash' at the end of period. So this rule creates copies of the Cash data points with appropriate 'qtrs' values. Moreover, it also includes the suffix 'EndOfPeriod' to all tags which could indicate 'Cash' at the end of Period.\n", "\n", "**PreRules**\n", "- `PreCorrectMixUpContinuingOperations`: In a Cash Flow statement, companies separate data points for continuing and discontinued activivities by using the appropriate tags. Continuing operation activities are tagged with`NetCashProvidedByUsedInXYZActivitiesContinuingOperations` and discontinued operation activities are tagged with `CashProvidedByUsedInXYZActivitiesDiscontinuedOperations` (where XYZ is either Operating, Financing, or Investing). Then there is also the `NetCashProvidedByUsedInXYZActivities` which should contain the sum of continuing and discontinued operation. When a company doesn't report discontinued operation (btw, only about 5% of the reports contain discontinued operation data points), they could use either `NetCashProvidedByUsedInXYZActivitiesContinuingOperations` or `NetCashProvidedByUsedInXYZActivities` since `CashProvidedByUsedInXYZActivitiesDiscontinuedOperations` is 0 in these cases. And in fact, in these cases, most of the time the `NetCashProvidedByUsedInXYZActivities` is used to report continuing operation (about 80% of the cases). However, there are reports which us `NetCashProvidedByUsedInXYZActivities` to report continuing activities, when they also report discontinued activities. Which is wrong. This rule is a first rule, which detects and corrects such cases. There is another POST rule at the very end, which detects additional cases like that.\n", "\n", "**MainRules**\n", "- `MAIN_CF_#1_NETCASH_#1/2/3_CashProvidedByUsedInXYZActivitiesDiscontinuedOperations<-CashProvidedByUsedInDiscontinuedOperationsXYZActivities`<br> As mentioned before, discontinued operations reported with the tag `CashProvidedByUsedInXYZActivitiesDiscontinuedOperations` (again, XYZ is either operating, financing, or investing). However, there are very few reports, which use the tag `CashProvidedByUsedInDiscontinuedOperationsXYZActivities` instead. So if the latter one is used, we just rename it.\n", "- `MAIN_CF_#1_NETCASH_#4/5/6_NetCashProvidedByUsedInXYZActivities`<br> Next, we calculate the sum tags NetCashProvidedByUsedInXYZActivities, if not already set, by adding tags for continuing and discontinued activities.\n", "- `MAIN_CF_#2_EFF_EXRATE` (Group) <br>This group defines the `EffectOfExchangeRateFinal` (Note: this is not an official US-GAAP tag and we use it to have a shorter name). There are two 'paths', how `EffectOfExchangeRateFinal` can be calculated. The first is to define \n", "`EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperations` if not set by adding `EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalents` and `EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsDisposalGroupIncludingDiscontinuedOperations`. If `EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalents` it could also be that `EffectOfExchangeRateOnCash` or `EffectOfExchangeRateOnCashAndCashEquivalents` was used. This path is used in about 90% of the reports were EffectOfExchangeRate is reported. The second path is the sum of `EffectOfExchangeRateOnCashAndCashEquivalentsContinuingOperations` and `EffectOfExchangeRateOnCashAndCashEquivalentsDiscontinuedOperations`. Instead of those two tags, also the tags `EffectOfExchangeRateOnCashContinuingOperations` and `EffectOfExchangeRateOnCashDiscontinuedOperations` could be used.\n", "- `MAIN_CF_#3_INC_DEC` (Group) <br>This group defines the `CashPeriodIncreaseDecreaseIncludingExRateEffectFinal` (Note: this is not an official US-GAAP tag name and is just used to have a shorter name). There are three tags, which could report CashPeriodIncDec. First priority is  `CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsPeriodIncreaseDecreaseIncludingExchangeRateEffect`. Second priority is `CashAndCashEquivalentsPeriodIncreaseDecrease`. Third priority is `CashPeriodIncreaseDecrease`.\n", "- `MAIN_CF_#4_EOP` (Group) <br>This group defines the `CashAndCashEquivalentsEndOfPeriod` (Note: this is not an official US-GAAP tag name and is just used to have a shorter name). There are multiple tags, which can indicate the Cash at a certain point in time and therefore can appear in a report. `CashAndCashEquivalentsEndOfPeriod` is set with the value of the following tags, where as the order is the priority: `CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperationsEndOfPeriod`, `CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsDisposalGroupIncludingDiscontinuedOperationsEndOfPeriod`, `CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsEndOfPeriod`, `CashAndCashEquivalentsAtCarryingValueIncludingDiscontinuedOperationsEndOfPeriod`, `CashAndCashEquivalentsAtCarryingValueEndOfPeriod`, `CashAndDueFromBanksEndOfPeriod`, `CashEndOfPeriod` (Note: the suffix was added by the PrePivotCashAtEndOfPeriod rule).\n", "- `MAIN_CF_#5_DeprDeplAmort` (Group) <br>This group of rules evaluates the final value for `DepreciationDepletionAndAmortization` according to the following formula:\n", "    <pre>\n", "                      + AmortizationOfIntangibleAssets\n", "                      + AmortizationOfDeferredCharges\n", "                      + AmortizationOfFinancingCosts\n", "                      -------------\n", "                    + Amortization\n", "                    + Depreciation\n", "                    --------------\n", "                  + DepreciationAndAmortization\n", "                  + Depletion\n", "                  ----------\n", "                    DepreciationDepletionAndAmortization\n", "    </pre>\n", "- `MAIN_CF_#6_ProSalesInvest_#1_ProceedsFromSaleOfInvestments`<br> Calculates the `ProceedsFromSaleOfInvestments` by summing up the values of `ProceedsFromSaleOfAvailableForSaleSecurities`, `ProceedsFromSaleOfTradingSecurities`, `ProceedsFromSaleOfEquitySecurities`, `ProceedsFromSaleOfDebtSecurities`, `ProceedsFromSaleAndMaturityOfOtherInvestments`, `ProceedsFromMaturitiesPrepaymentsAndCallsOfAvailableForSaleSecurities`, `ProceedsFromSaleOfInvestmentsInAffiliates`, `ProceedsFromSaleOfHeldToMaturitySecurities`\n", "- `MAIN_CF_#7_PayDividends_#1_PaymentsOfDividends` <br> Calculates the `PaymentsOfDividends` by summing up the values of `PaymentsOfDividendsCommonStock`, `PaymentsOfDividendsPreferredStockAndPreferenceStock`, `PaymentsOfDividendsMinorityInterest`\n", "\n", "**PostRules**\n", "- `POST_CF_#1/4/7_CashProvidedByUsedInXYZActivitiesDiscontinuedOperations`: <br> If `CashProvidedByUsedInXYZActivitiesDiscontinuedOperations` is not set, calculate it by `NetCashProvidedByUsedInXYZActivities` - `NetCashProvidedByUsedInXYZActivitiesContinuingOperations`\n", "- `POST_CF_#2/5/8_NetCashProvidedByUsedInXYZActivitiesContinuingOperations`: <br> If `NetCashProvidedByUsedInXYZActivitiesContinuingOperations` is not set, calculate it by `NetCashProvidedByUsedInXYZActivities` - `CashProvidedByUsedInXYZActivitiesDiscontinuedOperations`\n", "- `POST_CF_#3/6/9_NetCashProvidedByUsedInXYZActivitiesContinuingOperations/CashProvidedByUsedInXYZActivitiesDiscontinuedOperations`: <br> if onl< `NetCashProvidedByUsedInXYZActivities` is set, then assume that only continuing activities are reported and therfore set `NetCashProvidedByUsedInXYZActivitiesContinuingOperations` to `NetCashProvidedByUsedInXYZActivities` and set `CashProvidedByUsedInXYZActivitiesDiscontinuedOperations` to 0.0\n", "- `POST_CF_#10..8`: <br> If `NetCashProvidedByUsedInXYZActivitiesContinuingOperations`, `CashProvidedByUsedInXYZActivitiesDiscontinuedOperations`, or `NetCashProvidedByUsedInXYZActivities` were not set yet, set them 0\n", "- `POST_CF_#19_EffectOfExchangeRateFinal`: <br> If `EffectOfExchangeRateFinal` is not set, set it to 0.0\n", "- `POST_CF_#20_CashPeriodIncreaseDecreaseIncludingExRateEffectFinal`: <br> if `CashPeriodIncreaseDecreaseIncludingExRateEffectFinal` is not set yet, set it by summing up `NetCashProvidedByUsedInOperatingActivities`, `NetCashProvidedByUsedInInvestingActivities`, `NetCashProvidedByUsedInFinancingActivities`, `EffectOfExchangeRateFinal`\n", "- `POST_CF_#21_..` <br> This is the second rule that identifies cases, where `NetCashProvidedByUsedInXYZActivities` was used to report continuing activities instead of `NetCashProvidedByUsedInXYZActivitiesContinuingOperations`. \n", "\n", "**ValidationRules**\n", "There are five validation rules:\n", "- `NetCashProvidedByUsedInOperatingActivities` =  `NetCashProvidedByUsedInOperatingActivitiesContinuingOperations` + `CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations`\n", "- `NetCashProvidedByUsedInFinancingActivities` =  `NetCashProvidedByUsedInFinancingActivitiesContinuingOperations` + `CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations`\n", "- `NetCashProvidedByUsedInInvestingActivities` =  `NetCashProvidedByUsedInInvestingActivitiesContinuingOperations` + `CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations`\n", "- `CashPeriodIncreaseDecreaseIncludingExRateEffectFinal` = `NetCashProvidedByUsedInOperatingActivities` + `NetCashProvidedByUsedInFinancingActivities` + `NetCashProvidedByUsedInInvestingActivities` + `EffectOfExchangeRateFinal`\n", "- `CashAndCashEquivalentsEndOfPeriod` is set"]}, {"cell_type": "markdown", "id": "a32655cd-fb50-406d-9732-b60d883ef084", "metadata": {}, "source": ["The following overview tries to show the whole \"algorithm\" in textform:\n", "\n", "     PrePivot Rules\n", "       Deduplicate by ['adsh', 'coreg', 'report', 'ddate', 'uom', 'qtrs', 'tag', 'version']\n", "       Filter for qtrs <= 4\n", "       CorrectSign: should be a positive number     \n", "                                'ProceedsFromDivestitureOfBusinessesNetOfCashDivested', 'ProceedsFromIssuanceOfCommonStock',\n", "                                'ProceedsFromIssuanceOfDebt', 'ProceedsFromMaturitiesPrepaymentsAndCallsOfAvailableForSaleSecurities',\n", "                                'ProceedsFromSaleAndMaturityOfOtherInvestments', 'ProceedsFromSaleOfAvailableForSaleSecurities',\n", "                                'ProceedsFromSaleOfHeldToMaturitySecurities', 'ProceedsFromSaleOfIntangibleAssets',\n", "                                'ProceedsFromSaleOfPropertyPlantAndEquipment', 'ProceedsFromStockOptionsExercised',\n", "                                'AmortizationOfDeferredCharges', 'AmortizationOfFinancingCosts',\n", "                                'AmortizationOfIntangibleAssets', 'Depletion',\n", "                                'Depreciation', 'DepreciationAndAmortization',\n", "                                'DepreciationDepletionAndAmortization',\n", "       \n", "       CorrectSign: should be a negative number\n", "                                'PaymentsForRepurchaseOfCommonStock',\n", "                                'PaymentsOfDividends',\n", "                                'PaymentsOfDividendsCommonStock',\n", "                                'PaymentsOfDividendsMinorityInterest',\n", "                                'PaymentsOfDividendsPreferredStockAndPreferenceStock',\n", "                                'PaymentsToAcquireBusinessesNetOfCashAcquired',\n", "                                'PaymentsToAcquireIntangibleAssets',\n", "                                'PaymentsToAcquireInvestments',\n", "                                'PaymentsToAcquirePropertyPlantAndEquipment',\n", "                                'RepaymentsOfDebt',\n", "     \n", "       Ensure that CashAtEndOfPeriod information is available for every period\n", "     \n", "     \n", "     Pre Rules\n", "        Fix mixed up usage of NetCashProvidedByUsedXYZActivities/-ContinuingOperations\n", "     \n", "     Main Rules\n", "          + NetCashProvidedByUsedInOperatingActivitiesContinuingOperations\n", "          + CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations <- CashProvidedByUsedInDiscontinuedOperationsOperatingActivities\n", "          --------\n", "      + NetCashProvidedByUsedInOperatingActivities\n", "\n", "          + NetCashProvidedByUsedInInvestingActivitiesContinuingOperations\n", "          + CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations <- CashProvidedByUsedInDiscontinuedOperationsInvestingActivities\n", "          --------\n", "      + NetCashProvidedByUsedInInvestingActivities\n", "\n", "          + NetCashProvidedByUsedInInvestingActivitiesContinuingOperations\n", "          + CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations <- CashProvidedByUsedInDiscontinuedOperationsFinancingActivities\n", "          --------\n", "      + NetCashProvidedByUsedInInvestingActivities\n", "\n", "\n", "                  Prio 1\n", "                           <- EffectOfExchangeRateOnCash\n", "                       <- EffectOfExchangeRateOnCashAndCashEquivalents\n", "                  + EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalents\n", "                  + EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsDisposalGroupIncludingDiscontinuedOperations\n", "                  --------\n", "                  EffectOfExchangeRateOnCashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperations\n", "                 <-\n", "              EffectOfExchangeRateFinal\n", "\n", "\n", "                  Prio 2\n", "                  + EffectOfExchangeRateOnCashAndCashEquivalentsContinuingOperations <- EffectOfExchangeRateOnCashContinuingOperations\n", "                  + EffectOfExchangeRateOnCashAndCashEquivalentsDiscontinuedOperations <- EffectOfExchangeRateOnCashDiscontinuedOperations\n", "                  --------\n", "              EffectOfExchangeRateFinal\n", "\n", "\n", "      + EffectOfExchangeRateFinal\n", "      ---------------------------\n", "\n", "                   <- CashPeriodIncreaseDecrease\n", "             <- CashAndCashEquivalentsPeriodIncreaseDecrease\n", "        <- CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsPeriodIncreaseDecreaseIncludingExchangeRateEffect\n", "      CashPeriodIncreaseDecreaseIncludingExRateEffectFinal\n", "      ==================\n", "\n", "                                       <- CashEndOfPeriod\n", "                                  <- CashAndDueFromBanksEndOfPeriod\n", "                             <- CashAndCashEquivalentsAtCarryingValueEndOfPeriod\n", "                        <- CashAndCashEquivalentsAtCarryingValueIncludingDiscontinuedOperationsEndOfPeriod\n", "                   <- CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsEndOfPeriod\n", "              <- CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperationsEndOfPeriod\n", "        <- CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperationsEndOfPeriod\n", "      CashAndCashEquivalentsEndOfPeriod\n", "\n", "\n", "      Details of Operating Activities\n", "\n", "                  + AmortizationOfIntangibleAssets\n", "                  + AmortizationOfDeferredCharges\n", "                  + AmortizationOfFinancingCosts\n", "                  -------------\n", "                + Amortization\n", "                + Depreciation\n", "                --------------\n", "                + DepreciationAndAmortization\n", "                + Depletion\n", "                ----------\n", "            DepreciationDepletionAndAmortization\n", "\n", "            DeferredIncomeTaxExpenseBenefit\n", "            ShareBasedCompensation\n", "            IncreaseDecreaseInAccountsPayable\n", "            IncreaseDecreaseInAccruedLiabilities\n", "            InterestPaidNet\n", "            IncomeTaxesPaidNet\n", "\n", "\n", "      Details of Investing activities\n", "\n", "                + ProceedsFromSaleOfAvailableForSaleSecurities\n", "                + ProceedsFromSaleOfTradingSecurities\n", "                + ProceedsFromSaleOfEquitySecurities\n", "                + ProceedsFromSaleOfDebtSecurities\n", "                + ProceedsFromSaleAndMaturityOfOtherInvestments\n", "                + ProceedsFromMaturitiesPrepaymentsAndCallsOfAvailableForSaleSecurities\n", "                + ProceedsFromSaleOfInvestmentsInAffiliates\n", "                + ProceedsFromSaleOfHeldToMaturitySecurities\n", "                --------------------------------------------\n", "            ProceedsFromSaleOfInvestments\n", "\n", "            PaymentsToAcquirePropertyPlantAndEquipment\n", "            ProceedsFromSaleOfPropertyPlantAndEquipment\n", "            PaymentsToAcquireInvestments\n", "            PaymentsToAcquireBusinessesNetOfCashAcquired\n", "            ProceedsFromDivestitureOfBusinessesNetOfCashDivested\n", "            PaymentsToAcquireIntangibleAssets\n", "            ProceedsFromSaleOfIntangibleAssets\n", "\n", "\n", "      Details of Financing activities\n", "\n", "                  + PaymentsOfDividendsCommonStock\n", "                  + PaymentsOfDividendsPreferredStockAndPreferenceStock\n", "                  + PaymentsOfDividendsMinorityInterest\n", "                  -------------\n", "             PaymentsOfDividends\n", "\n", "             ProceedsFromIssuanceOfCommonStock\n", "             ProceedsFromStockOptionsExercised\n", "             PaymentsForRepurchaseOfCommonStock\n", "             ProceedsFromIssuanceOfDebt\n", "             RepaymentsOfDebt\n", "\n", "     Post Rules\n", "\n", "        Calculate missing \"Operating\" tags\n", "\n", "        + NetCashProvidedByUsedInOperatingActivities (if present)\n", "        - NetCashProvidedByUsedInOperatingActivitiesContinuingOperations (if present)\n", "        -------\n", "        = CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations (if not present)\n", "\n", "\n", "        + NetCashProvidedByUsedInOperatingActivities (if present)\n", "        - CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations (if present)\n", "        -------\n", "        = NetCashProvidedByUsedInOperatingActivitiesContinuingOperations (if not present)\n", "\n", "\n", "        if only NetCashProvidedByUsedInOperatingActivities is set:\n", "          NetCashProvidedByUsedInOperatingActivitiesContinuingOperations = NetCashProvidedByUsedInOperatingActivities\n", "          CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations = 0\n", "\n", "\n", "        Same as above for Financing and Investing triples\n", "\n", "        Set to Zero if still not set:\n", "           NetCashProvidedByUsedInOperatingActivitiesContinuingOperations\n", "           NetCashProvidedByUsedInInvestingActivitiesContinuingOperations\n", "           NetCashProvidedByUsedInFinancingActivitiesContinuingOperations\n", "           NetCashProvidedByUsedInOperatingActivities\n", "           NetCashProvidedByUsedInInvestingActivities\n", "           NetCashProvidedByUsedInFinancingActivities\n", "           CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations\n", "           CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations\n", "           CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations\n", "\n", "           EffectOfExchangeRateFinal\n", "\n", "        Set CashPeriodIncreaseDecreaseIncludingExRateEffectFinal (if not set yet)\n", "            + NetCashProvidedByUsedInOperatingActivities\n", "            + NetCashProvidedByUsedInInvestingActivities\n", "            + NetCashProvidedByUsedInFinancingActivities\n", "            + EffectOfExchangeRateFinal\n", "            ------------------\n", "            = CashPeriodIncreaseDecreaseIncludingExRateEffectFinal\n", "\n", "        Fix mixed up usage of NetCashProvidedByUsed...Activities/-ContinuingOperations\n"]}, {"cell_type": "markdown", "id": "c059bbc8-d260-4bbf-b5f2-26fe09ef8e14", "metadata": {}, "source": ["### Overview on applied rules\n", "It might be interesting to know how many rules are applied in general per report. In this example, we just look at the MAIN and PRE rules:"]}, {"cell_type": "code", "execution_count": 16, "id": "a6033eef-7032-4541-b973-b3c1a823c897", "metadata": {}, "outputs": [{"data": {"text/plain": ["0      2556\n", "1      1096\n", "2      9467\n", "3     14848\n", "4     23082\n", "5     22142\n", "6     26045\n", "7     55689\n", "8     36234\n", "9     35402\n", "10    41591\n", "11    26495\n", "12    22157\n", "13    15822\n", "14     6899\n", "15     3674\n", "16     2000\n", "17      735\n", "18      128\n", "19       32\n", "20       12\n", "Name: count_true_values, dtype: int64"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# just use a shorter variable name\n", "df=cf_standardizer_result_bag.applied_rules_log_df\n", "\n", "# we are just interested in the MAIN and PRE rules\n", "filtered_columns = df.columns[df.columns.str.contains('MAIN|PRE')]\n", "\n", "# count how many True values are in each row\n", "df['count_true_values'] = df[filtered_columns].sum(axis='columns')\n", "\n", "df.count_true_values.value_counts().sort_index()"]}, {"cell_type": "code", "execution_count": 17, "id": "3876170e-e71d-4262-a356-9c24673d644c", "metadata": {"tags": []}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "plt.hist(df.count_true_values)\n", "\n", "# Show the plot\n", "plt.show()"]}, {"cell_type": "markdown", "id": "f58fe161-8959-4dc9-b00d-8c1492b99349", "metadata": {}, "source": ["### Showing the applied rules for a specific report number\n", "If we analys a single report and want to know which rules were applied, we can do that with the following code:"]}, {"cell_type": "code", "execution_count": 18, "id": "7768b3f5-c855-4a24-a316-be0f533dcfb8", "metadata": {}, "outputs": [{"data": {"text/plain": ["['MAIN_1_CF_#3_INC_DEC_#3_CashPeriodIncreaseDecreaseIncludingExRateEffectFinal<-CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsPeriodIncreaseDecreaseIncludingExchangeRateEffect',\n", " 'MAIN_1_CF_#4_EOP_#5_CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsDisposalGroupIncludingDiscontinuedOperationsEndOfPeriod<-CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsEndOfPeriod',\n", " 'MAIN_1_CF_#4_EOP_#6_CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperationsEndOfPeriod<-CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsDisposalGroupIncludingDiscontinuedOperationsEndOfPeriod',\n", " 'MAIN_1_CF_#4_EOP_#7_CashAndCashEquivalentsEndOfPeriod<-CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsIncludingDisposalGroupAndDiscontinuedOperationsEndOfPeriod',\n", " 'MAIN_1_CF_#6_ProSalesInvest_#1_ProceedsFromSaleOfInvestments',\n", " 'POST_CF_#3_NetCashProvidedByUsedInOperatingActivitiesContinuingOperations/CashProvidedByUsedInOperatingActivitiesDiscontinuedOperations',\n", " 'POST_CF_#6_NetCashProvidedByUsedInFinancingActivitiesContinuingOperations/CashProvidedByUsedInFinancingActivitiesDiscontinuedOperations',\n", " 'POST_CF_#9_NetCashProvidedByUsedInInvestingActivitiesContinuingOperations/CashProvidedByUsedInInvestingActivitiesDiscontinuedOperations',\n", " 'POST_CF_#19_EffectOfExchangeRateFinal']"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["apple_10k_2022 = \"0000320193-22-000108\"\n", "apple_10k_2022_applied_rules_log_df = cf_standardizer_result_bag.applied_rules_log_df[cf_standardizer_result_bag.applied_rules_log_df.adsh==apple_10k_2022]\n", "\n", "# filter for the applied MAIN,PRE, and POST rules\n", "main_rule_cols =  df.columns[df.columns.str.contains('MAIN|PRE|POST')]\n", "main_rule_df = apple_10k_2022_applied_rules_log_df[main_rule_cols]\n", "\n", "# get the applied rules, by using the True and False values of main_rule_df.iloc[0] as a mask on the columns index\n", "main_rule_df.columns[main_rule_df.iloc[0]].tolist()"]}, {"cell_type": "markdown", "id": "a19d18d4-a0bf-4a4d-973e-2fb64f1e7f70", "metadata": {}, "source": ["- **MAIN_1_CF_#3_INC_DEC_#3_CashPeriodIncreaseDecreaseIncludingExRateEffectFinal<-CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsPeriodIncreaseDecreaseIncludingExchangeRateEffect'**: This is just a renaming to have a shorter name\n", "- **MAIN_1_CF_#4_EOP_#5/6/7..**: Apple reports Cash with the tag  `CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalentsEndOfPeriod` so this three rules just propagate its value to the tag `CashAndCashEquivalentsEndOfPeriod`\n", "- **MAIN_1_CF_#6_ProSalesInvest_#1_ProceedsFromSaleOfInvestments**: Apple has some income from sale of investments, this rule sums them up\n", "- **POST_CF_#3/6/9..**: Apple did not report discontinued activities and did report its operating activities with the `NetCashProvidedByUsedInXYZActivities` tag. In order to be comparable, its value is copied to the `NetCashProvidedByUsedInXYZActivitiesContinuingOperations` and `CashProvidedByUsedInXYZActivitiesDiscontinuedOperations` is set to 0\n", "- **POST_CF_#19_EffectOfExchangeRateFinal**:  Apple didn't report any EffectOnExchangeRate, so this tag is set to 0\n", "\n", "Compare it to the real report: https://www.sec.gov/Archives/edgar/data/320193/000032019322000108/0000320193-22-000108-index.htm\n", "\n", "As a conclusion, in the case of apples 2022 annual report, we just rename entries to standardized tag names and set values to 0, both of which are not critical."]}, {"cell_type": "code", "execution_count": null, "id": "f8dd7b37-31ac-405c-b407-41999f48cb85", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}
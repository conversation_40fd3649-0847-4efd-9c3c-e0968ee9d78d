Fragen:
- standard Framework verwenden, dass auch bereits parallelisierung beherrscht? -> Nein
- Alle Prozessschritte in Pipelines umwandeln, oder kann download z.B. belassen wie es ist? -> Ja
- Wie mit quarterly und daily umgehen? -> Im Moment wie gehabt

- Wie wird der Status gemanaged? Immer über Files, oder doch besser über eine Tabelle?
  -> Vorteil File -> der Ist Zustand ist immer korrekt
  -> Vorteil Status in Tabelle -> Abfrage ist immer identisch
- Wie machen wir das ganze robust / Fehlerbehandlung
  -> Wie wird geprüft, ob ein Schritt für einen Teil tatsächlich ausgeführt wurde
     -> Z.B. welche Files müssen vorhanden sein.
- Steps eines Tasks
  - calculate_tasks -> bestimmen, für welche Teile (zip-files) Tasks überhaupt erstellt werden muss
  - pro Task
    - prepare -> Verzeichnis estellen/vorhandenes löschen
    - do -> Schritt ausführen
    - commit -> irgen<PERSON><PERSON>e bestätigen, dass Task vollendet ist
    - finish (z.B. entfernen von zip files -> müsste aber im Transform gemacht werden
    - exception -> cleanup

- Performance Optimierung ist nicht wirklich das Thema, da die Logik nur einmal pro Quartal läuft

Schritte

- einfachste Variante:
  -> eine simple Hook Methode
- kleines Framework

- Hinzufügen von customer Schritten

Umbau von CheckForNewData
- müsste eigentlich im init-py vom Root Modul gemacht werden, so dass diese Logik immer läuft
  - dort könnte man einfach getconfiguration callen und dann den check ausführen
  - Vorteil -> check wird immer gemacht
  - Nachteil: wenn das aber so gemacht wird, dann ist die Frage, wie eigene Tasks registriert werden können.
              das wäre dann eigentlich immer zu spät, ausser man macht das über das setup py
              oder wir managen eigene Tasks separat -> eigenr check, ob sie ausgeführt werden müssen, oder nicht

    def call_function_from_string(module_function_str):

        try:
            module_str, function_str = module_function_str.rsplit('.', 1)
            module = importlib.import_module(module_str)
            function = getattr(module, function_str)
            return function()
        except ModuleNotFoundError:
            print(f"Module {module_str} not found.")
        except AttributeError:
            print(f"Function {function_str} not found in module {module_str}.")
- configparser.ConfigParser ist teil von Configuration, falls configfile geladen wurde
  -> müsste als Parameter bei der callback funktion übergeben werden.

Struktur
--------
  _1_1_filtered_raw
     quarter
       2009q2.zip
       ...
     all   -> memory intensiv

  _1_2_filtered_joined
     quarter
       2009q2.zip
       ...
     all   -> memory intensiv

  _1_3_filtered_by_stmt_joined
     quarter
       2009q2.zip
          BS
          IS
          CF
          ...
       ...

     all_by_stmt
       BS
       IS
       CF
       ...

     all -> memory intensiv oder nicht?

  _2_standardize
    -> read all By Stmt
       -> example für state in db
        -> neu machen -> auch nur ein single task, der allbystmt liest
        -> und in einen state schreibt, wenn all by stmt metainf file geänder hatchling

    all
       BS
       IS
       CF


Error
 by CF Standardisierung
  bereits im PRE Processing

   2024-11-19 06:46:24,031 [INFO] parallelexecution      items to process: 1
   2024-11-19 06:46:32,623 [INFO] standardizing  start PRE processing ...
   2024-11-19 06:46:44,066 [INFO] task_framework  Failed: StandardizerTask(root_path: C:\data\sec\automated\_1_3_filtered_by_stmt_joined\all_by_stmt, target_path: C:\data\sec\automated\_2_standardized) / Index contains duplicate entries, cannot reshape
   2024-11-19 06:46:44,814 [INFO] parallelexecution      commited chunk: 0
   2024-11-19 06:46:44,815 [WARNING] task_framework  not able to process StandardizerTask(root_path: C:\data\sec\automated\_1_3_filtered_by_stmt_joined\all_by_stmt, target_path: C:\data\sec\automated\_2_standardized)

 -> fehlt hier eventuell noch ein Filter
    direktes testen mit diesem Datenset




 open
    standardisieren könnte man direkt über all oder zuerst über die einzelnen quarter machen
    data/standardized/quarter/
        <zipname>/BS
                 /IS
                 /CF
        all      /BS
                 /IS
                 /CF

        allcombined

    data/standardizedcombined

    Ticker symbols
    outstanding shares / EPS_calc


-----------------
Duplicates:
    cf_joined_bag: JoinedDataBag = JoinedDataBag.load(
        str("C:/data/sec/automated/_1_3_filtered_by_stmt_joined/all_by_stmt/CF"))



    Entries: 11'471'352
    Duplicates: 359'471 by ['adsh', 'coreg', 'report', 'ddate', 'qtrs', 'tag', 'version']

vor pivot mit 'adsh', 'coreg', 'report', 'ddate', 'qtrs', 'tag']
0001391609-16-000491,,6,20160331,1,CashAndCashEquivalentsAtCarryingValueEndOfPeriod,us-gaap/2015,36.00000,13,0
0001391609-16-000542,,6,20160630,2,CashAndCashEquivalentsAtCarryingValueEndOfPeriod,us-gaap/2016,89.00000,14,0
0001589728-13-000008,,5,20130930,3,CashEndOfPeriod,us-gaap/2012,4933.00000,33,0

original df
-----------
sub_df     (322'285, 36)
pre_num_df (9'468'009, 16)

all_joined (OfficialTagsOnly)
------------
sub_df       (345'678, 36)
pre_num_df (10'157'235, 16)

  - no filter
sub_df        (345'678, 36)
pre_num_df (11'471'352, 16)




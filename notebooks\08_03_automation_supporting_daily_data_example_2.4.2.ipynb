{"cells": [{"cell_type": "markdown", "id": "c853d5fc-b232-4127-8deb-01aaeebf19a9", "metadata": {}, "source": ["## A working example of the postupdateprocesses function: `secfsdstools.x_examples.automation.memory_optimized_daily_automation.define_extra_processes` (introduced in 2.4.2)"]}, {"cell_type": "markdown", "id": "723689b5-7df4-43cb-9343-302b8465df4b", "metadata": {"tags": []}, "source": ["<span style=\"color: #FF8C00;\">==========================================================</span>\n", "\n", "**If you find this tool useful, a sponsorship would be greatly appreciated!**\n", "\n", "**https://github.com/sponsors/HansjoergW**\n", "\n", "How to get in touch\n", "\n", "* Found a bug: https://github.com/HansjoergW/sec-fincancial-statement-data-set/issues\n", "* Have a remark: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/general\n", "* Have an idea: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/ideas\n", "* Have a question: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/q-a\n", "* Have something to show: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/show-and-tell\n", "\n", "<span style=\"color: #FF8C00;\">==========================================================</span>"]}, {"cell_type": "markdown", "id": "6e575cc6-768a-424e-924b-3007abc95ca2", "metadata": {}, "source": ["### What this pipeline creates\n", "\n", "It result in creating the following bags:\n", "\n", "- from the quarterly zip files\n", "  - a single joined bag per statement (BS, IS, CF, ..) that will contain the data from all available quarter.\n", "  - single standardized bags for each of BS, IS, CF which contain data from all the available quarters.\n", "  - a single joined bag containing all the data from all statements from all available quarters.\n", "- from daily processing of data not yet available in the quarter zip files\n", "  - a single joined bag per statement (BS, IS, CF, ..).\n", "  - single standardized bags for each of BS, IS, CF.\n", "  - a single joined bag containing.\n", "- combined quarterly and daily processed data\n", "  - a single joined bag per statement (BS, IS, CF, ..).\n", "  - single standardized bags for each of BS, IS, CF.\n", "  - a single joined bag containing.\n", "  \n", "Moreover, new filed report are added every day.\n", "\n", "This version has a low memory footprint and should run without any problems on 16 GB.\n", "\n", "\n", "### How to use the example\n", "\n", "\n", "You can use this function directly by adding it to your configuration file together with some additional configuration parameters used by it: \n", "<pre>\n", "[DEFAULT]\n", "...\n", "postupdateprocesses=secfsdstools.x_examples.automation.memory_optimized_daily_automation.define_extra_processes\n", "dailyprocessing = True\n", "\n", "# configuration for quarterly data / daily data\n", "[Filter]\n", "filtered_quarterly_joined_by_stmt_dir = C:/data/sec/automated/_1_by_quarter/_1_filtered_joined_by_stmt\n", "filtered_daily_joined_by_stmt_dir = C:/data/sec/automated/_1_by_day/_1_filtered_joined_by_stmt\n", "parallelize = True\n", "\n", "[Standardizer]\n", "standardized_quarterly_by_stmt_dir = C:/data/sec/automated/_1_by_quarter/_2_standardized_by_stmt\n", "standardized_daily_by_stmt_dir = C:/data/sec/automated/_1_by_day/_2_standardized_by_stmt\n", "\n", "[<PERSON><PERSON>]\n", "concat_quarterly_joined_by_stmt_dir = C:/data/sec/automated/_2_all_quarter/_1_joined_by_stmt\n", "concat_daily_joined_by_stmt_dir = C:/data/sec/automated/_2_all_day/_1_joined_by_stmt\n", "\n", "concat_quarterly_joined_all_dir = C:/data/sec/automated/_2_all_quarter/_2_joined\n", "concat_daily_joined_all_dir = C:/data/sec/automated/_2_all_day/_2_joined\n", "\n", "concat_quarterly_standardized_by_stmt_dir = C:/data/sec/automated/_2_all_quarter/_3_standardized_by_stmt\n", "concat_daily_standardized_by_stmt_dir = C:/data/sec/automated/_2_all_day/_3_standardized_by_stmt\n", "\n", "concat_all_joined_by_stmt_dir = C:/data/sec/automated/_3_all/_1_joined_by_stmt\n", "concat_all_joined_dir = C:/data/sec/automated/_3_all/_2_joined\n", "concat_all_standardized_by_stmt_dir = C:/data/sec/automated/_3_all/_3_standardized_by_stmt\n", "</pre>\n", "\n", "The function will add additional steps to process the data added by the quartlerly zip files, to process the data of filed reports\n", "not yet in the quarterly zip files, and finally to combine all data together.\n", "\n", "#### Processing the data in the quarterly zip files\n", "The first step creates a joined bag for every zip file which is filtered for 10-K and 10-Q reports only\n", "and also applies the filters `ReportPeriodRawFilter`, `MainCoregRawFilter`, `USDOnlyRawFilter`, `OfficialTagsOnlyRawFilter`. \n", "Furthermore, the data is also split by stmt. If you set `parallelize = False`, the step will use less memory in the initial run\n", "but be a little bit slower. This will only make a difference during the first run, when all available quarters from the past\n", "are processed.\n", "\n", "The filtered joined bag is stored under the path that is defined under `filtered_quarterly_joined_by_stmt_dir` in the configuration file.\n", "The resulting directory structure will look like this:\n", "\n", "\n", "    <filtered_quarterly_joined_by_stmt_dir>\n", "        quarter\n", "            2009q2.zip\n", "                BS\n", "                CF\n", "                CI\n", "                CP\n", "                EQ\n", "                IS\n", "            ...\n", "\n", "The second step uses the the results of the first step and creates standardized bags for every quarter.\n", "The results are stored under the path that is defined under `standardized_quarterly_by_stmt_dir` and the structure will look like this:\n", "\n", "    <standardized_quarterly_by_stmt_dir>\n", "        2009q2.zip\n", "            BS\n", "            CF\n", "            IS\n", "        2009q3.zip\n", "            BS\n", "            CF\n", "            IS\n", "        ...\n", "\n", "The third step concatenates per statement all available dat from the first step.\n", "So, you will have one bag with all BS information for all quarters, one for CF, and so on.\n", "The results are stored under the path that is defined under `concat_quarterly_joined_by_stmt_dir` and the structure will look like this:\n", "\n", "    <concat_quarterly_joined_by_stmt_dir>\n", "        BS\n", "        CF\n", "        CI\n", "        CP\n", "        EQ\n", "        IS\n", "\n", "The fourth step concatenates the results from the third step into a single bag. \n", "So, you will have all data from all quarters in one bag. Especially when using predicate pushdown, you will still get\n", "reasonable load performance.\n", "\n", "The resutling bag is stored under the path that is defined under `concat_quarterly_joined_all_dir`.\n", "\n", "\n", "The fith step concatenates the standardized bags together (per statement). You will get a single standardize bag for each \n", "BS, CF, and IS containing all the datat from all quarters.\n", "\n", "The results are stored under the path that is defined under `concat_quarterly_standardized_by_stmt_dir` and the structure will look like this:\n", "\n", "    <concat_quarterly_standardized_by_stmt_dir>\n", "        BS\n", "        CF\n", "        IS\n", "        all\n", "\n", "\n", "#### Daily Processing the data not yet in quarterly zip files\n", "The same steps are executed for the filed reports that are not yet contained in the available quarterly zip files.\n", "\n", "The results of the filtering step are stored at `filtered_daily_joined_by_stmt_dir`.\n", "The results of the stndardizing step are stored at `standardized_daily_by_stmt_dir`.\n", "The bags that were the joined per statement are stored at `concat_daily_joined_by_stmt_dir`.\n", "The bag containing all joined data is stored at `concat_daily_joined_all_dir`.\n", "The standardize bags for BS, IS, and CF are stored at `concat_daily_standardized_by_stmt_dir`.\n", "\n", "#### Combining quarterly and daily data\n", "Finally, bags containing both the quarterly and the daily data are created in the following three steps.\n", "\n", "First, bags containing joined per statement data are stored under `concat_all_joined_by_stmt_dir`.\n", "Second, one single bag containing the joined data is stored under `concat_all_joined_dir` . \n", "Third, standardized bags for BS, IS, and CF are stored under `concat_all_standardized_by_stmt_dir`.\n", "\n", "**Hint: This bags can now be loaded directly with the load method of JoinedDataBag, resp StandardizedBag.**\n", "\n", "\n", "### How the example is implemented.\n", "\n", "Let us have a look at the implementation of the the function `define_extra_processes`:\n"]}, {"cell_type": "code", "execution_count": null, "id": "c1513103-ed92-43ca-b440-ec6484a9d3e0", "metadata": {}, "outputs": [], "source": ["def define_extra_processes(configuration: Configuration) -> List[AbstractProcess]:\n", "    filtered_quarterly_joined_by_stmt_dir = configuration.get_parser().get(\n", "        section=\"Filter\", option=\"filtered_quarterly_joined_by_stmt_dir\"\n", "    )\n", "    filtered_daily_joined_by_stmt_dir = configuration.get_parser().get(\n", "        section=\"Filter\", option=\"filtered_daily_joined_by_stmt_dir\"\n", "    )\n", "\n", "    filter_parallelize = configuration.get_parser().get(section=\"Filter\", option=\"parallelize\", fallback=True)\n", "\n", "    standardized_quarterly_by_stmt_dir = configuration.get_parser().get(\n", "        section=\"Standardizer\", option=\"standardized_quarterly_by_stmt_dir\"\n", "    )\n", "    standardized_daily_by_stmt_dir = configuration.get_parser().get(\n", "        section=\"Standardizer\", option=\"standardized_daily_by_stmt_dir\"\n", "    )\n", "\n", "    concat_quarterly_joined_by_stmt_dir = configuration.get_parser().get(\n", "        section=\"Concat\", option=\"concat_quarterly_joined_by_stmt_dir\"\n", "    )\n", "    concat_daily_joined_by_stmt_dir = configuration.get_parser().get(\n", "        section=\"Concat\", option=\"concat_daily_joined_by_stmt_dir\"\n", "    )\n", "\n", "    concat_quarterly_joined_all_dir = configuration.get_parser().get(\n", "        section=\"Concat\", option=\"concat_quarterly_joined_all_dir\"\n", "    )\n", "    concat_daily_joined_all_dir = configuration.get_parser().get(section=\"Concat\", option=\"concat_daily_joined_all_dir\")\n", "\n", "    concat_quarterly_standardized_by_stmt_dir = configuration.get_parser().get(\n", "        section=\"Concat\", option=\"concat_quarterly_standardized_by_stmt_dir\"\n", "    )\n", "    concat_daily_standardized_by_stmt_dir = configuration.get_parser().get(\n", "        section=\"Concat\", option=\"concat_daily_standardized_by_stmt_dir\"\n", "    )\n", "\n", "    concat_all_joined_by_stmt_dir = configuration.get_parser().get(\n", "        section=\"Concat\", option=\"concat_all_joined_by_stmt_dir\"\n", "    )\n", "\n", "    concat_all_joined_dir = configuration.get_parser().get(section=\"Concat\", option=\"concat_all_joined_dir\")\n", "\n", "    concat_all_standardized_by_stmt_dir = configuration.get_parser().get(\n", "        section=\"Concat\", option=\"concat_all_standardized_by_stmt_dir\"\n", "    )\n", "\n", "    processes: List[AbstractProcess] = []\n", "\n", "    # QUARTERLY DATA Processing    \n", "    # processes the data from the quarterly zip files from the SEC\n", "    processes.append(LoggingProcess(title=\"Post Update Processes For Quarterly Data Started\", lines=[]))\n", "    \n", "    processes.append(\n", "        # 1. Filter, join, and save by stmt\n", "        FilterProcess(\n", "            db_dir=configuration.db_dir,\n", "            target_dir=filtered_quarterly_joined_by_stmt_dir,\n", "            bag_type=\"joined\",\n", "            save_by_stmt=True,\n", "            execute_serial=not filter_parallelize,\n", "            file_type=\"quarter\",\n", "        )\n", "    )\n", "\n", "    processes.append(\n", "        # 2. Standardize the data for every quarter\n", "        StandardizeProcess(\n", "            root_dir=f\"{filtered_quarterly_joined_by_stmt_dir}/quarter\", target_dir=standardized_quarterly_by_stmt_dir\n", "        ),\n", "    )\n", "\n", "    processes.extend(\n", "        [\n", "            # 3. building datasets with all entries by stmt\n", "            ConcatByNewSubfoldersProcess(\n", "                root_dir=f\"{filtered_quarterly_joined_by_stmt_dir}/quarter\",\n", "                target_dir=f\"{concat_quarterly_joined_by_stmt_dir}/BS\",\n", "                pathfilter=\"*/BS\",\n", "            ),\n", "            ConcatByNewSubfoldersProcess(\n", "                root_dir=f\"{filtered_quarterly_joined_by_stmt_dir}/quarter\",\n", "                target_dir=f\"{concat_quarterly_joined_by_stmt_dir}/CF\",\n", "                pathfilter=\"*/CF\",\n", "            ),\n", "            ConcatByNewSubfoldersProcess(\n", "                root_dir=f\"{filtered_quarterly_joined_by_stmt_dir}/quarter\",\n", "                target_dir=f\"{concat_quarterly_joined_by_stmt_dir}/CI\",\n", "                pathfilter=\"*/CI\",\n", "            ),\n", "            ConcatByNewSubfoldersProcess(\n", "                root_dir=f\"{filtered_quarterly_joined_by_stmt_dir}/quarter\",\n", "                target_dir=f\"{concat_quarterly_joined_by_stmt_dir}/CP\",\n", "                pathfilter=\"*/CP\",\n", "            ),\n", "            ConcatByNewSubfoldersProcess(\n", "                root_dir=f\"{filtered_quarterly_joined_by_stmt_dir}/quarter\",\n", "                target_dir=f\"{concat_quarterly_joined_by_stmt_dir}/EQ\",\n", "                pathfilter=\"*/EQ\",\n", "            ),\n", "            ConcatByNewSubfoldersProcess(\n", "                root_dir=f\"{filtered_quarterly_joined_by_stmt_dir}/quarter\",\n", "                target_dir=f\"{concat_quarterly_joined_by_stmt_dir}/IS\",\n", "                pathfilter=\"*/IS\",\n", "            ),\n", "        ]\n", "    )\n", "\n", "    # 4. create a single joined bag with all the data filtered and joined\n", "    processes.append(\n", "        ConcatByChangedTimestampProcess(\n", "            root_dir=concat_quarterly_joined_by_stmt_dir,\n", "            target_dir=concat_quarterly_joined_all_dir,\n", "        )\n", "    )\n", "\n", "    # 5. concate the standardized bags together by stmt (BS, IS, CF).\n", "    processes.extend(\n", "        [\n", "            ConcatByNewSubfoldersProcess(\n", "                root_dir=standardized_quarterly_by_stmt_dir,\n", "                target_dir=f\"{concat_quarterly_standardized_by_stmt_dir}/BS\",\n", "                pathfilter=\"*/BS\",\n", "                in_memory=True,  # Standardized Bag only work with in_memory\n", "            ),\n", "            ConcatByNewSubfoldersProcess(\n", "                root_dir=standardized_quarterly_by_stmt_dir,\n", "                target_dir=f\"{concat_quarterly_standardized_by_stmt_dir}/CF\",\n", "                pathfilter=\"*/CF\",\n", "                in_memory=True,  # Standardized Bag only work with in_memory\n", "            ),\n", "            ConcatByNewSubfoldersProcess(\n", "                root_dir=standardized_quarterly_by_stmt_dir,\n", "                target_dir=f\"{concat_quarterly_standardized_by_stmt_dir}/IS\",\n", "                pathfilter=\"*/IS\",\n", "                in_memory=True,  # Standardized Bag only work with in_memory\n", "            ),\n", "        ]\n", "    )\n", "\n", "    # DAILY DATA Processing\n", "    processes.append(LoggingProcess(title=\"Post Update Processes For Daily Data Started\", lines=[]))\n", "\n", "    # clean daily data covered now by quarterly data\n", "    processes.append(\n", "        ClearDailyDataProcess(\n", "            db_dir=configuration.db_dir,\n", "            filtered_daily_joined_by_stmt_dir=filtered_daily_joined_by_stmt_dir,\n", "            standardized_daily_by_stmt_dir=standardized_daily_by_stmt_dir,\n", "        )\n", "    )\n", "\n", "    # 1. Filter, join, and save by stmt\n", "    processes.append(\n", "        FilterProcess(\n", "            db_dir=configuration.db_dir,\n", "            target_dir=filtered_daily_joined_by_stmt_dir,\n", "            bag_type=\"joined\",\n", "            save_by_stmt=True,\n", "            execute_serial=not filter_parallelize,\n", "            file_type=\"daily\",\n", "        )\n", "    )\n", "\n", "    processes.append(\n", "        # 2. Standardize the data for daily data\n", "        StandardizeProcess(\n", "            root_dir=f\"{filtered_daily_joined_by_stmt_dir}/daily\", target_dir=standardized_daily_by_stmt_dir\n", "        ),\n", "    )\n", "\n", "    processes.extend(\n", "        [\n", "            # 3. building datasets with all entries by stmt for daily data\n", "            ConcatByChangedTimestampProcess(\n", "                root_dir=f\"{filtered_daily_joined_by_stmt_dir}/daily\",\n", "                target_dir=f\"{concat_daily_joined_by_stmt_dir}/BS\",\n", "                pathfilter=\"*/BS\",\n", "            ),\n", "            ConcatByChangedTimestampProcess(\n", "                root_dir=f\"{filtered_daily_joined_by_stmt_dir}/daily\",\n", "                target_dir=f\"{concat_daily_joined_by_stmt_dir}/CF\",\n", "                pathfilter=\"*/CF\",\n", "            ),\n", "            ConcatByChangedTimestampProcess(\n", "                root_dir=f\"{filtered_daily_joined_by_stmt_dir}/daily\",\n", "                target_dir=f\"{concat_daily_joined_by_stmt_dir}/CI\",\n", "                pathfilter=\"*/CI\",\n", "            ),\n", "            ConcatByChangedTimestampProcess(\n", "                root_dir=f\"{filtered_daily_joined_by_stmt_dir}/daily\",\n", "                target_dir=f\"{concat_daily_joined_by_stmt_dir}/CP\",\n", "                pathfilter=\"*/CP\",\n", "            ),\n", "            ConcatByChangedTimestampProcess(\n", "                root_dir=f\"{filtered_daily_joined_by_stmt_dir}/daily\",\n", "                target_dir=f\"{concat_daily_joined_by_stmt_dir}/EQ\",\n", "                pathfilter=\"*/EQ\",\n", "            ),\n", "            ConcatByChangedTimestampProcess(\n", "                root_dir=f\"{filtered_daily_joined_by_stmt_dir}/daily\",\n", "                target_dir=f\"{concat_daily_joined_by_stmt_dir}/IS\",\n", "                pathfilter=\"*/IS\",\n", "            ),\n", "        ]\n", "    )\n", "\n", "    # 4. create a single joined bag with all the data filtered and joined for daily data\n", "    processes.append(\n", "        ConcatByChangedTimestampProcess(\n", "            root_dir=concat_daily_joined_by_stmt_dir,\n", "            target_dir=concat_daily_joined_all_dir,\n", "        )\n", "    )\n", "\n", "    # 5. concate the standardized bags together by stmt (BS, IS, CF) for daily data.\n", "    processes.extend(\n", "        [\n", "            ConcatByNewSubfoldersProcess(\n", "                root_dir=standardized_daily_by_stmt_dir,\n", "                target_dir=f\"{concat_daily_standardized_by_stmt_dir}/BS\",\n", "                pathfilter=\"*/BS\",\n", "                in_memory=True,  # Standardized Bag only work with in_memory\n", "            ),\n", "            ConcatByNewSubfoldersProcess(\n", "                root_dir=standardized_daily_by_stmt_dir,\n", "                target_dir=f\"{concat_daily_standardized_by_stmt_dir}/CF\",\n", "                pathfilter=\"*/CF\",\n", "                in_memory=True,  # Standardized Bag only work with in_memory\n", "            ),\n", "            ConcatByNewSubfoldersProcess(\n", "                root_dir=standardized_daily_by_stmt_dir,\n", "                target_dir=f\"{concat_daily_standardized_by_stmt_dir}/IS\",\n", "                pathfilter=\"*/IS\",\n", "                in_memory=True,  # Standardized Bag only work with in_memory\n", "            ),\n", "        ]\n", "    )\n", "\n", "    # Concat daily and quarter together\n", "    processes.append(\n", "        LoggingProcess(title=\"Post Update Processes To Combine Quarterly And Daily Data Started\", lines=[])\n", "    )\n", "\n", "    # 1. concat joined_by_statement\n", "    processes.extend(\n", "        [\n", "            ConcatMultiRootByChangedTimestampProcess(\n", "                root_dirs=[concat_quarterly_joined_by_stmt_dir, concat_daily_joined_by_stmt_dir],\n", "                target_dir=f\"{concat_all_joined_by_stmt_dir}/BS\",\n", "                pathfilter=\"BS\",\n", "            ),\n", "            ConcatMultiRootByChangedTimestampProcess(\n", "                root_dirs=[concat_quarterly_joined_by_stmt_dir, concat_daily_joined_by_stmt_dir],\n", "                target_dir=f\"{concat_all_joined_by_stmt_dir}/CF\",\n", "                pathfilter=\"CF\",\n", "            ),\n", "            ConcatMultiRootByChangedTimestampProcess(\n", "                root_dirs=[concat_quarterly_joined_by_stmt_dir, concat_daily_joined_by_stmt_dir],\n", "                target_dir=f\"{concat_all_joined_by_stmt_dir}/CI\",\n", "                pathfilter=\"CI\",\n", "            ),\n", "            ConcatMultiRootByChangedTimestampProcess(\n", "                root_dirs=[concat_quarterly_joined_by_stmt_dir, concat_daily_joined_by_stmt_dir],\n", "                target_dir=f\"{concat_all_joined_by_stmt_dir}/CP\",\n", "                pathfilter=\"CP\",\n", "            ),\n", "            ConcatMultiRootByChangedTimestampProcess(\n", "                root_dirs=[concat_quarterly_joined_by_stmt_dir, concat_daily_joined_by_stmt_dir],\n", "                target_dir=f\"{concat_all_joined_by_stmt_dir}/EQ\",\n", "                pathfilter=\"EQ\",\n", "            ),\n", "            ConcatMultiRootByChangedTimestampProcess(\n", "                root_dirs=[concat_quarterly_joined_by_stmt_dir, concat_daily_joined_by_stmt_dir],\n", "                target_dir=f\"{concat_all_joined_by_stmt_dir}/IS\",\n", "                pathfilter=\"IS\",\n", "            ),\n", "        ]\n", "    )\n", "\n", "    # 2. <PERSON><PERSON> joined\n", "    processes.append(\n", "        ConcatMultiRootByChangedTimestampProcess(\n", "            root_dirs=[concat_daily_joined_all_dir, concat_quarterly_joined_all_dir],\n", "            pathfilter=\"\",\n", "            target_dir=concat_all_joined_dir,\n", "        )\n", "    )\n", "\n", "    # 3. concat standardized by statement\n", "    processes.extend(\n", "        [\n", "            ConcatMultiRootByChangedTimestampProcess(\n", "                root_dirs=[concat_daily_standardized_by_stmt_dir, concat_quarterly_standardized_by_stmt_dir],\n", "                target_dir=f\"{concat_all_standardized_by_stmt_dir}/BS\",\n", "                pathfilter=\"BS\",\n", "                in_memory=True,  # Standardized Bag only work with in_memory\n", "            ),\n", "            ConcatMultiRootByChangedTimestampProcess(\n", "                root_dirs=[concat_daily_standardized_by_stmt_dir, concat_quarterly_standardized_by_stmt_dir],\n", "                target_dir=f\"{concat_all_standardized_by_stmt_dir}/CF\",\n", "                pathfilter=\"CF\",\n", "                in_memory=True,  # Standardized Bag only work with in_memory\n", "            ),\n", "            ConcatMultiRootByChangedTimestampProcess(\n", "                root_dirs=[concat_daily_standardized_by_stmt_dir, concat_quarterly_standardized_by_stmt_dir],\n", "                target_dir=f\"{concat_all_standardized_by_stmt_dir}/IS\",\n", "                pathfilter=\"IS\",\n", "                in_memory=True,  # Standardized Bag only work with in_memory\n", "            ),\n", "        ]\n", "    )\n", "    return processes"]}, {"cell_type": "markdown", "id": "d053ecb0-2488-4930-8356-a318c30735b3", "metadata": {}, "source": ["### How the created bags can be used"]}, {"cell_type": "markdown", "id": "9492792f-78f0-4f0b-8507-0ccb023220bb", "metadata": {}, "source": ["The most important bags are the ones that contain all the available data and are updated every day:\n", "    \n", "Joined and fitlered bags by stmt (statement):\n", "- **[concat_all_joined_by_stmt_dir]/BS** <br/> Contains a joined bag with all the BS datapoints that are available on the SEC\n", "- **[concat_all_joined_by_stmt_dir]/CF** <br/> Contains a joined bag with all the CF datapoints that are available on the SEC\n", "- **[concat_all_joined_by_stmt_dir]/CI** <br/> Contains a joined bag with all the CI datapoints that are available on the SEC\n", "- **[concat_all_joined_by_stmt_dir]/CP** <br/> Contains a joined bag with all the CP datapoints that are available on the SEC\n", "- **[concat_all_joined_by_stmt_dir]/EQ** <br/> Contains a joined bag with all the EQ datapoints that are available on the SEC\n", "- **[concat_all_joined_by_stmt_dir]/IS** <br/> Contains a joined bag with all the IS datapoints that are available on the SEC\n", "\n", "Joined and filtered single bag:\n", "- **[concat_all_joined_all_dir]** <br/> Contains a sinlge joined bag with all datapoints that are available on the SEC\n", "\n", "Standardized bags by stmt (statement):\n", "- **[concat_all_standardized_by_stmt_dir]/BS** <br/> Contains a standardized bag from all the BS datapoints that are available on the SEC\n", "- **[concat_all_standardized_by_stmt_dir]/CF** <br/> Contains a standardized bag from all the CF datapoints that are available on the SEC\n", "- **[concat_all_standardized_by_stmt_dir]/IS** <br/> Contains a standardized bag from all the IS datapoints that are available on the SEC\n"]}, {"cell_type": "markdown", "id": "bbc031d9-0ae4-4ea8-b6d4-fff9f59fb61e", "metadata": {}, "source": ["First, let us load the configuration, so that we can get the paths to the bags directly from the configuration file"]}, {"cell_type": "code", "execution_count": 1, "id": "9f642b11-e772-4705-9bf5-4e64d24fccd1", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-03-05 16:13:53,106 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n"]}], "source": ["import os\n", "from secfsdstools.a_config.configmodel import Configuration\n", "from secfsdstools.a_config.configmgt import ConfigurationManager, SECFSDSTOOLS_ENV_VAR_NAME\n", "\n", "# set the path to your configfile containg the above shown configuration into the SECFSDSTOOLS_CFG env variable, if it is not in your user home\n", "#os.environ[SECFSDSTOOLS_ENV_VAR_NAME] = \"...\" \n", "\n", "configuration = ConfigurationManager.read_config_file()"]}, {"cell_type": "markdown", "id": "7b42f636-eda8-4b4d-8b49-471b7a6b6269", "metadata": {}, "source": ["\n", "\n", "Next, If you want to analyze just **BS data**, you can simply load the appropriate bag:"]}, {"cell_type": "code", "execution_count": 11, "id": "ef94914c-d61b-466d-a4d2-89b981528547", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(19657047, 17)\n"]}], "source": ["from secfsdstools.d_container.databagmodel import JoinedDataBag\n", "\n", "concat_all_joined_by_stmt_dir = configuration.config_parser.get(section=\"Concat\", option=\"concat_all_joined_by_stmt_dir\")\n", "\n", "all_bs_joined_bag = JoinedDataBag.load(target_path=f\"{concat_all_joined_by_stmt_dir}/BS\") # loading all the available BS data\n", "print(all_bs_joined_bag.pre_num_df.shape)"]}, {"cell_type": "markdown", "id": "9112cc89-f6e2-4ee9-989a-ab0c2c0881c7", "metadata": {}, "source": ["With the single joined bag and using predicate pushdown, you can also easily load a single report by its adsh (also a recent one). This still performs quite ok, even if the file is about 1.3GB (as of Q1 2025)."]}, {"cell_type": "code", "execution_count": 3, "id": "545d6baa-36ac-4ac3-8074-308ad25fa469", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-03-05 16:14:32,022 [INFO] databagmodel  apply sub_df filter: [('adsh', 'in', ['0000320193-22-000108'])]\n", "2025-03-05 16:14:32,536 [INFO] databagmodel  apply pre_num_df filter: [\"('adsh', 'in', ['0000320193-22-000108'])\"]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["(1, 36)\n", "(179, 17)\n"]}], "source": ["from secfsdstools.d_container.databagmodel import JoinedDataBag\n", "\n", "concat_all_joined_all_dir = configuration.config_parser.get(section=\"Concat\", option=\"concat_all_joined_all_dir\")\n", "\n", "apple_10k_2022_adsh = \"0000320193-22-000108\"\n", "a_single_report = JoinedDataBag.load(target_path=f\"{concat_all_joined_all_dir}\", adshs_filter=[apple_10k_2022_adsh]) # loading all the available BS data\n", "\n", "print(a_single_report.sub_df.shape)\n", "print(a_single_report.pre_num_df.shape)"]}, {"cell_type": "markdown", "id": "54690e0e-0813-4058-80b7-c939fa80aee2", "metadata": {"tags": []}, "source": ["while this is a little bit slower than using the singlebag collector\n", "\n", "<pre>\n", "from secfsdstools.e_collector.reportcollecting import SingleReportCollector\n", "\n", "apple_10k_2022_adsh = \"0000320193-22-000108\"\n", "\n", "collector: SingleReportCollector = SingleReportCollector.get_report_by_adsh(adsh=apple_10k_2022_adsh)\n", "a_single_report = collector.collect().join()\n", "</pre>\n", "\n", "it still performs reasonably, and you use a bag, that is already filtered according to your needs.\n", "\n", "Moreover, since you have a single bag with all the data, you can also use it to load data for different companies and multiple years. Let's say, we want to read the data for all 10-K reports from Microsoft, Alphabet, and Amazon"]}, {"cell_type": "code", "execution_count": 6, "id": "10831e59-cf1d-4f8b-b929-4c8196de3378", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-03-05 16:18:49,404 [INFO] databagmodel  apply sub_df filter: [('cik', 'in', [789019, 1652044, 1018724]), ('form', 'in', ['10-K'])]\n", "2025-03-05 16:18:49,604 [INFO] databagmodel  apply pre_num_df filter: [\"('adsh', 'in', ['0001193125-10-016098', '0001193125-10-171791', '0001193125-11-016253', '0001193125-...)\"]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["(39, 36)\n", "(8098, 17)\n"]}], "source": ["ciks=[789019, 1652044,1018724] #Microsoft, Alphabet, Amazon\n", "\n", "all_asorted_10Ks = JoinedDataBag.load(target_path=f\"{concat_all_joined_all_dir}\", forms_filter=[\"10-K\"], ciks_filter=ciks) # loading all 10-Ks for Microsoft, Alphabet, and Amazon\n", "\n", "print(all_asorted_10Ks.sub_df.shape)\n", "print(all_asorted_10Ks.pre_num_df.shape)"]}, {"cell_type": "markdown", "id": "45e03514-ee43-4b80-9d14-b1f6b0426559", "metadata": {}, "source": ["Also with this example, it performs quite well thanks to predicate pushdown if we consider, that the file is about 1.3GB in size."]}, {"cell_type": "markdown", "id": "5de585eb-c7f3-4353-be04-07a966adf7da", "metadata": {}, "source": ["## Conclusion\n", "\n", "This example pipeline enables you to concat together all the data from all the quarters into a single bag. And this is done in a memory efficient way.\n", "\n", "Moreover, using predicate pushdown in the load methods, you can easily retrieve the data from single reports, or also from different companies. \n", "\n", "In addition, you also have the standardized data for BS, IS, and CF, so that you can compare the data between different years and/or companies.\n", "\n", "Last but not least, the bags are updated automatically, as soon as new data is available on the SEC's website.\n"]}, {"cell_type": "code", "execution_count": null, "id": "b3e168d5-0fd6-406e-b4b2-64b45fc1a8da", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "code", "execution_count": 1, "id": "429d36f6-9b27-4780-9335-621ed552d688", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "# ensure that all columns are shown and that colum content is not cut\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.max_colwidth', None)\n", "pd.set_option('display.width',1000)\n", "pd.set_option('display.max_rows', 500) # ensure that all rows are shown"]}, {"cell_type": "markdown", "id": "1df9d333-7247-4e89-b333-6c8f26e846f1", "metadata": {}, "source": ["# Financial Statements Standardizer"]}, {"cell_type": "markdown", "id": "5b19b2ea-fdfb-4b38-aee3-f80f13be9373", "metadata": {"tags": []}, "source": ["<span style=\"color: #FF8C00;\">==========================================================</span>\n", "\n", "**If you find this tool useful, a sponsorship would be greatly appreciated!**\n", "\n", "**https://github.com/sponsors/HansjoergW**\n", "\n", "How to get in touch\n", "\n", "* Found a bug: https://github.com/HansjoergW/sec-fincancial-statement-data-set/issues\n", "* Have a remark: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/general\n", "* Have an idea: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/ideas\n", "* Have a question: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/q-a\n", "* Have something to show: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/show-and-tell\n", "\n", "<span style=\"color: #FF8C00;\">==========================================================</span>"]}, {"cell_type": "markdown", "id": "b7134494-451f-4370-ab92-ffaa64d7d5d8", "metadata": {}, "source": ["## Goal"]}, {"cell_type": "markdown", "id": "9c841a03-6bb9-4cf1-8b0f-a8857a4ad8f5", "metadata": {}, "source": ["Even when adhering to the US-GAAP standard, financial statements among different companies or even across different years of the same company are often not directly comparable.\n", "\n", "Let's examine the balance sheet to illustrate a couple of problems that can arise:\n", "\n", "- There are over 3000 different tags that could be used in a balance sheet, even though a balance sheet typically only has about 30-40 rows.\n", "- Some tags have a similar meaning; for instance, the position \"Total assets\" can be tagged with \"Assets,\" but sometimes, the tag \"AssetsNet\" is also used.\n", "- Sometimes not all major positions are presented. For instance, normally, you expect Liabilities, LiabilitiesCurrent, and LiabilitiesNoncurrent to appear in the balance sheet. However, in some reports, only Liabilities, LiabilitiesCurrent, and only the detailed positions of LiabilitiesNoncurrent are listed, but no total position for LiabilitiesNonCurrent. Sometimes, even the total position for Liabilities is missing.\n", "\n", "The Standardizer processes the data and produces comparable statements that contain the main positions of a certain financial statement. For example, the balance sheet standardizer produces reports with values for Assets, AssetsCurrent, AssetsNoncurrent, Liabilities, LiabilitiesCurrent, LiabilitiesNoncurrent, Equity, as well as a few other positions that are not always present.\n", "\n", "To achieve this, the standardizer uses a **simple rule framework** that lets you define rules acting on the data. In the context of the balance sheet, a few rules include:\n", "- If there is an AssetsNet tag but no Assets tag, copy the value from AssetsNet to Assets.\n", "- If two of the tags Assets, AssetsCurrent, AssetsNoncurrent are present, calculate the missing one by applying the formula Assets = AssetsCurrent + AssetsNoncurrent.\n", "- If the LiabilitiesNoncurrent tag is missing, sum up any existing detail tags of LiabilitiesNoncurrent and store the sum in the LiabilitiesNoncurrent tag.\n", "\n", "Since calculations are involved, which under certain circumstances could be incorrect or problematic, **any action is logged**. Therefore, if a specific rule was applied for a certain report/financial statement, it is logged. With that information, a user can trace how many rules and which rules were applied to which tags of a particular report.\n", "\n", "As mentioned, applying certain rules could lead under certain circumstances to incorrect results or interpretations. Moreover, the input data could also be incorrect or essential information could be missing from the dataset altogether. Therefore, **validation rules** can be defined and are applied at the end of processing the data. In the case of the balance sheet, a few examples of validation rules are Assets = AssetsCurrent + AssetsNoncurrent, Liabilities = LiabilitiesCurrent + LiabilitiesNoncurrent, Assets = Liabilities + Equity. These validation checks are applied for every financial statement, and the results are presented with a relative error and a categorized error (category 0 = exact match, category 1 = less than 1% off, category 5 = less than 5% off, category 10 = less than 10% off, category 100 = greater than 10% off). For instance, if you want to use the data to train an ML model, you might want to include only data for reports where all validation rule have catagery of 5 or less."]}, {"cell_type": "markdown", "id": "21e4ccd7-c545-4620-bf9d-ad01b01d132b", "metadata": {}, "source": ["**Disclaimer** <br> **USE AT YOUR OWN RISK.** <br> As mentioned before, the applied rules could be wrong, the input data could be incorrect. Always check the official company filings if you want to make investement decisions!"]}, {"cell_type": "markdown", "id": "89000559-c0e5-41fe-9628-c6a5241e5184", "metadata": {}, "source": ["## Main Process"]}, {"cell_type": "markdown", "id": "1c186239-e3ac-4ca4-803d-20de270dfdbc", "metadata": {}, "source": ["The main process comprises four key steps:\n", "\n", "1. **Preprocessing**\n", "    1. Removal of unused tags: Based on predefined rules, all tags not utilized by these rules are eliminated.\n", "    1. Apply PrePivotRules, for instance deduplication: Occasionally, values for certain tags or even entire sets of tags in financial statements may be duplicated. These redundant entries need to be removed before the data can be pivoted.\n", "    1. Inversion of negated values: The sign of values marked as negated is inverted.\n", "    1. Table pivoting: Currently, each tag and its corresponding value have their own row in the dataset. The goal is to transform this structure so that each tag has its own column.\n", "    1. Filtering for main statements: Some financial reports contain multiple tables attributed to a specific financial statement. This step aims to retain only the main statement.\n", "    1. Application of preprocess rules: These rules are designed to rectify errors in the data. For example, there may be reports where the tags for Assets and AssetsNoncurrent are interchanged, causing the value of Assets to be tagged as AssetsNoncurrent and vice versa. Preprocess rules help correct such errors.\n", "    1. Preparation of log dataframes.\n", "\n", "2. **Main Processing**<br> This step applies the main rules, following the order in which they are defined. The entire rule tree can be executed multiple times, as applying a rule at the end of the tree could calculate a previously absent tag that can then be used to calculate another value in the next iteration.\n", "\n", "3. **Postprocessing**<br> Postprocess rules are applied in this step. Their primary purpose is to refine the results, such as setting values to zero.\n", "\n", "4. **Finalizing**<br> Validation rules are applied, and summary logs are generated.\n"]}, {"cell_type": "markdown", "id": "9c91ae5e-92f7-4323-9a08-f6b32f5b2858", "metadata": {}, "source": ["## Preparing the Example"]}, {"cell_type": "markdown", "id": "4beb91a3-6949-4b89-ab41-82f3d8126fc8", "metadata": {}, "source": ["In order to explain the details of the standardizer, we apply the balance sheet standardizer on the reports of the year 2022."]}, {"cell_type": "code", "execution_count": 2, "id": "aa279f56-6119-4ccf-aa22-1760b6203260", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-03 06:40:13,056 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-03 06:40:13,117 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-03 06:40:13,125 [INFO] parallelexecution      items to process: 4\n", "2025-02-03 06:40:49,603 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["number of loaded reports:  26316\n"]}], "source": ["# first, create a collector which collects all reports for 2022\n", "from secfsdstools.d_container.databagmodel import JoinedDataBag\n", "from secfsdstools.e_collector.zipcollecting import ZipCollector\n", "from secfsdstools.u_usecases.bulk_loading import default_postloadfilter\n", "\n", "collector = ZipCollector.get_zip_by_names(names=[\"2022q1.zip\", \"2022q2.zip\", \"2022q3.zip\", \"2022q4.zip\"], \n", "                                          forms_filter=[\"10-K\", \"10-Q\"],                                        \n", "                                          stmt_filter=[\"BS\"], post_load_filter=default_postloadfilter)\n", "\n", "joined_bag: JoinedDataBag = collector.collect().join()\n", "print(\"number of loaded reports: \", len(joined_bag.sub_df))"]}, {"cell_type": "markdown", "id": "c19e2355-9df8-4609-b60b-d6a535292b5d", "metadata": {"tags": []}, "source": ["**Note:** <br> we only load the 10-K and 10-Q reports. We also filter directly just for balance sheet information. We also apply the default_postloadfilter, which includes ReportPeriodRawFilter, MainCoregRawFilter, OfficialTagsOnlyRawFilter, and USDOnlyRawFilter. You definitely should apply the ReportPeriodRawFilter and the USDOnlyRawFilter. The standardizer should work without applying the MainCoregRawFilter and therefore also standardizing the statements for subsidiaries."]}, {"cell_type": "markdown", "id": "885839fe-6d4b-4827-8d5f-a62e12868330", "metadata": {}, "source": ["## Using the standardizer"]}, {"cell_type": "markdown", "id": "8c3ced4e-0d29-4a74-a468-c39c98fd7284", "metadata": {}, "source": ["The standardizer implements the presenter interface, so you can pass joinedbag as a parameter to the `present` method of the `JoinedDataBag` instance. You can also call the `process` method of the standardizer and provide the `pre_num_df` of the `JoinedDataBag` as input.\n", "\n", "However, there is a slight difference between those two methods. When you use the `present` method, then the following attributes from the sub_df are joined to the standardized result: \n", "* cik (company identifier)\n", "* name (company name)\n", "* form (10-K or 10Q)\n", "* fye (fiscal year ending)\n", "* fy (fiscal year)\n", "* fp (fiscal period)\n", "\n", "This makes it easier to identify the entries for one company and is therefore the recommended way."]}, {"cell_type": "code", "execution_count": 3, "id": "65573c47-2af0-4c78-abba-6a11327bf8b4", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-03 06:41:13,081 [INFO] standardizing  start PRE processing ...\n", "2025-02-03 06:41:13,735 [INFO] standardizing  start MAIN processing ...\n", "2025-02-03 06:41:14,050 [INFO] standardizing  start POST processing ...\n", "2025-02-03 06:41:14,077 [INFO] standardizing  start FINALIZE ...\n"]}], "source": ["from secfsdstools.f_standardize.bs_standardize import BalanceSheetStandardizer\n", "\n", "standardizer = BalanceSheetStandardizer()\n", "standardized_bs_df = joined_bag.present(standardizer)"]}, {"cell_type": "code", "execution_count": 4, "id": "decfbac0-d8ad-4ad5-aa2d-b1c1afefc7b0", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>adsh</th>\n", "      <th>cik</th>\n", "      <th>name</th>\n", "      <th>form</th>\n", "      <th>fye</th>\n", "      <th>fy</th>\n", "      <th>fp</th>\n", "      <th>date</th>\n", "      <th>filed</th>\n", "      <th>coreg</th>\n", "      <th>report</th>\n", "      <th>ddate</th>\n", "      <th>qtrs</th>\n", "      <th>Assets</th>\n", "      <th>AssetsCurrent</th>\n", "      <th>Cash</th>\n", "      <th>AssetsNoncurrent</th>\n", "      <th>Liabilities</th>\n", "      <th>LiabilitiesCurrent</th>\n", "      <th>LiabilitiesNoncurrent</th>\n", "      <th>Equity</th>\n", "      <th>HolderEquity</th>\n", "      <th>RetainedEarnings</th>\n", "      <th>AdditionalPaidInCapital</th>\n", "      <th>TreasuryStockValue</th>\n", "      <th>TemporaryEquity</th>\n", "      <th>RedeemableEquity</th>\n", "      <th>LiabilitiesAndEquity</th>\n", "      <th>Assets<PERSON><PERSON>ck_error</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON>_cat</th>\n", "      <th>LiabilitiesCheck_error</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON>_cat</th>\n", "      <th>EquityCheck_error</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON>_cat</th>\n", "      <th>AssetsLiaEquCheck_error</th>\n", "      <th>AssetsLiaEquCheck_cat</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>12372</th>\n", "      <td>0001663577-22-000212</td>\n", "      <td>1554906</td>\n", "      <td>CROWN BAUS CAPITAL CORP.</td>\n", "      <td>10-K</td>\n", "      <td>0430</td>\n", "      <td>2015.0</td>\n", "      <td>FY</td>\n", "      <td>2015-04-30</td>\n", "      <td>20220405</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20150430</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>253680.0</td>\n", "      <td>253680.0</td>\n", "      <td>0.0</td>\n", "      <td>-253680.0</td>\n", "      <td>-253680.0</td>\n", "      <td>-45278680.0</td>\n", "      <td>44885000.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12373</th>\n", "      <td>0001663577-22-000214</td>\n", "      <td>1554906</td>\n", "      <td>CROWN BAUS CAPITAL CORP.</td>\n", "      <td>10-K</td>\n", "      <td>0430</td>\n", "      <td>2016.0</td>\n", "      <td>FY</td>\n", "      <td>2016-04-30</td>\n", "      <td>20220405</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20160430</td>\n", "      <td>0</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>0.0</td>\n", "      <td>379980.0</td>\n", "      <td>379980.0</td>\n", "      <td>0.0</td>\n", "      <td>-379973.0</td>\n", "      <td>-379973.0</td>\n", "      <td>-45404973.0</td>\n", "      <td>44885000.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>7.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10562</th>\n", "      <td>0001493152-22-015967</td>\n", "      <td>1442853</td>\n", "      <td>INDO GLOBAL EXCHANGE(S) PTE, LTD.</td>\n", "      <td>10-K</td>\n", "      <td>0731</td>\n", "      <td>2016.0</td>\n", "      <td>FY</td>\n", "      <td>2016-07-31</td>\n", "      <td>20220606</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20160731</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>486515.0</td>\n", "      <td>486515.0</td>\n", "      <td>0.0</td>\n", "      <td>-486515.0</td>\n", "      <td>-486515.0</td>\n", "      <td>-7591643.0</td>\n", "      <td>6024427.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3298</th>\n", "      <td>0001477932-22-001114</td>\n", "      <td>1374881</td>\n", "      <td>KINGFISH HOLDING CORP</td>\n", "      <td>10-K</td>\n", "      <td>0930</td>\n", "      <td>2016.0</td>\n", "      <td>FY</td>\n", "      <td>2016-09-30</td>\n", "      <td>20220301</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20160930</td>\n", "      <td>0</td>\n", "      <td>21308.0</td>\n", "      <td>21308.0</td>\n", "      <td>21308.0</td>\n", "      <td>0.0</td>\n", "      <td>230324.0</td>\n", "      <td>210324.0</td>\n", "      <td>20000.0</td>\n", "      <td>-209016.0</td>\n", "      <td>-209016.0</td>\n", "      <td>-4579323.0</td>\n", "      <td>4378213.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>21308.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10563</th>\n", "      <td>0001493152-22-015996</td>\n", "      <td>1442853</td>\n", "      <td>INDO GLOBAL EXCHANGE(S) PTE, LTD.</td>\n", "      <td>10-Q</td>\n", "      <td>0731</td>\n", "      <td>2017.0</td>\n", "      <td>Q1</td>\n", "      <td>2016-10-31</td>\n", "      <td>20220606</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20161031</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>486515.0</td>\n", "      <td>486515.0</td>\n", "      <td>0.0</td>\n", "      <td>-486515.0</td>\n", "      <td>-486515.0</td>\n", "      <td>-7591643.0</td>\n", "      <td>6024427.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21396</th>\n", "      <td>0001099910-22-000217</td>\n", "      <td>1427644</td>\n", "      <td>TELCO CUBA, INC.</td>\n", "      <td>10-K</td>\n", "      <td>1130</td>\n", "      <td>2016.0</td>\n", "      <td>FY</td>\n", "      <td>2016-11-30</td>\n", "      <td>20221103</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20161130</td>\n", "      <td>0</td>\n", "      <td>41602.0</td>\n", "      <td>26864.0</td>\n", "      <td>21414.0</td>\n", "      <td>14738.0</td>\n", "      <td>5381900.0</td>\n", "      <td>5381900.0</td>\n", "      <td>0.0</td>\n", "      <td>-5340298.0</td>\n", "      <td>-5340298.0</td>\n", "      <td>-6114193.0</td>\n", "      <td>558926.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>41602.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14696</th>\n", "      <td>0001096906-22-001760</td>\n", "      <td>1387998</td>\n", "      <td>SNOOGOO CORP.</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2016.0</td>\n", "      <td>FY</td>\n", "      <td>2016-12-31</td>\n", "      <td>20220802</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20161231</td>\n", "      <td>0</td>\n", "      <td>10400.0</td>\n", "      <td>10400.0</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>506175.0</td>\n", "      <td>506175.0</td>\n", "      <td>0.0</td>\n", "      <td>-495775.0</td>\n", "      <td>-495775.0</td>\n", "      <td>-6390780.0</td>\n", "      <td>5704690.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>10400.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10564</th>\n", "      <td>0001493152-22-015999</td>\n", "      <td>1442853</td>\n", "      <td>INDO GLOBAL EXCHANGE(S) PTE, LTD.</td>\n", "      <td>10-Q</td>\n", "      <td>0731</td>\n", "      <td>2017.0</td>\n", "      <td>Q2</td>\n", "      <td>2017-01-31</td>\n", "      <td>20220606</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20170131</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>486506.0</td>\n", "      <td>486506.0</td>\n", "      <td>0.0</td>\n", "      <td>-486506.0</td>\n", "      <td>-486506.0</td>\n", "      <td>-7591643.0</td>\n", "      <td>6024427.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12374</th>\n", "      <td>0001663577-22-000215</td>\n", "      <td>1554906</td>\n", "      <td>CROWN BAUS CAPITAL CORP.</td>\n", "      <td>10-K</td>\n", "      <td>0430</td>\n", "      <td>2017.0</td>\n", "      <td>FY</td>\n", "      <td>2017-04-30</td>\n", "      <td>20220405</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20170430</td>\n", "      <td>0</td>\n", "      <td>6300000.0</td>\n", "      <td>6300000.0</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>500912.0</td>\n", "      <td>500912.0</td>\n", "      <td>0.0</td>\n", "      <td>5799088.0</td>\n", "      <td>5799088.0</td>\n", "      <td>-46575912.0</td>\n", "      <td>44885000.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>6300000.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10566</th>\n", "      <td>0001493152-22-016043</td>\n", "      <td>1442853</td>\n", "      <td>INDO GLOBAL EXCHANGE(S) PTE, LTD.</td>\n", "      <td>10-Q</td>\n", "      <td>0731</td>\n", "      <td>2017.0</td>\n", "      <td>Q3</td>\n", "      <td>2017-04-30</td>\n", "      <td>20220607</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20170430</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>486515.0</td>\n", "      <td>486515.0</td>\n", "      <td>0.0</td>\n", "      <td>-486515.0</td>\n", "      <td>-486515.0</td>\n", "      <td>-7591643.0</td>\n", "      <td>6024427.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                       adsh      cik                               name  form   fye      fy  fp       date     filed coreg  report     ddate  qtrs     Assets  AssetsCurrent     Cash  AssetsNoncurrent  Liabilities  LiabilitiesCurrent  LiabilitiesNoncurrent     Equity  HolderEquity  RetainedEarnings  AdditionalPaidInCapital  TreasuryStockValue  TemporaryEquity  RedeemableEquity  LiabilitiesAndEquity  AssetsCheck_error  AssetsCheck_cat  LiabilitiesCheck_error  LiabilitiesCheck_cat  EquityCheck_error  EquityCheck_cat  AssetsLiaEquCheck_error  AssetsLiaEquCheck_cat\n", "12372  0001663577-22-000212  1554906           CROWN BAUS CAPITAL CORP.  10-K  0430  2015.0  FY 2015-04-30  20220405             2  20150430     0        0.0            0.0      NaN               0.0     253680.0            253680.0                    0.0  -253680.0     -253680.0       -45278680.0               44885000.0                 0.0              0.0               0.0                   0.0                0.0              0.0                     0.0                   0.0                0.0              0.0                      0.0                    0.0\n", "12373  0001663577-22-000214  1554906           CROWN BAUS CAPITAL CORP.  10-K  0430  2016.0  FY 2016-04-30  20220405             2  20160430     0        7.0            7.0      7.0               0.0     379980.0            379980.0                    0.0  -379973.0     -379973.0       -45404973.0               44885000.0                 0.0              0.0               0.0                   7.0                0.0              0.0                     0.0                   0.0                0.0              0.0                      0.0                    0.0\n", "10562  0001493152-22-015967  1442853  INDO GLOBAL EXCHANGE(S) PTE, LTD.  10-K  0731  2016.0  FY 2016-07-31  20220606             2  20160731     0        0.0            0.0      NaN               0.0     486515.0            486515.0                    0.0  -486515.0     -486515.0        -7591643.0                6024427.0                 0.0              0.0               0.0                   0.0                0.0              0.0                     0.0                   0.0                0.0              0.0                      0.0                    0.0\n", "3298   0001477932-22-001114  1374881              KINGFISH HOLDING CORP  10-K  0930  2016.0  FY 2016-09-30  20220301             2  20160930     0    21308.0        21308.0  21308.0               0.0     230324.0            210324.0                20000.0  -209016.0     -209016.0        -4579323.0                4378213.0                 0.0              0.0               0.0               21308.0                0.0              0.0                     0.0                   0.0                0.0              0.0                      0.0                    0.0\n", "10563  0001493152-22-015996  1442853  INDO GLOBAL EXCHANGE(S) PTE, LTD.  10-Q  0731  2017.0  Q1 2016-10-31  20220606             2  20161031     0        0.0            0.0      0.0               0.0     486515.0            486515.0                    0.0  -486515.0     -486515.0        -7591643.0                6024427.0                 0.0              0.0               0.0                   0.0                0.0              0.0                     0.0                   0.0                0.0              0.0                      0.0                    0.0\n", "21396  0001099910-22-000217  1427644                   TELCO CUBA, INC.  10-K  1130  2016.0  FY 2016-11-30  20221103             2  20161130     0    41602.0        26864.0  21414.0           14738.0    5381900.0           5381900.0                    0.0 -5340298.0    -5340298.0        -6114193.0                 558926.0                 0.0              0.0               0.0               41602.0                0.0              0.0                     0.0                   0.0                0.0              0.0                      0.0                    0.0\n", "14696  0001096906-22-001760  1387998                      SNOOGOO CORP.  10-K  1231  2016.0  FY 2016-12-31  20220802             2  20161231     0    10400.0        10400.0      NaN               0.0     506175.0            506175.0                    0.0  -495775.0     -495775.0        -6390780.0                5704690.0                 0.0              0.0               0.0               10400.0                0.0              0.0                     0.0                   0.0                0.0              0.0                      0.0                    0.0\n", "10564  0001493152-22-015999  1442853  INDO GLOBAL EXCHANGE(S) PTE, LTD.  10-Q  0731  2017.0  Q2 2017-01-31  20220606             2  20170131     0        0.0            0.0      0.0               0.0     486506.0            486506.0                    0.0  -486506.0     -486506.0        -7591643.0                6024427.0                 0.0              0.0               0.0                   0.0                0.0              0.0                     0.0                   0.0                0.0              0.0                      0.0                    0.0\n", "12374  0001663577-22-000215  1554906           CROWN BAUS CAPITAL CORP.  10-K  0430  2017.0  FY 2017-04-30  20220405             2  20170430     0  6300000.0      6300000.0      NaN               0.0     500912.0            500912.0                    0.0  5799088.0     5799088.0       -46575912.0               44885000.0                 0.0              0.0               0.0             6300000.0                0.0              0.0                     0.0                   0.0                0.0              0.0                      0.0                    0.0\n", "10566  0001493152-22-016043  1442853  INDO GLOBAL EXCHANGE(S) PTE, LTD.  10-Q  0731  2017.0  Q3 2017-04-30  20220607             2  20170430     0        0.0            0.0      0.0               0.0     486515.0            486515.0                    0.0  -486515.0     -486515.0        -7591643.0                6024427.0                 0.0              0.0               0.0                   0.0                0.0              0.0                     0.0                   0.0                0.0              0.0                      0.0                    0.0"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["standardized_bs_df[:10]"]}, {"cell_type": "markdown", "id": "cde58ea6-6969-45b8-8c88-b19b27e26f86", "metadata": {}, "source": ["As you can see, the `present` method (as would the `process` method) returns the standardized dataframe. The \"index\" colums are **adsh**, **coreg**, **report**, **ddate**, and **uom**. Since we actually did use the UsdOnlyFilter and the MainCoreFilter, we could drop the coreg and the uom column.\n", "\n", "After the value columns, you find the results of the applied validation rules the `..Check_error` and `..Check_cat` columns. I will explain them later.\n", "\n", "As i mentioned before, different logs are created as well, which can be directly accessed in the instance of the Standardizer.\n", "\n", "The standardizer als has bag object which contains the result of the standardization and all the logs. You can get this bag by calling the method `get_standardize_bag`."]}, {"cell_type": "code", "execution_count": 5, "id": "87c54ea4-df6b-4838-bc30-d6d86318ef0d", "metadata": {"tags": []}, "outputs": [], "source": ["from secfsdstools.f_standardize.standardizing import StandardizedBag\n", "result_bag: StandardizedBag = standardizer.get_standardize_bag()"]}, {"cell_type": "markdown", "id": "58a988c9-2a62-4d34-9618-c714f46ee188", "metadata": {}, "source": ["The `StandardizedBag` class also has a save and load method, so you can store the results and analyse them later. (**Note**: you have to create the directory before saving)"]}, {"cell_type": "code", "execution_count": 7, "id": "5171e973-a681-478b-bd18-668e2bbbcc90", "metadata": {"tags": []}, "outputs": [], "source": ["import os\n", "# save the results\n", "result_bag.save('./bs_standardizer_results')"]}, {"cell_type": "code", "execution_count": 8, "id": "2728776c-aa9d-4fdb-8f70-6b1cd7cd5354", "metadata": {"tags": []}, "outputs": [], "source": ["from secfsdstools.f_standardize.standardizing import StandardizedBag\n", "# load the results\n", "result_bag: StandardizedBag = StandardizedBag.load('./bs_standardizer_results')"]}, {"cell_type": "markdown", "id": "c3668758-cc84-4fad-ad36-539a4d6e0c3a", "metadata": {}, "source": ["Inside the bag, we find the folliwing objects (as mentioned, this datasets are also directyl available in the Standardizer instance):\n", "\n", "- **result_df** <br> The dataframe that contains the result of the standardization process and also the validation results for every entry.\n", "- **process_description_df** <br> This dataframe gives a textual description of all rules and also shows there unique id.\n", "- **applied_prepivot_rules_log_df** <br> This dataframe contains the logs for applied pre-pivot-rules. Rules that were applied before the data set was pivoted. As mentioned above, this includes lines that were duplicated.\n", "- **applied_rules_log_df** <br> For every column in the result_df, this df contains the information which rules where applied on which entry.\n", "- **applied_rules_sum_s** <br> This series shows how often a rule was applied in total.\n", "- **stats_df** <br> This shows the total and the percentage of null values for columns for every processing step. You also see the improvement between the steps.\n", "- **validation_overview_df** <br> This dataframe gives a summary of the validation and therefore giving an indication about how successfull the standardization process was."]}, {"cell_type": "markdown", "id": "bcd60b27-97c1-4ccc-b771-91a0aa804e58", "metadata": {"tags": []}, "source": ["## Analyzing the logs"]}, {"cell_type": "markdown", "id": "04901882-1811-4fd3-9624-2083104b7dfb", "metadata": {}, "source": ["### **process_description_df**"]}, {"cell_type": "markdown", "id": "262ef825-55a5-4c73-8b22-e3e45feae1ee", "metadata": {}, "source": ["This dataframe shows all defined rules with their id and a textual description. The order in which they are shown is also the order in which they are applied."]}, {"cell_type": "code", "execution_count": 9, "id": "a91d15e7-d963-4ab8-b633-b2db62d47613", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>part</th>\n", "      <th>type</th>\n", "      <th>ruleclass</th>\n", "      <th>identifier</th>\n", "      <th>description</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>PREPIVOT</td>\n", "      <td>Group</td>\n", "      <td></td>\n", "      <td>PREPIVOT_BS_PREPIV</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>PREPIVOT</td>\n", "      <td>Rule</td>\n", "      <td>PrePivotDeduplicate</td>\n", "      <td>PREPIVOT_BS_PREPIV_#1_DeDup</td>\n", "      <td>Deduplicates the dataframe based on the columns ['adsh', 'coreg', 'report', 'ddate', 'qtrs', 'tag', 'version', 'value']</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>PRE</td>\n", "      <td>Group</td>\n", "      <td></td>\n", "      <td>PRE_BS_PRE</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>PRE</td>\n", "      <td>Rule</td>\n", "      <td>PreSumUpCorrection</td>\n", "      <td>PRE_BS_PRE_#1_Assets/AssetsNoncurrent</td>\n", "      <td>Swaps the values between the tag 'Assets' and 'AssetsNoncurrent' if the following equation is True \"'AssetsNoncurrent' = 'Assets' + 'AssetsCurrent\"  and 'AssetsCurrent' &gt; 0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>PRE</td>\n", "      <td>Rule</td>\n", "      <td>PreSumUpCorrection</td>\n", "      <td>PRE_BS_PRE_#2_Assets/AssetsCurrent</td>\n", "      <td>Swaps the values between the tag 'Assets' and 'AssetsCurrent' if the following equation is True \"'AssetsCurrent' = 'Assets' + 'AssetsNoncurrent\"  and 'AssetsNoncurrent' &gt; 0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>MAIN</td>\n", "      <td>Group</td>\n", "      <td></td>\n", "      <td>MAIN_BS</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>MAIN</td>\n", "      <td>Group</td>\n", "      <td></td>\n", "      <td>MAIN_BS_#1_BR</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_BS_#1_BR_#1_Assets&lt;-AssetsNet</td>\n", "      <td>Copies the values from AssetsNet to Assets if AssetsNet is not null and Assets is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_BS_#1_BR_#2_Cash&lt;-CashAndCashEquivalentsAtCarryingValue</td>\n", "      <td>Copies the values from CashAndCashEquivalentsAtCarryingValue to Cash if CashAndCashEquivalentsAtCarryingValue is not null and Cash is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_BS_#1_BR_#3_LiabilitiesAndEquity&lt;-LiabilitiesAndStockholdersEquity</td>\n", "      <td>Copies the values from LiabilitiesAndStockholdersEquity to LiabilitiesAndEquity if LiabilitiesAndStockholdersEquity is not null and LiabilitiesAndEquity is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_BS_#1_BR_#4_RetainedEarnings&lt;-RetainedEarningsAccumulatedDeficit</td>\n", "      <td>Copies the values from RetainedEarningsAccumulatedDeficit to RetainedEarnings if RetainedEarningsAccumulatedDeficit is not null and RetainedEarnings is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>MAIN</td>\n", "      <td>Group</td>\n", "      <td></td>\n", "      <td>MAIN_BS_#2_EQ</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_BS_#2_EQ_#1_HolderEquity&lt;-StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest</td>\n", "      <td>Copies the values from StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest to HolderEquity if StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest is not null and HolderEquity is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_BS_#2_EQ_#2_HolderEquity&lt;-PartnersCapital</td>\n", "      <td>Copies the values from PartnersCapital to HolderEquity if PartnersCapital is not null and HolderEquity is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_BS_#2_EQ_#3_HolderEquity&lt;-StockholdersEquity</td>\n", "      <td>Copies the values from StockholdersEquity to HolderEquity if StockholdersEquity is not null and HolderEquity is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SumUpRule</td>\n", "      <td>MAIN_BS_#2_EQ_#4_TemporaryEquity</td>\n", "      <td>Sums up the availalbe values in the columns ['TemporaryEquityAggregateAmountOfRedemptionRequirement', 'TemporaryEquityCarryingAmountAttributableToParent', 'TemporaryEquityRedemptionAmountAttributableToParent', 'TemporaryEquityRedemptionAmountAttributableToNoncontrollingInterest'] into the column 'TemporaryEquity', if the column 'TemporaryEquity' is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SumUpRule</td>\n", "      <td>MAIN_BS_#2_EQ_#5_RedeemableEquity</td>\n", "      <td>Sums up the availalbe values in the columns ['RedeemableNoncontrollingInterestEquityCarryingAmount', 'RedeemableNoncontrollingInterestEquityRedemptionAmount', 'RedeemableNoncontrollingInterestEquityOtherCarryingAmount', 'RedeemableNoncontrollingInterestEquityOtherRedemptionAmount', 'RedeemablePreferredStockEquityOtherCarryingAmount', 'RedeemablePreferredStockEquityOtherRedemptionAmount'] into the column 'RedeemableEquity', if the column 'RedeemableEquity' is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SumUpRule</td>\n", "      <td>MAIN_BS_#2_EQ_#6_Equity</td>\n", "      <td>Sums up the availalbe values in the columns ['HolderEquity', 'TemporaryEquity', 'RedeemableEquity'] into the column 'Equity', if the column 'Equity' is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>MAIN</td>\n", "      <td>Group</td>\n", "      <td></td>\n", "      <td>MAIN_BS_#3_SC</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>MissingSumRule</td>\n", "      <td>MAIN_BS_#3_SC_#1_Assets</td>\n", "      <td>Sums up the values in the columns ['AssetsCurrent', 'AssetsNoncurrent'] into the column 'Assets', if the column 'Assets' is nan and if all columns ['AssetsCurrent', 'AssetsNoncurrent'] have a value</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>MissingSummandRule</td>\n", "      <td>MAIN_BS_#3_SC_#2_AssetsCurrent</td>\n", "      <td>Calculates the value for the missing column 'AssetsCurrent' by subtracting the values of the columns '['AssetsNoncurrent']' from the column 'Assets' if all of the columns ['Assets', 'AssetsNoncurrent'] are set.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>MissingSummandRule</td>\n", "      <td>MAIN_BS_#3_SC_#3_AssetsNoncurrent</td>\n", "      <td>Calculates the value for the missing column 'AssetsNoncurrent' by subtracting the values of the columns '['AssetsCurrent']' from the column 'Assets' if all of the columns ['Assets', 'AssetsCurrent'] are set.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>MissingSumRule</td>\n", "      <td>MAIN_BS_#3_SC_#4_Liabilities</td>\n", "      <td>Sums up the values in the columns ['LiabilitiesCurrent', 'LiabilitiesNoncurrent'] into the column 'Liabilities', if the column 'Liabilities' is nan and if all columns ['LiabilitiesCurrent', 'LiabilitiesNoncurrent'] have a value</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>MissingSummandRule</td>\n", "      <td>MAIN_BS_#3_SC_#5_LiabilitiesCurrent</td>\n", "      <td>Calculates the value for the missing column 'LiabilitiesCurrent' by subtracting the values of the columns '['LiabilitiesNoncurrent']' from the column 'Liabilities' if all of the columns ['Liabilities', 'LiabilitiesNoncurrent'] are set.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>MissingSummandRule</td>\n", "      <td>MAIN_BS_#3_SC_#6_LiabilitiesNoncurrent</td>\n", "      <td>Calculates the value for the missing column 'LiabilitiesNoncurrent' by subtracting the values of the columns '['LiabilitiesCurrent']' from the column 'Liabilities' if all of the columns ['Liabilities', 'LiabilitiesCurrent'] are set.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>MissingSumRule</td>\n", "      <td>MAIN_BS_#3_SC_#7_Assets</td>\n", "      <td>Sums up the values in the columns ['Liabilities', 'Equity'] into the column 'Assets', if the column 'Assets' is nan and if all columns ['Liabilities', 'Equity'] have a value</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>MissingSummandRule</td>\n", "      <td>MAIN_BS_#3_SC_#8_Liabilities</td>\n", "      <td>Calculates the value for the missing column 'Liabilities' by subtracting the values of the columns '['Equity']' from the column 'Assets' if all of the columns ['Assets', 'Equity'] are set.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>MissingSummandRule</td>\n", "      <td>MAIN_BS_#3_SC_#9_Equity</td>\n", "      <td>Calculates the value for the missing column 'Equity' by subtracting the values of the columns '['Liabilities']' from the column 'Assets' if all of the columns ['Assets', 'Liabilities'] are set.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>MissingSumRule</td>\n", "      <td>MAIN_BS_#3_SC_#10_LiabilitiesAndEquity</td>\n", "      <td>Sums up the values in the columns ['Liabilities', 'Equity'] into the column 'LiabilitiesAndEquity', if the column 'LiabilitiesAndEquity' is nan and if all columns ['Liabilities', 'Equity'] have a value</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>MissingSummandRule</td>\n", "      <td>MAIN_BS_#3_SC_#11_Liabilities</td>\n", "      <td>Calculates the value for the missing column 'Liabilities' by subtracting the values of the columns '['Equity']' from the column 'LiabilitiesAndEquity' if all of the columns ['LiabilitiesAndEquity', 'Equity'] are set.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>MissingSummandRule</td>\n", "      <td>MAIN_BS_#3_SC_#12_Equity</td>\n", "      <td>Calculates the value for the missing column 'Equity' by subtracting the values of the columns '['Liabilities']' from the column 'LiabilitiesAndEquity' if all of the columns ['LiabilitiesAndEquity', 'Liabilities'] are set.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>MAIN</td>\n", "      <td>Group</td>\n", "      <td></td>\n", "      <td>MAIN_BS_#4_SU</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SumUpRule</td>\n", "      <td>MAIN_BS_#4_SU_#1_Cash</td>\n", "      <td>Sums up the availalbe values in the columns ['CashAndCashEquivalentsAtFairValue', 'CashAndDueFromBanks', 'CashCashEquivalentsAndFederalFundsSold', 'RestrictedCashAndCashEquivalentsAtCarryingValue', 'CashAndCashEquivalentsInForeignCurrencyAtCarryingValue'] into the column 'Cash', if the column 'Cash' is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SumUpRule</td>\n", "      <td>MAIN_BS_#4_SU_#2_RetainedEarnings</td>\n", "      <td>Sums up the availalbe values in the columns ['RetainedEarningsUnappropriated', 'RetainedEarningsAppropriated'] into the column 'RetainedEarnings', if the column 'RetainedEarnings' is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SumUpRule</td>\n", "      <td>MAIN_BS_#4_SU_#3_LongTermDebt</td>\n", "      <td>Sums up the availalbe values in the columns ['LongTermDebtNoncurrent', 'LongTermDebtAndCapitalLeaseObligations'] into the column 'LongTermDebt', if the column 'LongTermDebt' is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SumUpRule</td>\n", "      <td>MAIN_BS_#4_SU_#4_LiabilitiesNoncurrent</td>\n", "      <td>Sums up the availalbe values in the columns ['AccruedIncomeTaxesNoncurrent', 'DeferredAndPayableIncomeTaxes', 'DeferredIncomeTaxesAndOtherLiabilitiesNoncurrent', 'DeferredIncomeTaxLiabilitiesNet', 'DeferredTaxLiabilitiesNoncurrent', 'DefinedBenefitPensionPlanLiabilitiesNoncurrent', 'DerivativeLiabilitiesNoncurrent', 'FinanceLeaseLiabilityNoncurrent', 'LiabilitiesOtherThanLongtermDebtNoncurrent', 'LiabilitiesSubjectToCompromise', 'LiabilityForUncertainTaxPositionsNoncurrent', 'LongTermDebt', 'LongTermRetirementBenefitsAndOtherLiabilities', 'OperatingLeaseLiabilityNoncurrent', 'OtherLiabilitiesNoncurrent', 'OtherPostretirementDefinedBenefitPlanLiabilitiesNoncurrent', 'PensionAndOtherPostretirementDefinedBenefitPlansLiabilitiesNoncurrent', 'RegulatoryLiabilityNoncurrent', 'SelfInsuranceReserveNoncurrent'] into the column 'LiabilitiesNoncurrent', if the column 'LiabilitiesNoncurrent' is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>MAIN</td>\n", "      <td>Group</td>\n", "      <td></td>\n", "      <td>MAIN_BS_#5_SetSum</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SetSumIfOnlyOneSummand</td>\n", "      <td>MAIN_BS_#5_SetSum_#1_Assets/AssetsNoncurrent</td>\n", "      <td>Copies the value of the column 'AssetsCurrent' into the column 'Assets' and sets the columns ['AssetsNoncurrent'] to 0.0 if the column 'AssetsCurrent is set and the columns ['Assets', 'AssetsNoncurrent'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SetSumIfOnlyOneSummand</td>\n", "      <td>MAIN_BS_#5_SetSum_#2_Assets/AssetsCurrent</td>\n", "      <td>Copies the value of the column 'AssetsNoncurrent' into the column 'Assets' and sets the columns ['AssetsCurrent'] to 0.0 if the column 'AssetsNoncurrent is set and the columns ['Assets', 'AssetsCurrent'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SetSumIfOnlyOneSummand</td>\n", "      <td>MAIN_BS_#5_SetSum_#3_Liabilities/LiabilitiesNoncurrent</td>\n", "      <td>Copies the value of the column 'LiabilitiesCurrent' into the column 'Liabilities' and sets the columns ['LiabilitiesNoncurrent'] to 0.0 if the column 'LiabilitiesCurrent is set and the columns ['Liabilities', 'LiabilitiesNoncurrent'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SetSumIfOnlyOneSummand</td>\n", "      <td>MAIN_BS_#5_SetSum_#4_Liabilities/LiabilitiesCurrent</td>\n", "      <td>Copies the value of the column 'LiabilitiesNoncurrent' into the column 'Liabilities' and sets the columns ['LiabilitiesCurrent'] to 0.0 if the column 'LiabilitiesNoncurrent is set and the columns ['Liabilities', 'LiabilitiesCurrent'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>POST</td>\n", "      <td>Group</td>\n", "      <td></td>\n", "      <td>POST_BS_POST</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostCopyToFirstSummand</td>\n", "      <td>POST_BS_POST_#1_AssetsCurrent/AssetsNoncurrent</td>\n", "      <td>Copies the value of the 'Assets' to the first summand 'AssetsCurrent' and set the other summands ['AssetsNoncurrent'] to 0.0 if 'Assets is set and the summands ['AssetsCurrent', 'AssetsNoncurrent'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostCopyToFirstSummand</td>\n", "      <td>POST_BS_POST_#2_LiabilitiesCurrent/LiabilitiesNoncurrent</td>\n", "      <td>Copies the value of the 'Liabilities' to the first summand 'LiabilitiesCurrent' and set the other summands ['LiabilitiesNoncurrent'] to 0.0 if 'Liabilities is set and the summands ['LiabilitiesCurrent', 'LiabilitiesNoncurrent'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostSetToZero</td>\n", "      <td>POST_BS_POST_#3_Assets/AssetsCurrent/AssetsNoncurrent</td>\n", "      <td>Set the value of the ['Assets', 'AssetsCurrent', 'AssetsNoncurrent'] to 0.0 if all ['Assets', 'AssetsCurrent', 'AssetsNoncurrent'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostSetToZero</td>\n", "      <td>POST_BS_POST_#4_Liabilities/LiabilitiesCurrent/LiabilitiesNoncurrent</td>\n", "      <td>Set the value of the ['Liabilities', 'LiabilitiesCurrent', 'LiabilitiesNoncurrent'] to 0.0 if all ['Liabilities', 'LiabilitiesCurrent', 'LiabilitiesNoncurrent'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostSetToZero</td>\n", "      <td>POST_BS_POST_#5_TemporaryEquity</td>\n", "      <td>Set the value of the ['TemporaryEquity'] to 0.0 if all ['TemporaryEquity'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostSetToZero</td>\n", "      <td>POST_BS_POST_#6_RedeemableEquity</td>\n", "      <td>Set the value of the ['RedeemableEquity'] to 0.0 if all ['RedeemableEquity'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostSetToZero</td>\n", "      <td>POST_BS_POST_#7_AdditionalPaidInCapital</td>\n", "      <td>Set the value of the ['AdditionalPaidInCapital'] to 0.0 if all ['AdditionalPaidInCapital'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostSetToZero</td>\n", "      <td>POST_BS_POST_#8_TreasuryStockValue</td>\n", "      <td>Set the value of the ['TreasuryStockValue'] to 0.0 if all ['TreasuryStockValue'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50</th>\n", "      <td>VALID</td>\n", "      <td>Validation</td>\n", "      <td>SumValidationRule</td>\n", "      <td>As<PERSON><PERSON><PERSON>ck</td>\n", "      <td>Checks whether the sum of ['AssetsCurrent', 'AssetsNoncurrent'] equals the value in 'Assets'</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51</th>\n", "      <td>VALID</td>\n", "      <td>Validation</td>\n", "      <td>SumValidationRule</td>\n", "      <td>LiabilitiesCheck</td>\n", "      <td>Checks whether the sum of ['LiabilitiesCurrent', 'LiabilitiesNoncurrent'] equals the value in 'Liabilities'</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>VALID</td>\n", "      <td>Validation</td>\n", "      <td>SumValidationRule</td>\n", "      <td>EquityCheck</td>\n", "      <td>Checks whether the sum of ['Equity', 'Liabilities'] equals the value in 'LiabilitiesAndEquity'</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53</th>\n", "      <td>VALID</td>\n", "      <td>Validation</td>\n", "      <td>SumValidationRule</td>\n", "      <td>AssetsLiaEquCheck</td>\n", "      <td>Checks whether the sum of ['Equity', 'Liabilities'] equals the value in 'Assets'</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        part        type               ruleclass                                                                                             identifier  \\\n", "0   PREPIVOT       Group                                                                                                             PREPIVOT_BS_PREPIV   \n", "1   PREPIVOT        Rule     PrePivotDeduplicate                                                                            PREPIVOT_BS_PREPIV_#1_DeDup   \n", "2        PRE       Group                                                                                                                     PRE_BS_PRE   \n", "3        PRE        Rule      PreSumUpCorrection                                                                  PRE_BS_PRE_#1_Assets/AssetsNoncurrent   \n", "4        PRE        Rule      PreSumUpCorrection                                                                     PRE_BS_PRE_#2_Assets/AssetsCurrent   \n", "5       MAIN       Group                                                                                                                        MAIN_BS   \n", "6       MAIN       Group                                                                                                                  MAIN_BS_#1_BR   \n", "7       MAIN        Rule             CopyTagRule                                                                     MAIN_BS_#1_BR_#1_Assets<-AssetsNet   \n", "8       MAIN        Rule             CopyTagRule                                           MAIN_BS_#1_BR_#2_Cash<-CashAndCashEquivalentsAtCarryingValue   \n", "9       MAIN        Rule             CopyTagRule                                MAIN_BS_#1_BR_#3_LiabilitiesAndEquity<-LiabilitiesAndStockholdersEquity   \n", "10      MAIN        Rule             CopyTagRule                                  MAIN_BS_#1_BR_#4_RetainedEarnings<-RetainedEarningsAccumulatedDeficit   \n", "11      MAIN       Group                                                                                                                  MAIN_BS_#2_EQ   \n", "12      MAIN        Rule             CopyTagRule  MAIN_BS_#2_EQ_#1_HolderEquity<-StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest   \n", "13      MAIN        Rule             CopyTagRule                                                         MAIN_BS_#2_EQ_#2_HolderEquity<-PartnersCapital   \n", "14      MAIN        Rule             CopyTagRule                                                      MAIN_BS_#2_EQ_#3_HolderEquity<-StockholdersEquity   \n", "15      MAIN        Rule               SumUpRule                                                                       MAIN_BS_#2_EQ_#4_TemporaryEquity   \n", "16      MAIN        Rule               SumUpRule                                                                      MAIN_BS_#2_EQ_#5_RedeemableEquity   \n", "17      MAIN        Rule               SumUpRule                                                                                MAIN_BS_#2_EQ_#6_Equity   \n", "18      MAIN       Group                                                                                                                  MAIN_BS_#3_SC   \n", "19      MAIN        Rule          MissingSumRule                                                                                MAIN_BS_#3_SC_#1_Assets   \n", "20      MAIN        Rule      MissingSummandRule                                                                         MAIN_BS_#3_SC_#2_AssetsCurrent   \n", "21      MAIN        Rule      MissingSummandRule                                                                      MAIN_BS_#3_SC_#3_AssetsNoncurrent   \n", "22      MAIN        Rule          MissingSumRule                                                                           MAIN_BS_#3_SC_#4_Liabilities   \n", "23      MAIN        Rule      MissingSummandRule                                                                    MAIN_BS_#3_SC_#5_LiabilitiesCurrent   \n", "24      MAIN        Rule      MissingSummandRule                                                                 MAIN_BS_#3_SC_#6_LiabilitiesNoncurrent   \n", "25      MAIN        Rule          MissingSumRule                                                                                MAIN_BS_#3_SC_#7_Assets   \n", "26      MAIN        Rule      MissingSummandRule                                                                           MAIN_BS_#3_SC_#8_Liabilities   \n", "27      MAIN        Rule      MissingSummandRule                                                                                MAIN_BS_#3_SC_#9_Equity   \n", "28      MAIN        Rule          MissingSumRule                                                                 MAIN_BS_#3_SC_#10_LiabilitiesAndEquity   \n", "29      MAIN        Rule      MissingSummandRule                                                                          MAIN_BS_#3_SC_#11_Liabilities   \n", "30      MAIN        Rule      MissingSummandRule                                                                               MAIN_BS_#3_SC_#12_Equity   \n", "31      MAIN       Group                                                                                                                  MAIN_BS_#4_SU   \n", "32      MAIN        Rule               SumUpRule                                                                                  MAIN_BS_#4_SU_#1_Cash   \n", "33      MAIN        Rule               SumUpRule                                                                      MAIN_BS_#4_SU_#2_RetainedEarnings   \n", "34      MAIN        Rule               SumUpRule                                                                          MAIN_BS_#4_SU_#3_LongTermDebt   \n", "35      MAIN        Rule               SumUpRule                                                                 MAIN_BS_#4_SU_#4_LiabilitiesNoncurrent   \n", "36      MAIN       Group                                                                                                              MAIN_BS_#5_SetSum   \n", "37      MAIN        Rule  SetSumIfOnlyOneSummand                                                           MAIN_BS_#5_SetSum_#1_Assets/AssetsNoncurrent   \n", "38      MAIN        Rule  SetSumIfOnlyOneSummand                                                              MAIN_BS_#5_SetSum_#2_Assets/AssetsCurrent   \n", "39      MAIN        Rule  SetSumIfOnlyOneSummand                                                 MAIN_BS_#5_SetSum_#3_Liabilities/LiabilitiesNoncurrent   \n", "40      MAIN        Rule  SetSumIfOnlyOneSummand                                                    MAIN_BS_#5_SetSum_#4_Liabilities/LiabilitiesCurrent   \n", "41      POST       Group                                                                                                                   POST_BS_POST   \n", "42      POST        Rule  PostCopyToFirstSummand                                                         POST_BS_POST_#1_AssetsCurrent/AssetsNoncurrent   \n", "43      POST        Rule  PostCopyToFirstSummand                                               POST_BS_POST_#2_LiabilitiesCurrent/LiabilitiesNoncurrent   \n", "44      POST        Rule           PostSetToZero                                                  POST_BS_POST_#3_Assets/AssetsCurrent/AssetsNoncurrent   \n", "45      POST        Rule           PostSetToZero                                   POST_BS_POST_#4_Liabilities/LiabilitiesCurrent/LiabilitiesNoncurrent   \n", "46      POST        Rule           PostSetToZero                                                                        POST_BS_POST_#5_TemporaryEquity   \n", "47      POST        Rule           PostSetToZero                                                                       POST_BS_POST_#6_RedeemableEquity   \n", "48      POST        Rule           PostSetToZero                                                                POST_BS_POST_#7_AdditionalPaidInCapital   \n", "49      POST        Rule           PostSetToZero                                                                     POST_BS_POST_#8_TreasuryStockValue   \n", "50     VALID  Validation       SumValidationRule                                                                                            AssetsCheck   \n", "51     VALID  Validation       SumValidationRule                                                                                       LiabilitiesCheck   \n", "52     VALID  Validation       SumValidationRule                                                                                            EquityCheck   \n", "53     VALID  Validation       SumValidationRule                                                                                      AssetsLiaEquCheck   \n", "\n", "                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            description  \n", "0                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        \n", "1                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               Deduplicates the dataframe based on the columns ['adsh', 'coreg', 'report', 'ddate', 'qtrs', 'tag', 'version', 'value']  \n", "2                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        \n", "3                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          Swaps the values between the tag 'Assets' and 'AssetsNoncurrent' if the following equation is True \"'AssetsNoncurrent' = 'Assets' + 'AssetsCurrent\"  and 'AssetsCurrent' > 0  \n", "4                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          Swaps the values between the tag 'Assets' and 'AssetsCurrent' if the following equation is True \"'AssetsCurrent' = 'Assets' + 'AssetsNoncurrent\"  and 'AssetsNoncurrent' > 0  \n", "5                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        \n", "6                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        \n", "7                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 Copies the values from AssetsNet to Assets if AssetsNet is not null and Assets is nan  \n", "8                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             Copies the values from CashAndCashEquivalentsAtCarryingValue to Cash if CashAndCashEquivalentsAtCarryingValue is not null and Cash is nan  \n", "9                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       Copies the values from LiabilitiesAndStockholdersEquity to LiabilitiesAndEquity if LiabilitiesAndStockholdersEquity is not null and LiabilitiesAndEquity is nan  \n", "10                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          Copies the values from RetainedEarningsAccumulatedDeficit to RetainedEarnings if RetainedEarningsAccumulatedDeficit is not null and RetainedEarnings is nan  \n", "11                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       \n", "12                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          Copies the values from StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest to HolderEquity if StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest is not null and HolderEquity is nan  \n", "13                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        Copies the values from PartnersCapital to HolderEquity if PartnersCapital is not null and HolderEquity is nan  \n", "14                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  Copies the values from StockholdersEquity to HolderEquity if StockholdersEquity is not null and HolderEquity is nan  \n", "15                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   Sums up the availalbe values in the columns ['TemporaryEquityAggregateAmountOfRedemptionRequirement', 'TemporaryEquityCarryingAmountAttributableToParent', 'TemporaryEquityRedemptionAmountAttributableToParent', 'TemporaryEquityRedemptionAmountAttributableToNoncontrollingInterest'] into the column 'TemporaryEquity', if the column 'TemporaryEquity' is nan  \n", "16                                                                                                                                                                                                                                                                                                                                                                                                                                                   Sums up the availalbe values in the columns ['RedeemableNoncontrollingInterestEquityCarryingAmount', 'RedeemableNoncontrollingInterestEquityRedemptionAmount', 'RedeemableNoncontrollingInterestEquityOtherCarryingAmount', 'RedeemableNoncontrollingInterestEquityOtherRedemptionAmount', 'RedeemablePreferredStockEquityOtherCarryingAmount', 'RedeemablePreferredStockEquityOtherRedemptionAmount'] into the column 'RedeemableEquity', if the column 'RedeemableEquity' is nan  \n", "17                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          Sums up the availalbe values in the columns ['HolderEquity', 'TemporaryEquity', 'RedeemableEquity'] into the column 'Equity', if the column 'Equity' is nan  \n", "18                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       \n", "19                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                Sums up the values in the columns ['AssetsCurrent', 'AssetsNoncurrent'] into the column 'Assets', if the column 'Assets' is nan and if all columns ['AssetsCurrent', 'AssetsNoncurrent'] have a value  \n", "20                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   Calculates the value for the missing column 'AssetsCurrent' by subtracting the values of the columns '['AssetsNoncurrent']' from the column 'Assets' if all of the columns ['Assets', 'AssetsNoncurrent'] are set.  \n", "21                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      Calculates the value for the missing column 'AssetsNoncurrent' by subtracting the values of the columns '['AssetsCurrent']' from the column 'Assets' if all of the columns ['Assets', 'AssetsCurrent'] are set.  \n", "22                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  Sums up the values in the columns ['LiabilitiesCurrent', 'LiabilitiesNoncurrent'] into the column 'Liabilities', if the column 'Liabilities' is nan and if all columns ['LiabilitiesCurrent', 'LiabilitiesNoncurrent'] have a value  \n", "23                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          Calculates the value for the missing column 'LiabilitiesCurrent' by subtracting the values of the columns '['LiabilitiesNoncurrent']' from the column 'Liabilities' if all of the columns ['Liabilities', 'LiabilitiesNoncurrent'] are set.  \n", "24                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             Calculates the value for the missing column 'LiabilitiesNoncurrent' by subtracting the values of the columns '['LiabilitiesCurrent']' from the column 'Liabilities' if all of the columns ['Liabilities', 'LiabilitiesCurrent'] are set.  \n", "25                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        Sums up the values in the columns ['Liabilities', 'Equity'] into the column 'Assets', if the column 'Assets' is nan and if all columns ['Liabilities', 'Equity'] have a value  \n", "26                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         Calculates the value for the missing column 'Liabilities' by subtracting the values of the columns '['Equity']' from the column 'Assets' if all of the columns ['Assets', 'Equity'] are set.  \n", "27                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    Calculates the value for the missing column 'Equity' by subtracting the values of the columns '['Liabilities']' from the column 'Assets' if all of the columns ['Assets', 'Liabilities'] are set.  \n", "28                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            Sums up the values in the columns ['Liabilities', 'Equity'] into the column 'LiabilitiesAndEquity', if the column 'LiabilitiesAndEquity' is nan and if all columns ['Liabilities', 'Equity'] have a value  \n", "29                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             Calculates the value for the missing column 'Liabilities' by subtracting the values of the columns '['Equity']' from the column 'LiabilitiesAndEquity' if all of the columns ['LiabilitiesAndEquity', 'Equity'] are set.  \n", "30                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        Calculates the value for the missing column 'Equity' by subtracting the values of the columns '['Liabilities']' from the column 'LiabilitiesAndEquity' if all of the columns ['LiabilitiesAndEquity', 'Liabilities'] are set.  \n", "31                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       \n", "32                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  Sums up the availalbe values in the columns ['CashAndCashEquivalentsAtFairValue', 'CashAndDueFromBanks', 'CashCashEquivalentsAndFederalFundsSold', 'RestrictedCashAndCashEquivalentsAtCarryingValue', 'CashAndCashEquivalentsInForeignCurrencyAtCarryingValue'] into the column 'Cash', if the column 'Cash' is nan  \n", "33                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           Sums up the availalbe values in the columns ['RetainedEarningsUnappropriated', 'RetainedEarningsAppropriated'] into the column 'RetainedEarnings', if the column 'RetainedEarnings' is nan  \n", "34                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 Sums up the availalbe values in the columns ['LongTermDebtNoncurrent', 'LongTermDebtAndCapitalLeaseObligations'] into the column 'LongTermDebt', if the column 'LongTermDebt' is nan  \n", "35  Sums up the availalbe values in the columns ['AccruedIncomeTaxesNoncurrent', 'DeferredAndPayableIncomeTaxes', 'DeferredIncomeTaxesAndOtherLiabilitiesNoncurrent', 'DeferredIncomeTaxLiabilitiesNet', 'DeferredTaxLiabilitiesNoncurrent', 'DefinedBenefitPensionPlanLiabilitiesNoncurrent', 'DerivativeLiabilitiesNoncurrent', 'FinanceLeaseLiabilityNoncurrent', 'LiabilitiesOtherThanLongtermDebtNoncurrent', 'LiabilitiesSubjectToCompromise', 'LiabilityForUncertainTaxPositionsNoncurrent', 'LongTermDebt', 'LongTermRetirementBenefitsAndOtherLiabilities', 'OperatingLeaseLiabilityNoncurrent', 'OtherLiabilitiesNoncurrent', 'OtherPostretirementDefinedBenefitPlanLiabilitiesNoncurrent', 'PensionAndOtherPostretirementDefinedBenefitPlansLiabilitiesNoncurrent', 'RegulatoryLiabilityNoncurrent', 'SelfInsuranceReserveNoncurrent'] into the column 'LiabilitiesNoncurrent', if the column 'LiabilitiesNoncurrent' is nan  \n", "36                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       \n", "37                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 Copies the value of the column 'AssetsCurrent' into the column 'Assets' and sets the columns ['AssetsNoncurrent'] to 0.0 if the column 'AssetsCurrent is set and the columns ['Assets', 'AssetsNoncurrent'] are nan.  \n", "38                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 Copies the value of the column 'AssetsNoncurrent' into the column 'Assets' and sets the columns ['AssetsCurrent'] to 0.0 if the column 'AssetsNoncurrent is set and the columns ['Assets', 'AssetsCurrent'] are nan.  \n", "39                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   Copies the value of the column 'LiabilitiesCurrent' into the column 'Liabilities' and sets the columns ['LiabilitiesNoncurrent'] to 0.0 if the column 'LiabilitiesCurrent is set and the columns ['Liabilities', 'LiabilitiesNoncurrent'] are nan.  \n", "40                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   Copies the value of the column 'LiabilitiesNoncurrent' into the column 'Liabilities' and sets the columns ['LiabilitiesCurrent'] to 0.0 if the column 'LiabilitiesNoncurrent is set and the columns ['Liabilities', 'LiabilitiesCurrent'] are nan.  \n", "41                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       \n", "42                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       Copies the value of the 'Assets' to the first summand 'AssetsCurrent' and set the other summands ['AssetsNoncurrent'] to 0.0 if 'Assets is set and the summands ['AssetsCurrent', 'AssetsNoncurrent'] are nan.  \n", "43                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         Copies the value of the 'Liabilities' to the first summand 'LiabilitiesCurrent' and set the other summands ['LiabilitiesNoncurrent'] to 0.0 if 'Liabilities is set and the summands ['LiabilitiesCurrent', 'LiabilitiesNoncurrent'] are nan.  \n", "44                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          Set the value of the ['Assets', 'AssetsCurrent', 'AssetsNoncurrent'] to 0.0 if all ['Assets', 'AssetsCurrent', 'AssetsNoncurrent'] are nan.  \n", "45                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            Set the value of the ['Liabilities', 'LiabilitiesCurrent', 'LiabilitiesNoncurrent'] to 0.0 if all ['Liabilities', 'LiabilitiesCurrent', 'LiabilitiesNoncurrent'] are nan.  \n", "46                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  Set the value of the ['TemporaryEquity'] to 0.0 if all ['TemporaryEquity'] are nan.  \n", "47                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                Set the value of the ['RedeemableEquity'] to 0.0 if all ['RedeemableEquity'] are nan.  \n", "48                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  Set the value of the ['AdditionalPaidInCapital'] to 0.0 if all ['AdditionalPaidInCapital'] are nan.  \n", "49                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            Set the value of the ['TreasuryStockValue'] to 0.0 if all ['TreasuryStockValue'] are nan.  \n", "50                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         Checks whether the sum of ['AssetsCurrent', 'AssetsNoncurrent'] equals the value in 'Assets'  \n", "51                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          Checks whether the sum of ['LiabilitiesCurrent', 'LiabilitiesNoncurrent'] equals the value in 'Liabilities'  \n", "52                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       Checks whether the sum of ['Equity', 'Liabilities'] equals the value in 'LiabilitiesAndEquity'  \n", "53                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     Checks whether the sum of ['Equity', 'Liabilities'] equals the value in 'Assets'  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["result_bag.process_description_df"]}, {"cell_type": "markdown", "id": "2b3d5590-257a-4a79-a14c-5c1735e17e83", "metadata": {"tags": []}, "source": ["**part** is either PREPIVOT, PRE, MAIN, POST, or VALID. **ruleclass** tells you the classname of the applied rule. The **identifier** is a unique id that is used as column name in the `applied_rules_log_df`, resp. the `applied_prepivot_rules_log_df` for prepivot rules. Finally, **description** gives you a textual description about what the rule does, which tags they use and change, as well as the condition under which they are applied."]}, {"cell_type": "markdown", "id": "c0e15e48-bee3-4a25-990c-9f47e181c27f", "metadata": {}, "source": ["### applied_prepivot_rules_log_df"]}, {"cell_type": "code", "execution_count": 10, "id": "7d398d44-c8f9-43fe-a3f2-078182fa0238", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>adsh</th>\n", "      <th>coreg</th>\n", "      <th>report</th>\n", "      <th>ddate</th>\n", "      <th>qtrs</th>\n", "      <th>tag</th>\n", "      <th>version</th>\n", "      <th>id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>30003</th>\n", "      <td>0001437749-22-000287</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20211031</td>\n", "      <td>0</td>\n", "      <td>Assets</td>\n", "      <td>us-gaap/2021</td>\n", "      <td>PREPIVOT_BS_PREPIV_#1_DeDup</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35397</th>\n", "      <td>0001567892-22-000007</td>\n", "      <td></td>\n", "      <td>4</td>\n", "      <td>20211231</td>\n", "      <td>0</td>\n", "      <td>LiabilitiesSubjectToCompromise</td>\n", "      <td>us-gaap/2021</td>\n", "      <td>PREPIVOT_BS_PREPIV_#1_DeDup</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51363</th>\n", "      <td>0001628280-22-003530</td>\n", "      <td></td>\n", "      <td>7</td>\n", "      <td>20211231</td>\n", "      <td>0</td>\n", "      <td>StockholdersEquity</td>\n", "      <td>us-gaap/2021</td>\n", "      <td>PREPIVOT_BS_PREPIV_#1_DeDup</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61289</th>\n", "      <td>0000950170-22-001965</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20211231</td>\n", "      <td>0</td>\n", "      <td>StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest</td>\n", "      <td>us-gaap/2021</td>\n", "      <td>PREPIVOT_BS_PREPIV_#1_DeDup</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61757</th>\n", "      <td>0000950170-22-001423</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20211231</td>\n", "      <td>0</td>\n", "      <td>StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest</td>\n", "      <td>us-gaap/2021</td>\n", "      <td>PREPIVOT_BS_PREPIV_#1_DeDup</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87667</th>\n", "      <td>0000215466-22-000019</td>\n", "      <td></td>\n", "      <td>4</td>\n", "      <td>20211231</td>\n", "      <td>0</td>\n", "      <td>LongTermDebtAndCapitalLeaseObligations</td>\n", "      <td>us-gaap/2021</td>\n", "      <td>PREPIVOT_BS_PREPIV_#1_DeDup</td>\n", "    </tr>\n", "    <tr>\n", "      <th>95866</th>\n", "      <td>0001628280-22-003530</td>\n", "      <td></td>\n", "      <td>7</td>\n", "      <td>20211231</td>\n", "      <td>0</td>\n", "      <td>OperatingLeaseLiabilityNoncurrent</td>\n", "      <td>us-gaap/2021</td>\n", "      <td>PREPIVOT_BS_PREPIV_#1_DeDup</td>\n", "    </tr>\n", "    <tr>\n", "      <th>108025</th>\n", "      <td>0001437749-22-005398</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20220131</td>\n", "      <td>0</td>\n", "      <td>Assets</td>\n", "      <td>us-gaap/2021</td>\n", "      <td>PREPIVOT_BS_PREPIV_#1_DeDup</td>\n", "    </tr>\n", "    <tr>\n", "      <th>115154</th>\n", "      <td>0001567892-22-000007</td>\n", "      <td></td>\n", "      <td>4</td>\n", "      <td>20211231</td>\n", "      <td>0</td>\n", "      <td>Liabilities</td>\n", "      <td>us-gaap/2021</td>\n", "      <td>PREPIVOT_BS_PREPIV_#1_DeDup</td>\n", "    </tr>\n", "    <tr>\n", "      <th>116620</th>\n", "      <td>0001437749-22-005398</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20220131</td>\n", "      <td>0</td>\n", "      <td>Liabilities</td>\n", "      <td>us-gaap/2021</td>\n", "      <td>PREPIVOT_BS_PREPIV_#1_DeDup</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        adsh coreg  report     ddate  qtrs                                                                     tag       version                           id\n", "30003   0001437749-22-000287             2  20211031     0                                                                  Assets  us-gaap/2021  PREPIVOT_BS_PREPIV_#1_DeDup\n", "35397   0001567892-22-000007             4  20211231     0                                          LiabilitiesSubjectToCompromise  us-gaap/2021  PREPIVOT_BS_PREPIV_#1_DeDup\n", "51363   0001628280-22-003530             7  20211231     0                                                      StockholdersEquity  us-gaap/2021  PREPIVOT_BS_PREPIV_#1_DeDup\n", "61289   0000950170-22-001965             2  20211231     0  StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest  us-gaap/2021  PREPIVOT_BS_PREPIV_#1_DeDup\n", "61757   0000950170-22-001423             2  20211231     0  StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest  us-gaap/2021  PREPIVOT_BS_PREPIV_#1_DeDup\n", "87667   0000215466-22-000019             4  20211231     0                                  LongTermDebtAndCapitalLeaseObligations  us-gaap/2021  PREPIVOT_BS_PREPIV_#1_DeDup\n", "95866   0001628280-22-003530             7  20211231     0                                       OperatingLeaseLiabilityNoncurrent  us-gaap/2021  PREPIVOT_BS_PREPIV_#1_DeDup\n", "108025  0001437749-22-005398             2  20220131     0                                                                  Assets  us-gaap/2021  PREPIVOT_BS_PREPIV_#1_DeDup\n", "115154  0001567892-22-000007             4  20211231     0                                                             Liabilities  us-gaap/2021  PREPIVOT_BS_PREPIV_#1_DeDup\n", "116620  0001437749-22-005398             2  20220131     0                                                             Liabilities  us-gaap/2021  PREPIVOT_BS_PREPIV_#1_DeDup"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["result_bag.applied_prepivot_rules_log_df[:10]"]}, {"cell_type": "markdown", "id": "10f966b9-2327-49e4-89bc-86d815c06442", "metadata": {"tags": []}, "source": ["For the balance sheet standardizer, there is only the deduplication prepivot rule applied. Therefore, showing the entries in the original `pre_num_df` that were duplicated and had to be removed."]}, {"cell_type": "markdown", "id": "b84a1dbc-2ff2-49d3-9bb4-bf5464d1a402", "metadata": {}, "source": ["### applied_rules_log_df"]}, {"cell_type": "code", "execution_count": 11, "id": "54696c2b-3e46-4ed6-8616-667b037de23c", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>adsh</th>\n", "      <th>coreg</th>\n", "      <th>report</th>\n", "      <th>ddate</th>\n", "      <th>qtrs</th>\n", "      <th>PRE_BS_PRE_#1_Assets/AssetsNoncurrent</th>\n", "      <th>PRE_BS_PRE_#2_Assets/AssetsCurrent</th>\n", "      <th>MAIN_1_BS_#1_BR_#1_Assets&lt;-AssetsNet</th>\n", "      <th>MAIN_1_BS_#1_BR_#2_Cash&lt;-CashAndCashEquivalentsAtCarryingValue</th>\n", "      <th>MAIN_1_BS_#1_BR_#3_LiabilitiesAndEquity&lt;-LiabilitiesAndStockholdersEquity</th>\n", "      <th>MAIN_1_BS_#1_BR_#4_RetainedEarnings&lt;-RetainedEarningsAccumulatedDeficit</th>\n", "      <th>MAIN_1_BS_#2_EQ_#1_HolderEquity&lt;-StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest</th>\n", "      <th>MAIN_1_BS_#2_EQ_#2_HolderEquity&lt;-PartnersCapital</th>\n", "      <th>MAIN_1_BS_#2_EQ_#3_HolderEquity&lt;-StockholdersEquity</th>\n", "      <th>MAIN_1_BS_#2_EQ_#4_TemporaryEquity</th>\n", "      <th>MAIN_1_BS_#2_EQ_#5_RedeemableEquity</th>\n", "      <th>MAIN_1_BS_#2_EQ_#6_Equity</th>\n", "      <th>MAIN_1_BS_#3_SC_#1_Assets</th>\n", "      <th>MAIN_1_BS_#3_SC_#2_AssetsCurrent</th>\n", "      <th>MAIN_1_BS_#3_SC_#3_AssetsNoncurrent</th>\n", "      <th>MAIN_1_BS_#3_SC_#4_Liabilities</th>\n", "      <th>MAIN_1_BS_#3_SC_#5_LiabilitiesCurrent</th>\n", "      <th>MAIN_1_BS_#3_SC_#6_LiabilitiesNoncurrent</th>\n", "      <th>MAIN_1_BS_#3_SC_#7_Assets</th>\n", "      <th>MAIN_1_BS_#3_SC_#8_Liabilities</th>\n", "      <th>MAIN_1_BS_#3_SC_#9_Equity</th>\n", "      <th>MAIN_1_BS_#3_SC_#10_LiabilitiesAndEquity</th>\n", "      <th>MAIN_1_BS_#3_SC_#11_Liabilities</th>\n", "      <th>MAIN_1_BS_#3_SC_#12_Equity</th>\n", "      <th>MAIN_1_BS_#4_SU_#1_Cash</th>\n", "      <th>MAIN_1_BS_#4_SU_#2_RetainedEarnings</th>\n", "      <th>MAIN_1_BS_#4_SU_#3_LongTermDebt</th>\n", "      <th>MAIN_1_BS_#4_SU_#4_LiabilitiesNoncurrent</th>\n", "      <th>MAIN_1_BS_#5_SetSum_#1_Assets/AssetsNoncurrent</th>\n", "      <th>MAIN_1_BS_#5_SetSum_#2_Assets/AssetsCurrent</th>\n", "      <th>MAIN_1_BS_#5_SetSum_#3_Liabilities/LiabilitiesNoncurrent</th>\n", "      <th>MAIN_1_BS_#5_SetSum_#4_Liabilities/LiabilitiesCurrent</th>\n", "      <th>MAIN_2_BS_#1_BR_#1_Assets&lt;-AssetsNet</th>\n", "      <th>MAIN_2_BS_#1_BR_#2_Cash&lt;-CashAndCashEquivalentsAtCarryingValue</th>\n", "      <th>MAIN_2_BS_#1_BR_#3_LiabilitiesAndEquity&lt;-LiabilitiesAndStockholdersEquity</th>\n", "      <th>MAIN_2_BS_#1_BR_#4_RetainedEarnings&lt;-RetainedEarningsAccumulatedDeficit</th>\n", "      <th>MAIN_2_BS_#2_EQ_#1_HolderEquity&lt;-StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest</th>\n", "      <th>MAIN_2_BS_#2_EQ_#2_HolderEquity&lt;-PartnersCapital</th>\n", "      <th>MAIN_2_BS_#2_EQ_#3_HolderEquity&lt;-StockholdersEquity</th>\n", "      <th>MAIN_2_BS_#2_EQ_#4_TemporaryEquity</th>\n", "      <th>MAIN_2_BS_#2_EQ_#5_RedeemableEquity</th>\n", "      <th>MAIN_2_BS_#2_EQ_#6_Equity</th>\n", "      <th>MAIN_2_BS_#3_SC_#1_Assets</th>\n", "      <th>MAIN_2_BS_#3_SC_#2_AssetsCurrent</th>\n", "      <th>MAIN_2_BS_#3_SC_#3_AssetsNoncurrent</th>\n", "      <th>MAIN_2_BS_#3_SC_#4_Liabilities</th>\n", "      <th>MAIN_2_BS_#3_SC_#5_LiabilitiesCurrent</th>\n", "      <th>MAIN_2_BS_#3_SC_#6_LiabilitiesNoncurrent</th>\n", "      <th>MAIN_2_BS_#3_SC_#7_Assets</th>\n", "      <th>MAIN_2_BS_#3_SC_#8_Liabilities</th>\n", "      <th>MAIN_2_BS_#3_SC_#9_Equity</th>\n", "      <th>MAIN_2_BS_#3_SC_#10_LiabilitiesAndEquity</th>\n", "      <th>MAIN_2_BS_#3_SC_#11_Liabilities</th>\n", "      <th>MAIN_2_BS_#3_SC_#12_Equity</th>\n", "      <th>MAIN_2_BS_#4_SU_#1_Cash</th>\n", "      <th>MAIN_2_BS_#4_SU_#2_RetainedEarnings</th>\n", "      <th>MAIN_2_BS_#4_SU_#3_LongTermDebt</th>\n", "      <th>MAIN_2_BS_#4_SU_#4_LiabilitiesNoncurrent</th>\n", "      <th>MAIN_2_BS_#5_SetSum_#1_Assets/AssetsNoncurrent</th>\n", "      <th>MAIN_2_BS_#5_SetSum_#2_Assets/AssetsCurrent</th>\n", "      <th>MAIN_2_BS_#5_SetSum_#3_Liabilities/LiabilitiesNoncurrent</th>\n", "      <th>MAIN_2_BS_#5_SetSum_#4_Liabilities/LiabilitiesCurrent</th>\n", "      <th>MAIN_3_BS_#1_BR_#1_Assets&lt;-AssetsNet</th>\n", "      <th>MAIN_3_BS_#1_BR_#2_Cash&lt;-CashAndCashEquivalentsAtCarryingValue</th>\n", "      <th>MAIN_3_BS_#1_BR_#3_LiabilitiesAndEquity&lt;-LiabilitiesAndStockholdersEquity</th>\n", "      <th>MAIN_3_BS_#1_BR_#4_RetainedEarnings&lt;-RetainedEarningsAccumulatedDeficit</th>\n", "      <th>MAIN_3_BS_#2_EQ_#1_HolderEquity&lt;-StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest</th>\n", "      <th>MAIN_3_BS_#2_EQ_#2_HolderEquity&lt;-PartnersCapital</th>\n", "      <th>MAIN_3_BS_#2_EQ_#3_HolderEquity&lt;-StockholdersEquity</th>\n", "      <th>MAIN_3_BS_#2_EQ_#4_TemporaryEquity</th>\n", "      <th>MAIN_3_BS_#2_EQ_#5_RedeemableEquity</th>\n", "      <th>MAIN_3_BS_#2_EQ_#6_Equity</th>\n", "      <th>MAIN_3_BS_#3_SC_#1_Assets</th>\n", "      <th>MAIN_3_BS_#3_SC_#2_AssetsCurrent</th>\n", "      <th>MAIN_3_BS_#3_SC_#3_AssetsNoncurrent</th>\n", "      <th>MAIN_3_BS_#3_SC_#4_Liabilities</th>\n", "      <th>MAIN_3_BS_#3_SC_#5_LiabilitiesCurrent</th>\n", "      <th>MAIN_3_BS_#3_SC_#6_LiabilitiesNoncurrent</th>\n", "      <th>MAIN_3_BS_#3_SC_#7_Assets</th>\n", "      <th>MAIN_3_BS_#3_SC_#8_Liabilities</th>\n", "      <th>MAIN_3_BS_#3_SC_#9_Equity</th>\n", "      <th>MAIN_3_BS_#3_SC_#10_LiabilitiesAndEquity</th>\n", "      <th>MAIN_3_BS_#3_SC_#11_Liabilities</th>\n", "      <th>MAIN_3_BS_#3_SC_#12_Equity</th>\n", "      <th>MAIN_3_BS_#4_SU_#1_Cash</th>\n", "      <th>MAIN_3_BS_#4_SU_#2_RetainedEarnings</th>\n", "      <th>MAIN_3_BS_#4_SU_#3_LongTermDebt</th>\n", "      <th>MAIN_3_BS_#4_SU_#4_LiabilitiesNoncurrent</th>\n", "      <th>MAIN_3_BS_#5_SetSum_#1_Assets/AssetsNoncurrent</th>\n", "      <th>MAIN_3_BS_#5_SetSum_#2_Assets/AssetsCurrent</th>\n", "      <th>MAIN_3_BS_#5_SetSum_#3_Liabilities/LiabilitiesNoncurrent</th>\n", "      <th>MAIN_3_BS_#5_SetSum_#4_Liabilities/LiabilitiesCurrent</th>\n", "      <th>POST_BS_POST_#1_AssetsCurrent/AssetsNoncurrent</th>\n", "      <th>POST_BS_POST_#2_LiabilitiesCurrent/LiabilitiesNoncurrent</th>\n", "      <th>POST_BS_POST_#3_Assets/AssetsCurrent/AssetsNoncurrent</th>\n", "      <th>POST_BS_POST_#4_Liabilities/LiabilitiesCurrent/LiabilitiesNoncurrent</th>\n", "      <th>POST_BS_POST_#5_TemporaryEquity</th>\n", "      <th>POST_BS_POST_#6_RedeemableEquity</th>\n", "      <th>POST_BS_POST_#7_AdditionalPaidInCapital</th>\n", "      <th>POST_BS_POST_#8_TreasuryStockValue</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0000002178-22-000033</td>\n", "      <td></td>\n", "      <td>3</td>\n", "      <td>20211231</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0000002178-22-000046</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20220331</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0000002178-22-000066</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20220630</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0000002178-22-000089</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20220930</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0000002488-22-000016</td>\n", "      <td></td>\n", "      <td>5</td>\n", "      <td>20211231</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>0000002488-22-000078</td>\n", "      <td></td>\n", "      <td>4</td>\n", "      <td>20220331</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>0000002488-22-000123</td>\n", "      <td></td>\n", "      <td>4</td>\n", "      <td>20220630</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>0000002488-22-000170</td>\n", "      <td></td>\n", "      <td>4</td>\n", "      <td>20220930</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>0000002969-22-000010</td>\n", "      <td></td>\n", "      <td>5</td>\n", "      <td>20211231</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>0000002969-22-000026</td>\n", "      <td></td>\n", "      <td>5</td>\n", "      <td>20220331</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   adsh coreg  report     ddate  qtrs  PRE_BS_PRE_#1_Assets/AssetsNoncurrent  PRE_BS_PRE_#2_Assets/AssetsCurrent  MAIN_1_BS_#1_BR_#1_Assets<-AssetsNet  MAIN_1_BS_#1_BR_#2_Cash<-CashAndCashEquivalentsAtCarryingValue  MAIN_1_BS_#1_BR_#3_LiabilitiesAndEquity<-LiabilitiesAndStockholdersEquity  MAIN_1_BS_#1_BR_#4_RetainedEarnings<-RetainedEarningsAccumulatedDeficit  MAIN_1_BS_#2_EQ_#1_HolderEquity<-StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest  MAIN_1_BS_#2_EQ_#2_HolderEquity<-PartnersCapital  MAIN_1_BS_#2_EQ_#3_HolderEquity<-StockholdersEquity  MAIN_1_BS_#2_EQ_#4_TemporaryEquity  MAIN_1_BS_#2_EQ_#5_RedeemableEquity  MAIN_1_BS_#2_EQ_#6_Equity  MAIN_1_BS_#3_SC_#1_Assets  MAIN_1_BS_#3_SC_#2_AssetsCurrent  MAIN_1_BS_#3_SC_#3_AssetsNoncurrent  MAIN_1_BS_#3_SC_#4_Liabilities  MAIN_1_BS_#3_SC_#5_LiabilitiesCurrent  MAIN_1_BS_#3_SC_#6_LiabilitiesNoncurrent  MAIN_1_BS_#3_SC_#7_Assets  MAIN_1_BS_#3_SC_#8_Liabilities  MAIN_1_BS_#3_SC_#9_Equity  \\\n", "0  0000002178-22-000033             3  20211231     0                                  False                               False                                 False                                                            True                                                                       True                                                                     True                                                                                                    False                                             False                                                 True                               False                                False                       True                      False                             False                                 True                           False                                  False                                      True                      False                           False                      False   \n", "1  0000002178-22-000046             2  20220331     0                                  False                               False                                 False                                                            True                                                                       True                                                                     True                                                                                                    False                                             False                                                 True                               False                                False                       True                      False                             False                                 True                           False                                  False                                      True                      False                           False                      False   \n", "2  0000002178-22-000066             2  20220630     0                                  False                               False                                 False                                                            True                                                                       True                                                                     True                                                                                                    False                                             False                                                 True                               False                                False                       True                      False                             False                                 True                           False                                  False                                      True                      False                           False                      False   \n", "3  0000002178-22-000089             2  20220930     0                                  False                               False                                 False                                                            True                                                                       True                                                                     True                                                                                                    False                                             False                                                 True                               False                                False                       True                      False                             False                                 True                           False                                  False                                      True                      False                           False                      False   \n", "4  0000002488-22-000016             5  20211231     0                                  False                               False                                 False                                                            True                                                                       True                                                                     True                                                                                                    False                                             False                                                 True                               False                                False                       True                      False                             False                                 True                           False                                  False                                     False                      False                            True                      False   \n", "5  0000002488-22-000078             4  20220331     0                                  False                               False                                 False                                                            True                                                                       True                                                                     True                                                                                                    False                                             False                                                 True                               False                                False                       True                      False                             False                                 True                           False                                  False                                     False                      False                            True                      False   \n", "6  0000002488-22-000123             4  20220630     0                                  False                               False                                 False                                                            True                                                                       True                                                                     True                                                                                                    False                                             False                                                 True                               False                                False                       True                      False                             False                                 True                           False                                  False                                     False                      False                            True                      False   \n", "7  0000002488-22-000170             4  20220930     0                                  False                               False                                 False                                                            True                                                                       True                                                                     True                                                                                                    False                                             False                                                 True                               False                                False                       True                      False                             False                                 True                           False                                  False                                     False                      False                            True                      False   \n", "8  0000002969-22-000010             5  20211231     0                                  False                               False                                 False                                                            True                                                                       True                                                                     True                                                                                                     True                                             False                                                False                               False                                False                       True                      False                             False                                False                           False                                  False                                     False                      False                           False                      False   \n", "9  0000002969-22-000026             5  20220331     0                                  False                               False                                 False                                                            True                                                                       True                                                                     True                                                                                                     True                                             False                                                False                               False                                False                       True                      False                             False                                False                           False                                  False                                     False                      False                           False                      False   \n", "\n", "   MAIN_1_BS_#3_SC_#10_LiabilitiesAndEquity  MAIN_1_BS_#3_SC_#11_Liabilities  MAIN_1_BS_#3_SC_#12_Equity  MAIN_1_BS_#4_SU_#1_Cash  MAIN_1_BS_#4_SU_#2_RetainedEarnings  MAIN_1_BS_#4_SU_#3_LongTermDebt  MAIN_1_BS_#4_SU_#4_LiabilitiesNoncurrent  MAIN_1_BS_#5_SetSum_#1_Assets/AssetsNoncurrent  MAIN_1_BS_#5_SetSum_#2_Assets/AssetsCurrent  MAIN_1_BS_#5_SetSum_#3_Liabilities/LiabilitiesNoncurrent  MAIN_1_BS_#5_SetSum_#4_Liabilities/LiabilitiesCurrent  MAIN_2_BS_#1_BR_#1_Assets<-AssetsNet  MAIN_2_BS_#1_BR_#2_Cash<-CashAndCashEquivalentsAtCarryingValue  MAIN_2_BS_#1_BR_#3_LiabilitiesAndEquity<-LiabilitiesAndStockholdersEquity  MAIN_2_BS_#1_BR_#4_RetainedEarnings<-RetainedEarningsAccumulatedDeficit  MAIN_2_BS_#2_EQ_#1_HolderEquity<-StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest  MAIN_2_BS_#2_EQ_#2_HolderEquity<-PartnersCapital  MAIN_2_BS_#2_EQ_#3_HolderEquity<-StockholdersEquity  MAIN_2_BS_#2_EQ_#4_TemporaryEquity  MAIN_2_BS_#2_EQ_#5_RedeemableEquity  \\\n", "0                                     False                            False                       False                    False                                False                            False                                     False                                           False                                        False                                                     False                                                  False                                 False                                                           False                                                                      False                                                                    False                                                                                                    False                                             False                                                False                               False                                False   \n", "1                                     False                            False                       False                    False                                False                            False                                     False                                           False                                        False                                                     False                                                  False                                 False                                                           False                                                                      False                                                                    False                                                                                                    False                                             False                                                False                               False                                False   \n", "2                                     False                            False                       False                    False                                False                            False                                     False                                           False                                        False                                                     False                                                  False                                 False                                                           False                                                                      False                                                                    False                                                                                                    False                                             False                                                False                               False                                False   \n", "3                                     False                            False                       False                    False                                False                            False                                     False                                           False                                        False                                                     False                                                  False                                 False                                                           False                                                                      False                                                                    False                                                                                                    False                                             False                                                False                               False                                False   \n", "4                                     False                            False                       False                    False                                False                             True                                      True                                           False                                        False                                                     False                                                  False                                 False                                                           False                                                                      False                                                                    False                                                                                                    False                                             False                                                False                               False                                False   \n", "5                                     False                            False                       False                    False                                False                             True                                      True                                           False                                        False                                                     False                                                  False                                 False                                                           False                                                                      False                                                                    False                                                                                                    False                                             False                                                False                               False                                False   \n", "6                                     False                            False                       False                    False                                False                             True                                      True                                           False                                        False                                                     False                                                  False                                 False                                                           False                                                                      False                                                                    False                                                                                                    False                                             False                                                False                               False                                False   \n", "7                                     False                            False                       False                    False                                False                             True                                      True                                           False                                        False                                                     False                                                  False                                 False                                                           False                                                                      False                                                                    False                                                                                                    False                                             False                                                False                               False                                False   \n", "8                                     False                            False                       False                    False                                False                             True                                     False                                           False                                        False                                                     False                                                  False                                 False                                                           False                                                                      False                                                                    False                                                                                                    False                                             False                                                False                               False                                False   \n", "9                                     False                            False                       False                    False                                False                             True                                     False                                           False                                        False                                                     False                                                  False                                 False                                                           False                                                                      False                                                                    False                                                                                                    False                                             False                                                False                               False                                False   \n", "\n", "   MAIN_2_BS_#2_EQ_#6_Equity  MAIN_2_BS_#3_SC_#1_Assets  MAIN_2_BS_#3_SC_#2_AssetsCurrent  MAIN_2_BS_#3_SC_#3_AssetsNoncurrent  MAIN_2_BS_#3_SC_#4_Liabilities  MAIN_2_BS_#3_SC_#5_LiabilitiesCurrent  MAIN_2_BS_#3_SC_#6_LiabilitiesNoncurrent  MAIN_2_BS_#3_SC_#7_Assets  MAIN_2_BS_#3_SC_#8_Liabilities  MAIN_2_BS_#3_SC_#9_Equity  MAIN_2_BS_#3_SC_#10_LiabilitiesAndEquity  MAIN_2_BS_#3_SC_#11_Liabilities  MAIN_2_BS_#3_SC_#12_Equity  MAIN_2_BS_#4_SU_#1_Cash  MAIN_2_BS_#4_SU_#2_RetainedEarnings  MAIN_2_BS_#4_SU_#3_LongTermDebt  MAIN_2_BS_#4_SU_#4_LiabilitiesNoncurrent  MAIN_2_BS_#5_SetSum_#1_Assets/AssetsNoncurrent  MAIN_2_BS_#5_SetSum_#2_Assets/AssetsCurrent  MAIN_2_BS_#5_SetSum_#3_Liabilities/LiabilitiesNoncurrent  MAIN_2_BS_#5_SetSum_#4_Liabilities/LiabilitiesCurrent  MAIN_3_BS_#1_BR_#1_Assets<-AssetsNet  MAIN_3_BS_#1_BR_#2_Cash<-CashAndCashEquivalentsAtCarryingValue  MAIN_3_BS_#1_BR_#3_LiabilitiesAndEquity<-LiabilitiesAndStockholdersEquity  \\\n", "0                      False                      False                             False                                False                           False                                  False                                     False                      False                           False                      False                                     False                            False                       False                    False                                False                            False                                     False                                           False                                        False                                                     False                                                  False                                 False                                                           False                                                                      False   \n", "1                      False                      False                             False                                False                           False                                  False                                     False                      False                           False                      False                                     False                            False                       False                    False                                False                            False                                     False                                           False                                        False                                                     False                                                  False                                 False                                                           False                                                                      False   \n", "2                      False                      False                             False                                False                           False                                  False                                     False                      False                           False                      False                                     False                            False                       False                    False                                False                            False                                     False                                           False                                        False                                                     False                                                  False                                 False                                                           False                                                                      False   \n", "3                      False                      False                             False                                False                           False                                  False                                     False                      False                           False                      False                                     False                            False                       False                    False                                False                            False                                     False                                           False                                        False                                                     False                                                  False                                 False                                                           False                                                                      False   \n", "4                      False                      False                             False                                False                           False                                  False                                     False                      False                           False                      False                                     False                            False                       False                    False                                False                            False                                     False                                           False                                        False                                                     False                                                  False                                 False                                                           False                                                                      False   \n", "5                      False                      False                             False                                False                           False                                  False                                     False                      False                           False                      False                                     False                            False                       False                    False                                False                            False                                     False                                           False                                        False                                                     False                                                  False                                 False                                                           False                                                                      False   \n", "6                      False                      False                             False                                False                           False                                  False                                     False                      False                           False                      False                                     False                            False                       False                    False                                False                            False                                     False                                           False                                        False                                                     False                                                  False                                 False                                                           False                                                                      False   \n", "7                      False                      False                             False                                False                           False                                  False                                     False                      False                           False                      False                                     False                            False                       False                    False                                False                            False                                     False                                           False                                        False                                                     False                                                  False                                 False                                                           False                                                                      False   \n", "8                      False                      False                             False                                False                           False                                  False                                     False                      False                           False                      False                                     False                            False                       False                    False                                False                            False                                     False                                           False                                        False                                                     False                                                  False                                 False                                                           False                                                                      False   \n", "9                      False                      False                             False                                False                           False                                  False                                     False                      False                           False                      False                                     False                            False                       False                    False                                False                            False                                     False                                           False                                        False                                                     False                                                  False                                 False                                                           False                                                                      False   \n", "\n", "   MAIN_3_BS_#1_BR_#4_RetainedEarnings<-RetainedEarningsAccumulatedDeficit  MAIN_3_BS_#2_EQ_#1_HolderEquity<-StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest  MAIN_3_BS_#2_EQ_#2_HolderEquity<-PartnersCapital  MAIN_3_BS_#2_EQ_#3_HolderEquity<-StockholdersEquity  MAIN_3_BS_#2_EQ_#4_TemporaryEquity  MAIN_3_BS_#2_EQ_#5_RedeemableEquity  MAIN_3_BS_#2_EQ_#6_Equity  MAIN_3_BS_#3_SC_#1_Assets  MAIN_3_BS_#3_SC_#2_AssetsCurrent  MAIN_3_BS_#3_SC_#3_AssetsNoncurrent  MAIN_3_BS_#3_SC_#4_Liabilities  MAIN_3_BS_#3_SC_#5_LiabilitiesCurrent  MAIN_3_BS_#3_SC_#6_LiabilitiesNoncurrent  MAIN_3_BS_#3_SC_#7_Assets  MAIN_3_BS_#3_SC_#8_Liabilities  MAIN_3_BS_#3_SC_#9_Equity  MAIN_3_BS_#3_SC_#10_LiabilitiesAndEquity  MAIN_3_BS_#3_SC_#11_Liabilities  MAIN_3_BS_#3_SC_#12_Equity  MAIN_3_BS_#4_SU_#1_Cash  MAIN_3_BS_#4_SU_#2_RetainedEarnings  MAIN_3_BS_#4_SU_#3_LongTermDebt  MAIN_3_BS_#4_SU_#4_LiabilitiesNoncurrent  MAIN_3_BS_#5_SetSum_#1_Assets/AssetsNoncurrent  \\\n", "0                                                                    False                                                                                                    False                                             False                                                False                               False                                False                      False                      False                             False                                False                           False                                  False                                     False                      False                           False                      False                                     False                            False                       False                    False                                False                            False                                     False                                           False   \n", "1                                                                    False                                                                                                    False                                             False                                                False                               False                                False                      False                      False                             False                                False                           False                                  False                                     False                      False                           False                      False                                     False                            False                       False                    False                                False                            False                                     False                                           False   \n", "2                                                                    False                                                                                                    False                                             False                                                False                               False                                False                      False                      False                             False                                False                           False                                  False                                     False                      False                           False                      False                                     False                            False                       False                    False                                False                            False                                     False                                           False   \n", "3                                                                    False                                                                                                    False                                             False                                                False                               False                                False                      False                      False                             False                                False                           False                                  False                                     False                      False                           False                      False                                     False                            False                       False                    False                                False                            False                                     False                                           False   \n", "4                                                                    False                                                                                                    False                                             False                                                False                               False                                False                      False                      False                             False                                False                           False                                  False                                     False                      False                           False                      False                                     False                            False                       False                    False                                False                            False                                     False                                           False   \n", "5                                                                    False                                                                                                    False                                             False                                                False                               False                                False                      False                      False                             False                                False                           False                                  False                                     False                      False                           False                      False                                     False                            False                       False                    False                                False                            False                                     False                                           False   \n", "6                                                                    False                                                                                                    False                                             False                                                False                               False                                False                      False                      False                             False                                False                           False                                  False                                     False                      False                           False                      False                                     False                            False                       False                    False                                False                            False                                     False                                           False   \n", "7                                                                    False                                                                                                    False                                             False                                                False                               False                                False                      False                      False                             False                                False                           False                                  False                                     False                      False                           False                      False                                     False                            False                       False                    False                                False                            False                                     False                                           False   \n", "8                                                                    False                                                                                                    False                                             False                                                False                               False                                False                      False                      False                             False                                False                           False                                  False                                     False                      False                           False                      False                                     False                            False                       False                    False                                False                            False                                     False                                           False   \n", "9                                                                    False                                                                                                    False                                             False                                                False                               False                                False                      False                      False                             False                                False                           False                                  False                                     False                      False                           False                      False                                     False                            False                       False                    False                                False                            False                                     False                                           False   \n", "\n", "   MAIN_3_BS_#5_SetSum_#2_Assets/AssetsCurrent  MAIN_3_BS_#5_SetSum_#3_Liabilities/LiabilitiesNoncurrent  MAIN_3_BS_#5_SetSum_#4_Liabilities/LiabilitiesCurrent  POST_BS_POST_#1_AssetsCurrent/AssetsNoncurrent  POST_BS_POST_#2_LiabilitiesCurrent/LiabilitiesNoncurrent  POST_BS_POST_#3_Assets/AssetsCurrent/AssetsNoncurrent  POST_BS_POST_#4_Liabilities/LiabilitiesCurrent/LiabilitiesNoncurrent  POST_BS_POST_#5_TemporaryEquity  POST_BS_POST_#6_RedeemableEquity  POST_BS_POST_#7_AdditionalPaidInCapital  POST_BS_POST_#8_TreasuryStockValue  \n", "0                                        False                                                     False                                                  False                                           False                                                     False                                                  False                                                                 False                             True                              True                                    False                                True  \n", "1                                        False                                                     False                                                  False                                           False                                                     False                                                  False                                                                 False                             True                              True                                    False                                True  \n", "2                                        False                                                     False                                                  False                                           False                                                     False                                                  False                                                                 False                             True                              True                                    False                                True  \n", "3                                        False                                                     False                                                  False                                           False                                                     False                                                  False                                                                 False                             True                              True                                    False                                True  \n", "4                                        False                                                     False                                                  False                                           False                                                     False                                                  False                                                                 False                             True                              True                                     True                               False  \n", "5                                        False                                                     False                                                  False                                           False                                                     False                                                  False                                                                 False                             True                              True                                     True                               False  \n", "6                                        False                                                     False                                                  False                                           False                                                     False                                                  False                                                                 False                             True                              True                                     True                               False  \n", "7                                        False                                                     False                                                  False                                           False                                                     False                                                  False                                                                 False                             True                              True                                     True                               False  \n", "8                                        False                                                     False                                                  False                                           False                                                     False                                                  False                                                                 False                             True                              True                                     True                               False  \n", "9                                        False                                                     False                                                  False                                           False                                                     False                                                  False                                                                 False                             True                              True                                     True                               False  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["result_bag.applied_rules_log_df[:10]"]}, {"cell_type": "markdown", "id": "17ac83fd-a752-4926-9808-c4f6538acda5", "metadata": {}, "source": ["Obviously, this table has the same index columns as the `result_df`. Furthermore, for every rule that could be applied there is a column. Since the main rules are applied multiple times (3 iterations by default), you find multiple rule ids for the MAIN rules. **MAIN_1** indicate which MAIN-rules where applied in the first iteration, **MAIN_2** in the second iteration, and **MAIN_3** in the third.\n", "\n", "If you analyze the statements of a certain company, you should check how many rules were applied (at least the MAIN rules), since this gives an indication about how much \"assumption\" might in the standardized data.\n", "\n", "As an example, let us have a look at the 10-K of apple in 2022, which adsh is \"0000320193-22-000108\".\n", "\n", "First, lets have a look at the standardized entry for this report:"]}, {"cell_type": "code", "execution_count": 12, "id": "4b5e9a49-76e3-4dfd-8c35-ae6a66a7acea", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>adsh</th>\n", "      <th>cik</th>\n", "      <th>name</th>\n", "      <th>form</th>\n", "      <th>fye</th>\n", "      <th>fy</th>\n", "      <th>fp</th>\n", "      <th>date</th>\n", "      <th>filed</th>\n", "      <th>coreg</th>\n", "      <th>report</th>\n", "      <th>ddate</th>\n", "      <th>qtrs</th>\n", "      <th>Assets</th>\n", "      <th>AssetsCurrent</th>\n", "      <th>Cash</th>\n", "      <th>AssetsNoncurrent</th>\n", "      <th>Liabilities</th>\n", "      <th>LiabilitiesCurrent</th>\n", "      <th>LiabilitiesNoncurrent</th>\n", "      <th>Equity</th>\n", "      <th>HolderEquity</th>\n", "      <th>RetainedEarnings</th>\n", "      <th>AdditionalPaidInCapital</th>\n", "      <th>TreasuryStockValue</th>\n", "      <th>TemporaryEquity</th>\n", "      <th>RedeemableEquity</th>\n", "      <th>LiabilitiesAndEquity</th>\n", "      <th>Assets<PERSON><PERSON>ck_error</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON>_cat</th>\n", "      <th>LiabilitiesCheck_error</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON>_cat</th>\n", "      <th>EquityCheck_error</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON>_cat</th>\n", "      <th>AssetsLiaEquCheck_error</th>\n", "      <th>AssetsLiaEquCheck_cat</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>19931</th>\n", "      <td>0000320193-22-000108</td>\n", "      <td>320193</td>\n", "      <td>APPLE INC</td>\n", "      <td>10-K</td>\n", "      <td>0930</td>\n", "      <td>2022.0</td>\n", "      <td>FY</td>\n", "      <td>2022-09-30</td>\n", "      <td>20221028</td>\n", "      <td></td>\n", "      <td>5</td>\n", "      <td>20220930</td>\n", "      <td>0</td>\n", "      <td>3.527550e+11</td>\n", "      <td>1.354050e+11</td>\n", "      <td>2.364600e+10</td>\n", "      <td>2.173500e+11</td>\n", "      <td>3.020830e+11</td>\n", "      <td>1.539820e+11</td>\n", "      <td>1.481010e+11</td>\n", "      <td>5.067200e+10</td>\n", "      <td>5.067200e+10</td>\n", "      <td>-3.068000e+09</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3.527550e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                       adsh     cik       name  form   fye      fy  fp       date     filed coreg  report     ddate  qtrs        Assets  AssetsCurrent          Cash  AssetsNoncurrent   Liabilities  LiabilitiesCurrent  LiabilitiesNoncurrent        Equity  HolderEquity  RetainedEarnings  AdditionalPaidInCapital  TreasuryStockValue  TemporaryEquity  RedeemableEquity  LiabilitiesAndEquity  AssetsCheck_error  AssetsCheck_cat  LiabilitiesCheck_error  LiabilitiesCheck_cat  EquityCheck_error  EquityCheck_cat  AssetsLiaEquCheck_error  AssetsLiaEquCheck_cat\n", "19931  0000320193-22-000108  320193  APPLE INC  10-K  0930  2022.0  FY 2022-09-30  20221028             5  20220930     0  3.527550e+11   1.354050e+11  2.364600e+10      2.173500e+11  3.020830e+11        1.539820e+11           1.481010e+11  5.067200e+10  5.067200e+10     -3.068000e+09                      0.0                 0.0              0.0               0.0          3.527550e+11                0.0              0.0                     0.0                   0.0                0.0              0.0                      0.0                    0.0"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["apple_10k_2022 = \"0000320193-22-000108\"\n", "result_bag.result_df[result_bag.result_df.adsh==apple_10k_2022]"]}, {"cell_type": "markdown", "id": "71feee40-48f1-41a8-9b37-a1f92f970e41", "metadata": {}, "source": ["Next, we filter the applied rules on this entry."]}, {"cell_type": "code", "execution_count": 13, "id": "0fad00e0-864f-459c-90b5-814394e6c98f", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["['MAIN_1_BS_#1_BR_#2_Cash<-CashAndCashEquivalentsAtCarryingValue',\n", " 'MAIN_1_BS_#1_BR_#3_LiabilitiesAndEquity<-LiabilitiesAndStockholdersEquity',\n", " 'MAIN_1_BS_#1_BR_#4_RetainedEarnings<-RetainedEarningsAccumulatedDeficit',\n", " 'MAIN_1_BS_#2_EQ_#3_HolderEquity<-StockholdersEquity',\n", " 'MAIN_1_BS_#2_EQ_#6_Equity',\n", " 'MAIN_1_BS_#4_SU_#3_LongTermDebt']"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["apple_10k_2022 = \"0000320193-22-000108\"\n", "apple_10k_2022_applied_rules_log_df = result_bag.applied_rules_log_df[result_bag.applied_rules_log_df.adsh==apple_10k_2022]\n", "\n", "# just filter for the applied MAIN rules\n", "main_rule_cols = apple_10k_2022_applied_rules_log_df.columns[apple_10k_2022_applied_rules_log_df.columns.str.contains('MAIN')]\n", "main_rule_df = apple_10k_2022_applied_rules_log_df[main_rule_cols]\n", "\n", "# get the applied rules, by using the True and False values of main_rule_df.iloc[0] as a mask on the columns index\n", "main_rule_df.columns[main_rule_df.iloc[0]].tolist()"]}, {"cell_type": "markdown", "id": "9cae0630-5f48-4288-965b-e908dbab9f8c", "metadata": {}, "source": ["As you can see, all six applied rules were applied in the first iteration. Let's check them one by one in detail (please refer to the `process_description_df` for details):\n", "- **MAIN_0_BS_#1_BR_#2_Cash** <br> This is just a \"renaming\" (rule class is CopyTagRule) of CashAndCashEquivalentsAtCarryingValue, so there is no \"assumption\" by applying this rule\n", "- **MAIN_0_BS_#1_BR_#3_LiabilitiesAndEquity** <br> This is also just a \"renaming\" of LiabilitiesAndStockholdersEquity, so not problematic\n", "- **MAIN_0_BS_#1_BR_#4_RetainedEarnings** <br> Again, just a \"renamin\" of RetainedEarningsAccumulatedDeficit\n", "- **MAIN_0_BS_#2_EQ_#3_HolderEquity** <br> Also here, just a \"renaming\" of StockholdersEquity\n", "- **MAIN_0_BS_#2_EQ_#6_Equity** <br> This rules sums up HolderEquity, TemporaryEquity, and RedeemableEquity to  Equity. In the case of apple's 10K of 2022, there is just HolderEquity. So this is not probplematic\n", "- **MAIN_0_BS_#4_SU_#3_LongTermDebt** <br> This rules sums up the availalbe values in the columns ['LongTermDebtNoncurrent', 'LongTermDebtAndCapitalLeaseObligations'] into the column 'LongTermDebt'. LongTermDebt is only used to calculate LiabilitiesNoncurrent in the rule MAIN_BS_#4_SU_#4_LiabilitiesNoncurrent and is also not present in the final `result_df`. Since the rule MAIN_BS_#4_SU_#4_LiabilitiesNoncurrent hasn't been applied, MAIN_0_BS_#4_SU_#3_LongTermDebt doesn't have any effect.\n", "\n", "As we can see, the applied rules mainly are renaming rules. And since also all validation columns have a 0.0, which indicates an exact match of the expected values, we can \"trust\" the values for this statement from apple."]}, {"cell_type": "markdown", "id": "e6ed031b-58a3-4da3-a47c-800bd9c8b7df", "metadata": {}, "source": ["### applied_rules_sum_s"]}, {"cell_type": "markdown", "id": "77668605-e355-434e-9a74-eeff3736e171", "metadata": {}, "source": ["This log gives an overview about how often a rule was applied, giving an indication about how important a rule is. For instance, if you write your own rule, it might be useful to check how often it gets applied."]}, {"cell_type": "code", "execution_count": 14, "id": "1c9cb0a3-2a3a-4c11-94a3-6e3c0e6200bb", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["0\n", "NaN                                                                                                            0\n", "PREPIVOT_BS_PREPIV_#1_DeDup                                                                                  221\n", "PRE_BS_PRE_#1_Assets/AssetsNoncurrent                                                                          0\n", "PRE_BS_PRE_#2_Assets/AssetsCurrent                                                                             0\n", "MAIN_1_BS_#1_BR_#1_Assets<-AssetsNet                                                                           2\n", "MAIN_1_BS_#1_BR_#2_Cash<-CashAndCashEquivalentsAtCarryingValue                                             20157\n", "MAIN_1_BS_#1_BR_#3_LiabilitiesAndEquity<-LiabilitiesAndStockholdersEquity                                  25863\n", "MAIN_1_BS_#1_BR_#4_RetainedEarnings<-RetainedEarningsAccumulatedDeficit                                    24791\n", "MAIN_1_BS_#2_EQ_#1_HolderEquity<-StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest     6636\n", "MAIN_1_BS_#2_EQ_#2_HolderEquity<-PartnersCapital                                                             353\n", "MAIN_1_BS_#2_EQ_#3_HolderEquity<-StockholdersEquity                                                        18731\n", "MAIN_1_BS_#2_EQ_#4_TemporaryEquity                                                                          2627\n", "MAIN_1_BS_#2_EQ_#5_RedeemableEquity                                                                          980\n", "MAIN_1_BS_#2_EQ_#6_Equity                                                                                  25778\n", "MAIN_1_BS_#3_SC_#1_Assets                                                                                      2\n", "MAIN_1_BS_#3_SC_#2_AssetsCurrent                                                                               3\n", "MAIN_1_BS_#3_SC_#3_AssetsNoncurrent                                                                        19617\n", "MAIN_1_BS_#3_SC_#4_Liabilities                                                                               994\n", "MAIN_1_BS_#3_SC_#5_LiabilitiesCurrent                                                                          3\n", "MAIN_1_BS_#3_SC_#6_LiabilitiesNoncurrent                                                                   15916\n", "Name: 1, dtype: int64"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["result_bag.applied_rules_sum_s[:20]"]}, {"cell_type": "markdown", "id": "4c71d942-d92a-42d0-9f29-8c144395e277", "metadata": {}, "source": ["### validation_overview_df"]}, {"cell_type": "markdown", "id": "54aec5e9-1888-4a60-946d-032f3d89fd3d", "metadata": {}, "source": ["The validation overview counts the validation categories of the validation columns in the `result_df`. Validation catagories are:\n", "- **Category 0** <br> The validation was an exact match\n", "- **Category 1** <br> The error was less than 1% of the expected value.\n", "- **Category 5** <br> The error was less than 5% of the expected value.\n", "- **Category 10** <br> The error was less than 10% of the expected value.\n", "- **Category 100** <br> The error was above 10% of the expected value.\n", "\n", "The results are shown as total count and in percent of all available rows in the dataset.\n", "\n", "If you want to use the data for ML, you might want to consider only using rows which only have categories smaller than 10 or even 5."]}, {"cell_type": "code", "execution_count": 15, "id": "3e883fcd-ca25-4053-9694-bcea177c2980", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON>_cat</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON>_cat</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON>_cat</th>\n", "      <th>AssetsLiaEquCheck_cat</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON>_cat_pct</th>\n", "      <th>Lia<PERSON><PERSON><PERSON><PERSON>_cat_pct</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON>_cat_pct</th>\n", "      <th>AssetsLiaEquCheck_cat_pct</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>26139.0</td>\n", "      <td>25326</td>\n", "      <td>24279</td>\n", "      <td>24276</td>\n", "      <td>99.86</td>\n", "      <td>96.76</td>\n", "      <td>92.76</td>\n", "      <td>92.74</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>13.0</td>\n", "      <td>103</td>\n", "      <td>377</td>\n", "      <td>381</td>\n", "      <td>0.05</td>\n", "      <td>0.39</td>\n", "      <td>1.44</td>\n", "      <td>1.46</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>NaN</td>\n", "      <td>161</td>\n", "      <td>205</td>\n", "      <td>206</td>\n", "      <td>NaN</td>\n", "      <td>0.62</td>\n", "      <td>0.78</td>\n", "      <td>0.79</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>3.0</td>\n", "      <td>161</td>\n", "      <td>121</td>\n", "      <td>122</td>\n", "      <td>0.01</td>\n", "      <td>0.62</td>\n", "      <td>0.46</td>\n", "      <td>0.47</td>\n", "    </tr>\n", "    <tr>\n", "      <th>100</th>\n", "      <td>20.0</td>\n", "      <td>424</td>\n", "      <td>1188</td>\n", "      <td>1185</td>\n", "      <td>0.08</td>\n", "      <td>1.62</td>\n", "      <td>4.54</td>\n", "      <td>4.53</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     AssetsCheck_cat  LiabilitiesCheck_cat  EquityCheck_cat  AssetsLiaEquCheck_cat  AssetsCheck_cat_pct  LiabilitiesCheck_cat_pct  EquityCheck_cat_pct  AssetsLiaEquCheck_cat_pct\n", "0            26139.0                 25326            24279                  24276                99.86                     96.76                92.76                      92.74\n", "1               13.0                   103              377                    381                 0.05                      0.39                 1.44                       1.46\n", "5                NaN                   161              205                    206                  NaN                      0.62                 0.78                       0.79\n", "10               3.0                   161              121                    122                 0.01                      0.62                 0.46                       0.47\n", "100             20.0                   424             1188                   1185                 0.08                      1.62                 4.54                       4.53"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["result_bag.validation_overview_df"]}, {"cell_type": "markdown", "id": "b8b9a51e-6625-4f1f-b92e-f6c457ff9231", "metadata": {}, "source": ["### stats_df"]}, {"cell_type": "markdown", "id": "97648a46-5fd0-49db-86be-28a31640f46d", "metadata": {}, "source": ["The goal of the standardizer is to have a dataset in which all rows have meaningful values for all columns/tags. \n", "This dataframe gives an indication about how much every step/iteration adds to this goal by counting the nan values of the tags that are shown in the final dataframe."]}, {"cell_type": "code", "execution_count": 16, "id": "db139403-1f6d-4fbb-8c80-97a288378d7b", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>pre</th>\n", "      <th>pre_rel</th>\n", "      <th>MAIN_1</th>\n", "      <th>MAIN_1_rel</th>\n", "      <th>MAIN_1_gain</th>\n", "      <th>MAIN_2</th>\n", "      <th>MAIN_2_rel</th>\n", "      <th>MAIN_2_gain</th>\n", "      <th>MAIN_3</th>\n", "      <th>MAIN_3_rel</th>\n", "      <th>MAIN_3_gain</th>\n", "      <th>POST</th>\n", "      <th>POST_rel</th>\n", "      <th>POST_gain</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Assets</th>\n", "      <td>235</td>\n", "      <td>0.008978</td>\n", "      <td>37</td>\n", "      <td>0.001414</td>\n", "      <td>0.007564</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0.001414</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AssetsCurrent</th>\n", "      <td>4857</td>\n", "      <td>0.185559</td>\n", "      <td>4854</td>\n", "      <td>0.185444</td>\n", "      <td>0.000115</td>\n", "      <td>4854</td>\n", "      <td>0.185444</td>\n", "      <td>0.000000</td>\n", "      <td>4854</td>\n", "      <td>0.185444</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0.185444</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Cash</th>\n", "      <td>22918</td>\n", "      <td>0.875568</td>\n", "      <td>1883</td>\n", "      <td>0.071939</td>\n", "      <td>0.803629</td>\n", "      <td>1883</td>\n", "      <td>0.071939</td>\n", "      <td>0.000000</td>\n", "      <td>1883</td>\n", "      <td>0.071939</td>\n", "      <td>0.0</td>\n", "      <td>1883</td>\n", "      <td>0.071939</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AssetsNoncurrent</th>\n", "      <td>24550</td>\n", "      <td>0.937918</td>\n", "      <td>4881</td>\n", "      <td>0.186476</td>\n", "      <td>0.751442</td>\n", "      <td>4854</td>\n", "      <td>0.185444</td>\n", "      <td>0.001032</td>\n", "      <td>4854</td>\n", "      <td>0.185444</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0.185444</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Liabilities</th>\n", "      <td>3341</td>\n", "      <td>0.127641</td>\n", "      <td>42</td>\n", "      <td>0.001605</td>\n", "      <td>0.126036</td>\n", "      <td>5</td>\n", "      <td>0.000191</td>\n", "      <td>0.001414</td>\n", "      <td>5</td>\n", "      <td>0.000191</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000191</td>\n", "    </tr>\n", "    <tr>\n", "      <th>LiabilitiesCurrent</th>\n", "      <td>4875</td>\n", "      <td>0.186246</td>\n", "      <td>4872</td>\n", "      <td>0.186132</td>\n", "      <td>0.000115</td>\n", "      <td>3504</td>\n", "      <td>0.133868</td>\n", "      <td>0.052264</td>\n", "      <td>3504</td>\n", "      <td>0.133868</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0.133868</td>\n", "    </tr>\n", "    <tr>\n", "      <th>LiabilitiesNoncurrent</th>\n", "      <td>23081</td>\n", "      <td>0.881796</td>\n", "      <td>4139</td>\n", "      <td>0.158128</td>\n", "      <td>0.723668</td>\n", "      <td>3504</td>\n", "      <td>0.133868</td>\n", "      <td>0.024260</td>\n", "      <td>3504</td>\n", "      <td>0.133868</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0.133868</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Equity</th>\n", "      <td>26175</td>\n", "      <td>1.000000</td>\n", "      <td>44</td>\n", "      <td>0.001681</td>\n", "      <td>0.998319</td>\n", "      <td>5</td>\n", "      <td>0.000191</td>\n", "      <td>0.001490</td>\n", "      <td>5</td>\n", "      <td>0.000191</td>\n", "      <td>0.0</td>\n", "      <td>5</td>\n", "      <td>0.000191</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>HolderEquity</th>\n", "      <td>26175</td>\n", "      <td>1.000000</td>\n", "      <td>455</td>\n", "      <td>0.017383</td>\n", "      <td>0.982617</td>\n", "      <td>455</td>\n", "      <td>0.017383</td>\n", "      <td>0.000000</td>\n", "      <td>455</td>\n", "      <td>0.017383</td>\n", "      <td>0.0</td>\n", "      <td>455</td>\n", "      <td>0.017383</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RetainedEarnings</th>\n", "      <td>26175</td>\n", "      <td>1.000000</td>\n", "      <td>1281</td>\n", "      <td>0.048940</td>\n", "      <td>0.951060</td>\n", "      <td>1281</td>\n", "      <td>0.048940</td>\n", "      <td>0.000000</td>\n", "      <td>1281</td>\n", "      <td>0.048940</td>\n", "      <td>0.0</td>\n", "      <td>1281</td>\n", "      <td>0.048940</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>AdditionalPaidInCapital</th>\n", "      <td>12089</td>\n", "      <td>0.461853</td>\n", "      <td>12089</td>\n", "      <td>0.461853</td>\n", "      <td>0.000000</td>\n", "      <td>12089</td>\n", "      <td>0.461853</td>\n", "      <td>0.000000</td>\n", "      <td>12089</td>\n", "      <td>0.461853</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0.461853</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TreasuryStockValue</th>\n", "      <td>20572</td>\n", "      <td>0.785941</td>\n", "      <td>20572</td>\n", "      <td>0.785941</td>\n", "      <td>0.000000</td>\n", "      <td>20572</td>\n", "      <td>0.785941</td>\n", "      <td>0.000000</td>\n", "      <td>20572</td>\n", "      <td>0.785941</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0.785941</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TemporaryEquity</th>\n", "      <td>26175</td>\n", "      <td>1.000000</td>\n", "      <td>23548</td>\n", "      <td>0.899637</td>\n", "      <td>0.100363</td>\n", "      <td>23548</td>\n", "      <td>0.899637</td>\n", "      <td>0.000000</td>\n", "      <td>23548</td>\n", "      <td>0.899637</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0.899637</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RedeemableEquity</th>\n", "      <td>26175</td>\n", "      <td>1.000000</td>\n", "      <td>25195</td>\n", "      <td>0.962560</td>\n", "      <td>0.037440</td>\n", "      <td>25195</td>\n", "      <td>0.962560</td>\n", "      <td>0.000000</td>\n", "      <td>25195</td>\n", "      <td>0.962560</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "      <td>0.962560</td>\n", "    </tr>\n", "    <tr>\n", "      <th>LiabilitiesAndEquity</th>\n", "      <td>26175</td>\n", "      <td>1.000000</td>\n", "      <td>41</td>\n", "      <td>0.001566</td>\n", "      <td>0.998434</td>\n", "      <td>5</td>\n", "      <td>0.000191</td>\n", "      <td>0.001375</td>\n", "      <td>5</td>\n", "      <td>0.000191</td>\n", "      <td>0.0</td>\n", "      <td>5</td>\n", "      <td>0.000191</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           pre   pre_rel  MAIN_1  MAIN_1_rel  MAIN_1_gain  MAIN_2  MAIN_2_rel  MAIN_2_gain  MAIN_3  MAIN_3_rel  MAIN_3_gain  POST  POST_rel  POST_gain\n", "Assets                     235  0.008978      37    0.001414     0.007564       0    0.000000     0.001414       0    0.000000          0.0     0  0.000000   0.000000\n", "AssetsCurrent             4857  0.185559    4854    0.185444     0.000115    4854    0.185444     0.000000    4854    0.185444          0.0     0  0.000000   0.185444\n", "Cash                     22918  0.875568    1883    0.071939     0.803629    1883    0.071939     0.000000    1883    0.071939          0.0  1883  0.071939   0.000000\n", "AssetsNoncurrent         24550  0.937918    4881    0.186476     0.751442    4854    0.185444     0.001032    4854    0.185444          0.0     0  0.000000   0.185444\n", "Liabilities               3341  0.127641      42    0.001605     0.126036       5    0.000191     0.001414       5    0.000191          0.0     0  0.000000   0.000191\n", "LiabilitiesCurrent        4875  0.186246    4872    0.186132     0.000115    3504    0.133868     0.052264    3504    0.133868          0.0     0  0.000000   0.133868\n", "LiabilitiesNoncurrent    23081  0.881796    4139    0.158128     0.723668    3504    0.133868     0.024260    3504    0.133868          0.0     0  0.000000   0.133868\n", "Equity                   26175  1.000000      44    0.001681     0.998319       5    0.000191     0.001490       5    0.000191          0.0     5  0.000191   0.000000\n", "HolderEquity             26175  1.000000     455    0.017383     0.982617     455    0.017383     0.000000     455    0.017383          0.0   455  0.017383   0.000000\n", "RetainedEarnings         26175  1.000000    1281    0.048940     0.951060    1281    0.048940     0.000000    1281    0.048940          0.0  1281  0.048940   0.000000\n", "AdditionalPaidInCapital  12089  0.461853   12089    0.461853     0.000000   12089    0.461853     0.000000   12089    0.461853          0.0     0  0.000000   0.461853\n", "TreasuryStockValue       20572  0.785941   20572    0.785941     0.000000   20572    0.785941     0.000000   20572    0.785941          0.0     0  0.000000   0.785941\n", "TemporaryEquity          26175  1.000000   23548    0.899637     0.100363   23548    0.899637     0.000000   23548    0.899637          0.0     0  0.000000   0.899637\n", "RedeemableEquity         26175  1.000000   25195    0.962560     0.037440   25195    0.962560     0.000000   25195    0.962560          0.0     0  0.000000   0.962560\n", "LiabilitiesAndEquity     26175  1.000000      41    0.001566     0.998434       5    0.000191     0.001375       5    0.000191          0.0     5  0.000191   0.000000"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["result_bag.stats_df"]}, {"cell_type": "markdown", "id": "d81021a9-ea32-4b2c-94c0-42508caa15f7", "metadata": {}, "source": ["For instance, let's have a look at the Assets tag. After the preprocessing step, we count 271 nan values in over 26'000 rows. Which is about 1 percent. \n", "\n", "After applying the main rule set in the first iteration (MAIN_1) only 73 entries have a none value. This is about 0.3 percent of the total of 26'000 rows. So the gain, resp. the improvement was about 0.7 percent. And then again, after the second iteration (MAIN_2) only 26 entries in the Assets column had a nan value, which lead to another gain of 0.18 percent. After applying the main rules a third time, not additional gain was reached.\n", "\n", "In fact, if you look at the MAIN_3_gain column, you see that no improvement was possible for any tag after applying the main rules a third time. So for this dataset, applying the main rules only twice would had been enough.\n", "\n", "The log gives an overview about how \"complete\" the dataset is. It is helpful when developing the ruleset since it shows where the most missing values are."]}, {"cell_type": "code", "execution_count": null, "id": "eb74301b-e304-49c6-b4f0-df83e8b79c9b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "49732c83-321d-4988-8e86-fd42f60734f8", "metadata": {}, "source": ["# Daily Financial Report Updates"]}, {"cell_type": "markdown", "id": "5ff78ce9-364e-4429-9547-830e9eb2124e", "metadata": {"tags": []}, "source": ["## TLDR\n", "\n", "- Within its Financial Statement Data Sets, the SEC provides preprocessed financial report data only on a quarterly basis.\n", "- Introduced with version 2.4.0 secfsdstools now also provides daily updates for filed reports at the SEC.\n", "- This is an opt-in feature, so you need to activate it in the configuration.\n", "- This is an experimental feature yet.\n", "- There are some limitations."]}, {"cell_type": "markdown", "id": "5ebf36dd-ce63-425d-afde-dcf1c0ecf5e9", "metadata": {"tags": []}, "source": ["<span style=\"color: #FF8C00;\">==========================================================</span>\n", "\n", "**If you find this tool useful, a sponsorship would be greatly appreciated!**\n", "\n", "**https://github.com/sponsors/HansjoergW**\n", "\n", "How to get in touch\n", "\n", "* Found a bug: https://github.com/HansjoergW/sec-fincancial-statement-data-set/issues\n", "* Have a remark: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/general\n", "* Have an idea: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/ideas\n", "* Have a question: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/q-a\n", "* Have something to show: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/show-and-tell\n", "\n", "<span style=\"color: #FF8C00;\">==========================================================</span>"]}, {"cell_type": "markdown", "id": "4f930725-3bd8-4d81-8725-a4afecbe2724", "metadata": {}, "source": ["## Context\n", "\n", "In its Financial Statement Data Sets, the SEC provides preprocessed data for all filed reports. However, one major drawback is that this data is only provided quarterly.\n", "\n", "To overome this, version 2.4.0 introduced daily updates by downloading the raw information of new filed reports and replicating the preprocessing that the SEC does in its Financial Statement Data Sets. https://github.com/HansjoergW/sec-financial-statement-data-set-daily-processing is used for that.\n", "\n", "Please note that this is an experimental feature yet and there are some limitations."]}, {"cell_type": "markdown", "id": "e2f284a6-54cd-4868-92e4-bb5464dd633e", "metadata": {}, "source": ["## How it works\n", "\n", "In the update process, which either can be triggered manually or runs automatically if not turned off in the configuration, the following main steps are executed:\n", "\n", "1. It is checked if a new quarterly zip file is available at the SEC.gov\n", "1. If a new quarterly zip file is available\n", "   1. It is downloaded, transformed to parquet and added to the index table\n", "   1. **If there were formerly downloaded and preprocessed daily raw data of filed reports that are now covered by the downloaded quarterly zip file**\n", "      1. **The information for them are cleaned, so that only the official quarterly data remains**\n", "1. **It is checked if filed reports are available that are not covered yet by the quarterly zip files**\n", "   1. **If reports not yet covered by quarterly zip files are available, the raw data of these reports is downloaded, preprocessed in the same way as the SEC does, transformed to parquet and added to the index table.**\n", "1. Any configured automation pipelines are running\n"]}, {"cell_type": "markdown", "id": "58d7e6ed-a923-4ab2-b50f-3e42231101ad", "metadata": {}, "source": ["## How to activate it\n", "Since this is an experimeantal feature yet, you need to activate it in the configuration file:\n", "\n", "<pre>\n", "[DEFAULT]\n", "...\n", "dailyprocessing = True\n", "</pre>\n"]}, {"cell_type": "markdown", "id": "42bd85e7-f58d-49c8-b0e4-000cae73f227", "metadata": {"tags": []}, "source": ["## What you can do with the daily data\n", "\n", "Besides not being integrated in the automation examples, there is now difference in how you can use the data from daily downloads. \n", "\n"]}, {"cell_type": "markdown", "id": "b0cbf680-06ec-4719-b375-14ce6d48916e", "metadata": {}, "source": ["## Limitations\n", "\n", "- Using the daily data is not yet integrated in the automation examples (see notebooks 08_01_automation_a_first_example_1.8.0 or 08_02_automation_a_memory_optimized_example_2.2.0)\n", "- Only about 70% of the reports are available, since since \"in-html\" xbrl-tag reports are not yet supported\n", "- Segment information is not yet extracted for the daily data\n", "- Preprocessing is not 100% identical with the preprocessing that produces the quarterly zip files\n", "- The sub.txt of the daily data contain only a minimum number of columns\n"]}, {"cell_type": "code", "execution_count": null, "id": "e2690302-da56-49f0-b24a-68409e37919a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}
{"version": "0.2.0", "configurations": [{"name": "Debug Current File", "type": "debugpy", "request": "launch", "program": "${file}", "justMyCode": false, "console": "integratedTerminal"}, {"name": "Run Tests with pytest", "type": "debugpy", "request": "launch", "module": "pytest", "args": ["tests/", "-v"], "justMyCode": false, "console": "integratedTerminal"}, {"name": "Run Example <PERSON>", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/sandbox/presenter.py", "justMyCode": false, "console": "integratedTerminal"}]}
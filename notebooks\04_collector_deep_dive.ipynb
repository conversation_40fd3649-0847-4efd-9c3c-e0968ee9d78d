{"cells": [{"cell_type": "code", "execution_count": 1, "id": "f2dc2fc6-298a-41cb-bc73-cdbd96d43184", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "# ensure that all columns are shown and that colum content is not cut\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.max_colwidth', None)\n", "pd.set_option('display.width',1000)\n", "pd.set_option('display.max_rows', 500) # ensure that all rows are shown"]}, {"cell_type": "markdown", "id": "9ef5f81e-da5a-41f6-8b39-4da2ed1457c0", "metadata": {"tags": []}, "source": ["# Collector Deep Dive\n", "This notebooks dives a little deeper into using the collector classes."]}, {"cell_type": "markdown", "id": "50391e15-eb4a-48d8-b0df-5f48fd9c8df9", "metadata": {"tags": []}, "source": ["<span style=\"color: #FF8C00;\">==========================================================</span>\n", "\n", "**If you find this tool useful, a sponsorship would be greatly appreciated!**\n", "\n", "**https://github.com/sponsors/HansjoergW**\n", "\n", "How to get in touch\n", "\n", "* Found a bug: https://github.com/HansjoergW/sec-fincancial-statement-data-set/issues\n", "* Have a remark: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/general\n", "* Have an idea: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/ideas\n", "* Have a question: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/q-a\n", "* Have something to show: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/show-and-tell\n", "\n", "<span style=\"color: #FF8C00;\">==========================================================</span>"]}, {"cell_type": "markdown", "id": "4996a26b-debc-443b-beb1-84157c1d32f7", "metadata": {}, "source": ["## Basics"]}, {"cell_type": "markdown", "id": "1e12ad97-c7ed-4cb5-babf-7c95e20c9806", "metadata": {}, "source": ["All the `Collector` classes have their own factory method(s) which instantiates the class. Most of these factory methods\n", "also provide parameters to filter the data directly when being loaded from the parquet files.\n", "These are\n", "* the `forms_filter` <br> lets you select which report type should be loaded (e.g. \"10-K\" or \"10-Q\").<br>\n", "  Note: the fomrs filter affects all dataframes (sub, pre, num).\n", "* the `stmt_filter` <br> defines the statements that should be loaded (e.g., \"BS\" if only \"Balance Sheet\" data should be loaded) <br>\n", "  Note: the stmt filter only affects the pre dataframe.\n", "* the `tag_filter` <br> defines the tags, that should be loaded (e.g., \"Assets\" if only the \"Assets\" tag should be loaded) <br>\n", "  Note: the tag filter affects the pre and num dataframes."]}, {"cell_type": "markdown", "id": "26c22203-3389-4056-919c-ab40d12279a9", "metadata": {}, "source": ["It is also possible to apply filter for these attributes after the data is loaded, but since the `Collector` classes\n", "apply this filters directly during the load process from the parquet files (which means that fewer data is loaded from\n", "the disk and also the memory footprint is reduced) this is generally more efficient."]}, {"cell_type": "markdown", "id": "72bd6f76-815d-4dbd-8b55-ab9b7fe91436", "metadata": {}, "source": ["All `Collector` classes have a `collect` method which then loads the data from the parquet files and returns an instance\n", "of `RawDataBag`. The `RawDataBag` instance contains then a pandas dataframe for the `sub` (subscription) data,\n", "`pre` (presentation) data, and `num` (the numeric values) data."]}, {"cell_type": "markdown", "id": "f1e5cb98-da07-4d8d-8d8d-f2197b09a5be", "metadata": {"tags": []}, "source": ["## `SingleReportCollector`\n", "As the name suggests, this `Collector` returns the data of a single report. It is instantiated by providing the `adsh` of the desired report as parameter of the `get_report_by_adsh` factory method, \n", "or by using an instance of the `IndexReport` as parameter of the `get_report_by_indexreport`. (As a reminder: instances of `IndexReport` are returned by the `CompanyIndexReader` class)."]}, {"cell_type": "markdown", "id": "7bfc58ae-7a2d-49e6-8d62-3231e18e16d7", "metadata": {}, "source": ["Reading a single report: **Apples 10-K from 2022**"]}, {"cell_type": "code", "execution_count": 2, "id": "3aa3c47a-ea2d-4d0f-854f-6c3cf64563ba", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-01 07:34:13,223 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-01 07:34:13,278 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n"]}, {"name": "stdout", "output_type": "stream", "text": ["sub (1, 36)\n", "pre (100, 10)\n", "num (437, 10)\n", "pre_num (509, 17)\n"]}], "source": ["from secfsdstools.e_collector.reportcollecting import SingleReportCollector\n", "\n", "apple_10k_2022_adsh = \"0000320193-22-000108\"\n", "\n", "collector: SingleReportCollector = SingleReportCollector.get_report_by_adsh(adsh=apple_10k_2022_adsh)\n", "rawdatabag = collector.collect()\n", "\n", "# as expected, there is just one entry in the submission dataframe\n", "print(\"sub\", rawdatabag.sub_df.shape)\n", "\n", "# just print the size of the pre and num dataframes\n", "print(\"pre\", rawdatabag.pre_df.shape)\n", "print(\"num\", rawdatabag.num_df.shape)\n", "\n", "# joining the pre and num dataframes\n", "joineddatabag = rawdatabag.join()\n", "print(\"pre_num\", joineddatabag.pre_num_df.shape)"]}, {"cell_type": "markdown", "id": "ed9d14f6-b68f-42c9-979c-e205606e0ecd", "metadata": {}, "source": ["As mentioned above, we can also directly apply filters, to reduce the amount of data that is loaded. \n", "\n", "First, let's only load data for the **Balance Sheet** by using the `stmt_filter`."]}, {"cell_type": "code", "execution_count": 3, "id": "0cbf6d58-466b-41c0-ad7f-2ef26a60ee13", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-01 07:34:57,913 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n"]}, {"name": "stdout", "output_type": "stream", "text": ["sub (1, 36)\n", "pre (32, 10)\n", "num (437, 10)\n", "pre_num (171, 17)\n"]}], "source": ["from secfsdstools.e_collector.reportcollecting import SingleReportCollector\n", "\n", "collector: SingleReportCollector = SingleReportCollector.get_report_by_adsh(adsh=apple_10k_2022_adsh, stmt_filter=['BS'])\n", "rawdatabag = collector.collect()\n", "\n", "# as expected, there is just one entry in the submission dataframe\n", "print(\"sub\", rawdatabag.sub_df.shape)\n", "\n", "# just print the size of the pre and num dataframes\n", "print(\"pre\", rawdatabag.pre_df.shape)\n", "print(\"num\", rawdatabag.num_df.shape)\n", "\n", "# joining the pre and num dataframes\n", "joineddatabag = rawdatabag.join()\n", "print(\"pre_num\", joineddatabag.pre_num_df.shape)"]}, {"cell_type": "markdown", "id": "d55a40e8-7879-4e79-a64b-b21b06c89f3e", "metadata": {}, "source": ["As mentioned above, the stmt_filter only applies to the pre_df, since only the pre_df has information about the statement which a tag belongs to. But of course, also the joined dataframe is significantely smaller."]}, {"cell_type": "markdown", "id": "f0fd09f7-cc52-4887-bcd9-a835ab06c030", "metadata": {}, "source": ["Next, lets even be a bit more restrictive and just load the **'Assets'** tag by using the `tag_filter`."]}, {"cell_type": "code", "execution_count": 4, "id": "adedbab0-fdaa-47f6-b48e-a18fd3a8a039", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-01 07:35:23,207 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n"]}, {"name": "stdout", "output_type": "stream", "text": ["sub (1, 36)\n", "pre (1, 10)\n", "num (2, 10)\n", "pre_num (2, 17)\n"]}], "source": ["from secfsdstools.e_collector.reportcollecting import SingleReportCollector\n", "\n", "collector: SingleReportCollector = SingleReportCollector.get_report_by_adsh(adsh=apple_10k_2022_adsh, tag_filter=['Assets'])\n", "rawdatabag = collector.collect()\n", "\n", "# as expected, there is just one entry in the submission dataframe\n", "print(\"sub\", rawdatabag.sub_df.shape)\n", "\n", "# just print the size of the pre and num dataframes\n", "print(\"pre\", rawdatabag.pre_df.shape)\n", "print(\"num\", rawdatabag.num_df.shape)\n", "\n", "# joining the pre and num dataframes\n", "joineddatabag = rawdatabag.join()\n", "print(\"pre_num\", joineddatabag.pre_num_df.shape)"]}, {"cell_type": "markdown", "id": "7d264862-1165-4bd7-b8db-e2642a68759e", "metadata": {}, "source": ["Now, as expected, there should only be one Asset tag in the pre dataframe. The reason with have two entries in the num dataframe is, that there is a value for the year 2022 and a value for the previous year 2021, as can be seen in ddate column:"]}, {"cell_type": "code", "execution_count": 5, "id": "4b4f5240-e0bd-4460-9696-f57c583afe26", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>adsh</th>\n", "      <th>tag</th>\n", "      <th>version</th>\n", "      <th>ddate</th>\n", "      <th>qtrs</th>\n", "      <th>uom</th>\n", "      <th>segments</th>\n", "      <th>coreg</th>\n", "      <th>value</th>\n", "      <th>footnote</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0000320193-22-000108</td>\n", "      <td>Assets</td>\n", "      <td>us-gaap/2022</td>\n", "      <td>20220930</td>\n", "      <td>0</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>3.527550e+11</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0000320193-22-000108</td>\n", "      <td>Assets</td>\n", "      <td>us-gaap/2022</td>\n", "      <td>20210930</td>\n", "      <td>0</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>3.510020e+11</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   adsh     tag       version     ddate  qtrs  uom segments coreg         value footnote\n", "0  0000320193-22-000108  Assets  us-gaap/2022  20220930     0  USD                 3.527550e+11     None\n", "1  0000320193-22-000108  Assets  us-gaap/2022  20210930     0  USD                 3.510020e+11     None"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["rawdatabag.num_df"]}, {"cell_type": "markdown", "id": "9b3caa02-7092-4ded-99d1-dc8002815b49", "metadata": {"tags": []}, "source": ["# `MultiReportCollector`\n", "Contrary to the `SingleReportCollector`, this `Collector` can collect data from several\n", "reports. Moreover, the data of the reports are loaded in parallel, this  especially improves the performance if the\n", "reports are from different quarters (resp. are in different zip files). The class provides the factory methods \n", "`get_reports_by_adshs` and `get_reports_by_indexreports`. The first takes a list of adsh strings, the second a list\n", "of `IndexReport` instances."]}, {"cell_type": "markdown", "id": "1ddcb96f-a892-41f8-bead-5fa6dc0ff11c", "metadata": {}, "source": ["Reading two reports: **Apple's 10-K from 2022 and 2012**"]}, {"cell_type": "code", "execution_count": 6, "id": "5f38d7d1-625d-4fd9-8559-c0b9dc7b8086", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-01 07:35:46,930 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-01 07:35:46,934 [INFO] parallelexecution      items to process: 2\n", "2025-02-01 07:35:49,200 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["sub (2, 36)\n", "pre (191, 10)\n", "num (955, 10)\n", "pre_num (1169, 17)\n"]}], "source": ["from secfsdstools.e_collector.multireportcollecting import MultiReportCollector\n", "apple_10k_2022_adsh = \"0000320193-22-000108\"\n", "apple_10k_2012_adsh = \"0001193125-12-444068\"\n", "\n", "# load only the assets tags that are present in the 10-K report of apple in the years\n", "# 2022 and 2012\n", "collector: MultiReportCollector = \\\n", "    MultiReportCollector.get_reports_by_adshs(adshs=[apple_10k_2022_adsh,\n", "                                                     apple_10k_2012_adsh])\n", "rawdatabag = collector.collect()\n", "# as expected, there are just two entries in the submission dataframe\n", "print(\"sub\", rawdatabag.sub_df.shape)\n", "\n", "# just print the size of the pre and num dataframes\n", "print(\"pre\", rawdatabag.pre_df.shape)\n", "print(\"num\", rawdatabag.num_df.shape)\n", "\n", "# joining the pre and num dataframes\n", "joineddatabag = rawdatabag.join()\n", "print(\"pre_num\", joineddatabag.pre_num_df.shape)"]}, {"cell_type": "markdown", "id": "11d38e47-a9e7-4725-bdfb-cf8e7be979f8", "metadata": {}, "source": ["Again, using the filter parameters reduces the amount of data that is loaded. Let us load the tags **'Assets' and 'Liabilities'.**"]}, {"cell_type": "code", "execution_count": 7, "id": "f81e2149-2c5d-4221-8df3-90a40a23daae", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-01 07:35:57,479 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-01 07:35:57,483 [INFO] parallelexecution      items to process: 2\n", "2025-02-01 07:35:59,620 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["sub (2, 36)\n", "pre (4, 10)\n", "num (22, 10)\n", "pre_num (22, 17)\n"]}], "source": ["from secfsdstools.e_collector.multireportcollecting import MultiReportCollector\n", "apple_10k_2022_adsh = \"0000320193-22-000108\"\n", "apple_10k_2012_adsh = \"0001193125-12-444068\"\n", "\n", "# load only the assets tags that are present in the 10-K report of apple in the years\n", "# 2022 and 2012\n", "collector: MultiReportCollector = \\\n", "    MultiReportCollector.get_reports_by_adshs(adshs=[apple_10k_2022_adsh, apple_10k_2012_adsh],\n", "                                              tag_filter=['Assets', 'Liabilities'])\n", "rawdatabag = collector.collect()\n", "# as expected, there are just two entries in the submission dataframe\n", "print(\"sub\", rawdatabag.sub_df.shape)\n", "\n", "# just print the size of the pre and num dataframes\n", "print(\"pre\", rawdatabag.pre_df.shape)\n", "print(\"num\", rawdatabag.num_df.shape)\n", "\n", "# joining the pre and num dataframes\n", "joineddatabag = rawdatabag.join()\n", "print(\"pre_num\", joineddatabag.pre_num_df.shape)"]}, {"cell_type": "code", "execution_count": 8, "id": "9fe5e3c0-0fb5-4283-974e-6775017fa402", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>adsh</th>\n", "      <th>tag</th>\n", "      <th>version</th>\n", "      <th>ddate</th>\n", "      <th>qtrs</th>\n", "      <th>uom</th>\n", "      <th>segments</th>\n", "      <th>coreg</th>\n", "      <th>value</th>\n", "      <th>footnote</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0000320193-22-000108</td>\n", "      <td>Assets</td>\n", "      <td>us-gaap/2022</td>\n", "      <td>20220930</td>\n", "      <td>0</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>3.527550e+11</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0000320193-22-000108</td>\n", "      <td>Liabilities</td>\n", "      <td>us-gaap/2022</td>\n", "      <td>20220930</td>\n", "      <td>0</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>3.020830e+11</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0000320193-22-000108</td>\n", "      <td>Assets</td>\n", "      <td>us-gaap/2022</td>\n", "      <td>20210930</td>\n", "      <td>0</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>3.510020e+11</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0000320193-22-000108</td>\n", "      <td>Liabilities</td>\n", "      <td>us-gaap/2022</td>\n", "      <td>20210930</td>\n", "      <td>0</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2.879120e+11</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0001193125-12-444068</td>\n", "      <td>Assets</td>\n", "      <td>us-gaap/2012</td>\n", "      <td>20110930</td>\n", "      <td>0</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=AsiaAndPacific;</td>\n", "      <td></td>\n", "      <td>1.710000e+09</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>0001193125-12-444068</td>\n", "      <td>Assets</td>\n", "      <td>us-gaap/2012</td>\n", "      <td>20120930</td>\n", "      <td>0</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>1.760640e+11</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>0001193125-12-444068</td>\n", "      <td>Assets</td>\n", "      <td>us-gaap/2012</td>\n", "      <td>20110930</td>\n", "      <td>0</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=Retail;</td>\n", "      <td></td>\n", "      <td>2.151000e+09</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>0001193125-12-444068</td>\n", "      <td>Assets</td>\n", "      <td>us-gaap/2012</td>\n", "      <td>20110930</td>\n", "      <td>0</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=Europe;</td>\n", "      <td></td>\n", "      <td>1.520000e+09</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>0001193125-12-444068</td>\n", "      <td>Assets</td>\n", "      <td>us-gaap/2012</td>\n", "      <td>20120930</td>\n", "      <td>0</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=UnallocatedAmountToSegment;</td>\n", "      <td></td>\n", "      <td>1.607870e+11</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>0001193125-12-444068</td>\n", "      <td>Assets</td>\n", "      <td>us-gaap/2012</td>\n", "      <td>20110930</td>\n", "      <td>0</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>1.163710e+11</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>0001193125-12-444068</td>\n", "      <td>Liabilities</td>\n", "      <td>us-gaap/2012</td>\n", "      <td>20120930</td>\n", "      <td>0</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>5.785400e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>0001193125-12-444068</td>\n", "      <td>Assets</td>\n", "      <td>us-gaap/2012</td>\n", "      <td>20110930</td>\n", "      <td>0</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=Americas;</td>\n", "      <td></td>\n", "      <td>2.782000e+09</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>0001193125-12-444068</td>\n", "      <td>Assets</td>\n", "      <td>us-gaap/2012</td>\n", "      <td>20120930</td>\n", "      <td>0</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=Europe;</td>\n", "      <td></td>\n", "      <td>3.095000e+09</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>0001193125-12-444068</td>\n", "      <td>Assets</td>\n", "      <td>us-gaap/2012</td>\n", "      <td>20120930</td>\n", "      <td>0</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=Americas;</td>\n", "      <td></td>\n", "      <td>5.525000e+09</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>0001193125-12-444068</td>\n", "      <td>Assets</td>\n", "      <td>us-gaap/2012</td>\n", "      <td>20120930</td>\n", "      <td>0</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=AsiaAndPacific;</td>\n", "      <td></td>\n", "      <td>2.234000e+09</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>0001193125-12-444068</td>\n", "      <td>Assets</td>\n", "      <td>us-gaap/2012</td>\n", "      <td>20110930</td>\n", "      <td>0</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=UnallocatedAmountToSegment;</td>\n", "      <td></td>\n", "      <td>1.075710e+11</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>0001193125-12-444068</td>\n", "      <td>Assets</td>\n", "      <td>us-gaap/2012</td>\n", "      <td>20120930</td>\n", "      <td>0</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=Retail;</td>\n", "      <td></td>\n", "      <td>2.725000e+09</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>0001193125-12-444068</td>\n", "      <td>Assets</td>\n", "      <td>us-gaap/2012</td>\n", "      <td>20110930</td>\n", "      <td>0</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=OperatingSegments;</td>\n", "      <td></td>\n", "      <td>8.800000e+09</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>0001193125-12-444068</td>\n", "      <td>Assets</td>\n", "      <td>us-gaap/2012</td>\n", "      <td>20120930</td>\n", "      <td>0</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=JP;</td>\n", "      <td></td>\n", "      <td>1.698000e+09</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>0001193125-12-444068</td>\n", "      <td>Assets</td>\n", "      <td>us-gaap/2012</td>\n", "      <td>20120930</td>\n", "      <td>0</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=OperatingSegments;</td>\n", "      <td></td>\n", "      <td>1.527700e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>0001193125-12-444068</td>\n", "      <td>Assets</td>\n", "      <td>us-gaap/2012</td>\n", "      <td>20110930</td>\n", "      <td>0</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=JP;</td>\n", "      <td></td>\n", "      <td>6.370000e+08</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>0001193125-12-444068</td>\n", "      <td>Liabilities</td>\n", "      <td>us-gaap/2012</td>\n", "      <td>20110930</td>\n", "      <td>0</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>3.975600e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                    adsh          tag       version     ddate  qtrs  uom                                      segments coreg         value footnote\n", "0   0000320193-22-000108       Assets  us-gaap/2022  20220930     0  USD                                                      3.527550e+11     None\n", "1   0000320193-22-000108  Liabilities  us-gaap/2022  20220930     0  USD                                                      3.020830e+11     None\n", "2   0000320193-22-000108       Assets  us-gaap/2022  20210930     0  USD                                                      3.510020e+11     None\n", "3   0000320193-22-000108  Liabilities  us-gaap/2022  20210930     0  USD                                                      2.879120e+11     None\n", "4   0001193125-12-444068       Assets  us-gaap/2012  20110930     0  USD              BusinessSegments=AsiaAndPacific;        1.710000e+09     None\n", "5   0001193125-12-444068       Assets  us-gaap/2012  20120930     0  USD                                                      1.760640e+11     None\n", "6   0001193125-12-444068       Assets  us-gaap/2012  20110930     0  USD                      BusinessSegments=Retail;        2.151000e+09     None\n", "7   0001193125-12-444068       Assets  us-gaap/2012  20110930     0  USD                      BusinessSegments=Europe;        1.520000e+09     None\n", "8   0001193125-12-444068       Assets  us-gaap/2012  20120930     0  USD  BusinessSegments=UnallocatedAmountToSegment;        1.607870e+11     None\n", "9   0001193125-12-444068       Assets  us-gaap/2012  20110930     0  USD                                                      1.163710e+11     None\n", "10  0001193125-12-444068  Liabilities  us-gaap/2012  20120930     0  USD                                                      5.785400e+10     None\n", "11  0001193125-12-444068       Assets  us-gaap/2012  20110930     0  USD                    BusinessSegments=Americas;        2.782000e+09     None\n", "12  0001193125-12-444068       Assets  us-gaap/2012  20120930     0  USD                      BusinessSegments=Europe;        3.095000e+09     None\n", "13  0001193125-12-444068       Assets  us-gaap/2012  20120930     0  USD                    BusinessSegments=Americas;        5.525000e+09     None\n", "14  0001193125-12-444068       Assets  us-gaap/2012  20120930     0  USD              BusinessSegments=AsiaAndPacific;        2.234000e+09     None\n", "15  0001193125-12-444068       Assets  us-gaap/2012  20110930     0  USD  BusinessSegments=UnallocatedAmountToSegment;        1.075710e+11     None\n", "16  0001193125-12-444068       Assets  us-gaap/2012  20120930     0  USD                      BusinessSegments=Retail;        2.725000e+09     None\n", "17  0001193125-12-444068       Assets  us-gaap/2012  20110930     0  USD           BusinessSegments=OperatingSegments;        8.800000e+09     None\n", "18  0001193125-12-444068       Assets  us-gaap/2012  20120930     0  USD                          BusinessSegments=JP;        1.698000e+09     None\n", "19  0001193125-12-444068       Assets  us-gaap/2012  20120930     0  USD           BusinessSegments=OperatingSegments;        1.527700e+10     None\n", "20  0001193125-12-444068       Assets  us-gaap/2012  20110930     0  USD                          BusinessSegments=JP;        6.370000e+08     None\n", "21  0001193125-12-444068  Liabilities  us-gaap/2012  20110930     0  USD                                                      3.975600e+10     None"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["rawdatabag.num_df"]}, {"cell_type": "markdown", "id": "7ae2add6-4cac-4e8e-a4b6-e38810c53145", "metadata": {}, "source": ["As expected, the data now contains the values for 'Assets' and 'Liabilities' for the year 2012 and the previous year 2011, as well as for the year 2022 and the previoius year 2021. Note that we also have additional `segments` information."]}, {"cell_type": "markdown", "id": "f8d0c0e6-2b65-4297-bac3-9cab38317c67", "metadata": {}, "source": ["# `CompanyReportCollector`"]}, {"cell_type": "markdown", "id": "e6a53587-a35e-43cf-8957-ae9f3029243a", "metadata": {}, "source": ["This class returns reports for one or more companies. The factory method `get_company_collector` provides the parameter `ciks` which takes a list of cik numbers."]}, {"cell_type": "markdown", "id": "6afb4da1-22c1-4ff3-9867-7c83e9b4e7f0", "metadata": {"tags": []}, "source": ["Let us read the data for **all reports of Apple and Microsoft.** \n", "\n", "**Note**: To do that, the framework has to read data from all quarters, so this may take a few seconds."]}, {"cell_type": "code", "execution_count": 9, "id": "aa537bee-7d3a-444c-b7a1-4ddfd1aaa0df", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-01 07:37:13,169 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-01 07:37:13,380 [INFO] parallelexecution      items to process: 62\n", "2025-02-01 07:37:33,484 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["sub (129, 36)\n", "pre (12938, 10)\n", "num (58105, 10)\n", "pre_num (71713, 17)\n"]}], "source": ["from secfsdstools.e_collector.companycollecting import CompanyReportCollector\n", "\n", "apple_cik = 320193\n", "microsoft_cik = 789019\n", "collector = CompanyReportCollector.get_company_collector(ciks=[apple_cik, microsoft_cik])\n", "\n", "rawdatabag = collector.collect()\n", "\n", "print(\"sub\", rawdatabag.sub_df.shape)\n", "\n", "# just print the size of the pre and num dataframes\n", "print(\"pre\", rawdatabag.pre_df.shape)\n", "print(\"num\", rawdatabag.num_df.shape)\n", "\n", "# joining the pre and num dataframes\n", "joineddatabag = rawdatabag.join()\n", "print(\"pre_num\", joineddatabag.pre_num_df.shape)"]}, {"cell_type": "markdown", "id": "63569529-4578-44fd-995f-61a9ffadde91", "metadata": {}, "source": ["As you will see, this takes a couple dozens of seconds. But nonetheless, data from all zip files was loaded in parallel."]}, {"cell_type": "markdown", "id": "7cbebfa5-6498-4b90-b7ac-a4c5898a9be7", "metadata": {}, "source": ["But maybe, we just want to have a look at the **'Assets' of all 10-K reports.**"]}, {"cell_type": "code", "execution_count": 10, "id": "0a0c7909-4430-453f-82d6-7e4affa940dd", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-01 07:39:26,087 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-01 07:39:26,309 [INFO] parallelexecution      items to process: 31\n", "2025-02-01 07:39:36,260 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["sub (31, 36)\n", "pre (31, 10)\n", "num (131, 10)\n", "pre_num (131, 17)\n"]}], "source": ["from secfsdstools.e_collector.companycollecting import CompanyReportCollector\n", "\n", "collector = CompanyReportCollector.get_company_collector(ciks=[apple_cik, microsoft_cik],\n", "                                                        tag_filter=['Assets'],\n", "                                                        forms_filter=['10-K'])\n", "\n", "rawdatabag = collector.collect()\n", "\n", "print(\"sub\", rawdatabag.sub_df.shape)\n", "\n", "# just print the size of the pre and num dataframes\n", "print(\"pre\", rawdatabag.pre_df.shape)\n", "print(\"num\", rawdatabag.num_df.shape)\n", "\n", "# joining the pre and num dataframes\n", "joineddatabag = rawdatabag.join()\n", "print(\"pre_num\", joineddatabag.pre_num_df.shape)"]}, {"cell_type": "markdown", "id": "1d897d79-9b0e-49ed-90a2-e7b20512da6e", "metadata": {}, "source": ["# `ZipCollector`"]}, {"cell_type": "markdown", "id": "2a4aa488-49ec-434e-93e8-dbed9f36d3b2", "metadata": {}, "source": ["This `Collector` collects the data of one or more zip (resp. the folders that contain the parquet\n", "  files of this zip files). And since every of the original zip files contains the data for one quarter, the names you provide\n", "  in the `get_zip_by_name` or `get_zip_by_names` factory methods reflect the quarter which data you want to load: e.g. `2022q1.zip`.\n", "  \n", "There are several fatctory methods to provide the functionality. First let us load the data for the zip file **2022q1.zip**."]}, {"cell_type": "code", "execution_count": 11, "id": "639ee899-7e4a-4ff8-b191-82d0e2e8e8cb", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-01 07:39:50,210 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-01 07:39:50,215 [INFO] parallelexecution      items to process: 1\n", "2025-02-01 07:39:50,216 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2022q1.zip\n", "2025-02-01 07:39:52,483 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["sub (7237, 36)\n", "pre (832867, 10)\n", "num (3264632, 10)\n", "pre_num (4208329, 17)\n"]}], "source": ["from secfsdstools.e_collector.zipcollecting import ZipCollector\n", "\n", "collector: ZipCollector = ZipCollector.get_zip_by_name(name=\"2022q1.zip\")\n", "\n", "rawdatabag = collector.collect()\n", "\n", "print(\"sub\", rawdatabag.sub_df.shape)\n", "\n", "# just print the size of the pre and num dataframes\n", "print(\"pre\", rawdatabag.pre_df.shape)\n", "print(\"num\", rawdatabag.num_df.shape)\n", "\n", "# joining the pre and num dataframes\n", "joineddatabag = rawdatabag.join()\n", "print(\"pre_num\", joineddatabag.pre_num_df.shape)"]}, {"cell_type": "markdown", "id": "a09a9957-4b9d-48c2-8c28-3113f093347e", "metadata": {"tags": []}, "source": ["As you may notice this is quite a significant amount of data that was loaded, just for one single quarter. \n", "\n", "Next, we are going to load data for **all the quarters of 2022, but only the Balance Sheet of the 10-K reports.**"]}, {"cell_type": "code", "execution_count": 12, "id": "9cd4b816-705c-419a-8a8f-03f8c07b6108", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-01 07:40:21,535 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-01 07:40:21,539 [INFO] parallelexecution      items to process: 4\n", "2025-02-01 07:41:02,066 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["sub (6516, 36)\n", "pre (241088, 10)\n", "num (2818850, 10)\n", "pre_num (947786, 17)\n"]}], "source": ["from secfsdstools.e_collector.zipcollecting import ZipCollector\n", "\n", "collector: ZipCollector = ZipCollector.get_zip_by_names(names=[\"2022q1.zip\", \"2022q2.zip\", \"2022q3.zip\", \"2022q4.zip\"],\n", "                                                        forms_filter=[\"10-K\"],\n", "                                                        stmt_filter=[\"BS\"],)\n", "\n", "rawdatabag = collector.collect()\n", "\n", "print(\"sub\", rawdatabag.sub_df.shape)\n", "\n", "# just print the size of the pre and num dataframes\n", "print(\"pre\", rawdatabag.pre_df.shape)\n", "print(\"num\", rawdatabag.num_df.shape)\n", "\n", "# joining the pre and num dataframes\n", "joineddatabag = rawdatabag.join()\n", "print(\"pre_num\", joineddatabag.pre_num_df.shape)"]}, {"cell_type": "markdown", "id": "aff46af8-a1ae-4e24-93d1-8f338d988333", "metadata": {}, "source": ["But we can even be a little more bold and read data from **all zip files at once**. We will read **10-K and 10-Q reports, but read only the Assets tag.** This will take some time.\n", "\n", "Note: Use with caution, since this can fill up your memory if you don't provide a tag_filter."]}, {"cell_type": "code", "execution_count": 13, "id": "8e38b4a4-b66a-47ec-badb-a491ac90155d", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-01 07:41:04,032 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-01 07:41:04,046 [INFO] parallelexecution      items to process: 63\n", "2025-02-01 07:41:41,665 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["sub (350043, 36)\n", "pre (350347, 10)\n", "num (1595985, 10)\n", "pre_num (1654978, 17)\n"]}], "source": ["from secfsdstools.e_collector.zipcollecting import ZipCollector\n", "\n", "collector: ZipCollector = ZipCollector.get_all_zips(forms_filter=[\"10-K\", \"10-Q\"],\n", "                                                    tag_filter=[\"Assets\"])\n", "\n", "rawdatabag = collector.collect()\n", "\n", "print(\"sub\", rawdatabag.sub_df.shape)\n", "\n", "# just print the size of the pre and num dataframes\n", "print(\"pre\", rawdatabag.pre_df.shape)\n", "print(\"num\", rawdatabag.num_df.shape)\n", "\n", "# joining the pre and num dataframes\n", "joineddatabag = rawdatabag.join()\n", "print(\"pre_num\", joineddatabag.pre_num_df.shape)"]}, {"cell_type": "markdown", "id": "b10e6968-8654-4995-960f-b3adbd604785", "metadata": {}, "source": ["## `post_load_filter`\n", "The ZipCollector factory methods have an additional filter parameter: `post_load_filter`.\n", "\n", "Loading the from multiple zip files is done in parallel processes. The number of parallel prcocesses depends on the number of core the system has. First, a RawDataBag instance is created for every zip file that has to be loaded and once all the zip files are loaded, all these RawDataBag instances are concatenated into a single instance of a RawDataBag. After that, additional filters can be applied with the `filter` method of the RawDataBag.\n", "\n", "Especially when wanting to load data from all zip files at once, it would make sense to have a possibility to add an additional filter directly after the data of a single zip file has been loaded, since this would reduce the memory footprint significantly.\n", "\n", "This is what the `post_laod_filter` is for. It simply takes a function as parameter that receives a `RawDataBag`as parameter and returns a `RawDataBag` as result. Hence, it can also be defined as a lambda function.\n", "\n", "The following example will load the data for all available 10-K and 10-Q balance sheets. If we would do that\n", "without a `post_laod_filter` it might very likely in a out-of-memory exception before we change to reduce the data. Therfore, we add a `post_load_filter` that\n", "\n", "* filters only the datapoints of the current report (by using `ReportPeriodRawFilter`)\n", "* removes datapointss ob subsidiaries (by using `MainCoregFilter`)\n", "* create a real copy of the reduced dataframes so that dataframes containing all the data can be garbage collected (by using the `copy_bag()`) method\n", "\n", "The `post_load_filter` is just a function that receives a `RawDataBag` and has to return a `RawDataBag`. It can be either defined as a function or\n", "directly as a lambda expression.\n", "\n", "```\n", "    # as function\n", "    def postloadfilter(databag: RawDataBag) -> RawDataBag:\n", "        return databag[ReportPeriodRawFilter()][MainCoregFilter()]\n", "    \n", "    # as lambda\n", "    post_filter = lambda x: x[ReportPeriodRawFilter()][MainCoregFilter()]\n", "```\n", "\n", "**Attention 1:** while either defining a function or using a lambda did work perfectly when running the script directly from the command line or in an IDE, it didn't work within Jupyter. In Jupyter, I have to use a function and moreover, also the needed imports have to be included in the function itself:\n", "```\n", "def postloadfilter(databag: RawDataBag) -> RawDataBag:\n", "    from secfsdstools.e_filter.rawfiltering import ReportPeriodRawFilter, MainCoregFilter\n", "    return databag[ReportPeriodRawFilter()][MainCoregFilter()]\n", "```\n", "\n", "**Attention 2:** running this code took a few minutes on my 4-core/32GB laptop (around 6 minutes). Moreover, it still needed about 18GB (!) of free memory when launched. Open the TaskManager and watch your CPUs and Memory working."]}, {"cell_type": "code", "execution_count": 14, "id": "fb68cdee-3a4a-4aed-832a-6cd63e358304", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-01 07:46:15,435 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-01 07:46:15,439 [INFO] parallelexecution      items to process: 63\n", "2025-02-01 07:52:05,827 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["sub (350043, 36)\n", "pre (12431739, 10)\n", "num (59042640, 10)\n", "----------------------------------------\n", "pre_num (20508786, 17)\n", "----------------------------------------\n"]}], "source": ["import os\n", "\n", "from secfsdstools.e_collector.zipcollecting import ZipCollector\n", "from secfsdstools.d_container.databagmodel import RawDataBag\n", "from secfsdstools.e_filter.rawfiltering import ReportPeriodRawFilter, MainCoregRawFilter\n", "\n", "target_path = \"bs_10k_10q_all_joined\"\n", "os.makedirs(target_path, exist_ok = True)\n", "\n", "def postloadfilter(databag: RawDataBag) -> RawDataBag:\n", "    from secfsdstools.e_filter.rawfiltering import ReportPeriodRawFilter, MainCoregRawFilter\n", "    return databag[ReportPeriodRawFilter()][MainCoregRawFilter()]\n", "\n", "collector: ZipCollector = ZipCollector.get_all_zips(forms_filter=[\"10-K\", \"10-Q\"],\n", "                                                    stmt_filter=[\"BS\"],\n", "                                                    post_load_filter=postloadfilter)\n", "\n", "rawdatabag = collector.collect()\n", "\n", "print(\"sub\", rawdatabag.sub_df.shape)\n", "\n", "# just print the size of the pre and num dataframes\n", "print(\"pre\", rawdatabag.pre_df.shape)\n", "print(\"num\", rawdatabag.num_df.shape)\n", "\n", "print(\"----------------------------------------\")\n", "\n", "# joining the pre and num dataframes\n", "joineddatabag = rawdatabag.join()\n", "print(\"pre_num\", joineddatabag.pre_num_df.shape)\n", "\n", "print(\"----------------------------------------\")\n", "\n", "# of course, saving the the databag would be a good idea here\n", "# but remember, the path has to exist and has to be empty\n", "joineddatabag.save(target_path=target_path)"]}, {"cell_type": "markdown", "id": "f92e4640-f012-48e3-b20b-bd6c556c8848", "metadata": {}, "source": ["sub (316161, 36)\n", "pre (14085879, 10)\n", "num (51855742, 9)\n", "pre_num (10723938, 16)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}
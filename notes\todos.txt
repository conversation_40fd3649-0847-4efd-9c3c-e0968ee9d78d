todos
------

new release
- adapt readme / changelog
- adapt notebooks
- adapt "new features" info
- in gitpages branch
    - adapt release notes
    - adapt index
- adapt releases in project.toml
- merge back into main
- set tag and push tag (format vx.x.x) (git push --tags)
- on github -> under code releases: draft new release

2.4.1
-----
[] update secdaily to 0.2.2 (more robustness / prevent name clashes)
   [x] check for daily data cleanup if needed by secdaily



2.4.2
-----
[] support daily data in example automation pipelines





----------------------------------
- first only as additional pipeline steps, not integrated in db?
- linked in post

- adding more steps to automation

spenden via paypal: Bsp:
https://pypi.org/project/colorama/ -> Projekt mit Spenden-Button
https://www.paypal.com/donate/?cmd=_donations&business=2MZ9D2GMLYCUJ&item_name=Colorama&currency_code=USD -> Präsentation


----------
- use of str and Path is inconsistence. it would be nice if both could be used


- overwrite serial execution per task
- in cf haben wir oft auch zusätzliche 'cash' tags -> ben<PERSON><PERSON>gen wir diese im bs auch?
- maxqtr prepivot rule also for bs und is

- standardize oustanding shares, resp. validate
  (see: https://github.com/hansjoergw/sec-fincancial-statement-data-set/issues/13)
  "but as i understand, they do not have the same exact meaning. i haven't investigated that, so i am not sure if you
  can assume that they are the same, resp. close enough to treat them that way. in the cp, the value appears
  as entitycommonstocksharesoutstanding (if present), in the balance sheet as commonstocksharesoutstanding,
  and in the incomestatement as weightedaveragenumberofsharesoutstandingbasic."

  -> adding entitycommonstocksharesoutstanding from cp
  -> adding commonstocksharesoutstanding to bs
  -> provide default handling of choosing which value for final "outstandingshares"



- discord channel?
- evtl. sollte jede regel noch eine "explanation" bekommen. ein text, der die regel spezifisch erklärt,
  nicht allgemein wie in description.

- usecase/analyzes ausarbeiten und eigenes notebook mit beispielen

- idea: using configuration file as parameter or as option instead of only class:
  input medium feedback:
  "what do you think about letting update() and configurationmanager take config_file_name with default value ''?
  this seems to provide more explicit control. i know this would affect many functions which take config.
  how about replacing it by config_file_name?"


next:
- vlt switch um parallele verarbeitung komplett auszuschalten?
- vlt switch maximale parallelität?

- new notebook, examples
  -> reading primary financial statements for a single report, display bs, is, cf for a report
  -> analyse changes in one tag for one company
  -> compare basic data of two companies

- supporting dataset with notes -> v.2

- es wäre gut, wenn der standardstatementpresenter noch eine kolonne "year", noch welcher auch sortiert ist
  die sortierung über adsh alleine ermöglicht keine sauber sortierung pro jahr
  man könnte hierfür auch das report year aus sub verwenden

- pipelines: einfaches konzept um standard flows zu kapseln
  z.b. collector als input bis zur presentation
  -> z.b. für selektierte ciks daten so aufbereiten, dass sie alle vorhandenen jahre in spalten angezeigt werden

- update des config files
  fehlende inhalte müssen gesetzt und auf default gesetzt werden, oder?
  überflüssige inhalte müssen entfernt werden
  -> im moment noch nicht notwendig -> würde erst notwendig, falls
     optionen nicht mehr benützt werden

- in company collector -> eine sicht für sämtlich jahre zeigen -> mit jahren als spalten, aber
   - versuchen die selben tags zu zeigen, notfalls mit null...
   - reihenfolge könnte ein problem sein

- - warning, falls daten nicht indexiert sind
    -> hinweis message beim laden der config
    -> config for autocheck to download / autodownload


later
-build timeline for company
-db-version for updates


checkout für visualisierung
- https://gist.github.com/mwouts/a2de16feb90d33fd89334fb09f62742f
- https://www.linkedin.com/pulse/interactive-dataframes-jupyter-google-colab-vs-code-pycharm-wouts/?trk=articles_directory
- https://pbpython.com/dataframe-gui-overview.html


ideen
-----
- export excel
- cli
- https://streamlit.io/ ui


look at
-------
- https://pypi.org/project/edgartools/1.6.0/



History
-------
2.4.0 daily updates
Problems:
-[x] pylint config problems

- [x] eigenes dlddaily Verzeichnis
  - [x] config anpassen für default dlddaily
  - [x] bereits vorhandene struktur verschieben
  - [x] datenbank anpassen


Functions:
- [x] daily processing integration
     [x] clear index tables (for last zip quarter and before quarters)
     [x] clear daily parquet files (for last zip quarter and before quarters)
     [x] calculate start quarter
     [x] download and process daily files
     [x] transform new daily files to parquet
     [x] index new daily files
     [x] configuration daily processing -> enable
- [x] Testing daily integration
     [x] test different collectors with daily
     [x] test notebooks for daily reports -> zieht in übersicht nur quarter.
- [x] new features info message

- [x] colum fy_real is created and saved for daily sub_df
  
- [x] update documents
   [x] update readme / Changelog
   [x] new notebook for daily

- [x] load subdata of last report -> ensure it always comes from the quartery zip files

- [x] integrate new version secdaily 0.2.1

- ensure docs deploy is working when released



2.3.0 - maintenance release
- switch to vs code
  - [x] pyproject toml like secdaily
  - [x] new python poetry env
  - [x] ensure linting with pylint and ruff is working
  - [x] vscode configs like secdaily
  - [x] ensure test execution/debugging is working
  - [x] ensure linting hints in editor are working

- poetry build pipeline
  - [x] ensure lint/test build is working when pushed
  - [x] ensure pdoc build is working when pushed

- [x] versions check -> check if there is a new version to download
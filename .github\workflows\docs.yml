#  This workflow will run on every push to the master branch of your repository, and it will do the following:
#
#  Install the sphinx and sphinx-rtd-theme packages.
#  Generate the HTML documentation in the docs/_build/html directory.
#  Deploy the documentation to GitHub Pages using the peaceiris/actions-gh-pages action.
#  Deploy the documentation to ReadTheDocs using the peaceiris/actions-readthedocs action.
#
#  To use this workflow, you will need to create a personal access token on ReadTheDocs and add it
#  to your repository's secrets as READTHEDOCS_TOKEN. You can then use this token to authenticate
#  the peaceiris/actions-readthedocs action and allow it to publish your documentation to ReadTheDocs.

name: Build and Deploy Documentation

on:
  release:
    types: [published]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: 2.1.1
          virtualenvs-create: true
          virtualenvs-in-project: true

      - name: Install dependencies
        run: poetry install --with dev
            
      - name: pdoc
        run: |
          mkdir -p docs/_build/html
          poetry run pdoc3 --html --force -o docs/api src/secfsdstools
          cp README.md ./docs/read.md

      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: "docs"
          destination_dir: doc_latest
          enable_jekyll: true

      - name: Trigger GitHub pages rebuild
        run: |
          curl --fail --request POST \
            --url https://api.github.com/repos/${{ github.repository }}/pages/builds \
            --header "Authorization: Bearer $USER_TOKEN"
        env:
          USER_TOKEN: ${{ secrets.GITHUB_TOKEN }}




{"cells": [{"cell_type": "markdown", "id": "e75a3754-3ed2-4b83-8b81-05c3846a0017", "metadata": {}, "source": ["# Bulk Data - Memory Efficiency"]}, {"cell_type": "markdown", "id": "fb0dc1d5-a3c6-4c49-823c-a4690aec0012", "metadata": {}, "source": ["This notebook gives some ideas on how you keep your memory footprint as low as possible. This is especially crucial, when you work with larger datasets, for instance where all that data from all quarters are contained. "]}, {"cell_type": "markdown", "id": "70f90c95-a9a6-44b4-9d24-f9a7a6b5710a", "metadata": {"tags": []}, "source": ["<span style=\"color: #FF8C00;\">==========================================================</span>\n", "\n", "**If you find this tool useful, a sponsorship would be greatly appreciated!**\n", "\n", "**https://github.com/sponsors/HansjoergW**\n", "\n", "How to get in touch\n", "\n", "* Found a bug: https://github.com/HansjoergW/sec-fincancial-statement-data-set/issues\n", "* Have a remark: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/general\n", "* Have an idea: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/ideas\n", "* Have a question: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/q-a\n", "* Have something to show: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/show-and-tell\n", "\n", "<span style=\"color: #FF8C00;\">==========================================================</span>"]}, {"cell_type": "markdown", "id": "b11efeab-3ff5-448e-b163-d2ae7b87e764", "metadata": {}, "source": ["## Use Predicate Pushdown - Apply Filters During Loading"]}, {"cell_type": "markdown", "id": "a0246007-1fee-4d5b-a5ae-b6e582ae2b57", "metadata": {}, "source": ["One big advantage of using Parquet files is the ability to execute filters direct while loading so that less data has to be read into the memory. This is called Predicate Pushdown.\n", "For instance, the `read_parquet` method of Pandas provides provides this feature with the parameter `filter`.\n", "\n", "The SECFSDSTools also uses this feature."]}, {"cell_type": "markdown", "id": "f2ab45a5-9cf6-4542-941f-be1d34ab9ab3", "metadata": {}, "source": ["### Use Predicate Pushdown in Collectors\n", "\n", "The collectors `CompanyReportCollector`, `MultiReportCollector`, and `ZipCollector` provide the following optional filter parameters: `forms_filter`, `stmt_filter`, and `tag_filter`. Definting this filters ensures that less data is read and therefore data is loaded faster and less memory is consumed.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "8e17f94a-2daf-4b41-994f-c3816982af12", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-17 17:05:31,554 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-17 17:05:32,235 [INFO] parallelexecution      items to process: 46\n", "2025-02-17 17:05:50,111 [INFO] parallelexecution      commited chunk: 0\n"]}], "source": ["from typing import List\n", "from secfsdstools.e_collector.companycollecting import CompanyReportCollector\n", "\n", "ciks_to_load: List[int] = [320193, 789019, 1652044, 1045810, 1018724, 2488, 50863] # Apple, Microsoft, Alphabet, nvidia, Amazon, AMD, intel\n", "bag = CompanyReportCollector.get_company_collector(ciks=ciks_to_load, forms_filter=['10-K'], stmt_filter=['BS', 'IS', 'CF']).collect()"]}, {"cell_type": "markdown", "id": "5b01fc5b-4515-42b8-8a62-9ffd4bcd7974", "metadata": {}, "source": ["### Use Predicate Pushdown in Load Methods\n", "\n", "Coming with version 2.1, Predicate Pushdown is also supported in the `laod` methods of `RawDataBag` and `JoinedDataBag`. They are especially useful, if you created a single bag containing all prefiltered data (as described in 06_bulk_data_processing_deep_dive or in 08_00_automation_basics).\n", "\n", "**Some numbers**\n", "\n", "Reading the `_4_single_bag/all` bag (created as described in 08_00_automation_basics) and then filtering for all \"BS\" entries\n", "<pre>\n", "    file_path = Path(\"c:/data/sec/automated/_4_single_bag/all\")\n", "\n", "    joined = JoinedDataBag.load(str(file_path))\n", "    joined_filtered = joined[StmtJoinedFilter(stmts=['BS'])]\n", "</pre>\n", "\n", "took about **45 seconds** and needed about **20 GB** memory.\n", "\n", "Doing the same with Predicate Pushdown in the `load` method\n", "<pre>\n", "    file_path = Path(\"c:/data/sec/automated/_4_single_bag/all\")\n", "\n", "    joined = JoinedDataBag.load(target_path=str(file_path), stmt_filter=['BS'])\n", "</pre>\n", "\n", "took about **15 seconds** and consumed **7 GB** of memory."]}, {"cell_type": "markdown", "id": "8ca6000f-c2d4-4a0a-b4fc-6a924489d2e3", "metadata": {}, "source": ["### Write Your Own Predicate Pushdown Load Methods\n", "\n", "If you have some special filter requirements, maybe you want to consider writing your own `load` function using Predicate Pushdown."]}, {"cell_type": "markdown", "id": "be4cf435-1f45-474f-b8cc-6e90fdd9f790", "metadata": {}, "source": ["## Concat by Folders Instead of Concat by Loaded Bags\n", "\n", "In version 2.1 the possibility to concat bags directly on the folder/file level was introduced. When you use these features, parquet files (thanks to pyarrow) are directly concatenated on the file system instead of using Pandas concat function. This leads to a very low memory consumption and makes it possible to concat large amount data into a single file. Combine that with using Predicate Pushdown and you have the ability to work with a huge dataset also with limited hardware resources.\n", "\n", "In detail, the framework provides the following features:"]}, {"cell_type": "markdown", "id": "b152399b-c457-4c77-b492-b3934e57f2bb", "metadata": {}, "source": ["**`from secfsdstools.a_utils.fileutils import concat_parquet_files`**\n", "\n", "Concats sub.txt, num.txt, pre.txt, and pre_num.txt parquet files. Note: the defined output-file name will determine which file it is (sub.txt, num.txt, ..)."]}, {"cell_type": "code", "execution_count": 1, "id": "ad983cad-2a59-4f3d-95e6-d840a7d69ea1", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Skipping empty file: a/sub.txt.parquet\n", "Skipping empty file: b/sub.txt.parquet\n", "Skipping empty file: c/sub.txt.parquet\n"]}], "source": ["from secfsdstools.a_utils.fileutils import concat_parquet_files\n", "\n", "sub_txt_to_concat = ['a/sub.txt.parquet', 'b/sub.txt.parquet', 'c/sub.txt.parquet']\n", "output_file = 'sub.txt.parquet'\n", "\n", "concat_parquet_files(input_files=sub_txt_to_concat, output_file=output_file)"]}, {"cell_type": "markdown", "id": "537048d1-f33c-4335-9b08-37ed4ddf2113", "metadata": {}, "source": ["**`RawDataBag.concat_filebased` and `JoinedDataBag.concat_filebased`**\n", "\n", "The `concat_filebased` provided in `RawDataBag` and `JoinedDataBag` takes three input parameters. First, a list with the input folders (containing the data of a `RawDataBag`, resp. of a `JoinedDataBag`), the target_path, and a flag that indicates whether the sub_df must be checked for duplicates (for instance, if you separated the data for balance sheets, income statements, and cash flow statement in different bags and want to concat them together, all would have the same sub data so you need to make sure that you do not get duplicated entries in sub)."]}, {"cell_type": "code", "execution_count": 2, "id": "30bca247-4d05-47ba-807e-916c1b25c0de", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Skipping empty file: bag1\\pre.txt.parquet\n", "Skipping empty file: bag2\\pre.txt.parquet\n", "Skipping empty file: bag3\\pre.txt.parquet\n", "Skipping empty file: bag1\\num.txt.parquet\n", "Skipping empty file: bag2\\num.txt.parquet\n", "Skipping empty file: bag3\\num.txt.parquet\n", "Skipping empty file: bag1\\sub.txt.parquet\n", "Skipping empty file: bag2\\sub.txt.parquet\n", "Skipping empty file: bag3\\sub.txt.parquet\n"]}], "source": ["from pathlib import Path\n", "from secfsdstools.d_container.databagmodel import RawDataBag\n", "\n", "raw_databag_folders = [Path('bag1'), Path('bag2'), Path('bag3')]\n", "output_folder = Path('out')\n", "\n", "RawDataBag.concat_filebased(paths_to_concat=raw_databag_folders, target_path=output_folder, drop_duplicates_sub_df=False)"]}, {"cell_type": "markdown", "id": "79247920-6798-4941-940f-0a40cbbe04b4", "metadata": {}, "source": ["**Automation Processes `ConcatByChangedTimestampProcess` and `ConcatByChangedTimestampProcess`**\n", "\n", "If you use *automation* as described in 08_00_automation basics you very likely use `ConcatByChangedTimestampProcess` and `ConcatByChangedTimestampProcess`. With version 2.1, those classes now also use the `concat_filebased` methods and therefore have a significant lower memory footprint than in the previous version."]}, {"cell_type": "code", "execution_count": null, "id": "75a678e6-50df-453e-ac5b-c88c419820d4", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}
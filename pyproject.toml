[project]
name = "secfsdstools"
version = "2.4.2"
authors = [{ name = "<PERSON>joerg", email = "<EMAIL>" }]
description = "A few python tools to analyze the SEC.gov financial statements data sets (https://www.sec.gov/dera/data/financial-statement-data-sets)"
readme = "README.md"
requires-python = ">=3.10"
license = "Apache-2.0"

maintainers = [
    { name = "<PERSON><PERSON><PERSON><PERSON> Wingeier", email = "<EMAIL>" },
]

classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "License :: OSI Approved :: Apache Software License",
    "Operating System :: OS Independent",
    "Topic :: Office/Business :: Financial",
    "Topic :: Office/Business :: Financial :: Investment",
    "Intended Audience :: Developers",
    "Intended Audience :: Financial and Insurance Industry",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Utilities",
    "Natural Language :: English",
]

keywords = [
    "SEC.GOV",
    "SEC EDGAR",
    "SEC Filing",
    "EDGAR",
    "Finance",
    "CIK",
    "10-Q",
    "10-K",
    "8-K",
    "Financial Statements",
    "Financial Statements Dataset",
    "Financial Analysis",
    "Data Processing",
    "Financial Data",
    "SEC API",
    "XBRL",
]

dependencies = [
    "pandas>=1.3",
    "numpy>=1.0,<2.0",
    "requests>=2.0",
    "pathos~=0.3",
    "pyarrow>=8.0",
    "fastparquet>=0.5",
    "pandera==0.22.1",
    "secdaily>=0.2.2",
]

[project.urls]
"Homepage" = "https://hansjoergw.github.io/sec-fincancial-statement-data-set/"
"Bug Tracker" = "https://github.com/HansjoergW/sec-fincancial-statement-data-set/issues"
"Github" = "https://github.com/HansjoergW/sec-fincancial-statement-data-set"
"Funding" = "https://github.com/sponsors/HansjoergW"
"Forum" = "https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions"
"Change Log" = "https://github.com/HansjoergW/sec-fincancial-statement-data-set/blob/main/CHANGELOG.md"


[project.optional-dependencies]
dev = [
    "pytest~=8.3",
    "pylint~=3.0",
    "coverage~=7.0",
    "typing_extensions~=4.0",
    "pdoc3~=0.10",
    "ruff",
]

[tool.poetry]
packages = [{ include = "secfsdstools", from = "src" }]

[tool.poetry.group.dev.dependencies]
pytest = "^8.3"
pylint = "^3.0"
coverage = "^7.0"
typing_extensions = "^4.0"
pdoc3 = "^0.10"
black = "^24.2.0"
ruff = "^0.5.3"

[tool.ruff]
# Explicitly enable F401 (unused imports) and other rules
lint.select = [
    "F401", # unused imports
    "E",    # pycodestyle errors
    "F",    # other pyflakes rules
    "I",    # isort
    "W",    # pycodestyle warnings
]
lint.ignore = [
    "E501", # line too long
]
# Only check the src directory
include = ["src/**/*.py"]
# Explicitly exclude tests directory
exclude = ["tests/**", "sandbox/**"]
line-length = 120
fix = true

[tool.black]
line-length = 120

[tool.pylint]
# Base settings that apply to everything
ignore-paths = [
    "build",
    "dist",
    ".git",
    ".github",
    ".venv",
    "venv",
    "docs",
    "notebooks",
    "sandbox",
    "tests",
]

[tool.pylint.'MESSAGES CONTROL']
# Explicitly disable specific checks
disable = [
    "R0801", # duplicate-code
    "R0902", # too-many-instance-attributes
    "R0903", # too-few-public-methods
    "R0912", # too-many-branches
    "R0913", # too-many-arguments
    "R0917", # too-many-positional-arguments
    "W0511", # fixme
]

[tool.pylint.typecheck]
extension-pkg-allow-list = ["lxml"]

[tool.pylint.format]
max-line-length = 120

[tool.pytest.ini_options]
testpaths = [
    "tests",
    "sandbox", # sandbox tests
]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-ra -q --strict-markers"
markers = [
    "integration: marks tests as integration tests (deselect with '-m \"not integration\"')",
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
]
log_cli = true
log_cli_format = "%(asctime)s %(levelname)s %(message)s [%(name)s]"
log_cli_date_format = "%Y-%m-%d %H:%M:%S"
log_cli_level = "DEBUG"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

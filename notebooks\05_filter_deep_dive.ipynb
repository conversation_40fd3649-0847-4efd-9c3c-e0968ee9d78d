{"cells": [{"cell_type": "code", "execution_count": 1, "id": "064dcee2-22dc-48ca-afcb-bd8f62f34c03", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "# ensure that all columns are shown and that colum content is not cut\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.max_colwidth', None)\n", "pd.set_option('display.width',1000)\n", "pd.set_option('display.max_rows', 500) # ensure that all rows are shown"]}, {"cell_type": "markdown", "id": "dbc500ef-7d18-40eb-91ff-77c4a9608e51", "metadata": {}, "source": ["# Filter Deep Dive\n", "This notebook introduces all the available Filters"]}, {"cell_type": "markdown", "id": "9037e449-2d89-4141-8587-59e2ab59b7f1", "metadata": {"tags": []}, "source": ["<span style=\"color: #FF8C00;\">==========================================================</span>\n", "\n", "**If you find this tool useful, a sponsorship would be greatly appreciated!**\n", "\n", "**https://github.com/sponsors/HansjoergW**\n", "\n", "How to get in touch\n", "\n", "* Found a bug: https://github.com/HansjoergW/sec-fincancial-statement-data-set/issues\n", "* Have a remark: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/general\n", "* Have an idea: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/ideas\n", "* Have a question: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/q-a\n", "* Have something to show: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/show-and-tell\n", "\n", "<span style=\"color: #FF8C00;\">==========================================================</span>"]}, {"cell_type": "markdown", "id": "878a30ce-39c5-4a22-9358-035a7113dd5d", "metadata": {}, "source": ["## Basics\n", "\n", "A few basic points\n", "\n", "* All Filters are implmented for the RawDataBag and for the JoinedDataBag. Depending for which databag type the filter is implemented its postfix is either `RawFilter` or `JoinedFilter`.\n", "* All Filters do not copy the dataframes. They just apply filter on existing dataframes, but usually don't create new ones.\n", "* All Filters have a `filter()` method which takes a databag as parameter and returns a new databag as parameter (again, the dataframes are not copied in the new instance of the databag).\n", "* Instead of using the `filter()` method of a databag you can also use the index operator.\n", "```\n", "a_filter = USDOnlyRawFilter()\n", "a_rawdatabag: RawDataBag = ...\n", "\n", "# use the filter() method of the filter..\n", "new_databag = a_filter.filter(a_rawdatabag)\n", "\n", "# or use the filter method of the databab\n", "new_databag = a_rawdatabag.filter(a_filter)\n", "```\n", "* Calls to the `filter()` method of the databag can be chained as follows\n", "```\n", "filter1 = USDOnlyRawFilter()\n", "filter2 = OfficialTagsOnlyRawFilter()\n", "a_rawdatabag: RawDataBag = ...\n", "\n", "new_databag = a_rawdatabag.filter(filter1).filter(filter2)\n", "\n", "```\n", "* The index operator (`[]`) of the databag class is forwarded to the `filter()` method, therefore you can write the previous call as follows:\n", "```\n", "new_databag = a_rawdatabag[filter1][filter2]\n", "```"]}, {"cell_type": "markdown", "id": "8a4303be-fc28-4c10-895e-5328ced45861", "metadata": {}, "source": ["## Load Demo Databag\n", "\n", "We use the following dataset to demonstrate the filters."]}, {"cell_type": "code", "execution_count": 2, "id": "7ae5a999-6b76-4424-be7e-5ebbc1b9c5f2", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-02 06:13:13,658 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 06:13:14,372 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 06:13:14,377 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 06:13:14,379 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2022q4.zip\n", "2025-02-02 06:13:17,960 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["sub:  (7280, 36)\n", "pre:  (806371, 10)\n", "num:  (3543392, 10)\n"]}], "source": ["from secfsdstools.e_collector.zipcollecting import ZipCollector\n", "\n", "databag = ZipCollector.get_zip_by_name('2022q4.zip').collect()\n", "\n", "print(\"sub: \", databag.sub_df.shape)\n", "print(\"pre: \", databag.pre_df.shape)\n", "print(\"num: \", databag.num_df.shape)"]}, {"cell_type": "markdown", "id": "de4d180e-c199-4c14-bf6e-a72862bfe4f3", "metadata": {}, "source": ["## `<PERSON><PERSON><PERSON>aw<PERSON><PERSON>er`\n", "\n", "This filter lets you select the data for certain reports by their adsh number. Just provide the list of the adsh numbers you are interested in in the constructor of the filter.\n", "\n", "It operates on all dataframes (sub, pre, and num)."]}, {"cell_type": "code", "execution_count": 3, "id": "de2d1088-cf64-4775-8efa-96ecff7b8f6d", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sub:  (1, 36)\n", "pre:  (100, 10)\n", "num:  (437, 10)\n"]}], "source": ["from secfsdstools.e_filter.rawfiltering import AdshRawFilter\n", "\n", "apple_10k_2022_adsh = \"0000320193-22-000108\"\n", "adsh_filter = AdshRawFilter(adshs=[apple_10k_2022_adsh])\n", "\n", "filtered_databag = databag[adsh_filter]\n", "\n", "# since we filtered only a single adsh, there is only one line in the sub_df\n", "print(\"sub: \", filtered_databag.sub_df.shape)\n", "# also the pre_df and the num_df only contain data for a single report\n", "print(\"pre: \", filtered_databag.pre_df.shape)\n", "print(\"num: \", filtered_databag.num_df.shape)"]}, {"cell_type": "markdown", "id": "a37f2dd9-13c1-45d9-825f-c8a68597fc25", "metadata": {}, "source": ["## `StmtRawFilter`\n", "This filter filters the data by the stmt column, which defines to which financial statement (BalanceSheet, CashFlow, IncomeStatement, ..) a data point belongs.\n", "\n", "The stmt column is part of the pre_df, so only the pre_df is filtered by this filter."]}, {"cell_type": "code", "execution_count": 4, "id": "e718b240-df5e-4d18-9cbd-ab89ce15f1b9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sub:  (7280, 36)\n", "pre:  (508458, 10)\n", "num:  (3543392, 10)\n"]}], "source": ["from secfsdstools.e_filter.rawfiltering import StmtRawFilter\n", "\n", "stmt_filter = StmtRawFilter(stmts=['BS', 'CF'])\n", "\n", "filtered_databag = databag[stmt_filter]\n", "\n", "# as expected, only the shape of the pre_df is different from the unfiltered dataframes\n", "print(\"sub: \", filtered_databag.sub_df.shape)\n", "print(\"pre: \", filtered_databag.pre_df.shape)\n", "print(\"num: \", filtered_databag.num_df.shape)"]}, {"cell_type": "markdown", "id": "a47b5027-2269-4e18-8f0f-151d7311e7bc", "metadata": {}, "source": ["## `ReportPeriodRawFilter`\n", "A report always contains datapoints from previous years. For instance, in the BalanceSheet, IncomeStatement, and CashFlow statement also the values for the previous year is displayed.\n", "\n", "Moreover, it is common in an annual report that certain datapoints are presented for several previous years.\n", "\n", "You can use this filter, if you are only interested in datapoints that belongs to the \"period\" date of the report (defined in the period column of the sub_df).\n", "\n", "The column ddate of the num_df does define the date of the datapoint. So this filter only applies to the num_df."]}, {"cell_type": "markdown", "id": "bd3ebc4e-ce6e-487a-b152-a994d8e7985f", "metadata": {}, "source": ["To demonstrate that, let us first have a look at all the ddates that are present in apple's 2022 10-K report."]}, {"cell_type": "code", "execution_count": 9, "id": "d4cb94bd-528c-4d0f-854d-15b3ae4139a2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[20220930]\n", "20210930    169\n", "20220930    165\n", "20200930     95\n", "20190930      8\n", "Name: ddate, dtype: int64\n"]}], "source": ["from secfsdstools.e_filter.rawfiltering import AdshRawFilter\n", "\n", "apple_10k_2022_adsh = \"0000320193-22-000108\"\n", "adsh_filter = AdshRawFilter(adshs=[apple_10k_2022_adsh])\n", "\n", "apple_10k_2022_databag = databag[adsh_filter]\n", "\n", "# first, let's see for what period the report is\n", "# The format is YYYYMMDD, so it is the 30th of September 2022\n", "print(apple_10k_2022_databag.sub_df.period.tolist())\n", "\n", "# Next, lets have a look how many different datapoint ddates are in the num_df and lets also count how many datapoints there are\n", "print(apple_10k_2022_databag.num_df.ddate.value_counts())"]}, {"cell_type": "markdown", "id": "0af1b6eb-4d00-44e4-b638-1bc8be0c70ad", "metadata": {}, "source": ["As exepcted, most of the datapoints are from the period of the report (2022). But a significant number of datapoints is from the previous year (2021) and even the year before that (2020).\n", "\n", "By applying this filter, only datapoints are filtered that have the same value for ddate as the period column of the report to which they belong."]}, {"cell_type": "code", "execution_count": 11, "id": "a00de346-4ca1-458e-bd41-820c71e549a3", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["20220930    165\n", "Name: ddate, dtype: int64\n", "sub:  (1, 36)\n", "pre:  (100, 10)\n", "num:  (165, 10)\n"]}], "source": ["from secfsdstools.e_filter.rawfiltering import ReportPeriodRawFilter\n", "\n", "reportperiod_filter = ReportPeriodRawFilter()\n", "\n", "filtered_databag = apple_10k_2022_databag[reportperiod_filter]\n", "\n", "# let us check the number of datapoints per ddate after the filter. We expect to see just datapoints for one\n", "print(filtered_databag.num_df.ddate.value_counts())\n", "\n", "# as expected, only the shape of the num_df is different from the unfiltered dataframes\n", "print(\"sub: \", filtered_databag.sub_df.shape)\n", "print(\"pre: \", filtered_databag.pre_df.shape)\n", "print(\"num: \", filtered_databag.num_df.shape)"]}, {"cell_type": "markdown", "id": "f0ba926e-a52c-4027-a060-1e57e3a4aa53", "metadata": {}, "source": ["## `ReportPeriodAndPreviousPeriodRawFilter`\n", "This filter is similar to the `ReportPeriodRawFilter` filter. Instead of just filtering the datapoints of the period of a report, it also filters the datapoints of the previous year.\n", "\n", "It also operates only on the num_df."]}, {"cell_type": "code", "execution_count": 12, "id": "fafce32d-cade-4510-b5aa-36d43d8b08bb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["20210930    169\n", "20220930    165\n", "Name: ddate, dtype: int64\n", "sub:  (1, 36)\n", "pre:  (100, 10)\n", "num:  (334, 10)\n"]}], "source": ["from secfsdstools.e_filter.rawfiltering import ReportPeriodAndPreviousPeriodRawFilter\n", "\n", "reportperiodandprevious_filter = ReportPeriodAndPreviousPeriodRawFilter()\n", "\n", "filtered_databag = apple_10k_2022_databag[reportperiodandprevious_filter]\n", "\n", "# let us check the number of datapoints per ddate after the filter. We expect to see just datapoints for two ddates\n", "print(filtered_databag.num_df.ddate.value_counts())\n", "\n", "# as expected, only the shape of the num_df is different from the unfiltered dataframes\n", "print(\"sub: \", filtered_databag.sub_df.shape)\n", "print(\"pre: \", filtered_databag.pre_df.shape)\n", "print(\"num: \", filtered_databag.num_df.shape)"]}, {"cell_type": "markdown", "id": "b92cff61-20bf-47b9-9d52-e584df1fe477", "metadata": {}, "source": ["## `TagRawFilter` \n", "This filter filters only datapoints for the defined tags.\n", "\n", "it operates on the pre_df and the num_df."]}, {"cell_type": "code", "execution_count": 13, "id": "9212fdf8-7138-412d-a08d-aaaa6002ef6a", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sub:  (7280, 36)\n", "pre:  (14129, 10)\n", "num:  (43723, 10)\n", "Assets              7516\n", "AssetsCurrent       6081\n", "AssetsNoncurrent     532\n", "Name: tag, dtype: int64\n"]}], "source": ["from secfsdstools.e_filter.rawfiltering import TagRawFilter\n", "\n", "tag_filter = TagRawFilter(tags=['Assets', 'AssetsCurrent', 'AssetsNoncurrent'])\n", "\n", "filtered_databag = databag[tag_filter]\n", "\n", "# as expected, only the shape of the pre_df and num_df are different from the unfiltered dataframes\n", "print(\"sub: \", filtered_databag.sub_df.shape)\n", "print(\"pre: \", filtered_databag.pre_df.shape)\n", "print(\"num: \", filtered_databag.num_df.shape)\n", "\n", "print(filtered_databag.pre_df.tag.value_counts())"]}, {"cell_type": "markdown", "id": "494d70dd-ede0-400d-9cad-cd9f031ab0b2", "metadata": {}, "source": ["## `MainCoregRawFilter`\n", "Holding companies often also report data of their subsidiaries in their main report. Datapoints of subsidiares contain the name of the subsidiary in the coreg column of the num_df dataframe.\n", "\n", "A good example is \"AMERICAN ELECTRIC POWER CO INC\" (CIK 4904) which has about 7 subsidaries (e.g., Southwestern Electric Power Co, Indiana Michigan Power Co).\n", "\n", "If you are not interested in the datapoints of subsidiaries, you can use this filter to remove datapoints of subsidiaries.\n", "\n", "This filter operates only on the num_df dataframe."]}, {"cell_type": "code", "execution_count": 14, "id": "3b8b972d-38e3-40f4-a341-f5238b6a1d94", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['' 'OhioPowerCo' 'AEPTransmissionCo' 'IndianaMichiganPowerCo'\n", " 'AEPTexasInc.' 'SouthwesternElectricPowerCo' 'PublicServiceCoOfOklahoma'\n", " 'AppalachianPowerCo']\n", "sub:  (1, 36)\n", "pre:  (227, 10)\n", "num:  (4796, 10)\n"]}], "source": ["from secfsdstools.e_filter.rawfiltering import AdshRawFilter\n", "\n", "# let us select the 10q from American Electric Power Co Inc\n", "americanelectric_10q_adsh=\"0000004904-22-000093\"\n", "americanelectric_10q_bag = databag[AdshRawFilter(adshs=[americanelectric_10q_adsh])]\n", "\n", "# let's see what subsidiaries american electric has\n", "print(americanelectric_10q_bag.num_df.coreg.unique())\n", "\n", "print(\"sub: \", americanelectric_10q_bag.sub_df.shape)\n", "print(\"pre: \", americanelectric_10q_bag.pre_df.shape)\n", "print(\"num: \", americanelectric_10q_bag.num_df.shape)"]}, {"cell_type": "code", "execution_count": 16, "id": "227176da-8ad4-4040-81fd-eb4ff3a16790", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['']\n", "sub:  (1, 36)\n", "pre:  (227, 10)\n", "num:  (2854, 10)\n"]}], "source": ["from secfsdstools.e_filter.rawfiltering import MainCoregRawFilter\n", "\n", "# now filter only the datapoints for the holding company\n", "americanelectric_main_10q_bag_filtered = americanelectric_10q_bag[MainCoregRawFilter()]\n", "\n", "# let's see what subsidiaries american electric have after the applying the filter. There should only be an empty string in the list\n", "print(americanelectric_main_10q_bag_filtered.num_df.coreg.unique())\n", "\n", "\n", "print(\"sub: \", americanelectric_main_10q_bag_filtered.sub_df.shape)\n", "print(\"pre: \", americanelectric_main_10q_bag_filtered.pre_df.shape)\n", "print(\"num: \", americanelectric_main_10q_bag_filtered.num_df.shape)"]}, {"cell_type": "markdown", "id": "b25c4a22-92bf-4f81-ad05-e695ac8fa6c5", "metadata": {}, "source": ["## `OfficialTagsOnlyRawFilter`\n", "Sometimes companies use inofficial tags than the standard us-gaap tags defined by the xbrl taxonomy. In this cases, the version column contains the adsh number of the report, instead something like us-gaap/2022.\n", "\n", "Especially if you want to compare reports from different companies, it makes sense to only analyze the official tags.\n", "\n", "You can use this filter to do that.\n", "\n", "It operates on the pre_df and the num_df dataframe."]}, {"cell_type": "code", "execution_count": 17, "id": "6d9ba934-10d8-40e0-bd95-c994c9202a21", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["7090 ['us-gaap/2022' '0001822993-22-000032' '0001637207-22-000054'\n", " 'us-gaap/2021' '0001764013-22-000140' '0001398344-22-022208'\n", " '0000107140-22-000098' '0000914208-22-000500' '0001493152-22-034057'\n", " '0001213900-22-072201' '0001061219-22-000027' '0001477932-22-009083'\n", " 'ifrs/2021' '0001521951-22-000071' '0001193125-22-279123' 'ifrs/2022'\n", " 'us-gaap-sup/2022q3' '0000924901-22-000019' '0001493152-22-031374'\n", " '0001683168-22-007745']\n"]}], "source": ["# let's check how many different version tags there are in all the reports of a single quarter, and display the first 20\n", "print(len(databag.num_df.version.unique()), databag.num_df.version.unique()[:20])"]}, {"cell_type": "code", "execution_count": 19, "id": "12b57af8-e9a5-4904-bf5d-3910bf442cfc", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["9 ['us-gaap/2022' 'us-gaap/2021' 'ifrs/2021' 'ifrs/2022'\n", " 'us-gaap-sup/2022q3' 'srt/2022' 'srt-sup/2022q3' 'srt/2021' 'dei/2022']\n", "sub:  (7280, 36)\n", "pre:  (714187, 10)\n", "num:  (3217041, 10)\n"]}], "source": ["# it looks as if it is quite common to use inofficial tags as well, so it might definitely make sense to filter only the official tags\n", "from secfsdstools.e_filter.rawfiltering import OfficialTagsOnlyRawFilter\n", "\n", "# now filter only the datapoints for the holding company\n", "officialtagsonly_filter = OfficialTagsOnlyRawFilter()\n", "\n", "# let's check the number and values for the version field\n", "filtered_databag = databag[officialtagsonly_filter]\n", "\n", "print(len(filtered_databag.num_df.version.unique()), filtered_databag.num_df.version.unique()[:20])\n", "\n", "\n", "# as expected, only the shape of the pre_df and num_df are different from the unfiltered dataframes\n", "print(\"sub: \", filtered_databag.sub_df.shape)\n", "print(\"pre: \", filtered_databag.pre_df.shape)\n", "print(\"num: \", filtered_databag.num_df.shape)"]}, {"cell_type": "markdown", "id": "8cc79c2d-005e-4ded-a18d-a8f978d24594", "metadata": {}, "source": ["## `USDOnlyRawFilter`\n", "International companies often report datapoints also in other currencies than just USD.\n", "\n", "And again, if we want to compare the report of different companies, we might be only interested in USD datapoints.\n", "\n", "This filter operates on the uom column of the num_df dataframe and removes entries that are not USD."]}, {"cell_type": "code", "execution_count": 20, "id": "4591b5f5-a1a2-4f76-b2de-504cfec6ef5b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["71 ['USD' 'shares' 'CAD' 'ZAR' 'pure' 'EUR' 'CNY' 'BRL' 'AUD' 'JPY' 'GBP'\n", " 'MXN' 'Contract' 'ARS' 'HKD' 'Rate' 'CHF' 'Derivative' 'oz' 'vote' 'SEK'\n", " 'Litecoin' 'DKK' 'ISK' 'NOK' 'Share' 'Contracts' 'NumberOfContracts'\n", " 'Uniswap' 'Class']\n"]}], "source": ["# let's check how many different \"units\" there are in all the reports of a single quarter, and display the first 30\n", "print(len(databag.num_df.uom.unique()), databag.num_df.uom.unique()[:30])"]}, {"cell_type": "code", "execution_count": 22, "id": "97495364-7859-4c25-970a-32c6e9784815", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["39 ['USD' 'shares' 'pure' 'Contract' 'Rate' 'Derivative' 'oz' 'vote'\n", " 'Litecoin' 'Share' 'Contracts' 'NumberOfContracts' 'Uniswap' 'Class'\n", " 'Vote' 'BitcoinCash' 'Firm' 'contract' 'number_of_store' 'Ethereum'\n", " 'investment' 'dividend' 'agreement' 'Equipment' 'Decimal' 'Store'\n", " 'Bitcoin' 'Avalanche' 'Zec' 'Horizen']\n", "sub:  (7280, 36)\n", "pre:  (806371, 10)\n", "num:  (3480956, 10)\n"]}], "source": ["from secfsdstools.e_filter.rawfiltering import USDOnlyRawFilter\n", "\n", "# now filter only the datapoints for the holding company\n", "usdonly_filter = USDOnlyRawFilter()\n", "\n", "filtered_databag = databag[usdonly_filter]\n", "\n", "# let's check if just USD is kept as currency\n", "print(len(filtered_databag.num_df.uom.unique()), filtered_databag.num_df.uom.unique()[:30])\n", "\n", "# as expected, only the shape of the pre_df and num_df are different from the unfiltered dataframes\n", "print(\"sub: \", filtered_databag.sub_df.shape)\n", "print(\"pre: \", filtered_databag.pre_df.shape)\n", "print(\"num: \", filtered_databag.num_df.shape)"]}, {"cell_type": "markdown", "id": "5de81fa1-e529-45e5-a794-9ab7439e098b", "metadata": {}, "source": ["## `NoSegmentInfoRawFilter`\n", "\n", "In December of 2024, the SEC recreated all the datasets to include also segment information for datapoints. Segment information deliver additional information for a datapoint from different viewpoints or on different axis.\n", "\n", "For instance, a report normally contains the revenues of a company. However, with additional datapoints that contain the appropriate segment information, the report could also provide additional information that may show revenue for different region and/or the revenue for different products. So while the main number is reported with out segment information does provide the overall revenue, additional datapoints with segment information (but for the same tag) could provide more a breakdown on several \"axes\", like region or product.\n", "\n", "If you are not interested in a deeper breakdown of the data you can use the `NoSegmentInfoRawFilter` to remove all datapoints that contain 'segment' information."]}, {"cell_type": "code", "execution_count": 26, "id": "e9a18e9b-177f-4309-9ee8-c6aa51550685", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["153533 [''\n", " 'BusinessSegments=KingDigitalEntertainment;ConsolidationItems=OperatingSegments;ProductOrService=OtherDistributionChannels;'\n", " 'ClassOfStock=SeriesDPreferredStock;EquityComponents=PreferredStock;'\n", " 'Geographical=US;ProductOrService=UltraDeepwaterFloaters;'\n", " 'EquityComponents=AccumulatedOtherComprehensiveIncome;LegalEntity=AppalachianPowerCo;'\n", " 'EquityComponents=AccumulatedNetInvestmentGainLossIncludingPortionAttributableToNoncontrollingInterest;ReclassificationOutOfAccumulatedOtherComprehensiveIncome=ReclassificationOutOfAccumulatedOtherComprehensiveIncome;'\n", " 'Restatement=ScenarioPreviouslyReported;'\n", " 'EquityComponents=AccumulatedDistributionsInExcessOfNetIncome;'\n", " 'BusinessSegments=SpecialtySegment;'\n", " 'ClassOfStock=ConvertiblePreferredStock;EquityComponents=PreferredStock;'\n", " 'EquityComponents=SeriesAPreferredStocks;'\n", " 'FairValueByFairValueHierarchyLevel=FairValueInputsLevel1;FairValueByMeasurementFrequency=FairValueMeasurementsRecurring;FinancialInstrument=ExchangeTradedOptions;'\n", " 'ConsolidationItems=CorporateReconcilingItemsAndEliminations;'\n", " 'BusinessSegments=InvestmentSegment;'\n", " 'BusinessSegments=InvestmentInSunocoLP;'\n", " 'ClassOfStock=CommonClassI;EquityComponents=CommonStock;'\n", " 'IncomeStatementLocation=ScreenSystemSales;ProductOrService=Product;'\n", " 'Geographical=PermianDelawareBasin;ProductOrService=GasRevenues;'\n", " 'EquityComponents=RedeemableNoncontrollingInterest;'\n", " 'EquityComponents=AdditionalPaidInCapital;'\n", " 'LegalEntity=VirginiaElectricAndPowerCompany;'\n", " 'ConsolidationItems=ConsolidationEliminations;ProductOrService=MarketingCompetitiveRetailandRenewable;'\n", " 'IncomeStatementLocation=InterestIncomeExpenseNet;'\n", " 'ShareRepurchaseProgram=StockRepurchaseProgram;'\n", " 'ClassOfStock=SeriesCPreferredStock;'\n", " 'ConsolidatedEntities=GuarantorSubsidiaries;'\n", " 'FairValueByFairValueHierarchyLevel=FairValueInputsLevel1;'\n", " 'Counterparties=FrancoNevada;'\n", " 'RelatedPartyTransactionsByRelatedParty=ThirtyTwoSalesAgents;'\n", " 'LegalEntity=BCREDEmeraldJVLP;InvestmentIdentifier=Foundational Education Group, Inc.;']\n"]}], "source": ["# let's check how many different \"segment\" values are in all the reports of a single quarter, and display the first 30\n", "print(len(databag.num_df.segments.unique()), databag.num_df.segments.unique()[:30])"]}, {"cell_type": "code", "execution_count": 29, "id": "796e5089-b079-49c9-8129-9af40e62b896", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 ['']\n", "sub:  (7280, 36)\n", "pre:  (806371, 10)\n", "num:  (1749883, 10)\n"]}], "source": ["from secfsdstools.e_filter.rawfiltering import NoSegmentInfoRawFilter\n", "\n", "no_segment_filter = NoSegmentInfoRawFilter()\n", "\n", "filtered_databag = databag[no_segment_filter]\n", "\n", "# let us check the segment column again. as expected, there should only be datapoints with an emptystring in their segments column\n", "print(len(filtered_databag.num_df.segments.unique()), filtered_databag.num_df.segments.unique()[:30])\n", "\n", "# as expected, only the shape of the pre_df and num_df are different from the unfiltered dataframes\n", "print(\"sub: \", filtered_databag.sub_df.shape)\n", "print(\"pre: \", filtered_databag.pre_df.shape)\n", "print(\"num: \", filtered_databag.num_df.shape)"]}, {"cell_type": "markdown", "id": "393b8d3f-d274-4662-b7df-636c9bbf6612", "metadata": {}, "source": ["## Chaining Filters\n", "\n", "If you want to compare reports between companies and different years, it might make sense to apply several filters.\n", "\n", "For instance, you might only be interested in the datapoints of the actual period of a report (`ReportPeriodRawFilter`), you may only want to have datapoints of a holding company and don't care about the subsidiaries (`MainCoregRawFilter`), in order to have somewhat comparable data, you only want to have 'official' tags (`OfficialTagsOnlyRawFilter`), and finally, you only want datapoints in the USD (`USDOnlyRawFilter`).\n", "\n", "There are different ways how you could implement a filter chain."]}, {"cell_type": "code", "execution_count": 30, "id": "4cce79a7-036e-423c-8588-234794edeec9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sub:  (7280, 36)\n", "pre:  (714187, 10)\n", "num:  (1297892, 10)\n"]}], "source": ["# using a lambda function\n", "from typing import Callable\n", "from secfsdstools.d_container.databagmodel import RawDataBag\n", "from secfsdstools.e_filter.rawfiltering import ReportPeriodRawFilter, MainCoregRawFilter, OfficialTagsOnlyRawFilter, USDOnlyRawFilter\n", "\n", "filter_chain: Callable[[RawDataBag], RawDataBag] = lambda x: x[ReportPeriodRawFilter()][MainCoregRawFilter()][OfficialTagsOnlyRawFilter()][USDOnlyRawFilter()]\n", "\n", "filtered_databag = filter_chain(databag)\n", "\n", "print(\"sub: \", filtered_databag.sub_df.shape)\n", "print(\"pre: \", filtered_databag.pre_df.shape)\n", "print(\"num: \", filtered_databag.num_df.shape)"]}, {"cell_type": "code", "execution_count": 31, "id": "763efa52-7fc2-4047-b116-c8b5a01586f4", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sub:  (7280, 36)\n", "pre:  (714187, 10)\n", "num:  (1297892, 10)\n"]}], "source": ["# using a pure function\n", "from secfsdstools.d_container.databagmodel import RawDataBag\n", "from secfsdstools.e_filter.rawfiltering import ReportPeriodRawFilter, MainCoregRawFilter, OfficialTagsOnlyRawFilter, USDOnlyRawFilter\n", "\n", "def filter_func(databag: RawDataBag) -> RawDataBag:\n", "    return databag[ReportPeriodRawFilter()][MainCoregRawFilter()][OfficialTagsOnlyRawFilter()][USDOnlyRawFilter()]\n", "\n", "filtered_databag = filter_func(databag)\n", "\n", "print(\"sub: \", filtered_databag.sub_df.shape)\n", "print(\"pre: \", filtered_databag.pre_df.shape)\n", "print(\"num: \", filtered_databag.num_df.shape)"]}, {"cell_type": "code", "execution_count": 32, "id": "fc5859de-fd47-42cd-8ecc-49185cb27622", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sub:  (7280, 36)\n", "pre:  (714187, 10)\n", "num:  (1297892, 10)\n"]}], "source": ["# implementing your own filter class\n", "from secfsdstools.d_container.databagmodel import RawDataBag, FilterBase\n", "from secfsdstools.e_filter.rawfiltering import ReportPeriodRawFilter, MainCoregRawFilter, OfficialTagsOnlyRawFilter, USDOnlyRawFilter\n", "\n", "\n", "class FilterChain(FilterBase[RawDataBag]):\n", "    \n", "     def filter(self, databag: RawDataBag) -> RawDataBag:\n", "        return databag[ReportPeriodRawFilter()][MainCoregRawFilter()][OfficialTagsOnlyRawFilter()][USDOnlyRawFilter()]\n", "\n", "# implementing a filter class has the advantage that you can call the filter method on the databag, resp. that you can use the [] operator\n", "filtered_databag = databag[FilterChain()]\n", "\n", "print(\"sub: \", filtered_databag.sub_df.shape)\n", "print(\"pre: \", filtered_databag.pre_df.shape)\n", "print(\"num: \", filtered_databag.num_df.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "6761b082-ae22-4a13-b9fd-9c090cfc4b6d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "code", "execution_count": 1, "id": "9dac5d73-608a-4a10-bd4d-20826fa195d6", "metadata": {"tags": []}, "outputs": [], "source": ["# Basic import to support interactive widgets in notebooks\n", "import ipywidgets as widgets\n", "from IPython.display import display, Markdown\n", "from ipywidgets import interact, interact_manual\n", "\n", "import pandas as pd\n", "pd.set_option('display.max_rows', 500) # ensure that all rows are shown"]}, {"cell_type": "code", "execution_count": 20, "id": "fdcf62d3-23e4-4c88-b6dc-4613e1f26065", "metadata": {"tags": []}, "outputs": [], "source": ["from secfsdstools.d_container.databagmodel import JoinedDataBag\n", "from secfsdstools.e_filter.joinedfiltering import NoSegmentInfoJoinedFilter"]}, {"cell_type": "code", "execution_count": 4, "id": "f99931d8-6800-40fb-b5f0-636de0eee3a6", "metadata": {"tags": []}, "outputs": [], "source": ["data_dir = \"c:/data/sec/automated/_2_all/_1_joined_by_stmt/BS\""]}, {"cell_type": "code", "execution_count": 19, "id": "1c3d699f-ca4a-492b-8d34-0d4130d594ab", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(20107441, 17)\n"]}], "source": ["bs_bag = JoinedDataBag.load(data_dir)\n", "print(bs_bag.pre_num_df.shape)"]}, {"cell_type": "code", "execution_count": 21, "id": "a72b6f26-2d6e-48d3-80d3-80669ecc2044", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(11541310, 17)\n"]}], "source": ["no_segments = bs_bag[NoSegmentInfoJoinedFilter()]\n", "print(no_segments.pre_num_df.shape)"]}, {"cell_type": "code", "execution_count": 25, "id": "61a79285-ad09-444f-8612-af7649e55c13", "metadata": {}, "outputs": [], "source": ["value_counts = no_segments.pre_num_df.tag.value_counts()"]}, {"cell_type": "code", "execution_count": 26, "id": "6e42fa9b-517e-4d59-bba8-421f63348007", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["Index(['Assets', 'LiabilitiesAndStockholdersEquity', 'StockholdersEquity',\n", "       'RetainedEarningsAccumulatedDeficit', 'CommonStockValue',\n", "       'CashAndCashEquivalentsAtCarryingValue', 'Liabilities',\n", "       'CommonStockSharesAuthorized', 'AssetsCurrent', 'LiabilitiesCurrent',\n", "       ...\n", "       'PaymentsToAcquireRoyaltyInterestsInMiningProperties',\n", "       'DividendsPreferredStockCash',\n", "       'IncreaseDecreaseInAccruedIncomeTaxesPayable',\n", "       'PostconfirmationStockholdersEquity',\n", "       'SharesHeldInEmployeeStockOptionPlanCommittedToBeReleased',\n", "       'ProvisionForLoanLeaseAndOtherLosses',\n", "       'OtherDerivativesNotDesignatedAsHedgingInstrumentsAtFairValueNet',\n", "       'CapitalLeasesNetInvestmentInDirectFinancingLeasesAccumulatedAmortization',\n", "       'OtherComprehensiveIncomeReclassificationOfDefinedBenefitPlansNetGainLossRecognizedInNetPeriodicBenefitCostTax',\n", "       'ConstructionContractorReceivableRetainageAfterYearOne'],\n", "      dtype='object', length=3103)"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["value_counts.index"]}, {"cell_type": "code", "execution_count": 27, "id": "4c02785a-8f39-484e-9172-0d877627ecb4", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["245279"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["value_counts['CommonStockSharesOutstanding']"]}, {"cell_type": "code", "execution_count": 28, "id": "2dd75fff-f7f9-4f60-8815-c18f7b5ada30", "metadata": {"tags": []}, "outputs": [], "source": ["shares_outstanding = no_segments.pre_num_df[no_segments.pre_num_df.tag=='CommonStockSharesOutstanding']"]}, {"cell_type": "code", "execution_count": 29, "id": "41d00dee-235a-4dc0-8dd8-cb89b04a0bed", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>adsh</th>\n", "      <th>tag</th>\n", "      <th>version</th>\n", "      <th>ddate</th>\n", "      <th>qtrs</th>\n", "      <th>uom</th>\n", "      <th>segments</th>\n", "      <th>coreg</th>\n", "      <th>value</th>\n", "      <th>footnote</th>\n", "      <th>report</th>\n", "      <th>line</th>\n", "      <th>stmt</th>\n", "      <th>inpth</th>\n", "      <th>rfile</th>\n", "      <th>plabel</th>\n", "      <th>negating</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>111</th>\n", "      <td>0000714562-25-000010</td>\n", "      <td>CommonStockSharesOutstanding</td>\n", "      <td>us-gaap/2024</td>\n", "      <td>20241231</td>\n", "      <td>0</td>\n", "      <td>shares</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>11842539.0</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>BS</td>\n", "      <td>1</td>\n", "      <td>H</td>\n", "      <td>Common stock, Outstanding shares</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>136</th>\n", "      <td>0001493152-25-008460</td>\n", "      <td>CommonStockSharesOutstanding</td>\n", "      <td>us-gaap/2024</td>\n", "      <td>20241231</td>\n", "      <td>0</td>\n", "      <td>shares</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>14500685.0</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "      <td>8</td>\n", "      <td>BS</td>\n", "      <td>1</td>\n", "      <td>H</td>\n", "      <td>Common stock, shares outstanding</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>354</th>\n", "      <td>0001393905-25-000073</td>\n", "      <td>CommonStockSharesOutstanding</td>\n", "      <td>us-gaap/2024</td>\n", "      <td>20230930</td>\n", "      <td>0</td>\n", "      <td>shares</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>7800000.0</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>BS</td>\n", "      <td>1</td>\n", "      <td>H</td>\n", "      <td>Common Stock, Shares, Outstanding</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>395</th>\n", "      <td>0001558370-25-001826</td>\n", "      <td>CommonStockSharesOutstanding</td>\n", "      <td>us-gaap/2024</td>\n", "      <td>20241231</td>\n", "      <td>0</td>\n", "      <td>shares</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>171860000.0</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "      <td>6</td>\n", "      <td>BS</td>\n", "      <td>1</td>\n", "      <td>H</td>\n", "      <td>Ordinary shares, shares outstanding</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>421</th>\n", "      <td>0001628280-25-007202</td>\n", "      <td>CommonStockSharesOutstanding</td>\n", "      <td>us-gaap/2024</td>\n", "      <td>20241231</td>\n", "      <td>0</td>\n", "      <td>shares</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>32527244.0</td>\n", "      <td>None</td>\n", "      <td>4</td>\n", "      <td>13</td>\n", "      <td>BS</td>\n", "      <td>1</td>\n", "      <td>H</td>\n", "      <td>Common stock, shares outstanding (in shares)</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>492</th>\n", "      <td>0000950170-25-027757</td>\n", "      <td>CommonStockSharesOutstanding</td>\n", "      <td>us-gaap/2024</td>\n", "      <td>20241231</td>\n", "      <td>0</td>\n", "      <td>shares</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>27677000.0</td>\n", "      <td>None</td>\n", "      <td>5</td>\n", "      <td>4</td>\n", "      <td>BS</td>\n", "      <td>1</td>\n", "      <td>H</td>\n", "      <td>Common stock, shares outstanding</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>663</th>\n", "      <td>0001224608-25-000006</td>\n", "      <td>CommonStockSharesOutstanding</td>\n", "      <td>us-gaap/2024</td>\n", "      <td>20241231</td>\n", "      <td>0</td>\n", "      <td>shares</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>101618957.0</td>\n", "      <td>None</td>\n", "      <td>4</td>\n", "      <td>14</td>\n", "      <td>BS</td>\n", "      <td>1</td>\n", "      <td>H</td>\n", "      <td>Common stock, shares outstanding (in shares)</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>665</th>\n", "      <td>0001493152-25-011084</td>\n", "      <td>CommonStockSharesOutstanding</td>\n", "      <td>us-gaap/2024</td>\n", "      <td>20241231</td>\n", "      <td>0</td>\n", "      <td>shares</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>3652285.0</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "      <td>13</td>\n", "      <td>BS</td>\n", "      <td>1</td>\n", "      <td>H</td>\n", "      <td>Common stock, shares outstanding</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>678</th>\n", "      <td>0001628280-25-012407</td>\n", "      <td>CommonStockSharesOutstanding</td>\n", "      <td>us-gaap/2024</td>\n", "      <td>20241231</td>\n", "      <td>0</td>\n", "      <td>shares</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>124924185.0</td>\n", "      <td>None</td>\n", "      <td>4</td>\n", "      <td>5</td>\n", "      <td>BS</td>\n", "      <td>1</td>\n", "      <td>H</td>\n", "      <td>Common stock, shares outstanding (in shares)</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>711</th>\n", "      <td>0001640334-25-000058</td>\n", "      <td>CommonStockSharesOutstanding</td>\n", "      <td>us-gaap/2024</td>\n", "      <td>20241130</td>\n", "      <td>0</td>\n", "      <td>shares</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>17452594.0</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>BS</td>\n", "      <td>1</td>\n", "      <td>H</td>\n", "      <td>Common stock, shares, outstanding</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                     adsh                           tag       version  \\\n", "111  0000714562-25-000010  CommonStockSharesOutstanding  us-gaap/2024   \n", "136  0001493152-25-008460  CommonStockSharesOutstanding  us-gaap/2024   \n", "354  0001393905-25-000073  CommonStockSharesOutstanding  us-gaap/2024   \n", "395  0001558370-25-001826  CommonStockSharesOutstanding  us-gaap/2024   \n", "421  0001628280-25-007202  CommonStockSharesOutstanding  us-gaap/2024   \n", "492  0000950170-25-027757  CommonStockSharesOutstanding  us-gaap/2024   \n", "663  0001224608-25-000006  CommonStockSharesOutstanding  us-gaap/2024   \n", "665  0001493152-25-011084  CommonStockSharesOutstanding  us-gaap/2024   \n", "678  0001628280-25-012407  CommonStockSharesOutstanding  us-gaap/2024   \n", "711  0001640334-25-000058  CommonStockSharesOutstanding  us-gaap/2024   \n", "\n", "        ddate  qtrs     uom segments coreg        value footnote  report  \\\n", "111  20241231     0  shares                  11842539.0     None       3   \n", "136  20241231     0  shares                  14500685.0     None       3   \n", "354  20230930     0  shares                   7800000.0     None       3   \n", "395  20241231     0  shares                 171860000.0     None       3   \n", "421  20241231     0  shares                  32527244.0     None       4   \n", "492  20241231     0  shares                  27677000.0     None       5   \n", "663  20241231     0  shares                 101618957.0     None       4   \n", "665  20241231     0  shares                   3652285.0     None       3   \n", "678  20241231     0  shares                 124924185.0     None       4   \n", "711  20241130     0  shares                  17452594.0     None       3   \n", "\n", "     line stmt  inpth rfile                                        plabel  \\\n", "111     4   BS      1     H              Common stock, Outstanding shares   \n", "136     8   BS      1     H              Common stock, shares outstanding   \n", "354     3   BS      1     H             Common Stock, Shares, Outstanding   \n", "395     6   BS      1     H           Ordinary shares, shares outstanding   \n", "421    13   BS      1     H  Common stock, shares outstanding (in shares)   \n", "492     4   BS      1     H              Common stock, shares outstanding   \n", "663    14   BS      1     H  Common stock, shares outstanding (in shares)   \n", "665    13   BS      1     H              Common stock, shares outstanding   \n", "678     5   BS      1     H  Common stock, shares outstanding (in shares)   \n", "711     4   BS      1     H             Common stock, shares, outstanding   \n", "\n", "     negating  \n", "111         0  \n", "136         0  \n", "354         0  \n", "395         0  \n", "421         0  \n", "492         0  \n", "663         0  \n", "665         0  \n", "678         0  \n", "711         0  "]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["shares_outstanding[:10]"]}, {"cell_type": "code", "execution_count": null, "id": "ada4500d-8eac-443c-9de3-7557bcce5e7c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "code", "execution_count": 1, "id": "85c29dd4-27dc-4861-bfa6-5c56e3aa58be", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "# ensure that all columns are shown and that colum content is not cut\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.max_colwidth', None)\n", "pd.set_option('display.width',1000)\n", "pd.set_option('display.max_rows', 500) # ensure that all rows are shown"]}, {"cell_type": "markdown", "id": "b6109579-ebbe-4258-9ec4-f3255c9c32d6", "metadata": {}, "source": ["# `BalanceSheetStandardizer`"]}, {"cell_type": "markdown", "id": "975d90d1-b0fd-4721-870a-4bc2ba83715e", "metadata": {"tags": []}, "source": ["<span style=\"color: #FF8C00;\">==========================================================</span>\n", "\n", "**If you find this tool useful, a sponsorship would be greatly appreciated!**\n", "\n", "**https://github.com/sponsors/HansjoergW**\n", "\n", "How to get in touch\n", "\n", "* Found a bug: https://github.com/HansjoergW/sec-fincancial-statement-data-set/issues\n", "* Have a remark: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/general\n", "* Have an idea: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/ideas\n", "* Have a question: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/q-a\n", "* Have something to show: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/show-and-tell\n", "\n", "<span style=\"color: #FF8C00;\">==========================================================</span>"]}, {"cell_type": "markdown", "id": "1f4c69ae-dd2e-4b70-9c4c-0788792576bb", "metadata": {}, "source": ["In the `07_00_stanardizer_basics.ipynb` we looked at the basic principles of the standardizer. And now we are going to explore the details of the `BalanceSheetStandardizer`."]}, {"cell_type": "markdown", "id": "bc6364c8-0ab6-49fa-86b3-2c051f1b5d8f", "metadata": {}, "source": ["## Main Goal\n", "The main Goal of the `BalanceSheetStandardizer` is to provide a consilidated, standardized view that contains the main positions of a balance sheet.\n", "\n", "The current implementation tries to find/calculate the values for the following positions:\n", "\n", "- Assets\n", "    - Assets<PERSON><PERSON>rent\n", "        - Cash\n", "    - AssetsNoncurrent\n", "- Liabilities\n", "    - LiabilitiesCurrent\n", "    - LiabilitiesNoncurrent\n", "- Equity\n", "    - HolderEquity (mainly StockholderEquity or PartnerCapital)\n", "        - RetainedEarnings\n", "        - AdditionalPaidInCapital\n", "        - TreasuryStockValue\n", "    - TemporaryEquity\n", "    - RedeemableEquity\n", "- LiabilitiesAndEquity\n"]}, {"cell_type": "markdown", "id": "c83fb1c5-a9d9-4f3f-aceb-9edca099d66a", "metadata": {}, "source": ["## Prepare the dataset"]}, {"cell_type": "markdown", "id": "8028c20e-7844-4d49-ab43-133603817d53", "metadata": {}, "source": ["As input, we are going to use the dataset which was created with the `06_bulk_data_processing_deep_dive.ipynb`. That dataset contains all available data for balance sheets. The path to this dataset - on my machine - is either `set/parallel/BS/joined` or `set/serial/BS/joined` depending whether it was produced with the faster parallel or slower serial processing approach.\n", "\n", "The data is already filtered for 10-K and 10-Q reports. Moreover, the following filters were applied as well: `ReportPeriodRawFilter`, `MainCoregRawFilter`, `OfficialTagsOnlyRawFilter`, `USDOnlyRawFilter`. The dataset is already joined, so we can use it directly with the `BalanceSheetStandardizer`.\n", "\n", "Of course, if you prefer another dataset, for instance all data of a few companies, or all data of a single year, feel free to do so.\n", "\n", "\n", "    # As an alternative, using the data of a single year\n", "    from secfsdstools.d_container.databagmodel import JoinedDataBag\n", "    from secfsdstools.e_collector.zipcollecting import ZipCollector\n", "    from secfsdstools.u_usecases.bulk_loading import default_postloadfilter\n", "\n", "    collector = ZipCollector.get_zip_by_names(names=[\"2022q1.zip\", \"2022q2.zip\", \"2022q3.zip\", \"2022q4.zip\"], \n", "                                              forms_filter=[\"10-K\", \"10-Q\"],                                        \n", "                                              stmt_filter=[\"BS\"], post_load_filter=default_postloadfilter)\n", "\n", "    all_bs_joinedbag: JoinedDataBag = collector.collect().join()\n", "    \n", "    from secfsdstools.d_container.databagmodel import JoinedDataBag\n", "    from secfsdstools.f_standardize.bs_standardize import BalanceSheetStandardizer\n", "\n", "    bs_standardizer = BalanceSheetStandardizer()\n", "\n", "    # standardize the data\n", "    all_bs_joinedbag.present(bs_standardizer)"]}, {"cell_type": "code", "execution_count": 3, "id": "7c4c7669-020a-40a5-b260-e5f0f5b03ebd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loading data...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-04 06:22:36,001 [INFO] standardizing  start PRE processing ...\n", "2025-02-04 06:22:46,701 [INFO] standardizing  start MAIN processing ...\n", "2025-02-04 06:22:47,979 [INFO] standardizing  start POST processing ...\n", "2025-02-04 06:22:48,085 [INFO] standardizing  start FINALIZE ...\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>adsh</th>\n", "      <th>cik</th>\n", "      <th>name</th>\n", "      <th>form</th>\n", "      <th>fye</th>\n", "      <th>fy</th>\n", "      <th>fp</th>\n", "      <th>date</th>\n", "      <th>filed</th>\n", "      <th>coreg</th>\n", "      <th>report</th>\n", "      <th>ddate</th>\n", "      <th>qtrs</th>\n", "      <th>Assets</th>\n", "      <th>AssetsCurrent</th>\n", "      <th>Cash</th>\n", "      <th>AssetsNoncurrent</th>\n", "      <th>Liabilities</th>\n", "      <th>LiabilitiesCurrent</th>\n", "      <th>LiabilitiesNoncurrent</th>\n", "      <th>Equity</th>\n", "      <th>HolderEquity</th>\n", "      <th>RetainedEarnings</th>\n", "      <th>AdditionalPaidInCapital</th>\n", "      <th>TreasuryStockValue</th>\n", "      <th>TemporaryEquity</th>\n", "      <th>RedeemableEquity</th>\n", "      <th>LiabilitiesAndEquity</th>\n", "      <th>Assets<PERSON><PERSON>ck_error</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON>_cat</th>\n", "      <th>LiabilitiesCheck_error</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON>_cat</th>\n", "      <th>EquityCheck_error</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON>_cat</th>\n", "      <th>AssetsLiaEquCheck_error</th>\n", "      <th>AssetsLiaEquCheck_cat</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>200445</th>\n", "      <td>0001096906-21-001168</td>\n", "      <td>1089297</td>\n", "      <td>NOVAGANT CORP</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2004.0</td>\n", "      <td>FY</td>\n", "      <td>2004-12-31</td>\n", "      <td>20210517</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20041231</td>\n", "      <td>0</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>9.145700e+04</td>\n", "      <td>9.145700e+04</td>\n", "      <td>0.000000e+00</td>\n", "      <td>-9.145700e+04</td>\n", "      <td>-9.145700e+04</td>\n", "      <td>-1.832780e+07</td>\n", "      <td>1.821434e+07</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>200446</th>\n", "      <td>0001096906-21-001172</td>\n", "      <td>1089297</td>\n", "      <td>NOVAGANT CORP</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2005.0</td>\n", "      <td>FY</td>\n", "      <td>2005-12-31</td>\n", "      <td>20210517</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20051231</td>\n", "      <td>0</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>9.145700e+04</td>\n", "      <td>9.145700e+04</td>\n", "      <td>0.000000e+00</td>\n", "      <td>-9.145700e+04</td>\n", "      <td>-9.145700e+04</td>\n", "      <td>-1.832780e+07</td>\n", "      <td>1.821434e+07</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>200449</th>\n", "      <td>0001096906-21-001180</td>\n", "      <td>1089297</td>\n", "      <td>NOVAGANT CORP</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2006.0</td>\n", "      <td>FY</td>\n", "      <td>2006-12-31</td>\n", "      <td>20210517</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20061231</td>\n", "      <td>0</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>9.145700e+04</td>\n", "      <td>9.145700e+04</td>\n", "      <td>0.000000e+00</td>\n", "      <td>-9.145700e+04</td>\n", "      <td>-9.145700e+04</td>\n", "      <td>-1.832780e+07</td>\n", "      <td>1.821434e+07</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>200450</th>\n", "      <td>0001096906-21-001182</td>\n", "      <td>1089297</td>\n", "      <td>NOVAGANT CORP</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2007.0</td>\n", "      <td>FY</td>\n", "      <td>2007-12-31</td>\n", "      <td>20210517</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20071231</td>\n", "      <td>0</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>9.145700e+04</td>\n", "      <td>9.145700e+04</td>\n", "      <td>0.000000e+00</td>\n", "      <td>-9.145700e+04</td>\n", "      <td>-9.145700e+04</td>\n", "      <td>-1.832780e+07</td>\n", "      <td>1.821434e+07</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>200451</th>\n", "      <td>0001096906-21-001184</td>\n", "      <td>1089297</td>\n", "      <td>NOVAGANT CORP</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2008.0</td>\n", "      <td>FY</td>\n", "      <td>2008-12-31</td>\n", "      <td>20210517</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20081231</td>\n", "      <td>0</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>9.145700e+04</td>\n", "      <td>9.145700e+04</td>\n", "      <td>0.000000e+00</td>\n", "      <td>-9.145700e+04</td>\n", "      <td>-9.145700e+04</td>\n", "      <td>-1.832780e+07</td>\n", "      <td>1.821434e+07</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>161593</th>\n", "      <td>0000723125-24-000047</td>\n", "      <td>723125</td>\n", "      <td>MICRON TECHNOLOGY INC</td>\n", "      <td>10-Q</td>\n", "      <td>0831</td>\n", "      <td>2025.0</td>\n", "      <td>Q1</td>\n", "      <td>2024-11-30</td>\n", "      <td>20241219</td>\n", "      <td></td>\n", "      <td>4</td>\n", "      <td>20241130</td>\n", "      <td>0</td>\n", "      <td>7.146100e+10</td>\n", "      <td>2.449300e+10</td>\n", "      <td>6.693000e+09</td>\n", "      <td>4.696800e+10</td>\n", "      <td>2.466400e+10</td>\n", "      <td>9.015000e+09</td>\n", "      <td>1.564900e+10</td>\n", "      <td>4.679700e+10</td>\n", "      <td>4.679700e+10</td>\n", "      <td>4.242700e+10</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>7.146100e+10</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>163923</th>\n", "      <td>0000909832-24-000079</td>\n", "      <td>909832</td>\n", "      <td>COSTCO WHOLESALE CORP /NEW</td>\n", "      <td>10-Q</td>\n", "      <td>0831</td>\n", "      <td>2025.0</td>\n", "      <td>Q1</td>\n", "      <td>2024-11-30</td>\n", "      <td>20241219</td>\n", "      <td></td>\n", "      <td>4</td>\n", "      <td>20241130</td>\n", "      <td>0</td>\n", "      <td>7.338600e+10</td>\n", "      <td>3.752300e+10</td>\n", "      <td>1.090700e+10</td>\n", "      <td>3.586300e+10</td>\n", "      <td>4.893500e+10</td>\n", "      <td>3.828900e+10</td>\n", "      <td>1.064600e+10</td>\n", "      <td>2.445100e+10</td>\n", "      <td>2.445100e+10</td>\n", "      <td>1.870000e+10</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>7.338600e+10</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>161657</th>\n", "      <td>0001193125-24-281288</td>\n", "      <td>40704</td>\n", "      <td>GENERAL MILLS INC</td>\n", "      <td>10-Q</td>\n", "      <td>0531</td>\n", "      <td>2025.0</td>\n", "      <td>Q2</td>\n", "      <td>2024-11-30</td>\n", "      <td>20241218</td>\n", "      <td></td>\n", "      <td>4</td>\n", "      <td>20241130</td>\n", "      <td>0</td>\n", "      <td>3.339610e+10</td>\n", "      <td>7.381400e+09</td>\n", "      <td>2.292800e+09</td>\n", "      <td>2.601470e+10</td>\n", "      <td>2.394690e+10</td>\n", "      <td>8.024300e+09</td>\n", "      <td>1.592260e+10</td>\n", "      <td>9.449200e+09</td>\n", "      <td>9.449200e+09</td>\n", "      <td>2.134030e+10</td>\n", "      <td>0.000000e+00</td>\n", "      <td>-1.087330e+10</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3.339610e+10</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>162289</th>\n", "      <td>0001640334-24-001969</td>\n", "      <td>1584480</td>\n", "      <td>STARTECH LABS, INC.</td>\n", "      <td>10-Q</td>\n", "      <td>0531</td>\n", "      <td>2025.0</td>\n", "      <td>Q2</td>\n", "      <td>2024-11-30</td>\n", "      <td>20241231</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20241130</td>\n", "      <td>0</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>NaN</td>\n", "      <td>0.000000e+00</td>\n", "      <td>4.106260e+05</td>\n", "      <td>4.106260e+05</td>\n", "      <td>0.000000e+00</td>\n", "      <td>-4.106260e+05</td>\n", "      <td>-4.106260e+05</td>\n", "      <td>-3.912590e+07</td>\n", "      <td>3.865462e+07</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>238421</th>\n", "      <td>0001835681-24-000069</td>\n", "      <td>1835681</td>\n", "      <td>POWERSCHOOL HOLDINGS, INC.</td>\n", "      <td>10-Q</td>\n", "      <td>1231</td>\n", "      <td>2024.0</td>\n", "      <td>Q1</td>\n", "      <td>2024-12-31</td>\n", "      <td>20240507</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20241231</td>\n", "      <td>0</td>\n", "      <td>3.766867e+09</td>\n", "      <td>1.301550e+08</td>\n", "      <td>1.742500e+07</td>\n", "      <td>3.636712e+09</td>\n", "      <td>2.022553e+09</td>\n", "      <td>5.362210e+08</td>\n", "      <td>1.486332e+09</td>\n", "      <td>1.744314e+09</td>\n", "      <td>1.744314e+09</td>\n", "      <td>-2.379450e+08</td>\n", "      <td>1.532371e+09</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3.766867e+09</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>346570 rows × 36 columns</p>\n", "</div>"], "text/plain": ["                        adsh      cik                        name  form   fye      fy  fp       date     filed coreg  report     ddate  qtrs        Assets  AssetsCurrent          Cash  AssetsNoncurrent   Liabilities  LiabilitiesCurrent  LiabilitiesNoncurrent        Equity  HolderEquity  RetainedEarnings  AdditionalPaidInCapital  TreasuryStockValue  TemporaryEquity  RedeemableEquity  LiabilitiesAndEquity  AssetsCheck_error  AssetsCheck_cat  LiabilitiesCheck_error  LiabilitiesCheck_cat  EquityCheck_error  EquityCheck_cat  AssetsLiaEquCheck_error  AssetsLiaEquCheck_cat\n", "200445  0001096906-21-001168  1089297               NOVAGANT CORP  10-K  1231  2004.0  FY 2004-12-31  20210517             2  20041231     0  0.000000e+00   0.000000e+00  0.000000e+00      0.000000e+00  9.145700e+04        9.145700e+04           0.000000e+00 -9.145700e+04 -9.145700e+04     -1.832780e+07             1.821434e+07        0.000000e+00              0.0               0.0          0.000000e+00                0.0              0.0                     0.0                   0.0                0.0              0.0                      0.0                    0.0\n", "200446  0001096906-21-001172  1089297               NOVAGANT CORP  10-K  1231  2005.0  FY 2005-12-31  20210517             2  20051231     0  0.000000e+00   0.000000e+00  0.000000e+00      0.000000e+00  9.145700e+04        9.145700e+04           0.000000e+00 -9.145700e+04 -9.145700e+04     -1.832780e+07             1.821434e+07        0.000000e+00              0.0               0.0          0.000000e+00                0.0              0.0                     0.0                   0.0                0.0              0.0                      0.0                    0.0\n", "200449  0001096906-21-001180  1089297               NOVAGANT CORP  10-K  1231  2006.0  FY 2006-12-31  20210517             2  20061231     0  0.000000e+00   0.000000e+00  0.000000e+00      0.000000e+00  9.145700e+04        9.145700e+04           0.000000e+00 -9.145700e+04 -9.145700e+04     -1.832780e+07             1.821434e+07        0.000000e+00              0.0               0.0          0.000000e+00                0.0              0.0                     0.0                   0.0                0.0              0.0                      0.0                    0.0\n", "200450  0001096906-21-001182  1089297               NOVAGANT CORP  10-K  1231  2007.0  FY 2007-12-31  20210517             2  20071231     0  0.000000e+00   0.000000e+00  0.000000e+00      0.000000e+00  9.145700e+04        9.145700e+04           0.000000e+00 -9.145700e+04 -9.145700e+04     -1.832780e+07             1.821434e+07        0.000000e+00              0.0               0.0          0.000000e+00                0.0              0.0                     0.0                   0.0                0.0              0.0                      0.0                    0.0\n", "200451  0001096906-21-001184  1089297               NOVAGANT CORP  10-K  1231  2008.0  FY 2008-12-31  20210517             2  20081231     0  0.000000e+00   0.000000e+00  0.000000e+00      0.000000e+00  9.145700e+04        9.145700e+04           0.000000e+00 -9.145700e+04 -9.145700e+04     -1.832780e+07             1.821434e+07        0.000000e+00              0.0               0.0          0.000000e+00                0.0              0.0                     0.0                   0.0                0.0              0.0                      0.0                    0.0\n", "...                      ...      ...                         ...   ...   ...     ...  ..        ...       ...   ...     ...       ...   ...           ...            ...           ...               ...           ...                 ...                    ...           ...           ...               ...                      ...                 ...              ...               ...                   ...                ...              ...                     ...                   ...                ...              ...                      ...                    ...\n", "161593  0000723125-24-000047   723125       MICRON TECHNOLOGY INC  10-Q  0831  2025.0  Q1 2024-11-30  20241219             4  20241130     0  7.146100e+10   2.449300e+10  6.693000e+09      4.696800e+10  2.466400e+10        9.015000e+09           1.564900e+10  4.679700e+10  4.679700e+10      4.242700e+10             0.000000e+00        0.000000e+00              0.0               0.0          7.146100e+10                0.0              0.0                     0.0                   0.0                0.0              0.0                      0.0                    0.0\n", "163923  0000909832-24-000079   909832  COSTCO WHOLESALE CORP /NEW  10-Q  0831  2025.0  Q1 2024-11-30  20241219             4  20241130     0  7.338600e+10   3.752300e+10  1.090700e+10      3.586300e+10  4.893500e+10        3.828900e+10           1.064600e+10  2.445100e+10  2.445100e+10      1.870000e+10             0.000000e+00        0.000000e+00              0.0               0.0          7.338600e+10                0.0              0.0                     0.0                   0.0                0.0              0.0                      0.0                    0.0\n", "161657  0001193125-24-281288    40704           GENERAL MILLS INC  10-Q  0531  2025.0  Q2 2024-11-30  20241218             4  20241130     0  3.339610e+10   7.381400e+09  2.292800e+09      2.601470e+10  2.394690e+10        8.024300e+09           1.592260e+10  9.449200e+09  9.449200e+09      2.134030e+10             0.000000e+00       -1.087330e+10              0.0               0.0          3.339610e+10                0.0              0.0                     0.0                   0.0                0.0              0.0                      0.0                    0.0\n", "162289  0001640334-24-001969  1584480         STARTECH LABS, INC.  10-Q  0531  2025.0  Q2 2024-11-30  20241231             2  20241130     0  0.000000e+00   0.000000e+00           NaN      0.000000e+00  4.106260e+05        4.106260e+05           0.000000e+00 -4.106260e+05 -4.106260e+05     -3.912590e+07             3.865462e+07        0.000000e+00              0.0               0.0          0.000000e+00                0.0              0.0                     0.0                   0.0                0.0              0.0                      0.0                    0.0\n", "238421  0001835681-24-000069  1835681  POWERSCHOOL HOLDINGS, INC.  10-Q  1231  2024.0  Q1 2024-12-31  20240507             2  20241231     0  3.766867e+09   1.301550e+08  1.742500e+07      3.636712e+09  2.022553e+09        5.362210e+08           1.486332e+09  1.744314e+09  1.744314e+09     -2.379450e+08             1.532371e+09        0.000000e+00              0.0               0.0          3.766867e+09                0.0              0.0                     0.0                   0.0                0.0              0.0                      0.0                    0.0\n", "\n", "[346570 rows x 36 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from secfsdstools.d_container.databagmodel import JoinedDataBag\n", "from secfsdstools.f_standardize.bs_standardize import BalanceSheetStandardizer\n", "\n", "print(\"loading data...\")\n", "all_bs_joinedbag:JoinedDataBag = JoinedDataBag.load(target_path=\"set/parallel/BS/joined\")\n", "bs_standardizer = BalanceSheetStandardizer()\n", "\n", "# standardize the data\n", "all_bs_joinedbag.present(bs_standardizer)"]}, {"cell_type": "markdown", "id": "f42c9d12-f3ca-40d9-ad34-96560aab033b", "metadata": {}, "source": ["At first, we will save the results, including all the logs, so that we can use it directly in the future, without the need to process it again.<br>\n", "**Note:** you need to create the target directory before storing the data"]}, {"cell_type": "code", "execution_count": 4, "id": "47953a5f-b05b-4563-990e-439b62f7d80f", "metadata": {}, "outputs": [], "source": ["import os\n", "target_path = \"standardized/BS\"\n", "os.makedirs(target_path, exist_ok=True)\n", "\n", "bs_standardizer.get_standardize_bag().save(target_path)"]}, {"cell_type": "markdown", "id": "e715bbc9-88e1-482a-8448-f9815d831687", "metadata": {}, "source": ["## Load the dataset\n", "Once the data has been standardized and saved, you can load it directly."]}, {"cell_type": "code", "execution_count": 6, "id": "7f7576ec-6e5a-42ba-bdd0-eabda14e9ee2", "metadata": {"tags": []}, "outputs": [], "source": ["from secfsdstools.f_standardize.standardizing import StandardizedBag\n", "\n", "bs_standardizer_result_bag = StandardizedBag.load(\"standardized/BS\")"]}, {"cell_type": "markdown", "id": "d7e8f196-bf59-45a2-a6e0-5c547a4d7b1b", "metadata": {}, "source": ["## Overview"]}, {"cell_type": "markdown", "id": "3f338fcc-5c17-45d4-886f-3ce551c74820", "metadata": {}, "source": ["Before we dive into what the `BalanceSheetStandardizer` does, lets get a first impression of the the produced data. First, let us see how many rows we have."]}, {"cell_type": "code", "execution_count": 7, "id": "77709469-52b9-46e0-a334-9fc771f165ae", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["346570"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["len(bs_standardizer_result_bag.result_df)"]}, {"cell_type": "markdown", "id": "56607925-ed18-4ab6-b5c4-c7c7bbf8e454", "metadata": {}, "source": ["Next, a good idea is to look at the `validation_overview_df`. This table gives an idea about the \"quality\" of the dateset based on the summary of the results of the applied validation rules."]}, {"cell_type": "code", "execution_count": 8, "id": "4261f80a-6990-472b-9c06-8a90ecb250ab", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON>_cat</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON>_cat</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON>_cat</th>\n", "      <th>AssetsLiaEquCheck_cat</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON>_cat_pct</th>\n", "      <th>Lia<PERSON><PERSON><PERSON><PERSON>_cat_pct</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON>_cat_pct</th>\n", "      <th>AssetsLiaEquCheck_cat_pct</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>341277</td>\n", "      <td>329215</td>\n", "      <td>325014</td>\n", "      <td>324751</td>\n", "      <td>98.47</td>\n", "      <td>94.99</td>\n", "      <td>93.78</td>\n", "      <td>93.70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>333</td>\n", "      <td>2226</td>\n", "      <td>6409</td>\n", "      <td>6549</td>\n", "      <td>0.10</td>\n", "      <td>0.64</td>\n", "      <td>1.85</td>\n", "      <td>1.89</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>614</td>\n", "      <td>3499</td>\n", "      <td>3578</td>\n", "      <td>3616</td>\n", "      <td>0.18</td>\n", "      <td>1.01</td>\n", "      <td>1.03</td>\n", "      <td>1.04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>487</td>\n", "      <td>2841</td>\n", "      <td>1406</td>\n", "      <td>1425</td>\n", "      <td>0.14</td>\n", "      <td>0.82</td>\n", "      <td>0.41</td>\n", "      <td>0.41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>100</th>\n", "      <td>3859</td>\n", "      <td>8789</td>\n", "      <td>9892</td>\n", "      <td>9958</td>\n", "      <td>1.11</td>\n", "      <td>2.54</td>\n", "      <td>2.85</td>\n", "      <td>2.87</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     AssetsCheck_cat  LiabilitiesCheck_cat  EquityCheck_cat  AssetsLiaEquCheck_cat  AssetsCheck_cat_pct  LiabilitiesCheck_cat_pct  EquityCheck_cat_pct  AssetsLiaEquCheck_cat_pct\n", "0             341277                329215           325014                 324751                98.47                     94.99                93.78                      93.70\n", "1                333                  2226             6409                   6549                 0.10                      0.64                 1.85                       1.89\n", "5                614                  3499             3578                   3616                 0.18                      1.01                 1.03                       1.04\n", "10               487                  2841             1406                   1425                 0.14                      0.82                 0.41                       0.41\n", "100             3859                  8789             9892                   9958                 1.11                      2.54                 2.85                       2.87"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["bs_standardizer_result_bag.validation_overview_df"]}, {"cell_type": "markdown", "id": "e9bdfa26-163e-4d78-b7ab-af4f336a4045", "metadata": {}, "source": ["This seems to be quite ok, since we have around 95% of the data in the first two categories. As a reminder, Category 0 means it is an exact match, catagory 1 means that it is less than 1 percent off the expected value (see notebook `07_00_standardizer_basics.ipynb` for details)."]}, {"cell_type": "markdown", "id": "bc651e14-9205-458a-b3a7-fff4cd1bda4c", "metadata": {"tags": []}, "source": ["## Analysis on the whole dataset\n", "The following examples are just some ideas to show, what we could do with the standardized balance sheet dataset.\n", "\n", "First let us have a look at the distribution of Equity using a box plot."]}, {"cell_type": "code", "execution_count": 10, "id": "7be96761-fd1e-4df2-b698-dc6c4d80fbc7", "metadata": {"tags": []}, "outputs": [{"data": {"image/png": "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********************************/bwmvsRh5vMbioDyWeuY1Fl5j4TUWXmNxKM/fwuJoIywOymOpd8PpXSHXXntt2bZtW7nnnnu6vSvknHPOKXPmzOm2nfnz5w/Yu0Luueee2jw9vSvklltuqb3T4fzzzy8RUW655ZZDelfIunXrenxXyOWXX97rsYqIMmrUqNrnfb0rZN26daWlpaWsW7euz3eF3HPPPb2+K6SnferpXSHnnntuOfPMMzsd+77eFVLdbsePL37xi6VSqdTeBVL9qH5900039fqukL72teux7viukI7rD8W7Qnqau6d3hTz66KPl0UcfrR23EfGukN27d5dNmzaVTZs2lYgoK1asKJs2bSpbt25NHay/PBkcImFxUB5LffN7LHJ/j8XMmTOH1e+x6DjP4f4ei67bGKrfY9Hxvjqcfe06S1/31UA52Nx9HeOh+j0WlVL697Lt1atXx4UXXtht+ZVXXhm/+MUvDnr96m/p27VrVzQ3N/fnpvu0cePGmDNnTmzYsCHOPvvstO0ecbY/E3HvpyOueTxi0v8M9TTDksdS36rH5/bbb4/bbrvNb94Mv3nTb94cWMPlN28e6vN3v8Pi/RIWQ0xYHJTHUt8cHzg6Herzt/+EDABIIywAgDTCAgBIIywAgDTCAgBIIywAgDTCAgBIIywAgDTCAuiX008/PTZs2BCnn376UI8CDEMD/t+mA0eWpqYmv3ET6JWfWAAAaYQFAJBGWAAAaYQFAJBGWAAAaYQFAJBGWAAAaYQFAJBGWAAAaYQFAJDmiPmV3nv37o2IiI0bNw7xJMNb43//GbMj4oXNm+Od19qHepxh6YUXXhjqEQBGrCMmLDZv3hwREVdfffUQTzK8nXRsJa6dMyru+dH/jdfeLkM9zrA2ZsyYoR4BYMQ5YsLi85//fES89z8vNjU1De0wI8BlQz3AMDdmzJiYNWvWUI8BMOJUSimD+tfWlpaWGDt2bOzatSuam5sH86YBgMN0qM/fXrwJAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKQRFgBAGmEBAKRpGOwbLKVERERLS8tg3zQAcJiqz9vV5/HeDHpY7N69OyIipk6dOtg3DQC8T7t3746xY8f2enmlHCw9krW3t8f27dtjzJgxUalU+nXdc845J5566qkBmuzwDeZcA3FbWds83O0czvX6c51DWbelpSWmTp0ar7zySjQ3N/drliPJcD3HIpxn73cb/b3uQKzvPHvPcD3PDjZXKSV2794dkyZNirq63l9JMeg/sairq4spU6Yc1nXr6+uH5YNxMOcaiNvK2ubhbudwrtef6/Rn3ebm5mH5GBssw/Uci3Cevd9t9Pe6A7m+82x4nmeHMldfP6moGlEv3rzuuuuGeoQeDeZcA3FbWds83O0czvX6c53h+rgZjobzsXKevb9t9Pe6A73+0Wy4HqusuQb9n0JgOGppaYmxY8fGrl27huXfJOBI4Dw7Ooyon1jAQDnmmGPipptuimOOOWaoR4EjlvPs6OAnFgBAGj+xAADSCAsAII2wAADSCAsAII2wAADSCAvowRVXXBHHH398fOELX+i0/KGHHooPf/jDMWvWrPjpT386RNPBkaG386y35YwMwgJ6cP3118cvf/nLTssOHDgQy5Yti8ceeyw2bdoUt99+e7z55ptDNCGMfD2dZ30tZ2QQFtCDBQsWxJgxYzote/LJJ+OjH/1oTJ48OY499thYuHBh/PnPfx6iCWHk6+k862s5I4Ow4IizZs2aWLJkSUyaNCkqlUr8/ve/77bOnXfeGTNmzIjRo0fHJz7xiXjyyScPut3t27fH5MmTa19Pnjw5tm3bljk6jBgDdZ4x8gkLjjh79uyJs846K+68884eL//1r38dy5Yti5tuuik2btwYZ511VlxyySWxc+fOQZ4URi7nGb0RFhxxFi5cGDfffHNcccUVPV6+YsWKuPrqq+Oqq66Kj3zkI3H33XdHU1NT/PznP+9zu5MmTer0E4pt27bFpEmTUmeHkWKgzjNGPmHBUaW1tTU2bNgQF198cW1ZXV1dXHzxxbF+/fo+rztv3rx47rnnYtu2bfH222/HH//4x7jkkksGemQYcd7PecbI1zDUA8Bg+s9//hNtbW0xYcKETssnTJgQmzdvrn198cUXx7PPPht79uyJKVOmxG9+85s477zz4kc/+lFceOGF0d7eHt/+9rfjQx/60GDvAgx77/c86205I4OwgB785S9/6XH5ZZddFpdddtkgTwNHpt7Os96WMzL4pxCOKieeeGLU19fH66+/3mn566+/HieddNIQTQVHFufZ0U1YcFQZNWpUzJkzJ1atWlVb1t7eHqtWrfKjVkjiPDu6+acQjjhvv/12/Otf/6p9/fLLL8czzzwTJ5xwQkybNi2WLVsWV155ZcydOzfmzZsXP/nJT2LPnj1x1VVXDeHUMLI4z+hVgSPMX//61xIR3T6uvPLK2jp33HFHmTZtWhk1alSZN29e+dvf/jZ0A8MI5DyjN5VSShmqqAEAjixeYwEApBEWAEAaYQEApBEWAEAaYQEApBEWAEAaYQEApBEWAEAaYQEApBEWAEAaYQEApBEWAECa/w/JctkcySpYdwAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "data = bs_standardizer_result_bag.result_df.Equity[:1000]\n", "\n", "plt.boxplot(data, vert=False)\n", "plt.xscale('log') # using a logarithmic scale, we will lose negativ values though"]}, {"cell_type": "markdown", "id": "1421a47d-c717-41cb-ba49-2d030863a144", "metadata": {}, "source": ["Let's figure out, which report has the most Equity and then try to show the history of the Equity for that company."]}, {"cell_type": "code", "execution_count": 11, "id": "699bcc8f-96f7-481f-96ad-2b93fb598784", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>adsh</th>\n", "      <th>cik</th>\n", "      <th>name</th>\n", "      <th>form</th>\n", "      <th>fye</th>\n", "      <th>fy</th>\n", "      <th>fp</th>\n", "      <th>date</th>\n", "      <th>filed</th>\n", "      <th>coreg</th>\n", "      <th>report</th>\n", "      <th>ddate</th>\n", "      <th>qtrs</th>\n", "      <th>Assets</th>\n", "      <th>AssetsCurrent</th>\n", "      <th>Cash</th>\n", "      <th>AssetsNoncurrent</th>\n", "      <th>Liabilities</th>\n", "      <th>LiabilitiesCurrent</th>\n", "      <th>LiabilitiesNoncurrent</th>\n", "      <th>Equity</th>\n", "      <th>HolderEquity</th>\n", "      <th>RetainedEarnings</th>\n", "      <th>AdditionalPaidInCapital</th>\n", "      <th>TreasuryStockValue</th>\n", "      <th>TemporaryEquity</th>\n", "      <th>RedeemableEquity</th>\n", "      <th>LiabilitiesAndEquity</th>\n", "      <th>Assets<PERSON><PERSON>ck_error</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON>_cat</th>\n", "      <th>LiabilitiesCheck_error</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON>_cat</th>\n", "      <th>EquityCheck_error</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON>_cat</th>\n", "      <th>AssetsLiaEquCheck_error</th>\n", "      <th>AssetsLiaEquCheck_cat</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>277912</th>\n", "      <td>0001140361-11-038595</td>\n", "      <td>1115055</td>\n", "      <td>PINNACLE FINANCIAL PARTNERS INC</td>\n", "      <td>10-Q</td>\n", "      <td>1231</td>\n", "      <td>2011.0</td>\n", "      <td>Q2</td>\n", "      <td>2011-06-30</td>\n", "      <td>20110729</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20110630</td>\n", "      <td>0</td>\n", "      <td>4.831333e+12</td>\n", "      <td>4.831333e+12</td>\n", "      <td>2.785681e+11</td>\n", "      <td>0.0</td>\n", "      <td>4.132105e+12</td>\n", "      <td>4.132105e+12</td>\n", "      <td>0.0</td>\n", "      <td>6.992280e+11</td>\n", "      <td>6.992280e+11</td>\n", "      <td>1.986414e+10</td>\n", "      <td>5.335573e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>4.831333e+12</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        adsh      cik                             name  form   fye      fy  fp       date     filed coreg  report     ddate  qtrs        Assets  AssetsCurrent          Cash  AssetsNoncurrent   Liabilities  LiabilitiesCurrent  LiabilitiesNoncurrent        Equity  HolderEquity  RetainedEarnings  AdditionalPaidInCapital  TreasuryStockValue  TemporaryEquity  RedeemableEquity  LiabilitiesAndEquity  AssetsCheck_error  AssetsCheck_cat  LiabilitiesCheck_error  LiabilitiesCheck_cat  EquityCheck_error  EquityCheck_cat  AssetsLiaEquCheck_error  AssetsLiaEquCheck_cat\n", "277912  0001140361-11-038595  1115055  PINNACLE FINANCIAL PARTNERS INC  10-Q  1231  2011.0  Q2 2011-06-30  20110729             2  20110630     0  4.831333e+12   4.831333e+12  2.785681e+11               0.0  4.132105e+12        4.132105e+12                    0.0  6.992280e+11  6.992280e+11      1.986414e+10             5.335573e+11                 0.0              0.0               0.0          4.831333e+12                0.0              0.0                     0.0                   0.0                0.0              0.0                      0.0                    0.0"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["bs_standardizer_result_bag.result_df[bs_standardizer_result_bag.result_df.Equity == bs_standardizer_result_bag.result_df.Equity.max()]"]}, {"cell_type": "markdown", "id": "83b210fd-e9a6-467c-938b-bfbccfcffa68", "metadata": {}, "source": ["Since we used the `present` method of the standardizer, the cik, form, fye, fy, and fp attributes from the sub_df were directly merged in the result. Also a `date` column with a date datatype was added and the data is already sorted by date."]}, {"cell_type": "markdown", "id": "8316c00d-8e5e-4568-a600-765036091177", "metadata": {}, "source": ["Next, get reports for this company and filter our standardized balance sheet data for it."]}, {"cell_type": "code", "execution_count": 12, "id": "65fd1988-7064-4eb6-8167-03f0222127de", "metadata": {"tags": []}, "outputs": [], "source": ["reports_of_1115055 = bs_standardizer_result_bag.result_df[bs_standardizer_result_bag.result_df.cik==1115055]"]}, {"cell_type": "code", "execution_count": 13, "id": "9998d276-17aa-4680-99e5-dd2f9a9b63c0", "metadata": {"tags": []}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plotting\n", "plt.plot(reports_of_1115055['date'], reports_of_1115055['Equity'], linestyle='-')\n", "plt.yscale('log')"]}, {"cell_type": "markdown", "id": "25be704b-805a-4a43-a27a-82fa56544e9a", "metadata": {"tags": []}, "source": ["The first data point is obviously faulty."]}, {"cell_type": "markdown", "id": "ca75e634-c65c-41b9-a85f-4c6a4df9e680", "metadata": {}, "source": ["Let us repeat the steps for apple (cik=320193) and display Equity, Assets, and Liabilities."]}, {"cell_type": "code", "execution_count": 14, "id": "79087c33-9ef9-4319-87ac-afd330a9f1c5", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["<matplotlib.legend.Legend at 0x14a01bba830>"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["apple_reports_df = bs_standardizer_result_bag.result_df[bs_standardizer_result_bag.result_df.cik==320193]\n", "\n", "# Plotting\n", "plt.plot(apple_reports_df['date'], apple_reports_df['Equity'], label='Equity', linestyle='-')\n", "plt.plot(apple_reports_df['date'], apple_reports_df['Assets'], label='Assets', linestyle='-')\n", "plt.plot(apple_reports_df['date'], apple_reports_df['Liabilities'], label='Liabilities', linestyle='-')\n", "plt.legend()\n"]}, {"cell_type": "markdown", "id": "28b1b97e-11a2-4932-b7a5-f8964c08a180", "metadata": {}, "source": ["### Compare companies"]}, {"cell_type": "markdown", "id": "d24f1215-9440-42ee-8564-1855a646c964", "metadata": {}, "source": ["Let's visualize and compare the history of euqity for a few companies:"]}, {"cell_type": "code", "execution_count": 15, "id": "43924bdd-763c-4061-8543-3a269406195d", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["<matplotlib.legend.Legend at 0x14a0132cd90>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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**************************************************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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ciks_to_consider = [320193, 789019, 1652044, 1045810, 1018724, 2488, 50863] # Apple, Microsoft, Alphabet, nvidia, Amazon, AMD, intel\n", "\n", "df = bs_standardizer_result_bag.result_df[bs_standardizer_result_bag.result_df.cik.isin(ciks_to_consider)].copy()\n", "\n", "# Group by 'name' and plot equity for each group\n", "# Note: using the `present` method ensured that the same cik has always the same name even if the company name did change in the past\n", "for name, group in df.groupby('name'):\n", "    plt.plot(group['date'], group['Equity'], label=name, linestyle='-')\n", "\n", "# Add labels and title\n", "plt.xlabel('Date')\n", "plt.ylabel('Equity')\n", "plt.title('Equity Over Time for Different Companies (CIKs)')\n", "\n", "# Display legend\n", "plt.legend()"]}, {"cell_type": "markdown", "id": "e47fe84e-f170-4f3f-a988-445ee6cbbe06", "metadata": {"tags": []}, "source": ["Cloud providers seem to do exceptionally well these days: alphabet, amazon, and microsoft almost have an exponential grow in equity."]}, {"cell_type": "markdown", "id": "fd873bb2-9bdf-44a9-adb4-603eaaadf593", "metadata": {}, "source": ["What was going with AMD? They had a massive increase in Equity in 2022. AssetsNoncurrent did increase 10 fold ..."]}, {"cell_type": "code", "execution_count": 16, "id": "b755fc59-ac6d-4081-8129-a77d3651afcc", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["<matplotlib.legend.Legend at 0x14a01bb87c0>"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["amd_reports_df = bs_standardizer_result_bag.result_df[bs_standardizer_result_bag.result_df.cik==2488]\n", "\n", "plt.plot(amd_reports_df['date'], amd_reports_df['Equity'], label='Equity', linestyle='-')\n", "plt.plot(amd_reports_df['date'], amd_reports_df['Assets'], label='Assets', linestyle='-')\n", "plt.plot(amd_reports_df['date'], amd_reports_df['AssetsCurrent'], label='AssetsCurrent', linestyle='-')\n", "plt.plot(amd_reports_df['date'], amd_reports_df['AssetsNoncurrent'], label='AssetsNoncurrent', linestyle='-')\n", "plt.plot(amd_reports_df['date'], amd_reports_df['Liabilities'], label='Liabilities', linestyle='-')\n", "plt.legend()"]}, {"cell_type": "markdown", "id": "0d58090a-fcdd-416c-ae0f-281424e9a731", "metadata": {}, "source": ["### Conclusion\n", "\n", "With the Balance Sheet Standardizer, we have the possibility to actually compare data between companies and also to create input for ML models. \n", "\n", "The great thing is, that we can do this with official and free data of over 300'000 reports filed by about 14'000 companies since 2010.\n", "\n", "Thanks to secfsdstools package, we have the possibility to gather and filter the data in a simple and efficient way, which otherwise would only be possible if you pay for the data. And you have all the data on your computer, no need to for slow api calls.\n", "\n", "The Standardizer framework is simple and can be extended with additional rules to make other data points available. With the validation rules we also have a way to assess the quality of single rows in the dataset.\n", "\n", "Of course, calculating financial ratios based on the standardized dataset is really simple now.\n", "\n", "Also the size of the standardized dataset (about 30MB) is really easy to handle."]}, {"cell_type": "markdown", "id": "e408232a-e6b6-419a-bd2c-f4ed17dd8bd5", "metadata": {"tags": []}, "source": ["## Rules"]}, {"cell_type": "markdown", "id": "14803db8-1320-494e-9afa-cbe6e743930a", "metadata": {"tags": []}, "source": ["**Note:** \n", "\n", "**The following section tries to explain how the results are calculated and what kind of rules are applied. It isn't really necessary to understand this section in detail, but it gives you an idea what happens under the hood.**"]}, {"cell_type": "markdown", "id": "49872259-fa55-4e36-9f57-394597f89438", "metadata": {}, "source": ["Next, let us see how often which rule was applied. This gives an idea about how much \"calculation\" had to be done in order to create a standardized dataset. We can to this by looking at the `applied_rules_sum_s` pandas Series object."]}, {"cell_type": "code", "execution_count": 17, "id": "5c627784-4d98-4b43-b10d-115d1586a0bc", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["0\n", "NaN                                                                                                             0\n", "PREPIVOT_BS_PREPIV_#1_DeDup                                                                                  1450\n", "PRE_BS_PRE_#1_Assets/AssetsNoncurrent                                                                         122\n", "PRE_BS_PRE_#2_Assets/AssetsCurrent                                                                              0\n", "MAIN_1_BS_#1_BR_#1_Assets<-AssetsNet                                                                          160\n", "MAIN_1_BS_#1_BR_#2_Cash<-CashAndCashEquivalentsAtCarryingValue                                             274082\n", "MAIN_1_BS_#1_BR_#3_LiabilitiesAndEquity<-LiabilitiesAndStockholdersEquity                                  341667\n", "MAIN_1_BS_#1_BR_#4_RetainedEarnings<-RetainedEarningsAccumulatedDeficit                                    308581\n", "MAIN_1_BS_#2_EQ_#1_HolderEquity<-StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest     93291\n", "MAIN_1_BS_#2_EQ_#2_HolderEquity<-PartnersCapital                                                             9738\n", "MAIN_1_BS_#2_EQ_#3_HolderEquity<-StockholdersEquity                                                        236497\n", "MAIN_1_BS_#2_EQ_#4_TemporaryEquity                                                                          12292\n", "MAIN_1_BS_#2_EQ_#5_RedeemableEquity                                                                          9618\n", "MAIN_1_BS_#2_EQ_#6_Equity                                                                                  340030\n", "MAIN_1_BS_#3_SC_#1_Assets                                                                                      89\n", "MAIN_1_BS_#3_SC_#2_AssetsCurrent                                                                              103\n", "MAIN_1_BS_#3_SC_#3_AssetsNoncurrent                                                                        244615\n", "MAIN_1_BS_#3_SC_#4_Liabilities                                                                              16745\n", "MAIN_1_BS_#3_SC_#5_LiabilitiesCurrent                                                                          74\n", "MAIN_1_BS_#3_SC_#6_LiabilitiesNoncurrent                                                                   173265\n", "MAIN_1_BS_#3_SC_#7_Assets                                                                                    1896\n", "MAIN_1_BS_#3_SC_#8_Liabilities                                                                              51532\n", "MAIN_1_BS_#3_SC_#9_Equity                                                                                    4942\n", "MAIN_1_BS_#3_SC_#10_LiabilitiesAndEquity                                                                     3960\n", "MAIN_1_BS_#3_SC_#11_Liabilities                                                                              1389\n", "MAIN_1_BS_#3_SC_#12_Equity                                                                                     14\n", "MAIN_1_BS_#4_SU_#1_Cash                                                                                     13913\n", "MAIN_1_BS_#4_SU_#2_RetainedEarnings                                                                          2537\n", "MAIN_1_BS_#4_SU_#3_LongTermDebt                                                                            109187\n", "MAIN_1_BS_#4_SU_#4_LiabilitiesNoncurrent                                                                    50063\n", "MAIN_1_BS_#5_SetSum_#1_Assets/AssetsNoncurrent                                                               1561\n", "MAIN_1_BS_#5_SetSum_#2_Assets/AssetsCurrent                                                                     4\n", "MAIN_1_BS_#5_SetSum_#3_Liabilities/LiabilitiesNoncurrent                                                     1015\n", "MAIN_1_BS_#5_SetSum_#4_Liabilities/LiabilitiesCurrent                                                          38\n", "MAIN_2_BS_#1_BR_#1_Assets<-AssetsNet                                                                            0\n", "MAIN_2_BS_#1_BR_#2_Cash<-CashAndCashEquivalentsAtCarryingValue                                                  0\n", "MAIN_2_BS_#1_BR_#3_LiabilitiesAndEquity<-LiabilitiesAndStockholdersEquity                                       0\n", "MAIN_2_BS_#1_BR_#4_RetainedEarnings<-RetainedEarningsAccumulatedDeficit                                         0\n", "MAIN_2_BS_#2_EQ_#1_HolderEquity<-StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest         0\n", "MAIN_2_BS_#2_EQ_#2_HolderEquity<-PartnersCapital                                                                0\n", "MAIN_2_BS_#2_EQ_#3_HolderEquity<-StockholdersEquity                                                             0\n", "MAIN_2_BS_#2_EQ_#4_TemporaryEquity                                                                              0\n", "MAIN_2_BS_#2_EQ_#5_RedeemableEquity                                                                             0\n", "MAIN_2_BS_#2_EQ_#6_Equity                                                                                       0\n", "MAIN_2_BS_#3_SC_#1_Assets                                                                                       0\n", "MAIN_2_BS_#3_SC_#2_AssetsCurrent                                                                                1\n", "MAIN_2_BS_#3_SC_#3_AssetsNoncurrent                                                                           437\n", "MAIN_2_BS_#3_SC_#4_Liabilities                                                                                938\n", "MAIN_2_BS_#3_SC_#5_LiabilitiesCurrent                                                                       18373\n", "MAIN_2_BS_#3_SC_#6_LiabilitiesNoncurrent                                                                    20969\n", "MAIN_2_BS_#3_SC_#7_Assets                                                                                     714\n", "MAIN_2_BS_#3_SC_#8_Liabilities                                                                                  7\n", "MAIN_2_BS_#3_SC_#9_Equity                                                                                    1309\n", "MAIN_2_BS_#3_SC_#10_LiabilitiesAndEquity                                                                      821\n", "MAIN_2_BS_#3_SC_#11_Liabilities                                                                                 0\n", "MAIN_2_BS_#3_SC_#12_Equity                                                                                      4\n", "MAIN_2_BS_#4_SU_#1_Cash                                                                                         0\n", "MAIN_2_BS_#4_SU_#2_RetainedEarnings                                                                             0\n", "MAIN_2_BS_#4_SU_#3_LongTermDebt                                                                                 0\n", "MAIN_2_BS_#4_SU_#4_LiabilitiesNoncurrent                                                                        0\n", "MAIN_2_BS_#5_SetSum_#1_Assets/AssetsNoncurrent                                                                  0\n", "MAIN_2_BS_#5_SetSum_#2_Assets/AssetsCurrent                                                                     0\n", "MAIN_2_BS_#5_SetSum_#3_Liabilities/LiabilitiesNoncurrent                                                        0\n", "MAIN_2_BS_#5_SetSum_#4_Liabilities/LiabilitiesCurrent                                                           0\n", "MAIN_3_BS_#1_BR_#1_Assets<-AssetsNet                                                                            0\n", "MAIN_3_BS_#1_BR_#2_Cash<-CashAndCashEquivalentsAtCarryingValue                                                  0\n", "MAIN_3_BS_#1_BR_#3_LiabilitiesAndEquity<-LiabilitiesAndStockholdersEquity                                       0\n", "MAIN_3_BS_#1_BR_#4_RetainedEarnings<-RetainedEarningsAccumulatedDeficit                                         0\n", "MAIN_3_BS_#2_EQ_#1_HolderEquity<-StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest         0\n", "MAIN_3_BS_#2_EQ_#2_HolderEquity<-PartnersCapital                                                                0\n", "MAIN_3_BS_#2_EQ_#3_HolderEquity<-StockholdersEquity                                                             0\n", "MAIN_3_BS_#2_EQ_#4_TemporaryEquity                                                                              0\n", "MAIN_3_BS_#2_EQ_#5_RedeemableEquity                                                                             0\n", "MAIN_3_BS_#2_EQ_#6_Equity                                                                                       0\n", "MAIN_3_BS_#3_SC_#1_Assets                                                                                       0\n", "MAIN_3_BS_#3_SC_#2_AssetsCurrent                                                                                0\n", "MAIN_3_BS_#3_SC_#3_AssetsNoncurrent                                                                             0\n", "MAIN_3_BS_#3_SC_#4_Liabilities                                                                                  0\n", "MAIN_3_BS_#3_SC_#5_LiabilitiesCurrent                                                                           0\n", "MAIN_3_BS_#3_SC_#6_LiabilitiesNoncurrent                                                                        0\n", "MAIN_3_BS_#3_SC_#7_Assets                                                                                       4\n", "MAIN_3_BS_#3_SC_#8_Liabilities                                                                                  0\n", "MAIN_3_BS_#3_SC_#9_Equity                                                                                       0\n", "MAIN_3_BS_#3_SC_#10_LiabilitiesAndEquity                                                                        0\n", "MAIN_3_BS_#3_SC_#11_Liabilities                                                                                 0\n", "MAIN_3_BS_#3_SC_#12_Equity                                                                                      0\n", "MAIN_3_BS_#4_SU_#1_Cash                                                                                         0\n", "MAIN_3_BS_#4_SU_#2_RetainedEarnings                                                                             0\n", "MAIN_3_BS_#4_SU_#3_LongTermDebt                                                                                 0\n", "MAIN_3_BS_#4_SU_#4_LiabilitiesNoncurrent                                                                        0\n", "MAIN_3_BS_#5_SetSum_#1_Assets/AssetsNoncurrent                                                                  0\n", "MAIN_3_BS_#5_SetSum_#2_Assets/AssetsCurrent                                                                     0\n", "MAIN_3_BS_#5_SetSum_#3_Liabilities/LiabilitiesNoncurrent                                                        0\n", "MAIN_3_BS_#5_SetSum_#4_Liabilities/LiabilitiesCurrent                                                           0\n", "POST_BS_POST_#1_AssetsCurrent/AssetsNoncurrent                                                              78505\n", "POST_BS_POST_#2_LiabilitiesCurrent/LiabilitiesNoncurrent                                                    59897\n", "POST_BS_POST_#3_Assets/AssetsCurrent/AssetsNoncurrent                                                          23\n", "POST_BS_POST_#4_Liabilities/LiabilitiesCurrent/LiabilitiesNoncurrent                                          248\n", "POST_BS_POST_#5_TemporaryEquity                                                                            334278\n", "POST_BS_POST_#6_RedeemableEquity                                                                           336952\n", "POST_BS_POST_#7_AdditionalPaidInCapital                                                                    173878\n", "POST_BS_POST_#8_TreasuryStockValue                                                                         254898\n", "Name: 1, dtype: int64"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["bs_standardizer_result_bag.applied_rules_sum_s"]}, {"cell_type": "markdown", "id": "c4bb3a15-4b18-46fe-92b4-c0b5f95d24d4", "metadata": {}, "source": ["### Applied Rules\n", "To be able to assess the content of `applied_rules_sum_s`  we need to understand the rules that are applied. The simplest way to do this is to print the description of them:"]}, {"cell_type": "code", "execution_count": 18, "id": "b13d5ad1-96de-488b-ad09-4564560d74f7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>part</th>\n", "      <th>type</th>\n", "      <th>ruleclass</th>\n", "      <th>identifier</th>\n", "      <th>description</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>PREPIVOT</td>\n", "      <td>Group</td>\n", "      <td></td>\n", "      <td>PREPIVOT_BS_PREPIV</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>PREPIVOT</td>\n", "      <td>Rule</td>\n", "      <td>PrePivotDeduplicate</td>\n", "      <td>PREPIVOT_BS_PREPIV_#1_DeDup</td>\n", "      <td>Deduplicates the dataframe based on the columns ['adsh', 'coreg', 'report', 'ddate', 'qtrs', 'tag', 'version', 'value']</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>PRE</td>\n", "      <td>Group</td>\n", "      <td></td>\n", "      <td>PRE_BS_PRE</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>PRE</td>\n", "      <td>Rule</td>\n", "      <td>PreSumUpCorrection</td>\n", "      <td>PRE_BS_PRE_#1_Assets/AssetsNoncurrent</td>\n", "      <td>Swaps the values between the tag 'Assets' and 'AssetsNoncurrent' if the following equation is True \"'AssetsNoncurrent' = 'Assets' + 'AssetsCurrent\"  and 'AssetsCurrent' &gt; 0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>PRE</td>\n", "      <td>Rule</td>\n", "      <td>PreSumUpCorrection</td>\n", "      <td>PRE_BS_PRE_#2_Assets/AssetsCurrent</td>\n", "      <td>Swaps the values between the tag 'Assets' and 'AssetsCurrent' if the following equation is True \"'AssetsCurrent' = 'Assets' + 'AssetsNoncurrent\"  and 'AssetsNoncurrent' &gt; 0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>MAIN</td>\n", "      <td>Group</td>\n", "      <td></td>\n", "      <td>MAIN_BS</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>MAIN</td>\n", "      <td>Group</td>\n", "      <td></td>\n", "      <td>MAIN_BS_#1_BR</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_BS_#1_BR_#1_Assets&lt;-AssetsNet</td>\n", "      <td>Copies the values from AssetsNet to Assets if AssetsNet is not null and Assets is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_BS_#1_BR_#2_Cash&lt;-CashAndCashEquivalentsAtCarryingValue</td>\n", "      <td>Copies the values from CashAndCashEquivalentsAtCarryingValue to Cash if CashAndCashEquivalentsAtCarryingValue is not null and Cash is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_BS_#1_BR_#3_LiabilitiesAndEquity&lt;-LiabilitiesAndStockholdersEquity</td>\n", "      <td>Copies the values from LiabilitiesAndStockholdersEquity to LiabilitiesAndEquity if LiabilitiesAndStockholdersEquity is not null and LiabilitiesAndEquity is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_BS_#1_BR_#4_RetainedEarnings&lt;-RetainedEarningsAccumulatedDeficit</td>\n", "      <td>Copies the values from RetainedEarningsAccumulatedDeficit to RetainedEarnings if RetainedEarningsAccumulatedDeficit is not null and RetainedEarnings is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>MAIN</td>\n", "      <td>Group</td>\n", "      <td></td>\n", "      <td>MAIN_BS_#2_EQ</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_BS_#2_EQ_#1_HolderEquity&lt;-StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest</td>\n", "      <td>Copies the values from StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest to HolderEquity if StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest is not null and HolderEquity is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_BS_#2_EQ_#2_HolderEquity&lt;-PartnersCapital</td>\n", "      <td>Copies the values from PartnersCapital to HolderEquity if PartnersCapital is not null and HolderEquity is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>CopyTagRule</td>\n", "      <td>MAIN_BS_#2_EQ_#3_HolderEquity&lt;-StockholdersEquity</td>\n", "      <td>Copies the values from StockholdersEquity to HolderEquity if StockholdersEquity is not null and HolderEquity is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SumUpRule</td>\n", "      <td>MAIN_BS_#2_EQ_#4_TemporaryEquity</td>\n", "      <td>Sums up the availalbe values in the columns ['TemporaryEquityAggregateAmountOfRedemptionRequirement', 'TemporaryEquityCarryingAmountAttributableToParent', 'TemporaryEquityRedemptionAmountAttributableToParent', 'TemporaryEquityRedemptionAmountAttributableToNoncontrollingInterest'] into the column 'TemporaryEquity', if the column 'TemporaryEquity' is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SumUpRule</td>\n", "      <td>MAIN_BS_#2_EQ_#5_RedeemableEquity</td>\n", "      <td>Sums up the availalbe values in the columns ['RedeemableNoncontrollingInterestEquityCarryingAmount', 'RedeemableNoncontrollingInterestEquityRedemptionAmount', 'RedeemableNoncontrollingInterestEquityOtherCarryingAmount', 'RedeemableNoncontrollingInterestEquityOtherRedemptionAmount', 'RedeemablePreferredStockEquityOtherCarryingAmount', 'RedeemablePreferredStockEquityOtherRedemptionAmount'] into the column 'RedeemableEquity', if the column 'RedeemableEquity' is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SumUpRule</td>\n", "      <td>MAIN_BS_#2_EQ_#6_Equity</td>\n", "      <td>Sums up the availalbe values in the columns ['HolderEquity', 'TemporaryEquity', 'RedeemableEquity'] into the column 'Equity', if the column 'Equity' is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>MAIN</td>\n", "      <td>Group</td>\n", "      <td></td>\n", "      <td>MAIN_BS_#3_SC</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>MissingSumRule</td>\n", "      <td>MAIN_BS_#3_SC_#1_Assets</td>\n", "      <td>Sums up the values in the columns ['AssetsCurrent', 'AssetsNoncurrent'] into the column 'Assets', if the column 'Assets' is nan and if all columns ['AssetsCurrent', 'AssetsNoncurrent'] have a value</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>MissingSummandRule</td>\n", "      <td>MAIN_BS_#3_SC_#2_AssetsCurrent</td>\n", "      <td>Calculates the value for the missing column 'AssetsCurrent' by subtracting the values of the columns '['AssetsNoncurrent']' from the column 'Assets' if all of the columns ['Assets', 'AssetsNoncurrent'] are set.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>MissingSummandRule</td>\n", "      <td>MAIN_BS_#3_SC_#3_AssetsNoncurrent</td>\n", "      <td>Calculates the value for the missing column 'AssetsNoncurrent' by subtracting the values of the columns '['AssetsCurrent']' from the column 'Assets' if all of the columns ['Assets', 'AssetsCurrent'] are set.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>MissingSumRule</td>\n", "      <td>MAIN_BS_#3_SC_#4_Liabilities</td>\n", "      <td>Sums up the values in the columns ['LiabilitiesCurrent', 'LiabilitiesNoncurrent'] into the column 'Liabilities', if the column 'Liabilities' is nan and if all columns ['LiabilitiesCurrent', 'LiabilitiesNoncurrent'] have a value</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>MissingSummandRule</td>\n", "      <td>MAIN_BS_#3_SC_#5_LiabilitiesCurrent</td>\n", "      <td>Calculates the value for the missing column 'LiabilitiesCurrent' by subtracting the values of the columns '['LiabilitiesNoncurrent']' from the column 'Liabilities' if all of the columns ['Liabilities', 'LiabilitiesNoncurrent'] are set.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>MissingSummandRule</td>\n", "      <td>MAIN_BS_#3_SC_#6_LiabilitiesNoncurrent</td>\n", "      <td>Calculates the value for the missing column 'LiabilitiesNoncurrent' by subtracting the values of the columns '['LiabilitiesCurrent']' from the column 'Liabilities' if all of the columns ['Liabilities', 'LiabilitiesCurrent'] are set.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>MissingSumRule</td>\n", "      <td>MAIN_BS_#3_SC_#7_Assets</td>\n", "      <td>Sums up the values in the columns ['Liabilities', 'Equity'] into the column 'Assets', if the column 'Assets' is nan and if all columns ['Liabilities', 'Equity'] have a value</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>MissingSummandRule</td>\n", "      <td>MAIN_BS_#3_SC_#8_Liabilities</td>\n", "      <td>Calculates the value for the missing column 'Liabilities' by subtracting the values of the columns '['Equity']' from the column 'Assets' if all of the columns ['Assets', 'Equity'] are set.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>MissingSummandRule</td>\n", "      <td>MAIN_BS_#3_SC_#9_Equity</td>\n", "      <td>Calculates the value for the missing column 'Equity' by subtracting the values of the columns '['Liabilities']' from the column 'Assets' if all of the columns ['Assets', 'Liabilities'] are set.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>MissingSumRule</td>\n", "      <td>MAIN_BS_#3_SC_#10_LiabilitiesAndEquity</td>\n", "      <td>Sums up the values in the columns ['Liabilities', 'Equity'] into the column 'LiabilitiesAndEquity', if the column 'LiabilitiesAndEquity' is nan and if all columns ['Liabilities', 'Equity'] have a value</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>MissingSummandRule</td>\n", "      <td>MAIN_BS_#3_SC_#11_Liabilities</td>\n", "      <td>Calculates the value for the missing column 'Liabilities' by subtracting the values of the columns '['Equity']' from the column 'LiabilitiesAndEquity' if all of the columns ['LiabilitiesAndEquity', 'Equity'] are set.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>MissingSummandRule</td>\n", "      <td>MAIN_BS_#3_SC_#12_Equity</td>\n", "      <td>Calculates the value for the missing column 'Equity' by subtracting the values of the columns '['Liabilities']' from the column 'LiabilitiesAndEquity' if all of the columns ['LiabilitiesAndEquity', 'Liabilities'] are set.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>MAIN</td>\n", "      <td>Group</td>\n", "      <td></td>\n", "      <td>MAIN_BS_#4_SU</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SumUpRule</td>\n", "      <td>MAIN_BS_#4_SU_#1_Cash</td>\n", "      <td>Sums up the availalbe values in the columns ['CashAndCashEquivalentsAtFairValue', 'CashAndDueFromBanks', 'CashCashEquivalentsAndFederalFundsSold', 'RestrictedCashAndCashEquivalentsAtCarryingValue', 'CashAndCashEquivalentsInForeignCurrencyAtCarryingValue'] into the column 'Cash', if the column 'Cash' is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SumUpRule</td>\n", "      <td>MAIN_BS_#4_SU_#2_RetainedEarnings</td>\n", "      <td>Sums up the availalbe values in the columns ['RetainedEarningsUnappropriated', 'RetainedEarningsAppropriated'] into the column 'RetainedEarnings', if the column 'RetainedEarnings' is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SumUpRule</td>\n", "      <td>MAIN_BS_#4_SU_#3_LongTermDebt</td>\n", "      <td>Sums up the availalbe values in the columns ['LongTermDebtNoncurrent', 'LongTermDebtAndCapitalLeaseObligations'] into the column 'LongTermDebt', if the column 'LongTermDebt' is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SumUpRule</td>\n", "      <td>MAIN_BS_#4_SU_#4_LiabilitiesNoncurrent</td>\n", "      <td>Sums up the availalbe values in the columns ['AccruedIncomeTaxesNoncurrent', 'DeferredAndPayableIncomeTaxes', 'DeferredIncomeTaxesAndOtherLiabilitiesNoncurrent', 'DeferredIncomeTaxLiabilitiesNet', 'DeferredTaxLiabilitiesNoncurrent', 'DefinedBenefitPensionPlanLiabilitiesNoncurrent', 'DerivativeLiabilitiesNoncurrent', 'FinanceLeaseLiabilityNoncurrent', 'LiabilitiesOtherThanLongtermDebtNoncurrent', 'LiabilitiesSubjectToCompromise', 'LiabilityForUncertainTaxPositionsNoncurrent', 'LongTermDebt', 'LongTermRetirementBenefitsAndOtherLiabilities', 'OperatingLeaseLiabilityNoncurrent', 'OtherLiabilitiesNoncurrent', 'OtherPostretirementDefinedBenefitPlanLiabilitiesNoncurrent', 'PensionAndOtherPostretirementDefinedBenefitPlansLiabilitiesNoncurrent', 'RegulatoryLiabilityNoncurrent', 'SelfInsuranceReserveNoncurrent'] into the column 'LiabilitiesNoncurrent', if the column 'LiabilitiesNoncurrent' is nan</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>MAIN</td>\n", "      <td>Group</td>\n", "      <td></td>\n", "      <td>MAIN_BS_#5_SetSum</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SetSumIfOnlyOneSummand</td>\n", "      <td>MAIN_BS_#5_SetSum_#1_Assets/AssetsNoncurrent</td>\n", "      <td>Copies the value of the column 'AssetsCurrent' into the column 'Assets' and sets the columns ['AssetsNoncurrent'] to 0.0 if the column 'AssetsCurrent is set and the columns ['Assets', 'AssetsNoncurrent'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SetSumIfOnlyOneSummand</td>\n", "      <td>MAIN_BS_#5_SetSum_#2_Assets/AssetsCurrent</td>\n", "      <td>Copies the value of the column 'AssetsNoncurrent' into the column 'Assets' and sets the columns ['AssetsCurrent'] to 0.0 if the column 'AssetsNoncurrent is set and the columns ['Assets', 'AssetsCurrent'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SetSumIfOnlyOneSummand</td>\n", "      <td>MAIN_BS_#5_SetSum_#3_Liabilities/LiabilitiesNoncurrent</td>\n", "      <td>Copies the value of the column 'LiabilitiesCurrent' into the column 'Liabilities' and sets the columns ['LiabilitiesNoncurrent'] to 0.0 if the column 'LiabilitiesCurrent is set and the columns ['Liabilities', 'LiabilitiesNoncurrent'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>MAIN</td>\n", "      <td>Rule</td>\n", "      <td>SetSumIfOnlyOneSummand</td>\n", "      <td>MAIN_BS_#5_SetSum_#4_Liabilities/LiabilitiesCurrent</td>\n", "      <td>Copies the value of the column 'LiabilitiesNoncurrent' into the column 'Liabilities' and sets the columns ['LiabilitiesCurrent'] to 0.0 if the column 'LiabilitiesNoncurrent is set and the columns ['Liabilities', 'LiabilitiesCurrent'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>POST</td>\n", "      <td>Group</td>\n", "      <td></td>\n", "      <td>POST_BS_POST</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostCopyToFirstSummand</td>\n", "      <td>POST_BS_POST_#1_AssetsCurrent/AssetsNoncurrent</td>\n", "      <td>Copies the value of the 'Assets' to the first summand 'AssetsCurrent' and set the other summands ['AssetsNoncurrent'] to 0.0 if 'Assets is set and the summands ['AssetsCurrent', 'AssetsNoncurrent'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostCopyToFirstSummand</td>\n", "      <td>POST_BS_POST_#2_LiabilitiesCurrent/LiabilitiesNoncurrent</td>\n", "      <td>Copies the value of the 'Liabilities' to the first summand 'LiabilitiesCurrent' and set the other summands ['LiabilitiesNoncurrent'] to 0.0 if 'Liabilities is set and the summands ['LiabilitiesCurrent', 'LiabilitiesNoncurrent'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostSetToZero</td>\n", "      <td>POST_BS_POST_#3_Assets/AssetsCurrent/AssetsNoncurrent</td>\n", "      <td>Set the value of the ['Assets', 'AssetsCurrent', 'AssetsNoncurrent'] to 0.0 if all ['Assets', 'AssetsCurrent', 'AssetsNoncurrent'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostSetToZero</td>\n", "      <td>POST_BS_POST_#4_Liabilities/LiabilitiesCurrent/LiabilitiesNoncurrent</td>\n", "      <td>Set the value of the ['Liabilities', 'LiabilitiesCurrent', 'LiabilitiesNoncurrent'] to 0.0 if all ['Liabilities', 'LiabilitiesCurrent', 'LiabilitiesNoncurrent'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostSetToZero</td>\n", "      <td>POST_BS_POST_#5_TemporaryEquity</td>\n", "      <td>Set the value of the ['TemporaryEquity'] to 0.0 if all ['TemporaryEquity'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostSetToZero</td>\n", "      <td>POST_BS_POST_#6_RedeemableEquity</td>\n", "      <td>Set the value of the ['RedeemableEquity'] to 0.0 if all ['RedeemableEquity'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostSetToZero</td>\n", "      <td>POST_BS_POST_#7_AdditionalPaidInCapital</td>\n", "      <td>Set the value of the ['AdditionalPaidInCapital'] to 0.0 if all ['AdditionalPaidInCapital'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>POST</td>\n", "      <td>Rule</td>\n", "      <td>PostSetToZero</td>\n", "      <td>POST_BS_POST_#8_TreasuryStockValue</td>\n", "      <td>Set the value of the ['TreasuryStockValue'] to 0.0 if all ['TreasuryStockValue'] are nan.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50</th>\n", "      <td>VALID</td>\n", "      <td>Validation</td>\n", "      <td>SumValidationRule</td>\n", "      <td>As<PERSON><PERSON><PERSON>ck</td>\n", "      <td>Checks whether the sum of ['AssetsCurrent', 'AssetsNoncurrent'] equals the value in 'Assets'</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51</th>\n", "      <td>VALID</td>\n", "      <td>Validation</td>\n", "      <td>SumValidationRule</td>\n", "      <td>LiabilitiesCheck</td>\n", "      <td>Checks whether the sum of ['LiabilitiesCurrent', 'LiabilitiesNoncurrent'] equals the value in 'Liabilities'</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>VALID</td>\n", "      <td>Validation</td>\n", "      <td>SumValidationRule</td>\n", "      <td>EquityCheck</td>\n", "      <td>Checks whether the sum of ['Equity', 'Liabilities'] equals the value in 'LiabilitiesAndEquity'</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53</th>\n", "      <td>VALID</td>\n", "      <td>Validation</td>\n", "      <td>SumValidationRule</td>\n", "      <td>AssetsLiaEquCheck</td>\n", "      <td>Checks whether the sum of ['Equity', 'Liabilities'] equals the value in 'Assets'</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        part        type               ruleclass                                                                                             identifier  \\\n", "0   PREPIVOT       Group                                                                                                             PREPIVOT_BS_PREPIV   \n", "1   PREPIVOT        Rule     PrePivotDeduplicate                                                                            PREPIVOT_BS_PREPIV_#1_DeDup   \n", "2        PRE       Group                                                                                                                     PRE_BS_PRE   \n", "3        PRE        Rule      PreSumUpCorrection                                                                  PRE_BS_PRE_#1_Assets/AssetsNoncurrent   \n", "4        PRE        Rule      PreSumUpCorrection                                                                     PRE_BS_PRE_#2_Assets/AssetsCurrent   \n", "5       MAIN       Group                                                                                                                        MAIN_BS   \n", "6       MAIN       Group                                                                                                                  MAIN_BS_#1_BR   \n", "7       MAIN        Rule             CopyTagRule                                                                     MAIN_BS_#1_BR_#1_Assets<-AssetsNet   \n", "8       MAIN        Rule             CopyTagRule                                           MAIN_BS_#1_BR_#2_Cash<-CashAndCashEquivalentsAtCarryingValue   \n", "9       MAIN        Rule             CopyTagRule                                MAIN_BS_#1_BR_#3_LiabilitiesAndEquity<-LiabilitiesAndStockholdersEquity   \n", "10      MAIN        Rule             CopyTagRule                                  MAIN_BS_#1_BR_#4_RetainedEarnings<-RetainedEarningsAccumulatedDeficit   \n", "11      MAIN       Group                                                                                                                  MAIN_BS_#2_EQ   \n", "12      MAIN        Rule             CopyTagRule  MAIN_BS_#2_EQ_#1_HolderEquity<-StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest   \n", "13      MAIN        Rule             CopyTagRule                                                         MAIN_BS_#2_EQ_#2_HolderEquity<-PartnersCapital   \n", "14      MAIN        Rule             CopyTagRule                                                      MAIN_BS_#2_EQ_#3_HolderEquity<-StockholdersEquity   \n", "15      MAIN        Rule               SumUpRule                                                                       MAIN_BS_#2_EQ_#4_TemporaryEquity   \n", "16      MAIN        Rule               SumUpRule                                                                      MAIN_BS_#2_EQ_#5_RedeemableEquity   \n", "17      MAIN        Rule               SumUpRule                                                                                MAIN_BS_#2_EQ_#6_Equity   \n", "18      MAIN       Group                                                                                                                  MAIN_BS_#3_SC   \n", "19      MAIN        Rule          MissingSumRule                                                                                MAIN_BS_#3_SC_#1_Assets   \n", "20      MAIN        Rule      MissingSummandRule                                                                         MAIN_BS_#3_SC_#2_AssetsCurrent   \n", "21      MAIN        Rule      MissingSummandRule                                                                      MAIN_BS_#3_SC_#3_AssetsNoncurrent   \n", "22      MAIN        Rule          MissingSumRule                                                                           MAIN_BS_#3_SC_#4_Liabilities   \n", "23      MAIN        Rule      MissingSummandRule                                                                    MAIN_BS_#3_SC_#5_LiabilitiesCurrent   \n", "24      MAIN        Rule      MissingSummandRule                                                                 MAIN_BS_#3_SC_#6_LiabilitiesNoncurrent   \n", "25      MAIN        Rule          MissingSumRule                                                                                MAIN_BS_#3_SC_#7_Assets   \n", "26      MAIN        Rule      MissingSummandRule                                                                           MAIN_BS_#3_SC_#8_Liabilities   \n", "27      MAIN        Rule      MissingSummandRule                                                                                MAIN_BS_#3_SC_#9_Equity   \n", "28      MAIN        Rule          MissingSumRule                                                                 MAIN_BS_#3_SC_#10_LiabilitiesAndEquity   \n", "29      MAIN        Rule      MissingSummandRule                                                                          MAIN_BS_#3_SC_#11_Liabilities   \n", "30      MAIN        Rule      MissingSummandRule                                                                               MAIN_BS_#3_SC_#12_Equity   \n", "31      MAIN       Group                                                                                                                  MAIN_BS_#4_SU   \n", "32      MAIN        Rule               SumUpRule                                                                                  MAIN_BS_#4_SU_#1_Cash   \n", "33      MAIN        Rule               SumUpRule                                                                      MAIN_BS_#4_SU_#2_RetainedEarnings   \n", "34      MAIN        Rule               SumUpRule                                                                          MAIN_BS_#4_SU_#3_LongTermDebt   \n", "35      MAIN        Rule               SumUpRule                                                                 MAIN_BS_#4_SU_#4_LiabilitiesNoncurrent   \n", "36      MAIN       Group                                                                                                              MAIN_BS_#5_SetSum   \n", "37      MAIN        Rule  SetSumIfOnlyOneSummand                                                           MAIN_BS_#5_SetSum_#1_Assets/AssetsNoncurrent   \n", "38      MAIN        Rule  SetSumIfOnlyOneSummand                                                              MAIN_BS_#5_SetSum_#2_Assets/AssetsCurrent   \n", "39      MAIN        Rule  SetSumIfOnlyOneSummand                                                 MAIN_BS_#5_SetSum_#3_Liabilities/LiabilitiesNoncurrent   \n", "40      MAIN        Rule  SetSumIfOnlyOneSummand                                                    MAIN_BS_#5_SetSum_#4_Liabilities/LiabilitiesCurrent   \n", "41      POST       Group                                                                                                                   POST_BS_POST   \n", "42      POST        Rule  PostCopyToFirstSummand                                                         POST_BS_POST_#1_AssetsCurrent/AssetsNoncurrent   \n", "43      POST        Rule  PostCopyToFirstSummand                                               POST_BS_POST_#2_LiabilitiesCurrent/LiabilitiesNoncurrent   \n", "44      POST        Rule           PostSetToZero                                                  POST_BS_POST_#3_Assets/AssetsCurrent/AssetsNoncurrent   \n", "45      POST        Rule           PostSetToZero                                   POST_BS_POST_#4_Liabilities/LiabilitiesCurrent/LiabilitiesNoncurrent   \n", "46      POST        Rule           PostSetToZero                                                                        POST_BS_POST_#5_TemporaryEquity   \n", "47      POST        Rule           PostSetToZero                                                                       POST_BS_POST_#6_RedeemableEquity   \n", "48      POST        Rule           PostSetToZero                                                                POST_BS_POST_#7_AdditionalPaidInCapital   \n", "49      POST        Rule           PostSetToZero                                                                     POST_BS_POST_#8_TreasuryStockValue   \n", "50     VALID  Validation       SumValidationRule                                                                                            AssetsCheck   \n", "51     VALID  Validation       SumValidationRule                                                                                       LiabilitiesCheck   \n", "52     VALID  Validation       SumValidationRule                                                                                            EquityCheck   \n", "53     VALID  Validation       SumValidationRule                                                                                      AssetsLiaEquCheck   \n", "\n", "                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            description  \n", "0                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        \n", "1                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               Deduplicates the dataframe based on the columns ['adsh', 'coreg', 'report', 'ddate', 'qtrs', 'tag', 'version', 'value']  \n", "2                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        \n", "3                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          Swaps the values between the tag 'Assets' and 'AssetsNoncurrent' if the following equation is True \"'AssetsNoncurrent' = 'Assets' + 'AssetsCurrent\"  and 'AssetsCurrent' > 0  \n", "4                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          Swaps the values between the tag 'Assets' and 'AssetsCurrent' if the following equation is True \"'AssetsCurrent' = 'Assets' + 'AssetsNoncurrent\"  and 'AssetsNoncurrent' > 0  \n", "5                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        \n", "6                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        \n", "7                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 Copies the values from AssetsNet to Assets if AssetsNet is not null and Assets is nan  \n", "8                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             Copies the values from CashAndCashEquivalentsAtCarryingValue to Cash if CashAndCashEquivalentsAtCarryingValue is not null and Cash is nan  \n", "9                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       Copies the values from LiabilitiesAndStockholdersEquity to LiabilitiesAndEquity if LiabilitiesAndStockholdersEquity is not null and LiabilitiesAndEquity is nan  \n", "10                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          Copies the values from RetainedEarningsAccumulatedDeficit to RetainedEarnings if RetainedEarningsAccumulatedDeficit is not null and RetainedEarnings is nan  \n", "11                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       \n", "12                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          Copies the values from StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest to HolderEquity if StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest is not null and HolderEquity is nan  \n", "13                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        Copies the values from PartnersCapital to HolderEquity if PartnersCapital is not null and HolderEquity is nan  \n", "14                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  Copies the values from StockholdersEquity to HolderEquity if StockholdersEquity is not null and HolderEquity is nan  \n", "15                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   Sums up the availalbe values in the columns ['TemporaryEquityAggregateAmountOfRedemptionRequirement', 'TemporaryEquityCarryingAmountAttributableToParent', 'TemporaryEquityRedemptionAmountAttributableToParent', 'TemporaryEquityRedemptionAmountAttributableToNoncontrollingInterest'] into the column 'TemporaryEquity', if the column 'TemporaryEquity' is nan  \n", "16                                                                                                                                                                                                                                                                                                                                                                                                                                                   Sums up the availalbe values in the columns ['RedeemableNoncontrollingInterestEquityCarryingAmount', 'RedeemableNoncontrollingInterestEquityRedemptionAmount', 'RedeemableNoncontrollingInterestEquityOtherCarryingAmount', 'RedeemableNoncontrollingInterestEquityOtherRedemptionAmount', 'RedeemablePreferredStockEquityOtherCarryingAmount', 'RedeemablePreferredStockEquityOtherRedemptionAmount'] into the column 'RedeemableEquity', if the column 'RedeemableEquity' is nan  \n", "17                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          Sums up the availalbe values in the columns ['HolderEquity', 'TemporaryEquity', 'RedeemableEquity'] into the column 'Equity', if the column 'Equity' is nan  \n", "18                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       \n", "19                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                Sums up the values in the columns ['AssetsCurrent', 'AssetsNoncurrent'] into the column 'Assets', if the column 'Assets' is nan and if all columns ['AssetsCurrent', 'AssetsNoncurrent'] have a value  \n", "20                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   Calculates the value for the missing column 'AssetsCurrent' by subtracting the values of the columns '['AssetsNoncurrent']' from the column 'Assets' if all of the columns ['Assets', 'AssetsNoncurrent'] are set.  \n", "21                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      Calculates the value for the missing column 'AssetsNoncurrent' by subtracting the values of the columns '['AssetsCurrent']' from the column 'Assets' if all of the columns ['Assets', 'AssetsCurrent'] are set.  \n", "22                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  Sums up the values in the columns ['LiabilitiesCurrent', 'LiabilitiesNoncurrent'] into the column 'Liabilities', if the column 'Liabilities' is nan and if all columns ['LiabilitiesCurrent', 'LiabilitiesNoncurrent'] have a value  \n", "23                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          Calculates the value for the missing column 'LiabilitiesCurrent' by subtracting the values of the columns '['LiabilitiesNoncurrent']' from the column 'Liabilities' if all of the columns ['Liabilities', 'LiabilitiesNoncurrent'] are set.  \n", "24                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             Calculates the value for the missing column 'LiabilitiesNoncurrent' by subtracting the values of the columns '['LiabilitiesCurrent']' from the column 'Liabilities' if all of the columns ['Liabilities', 'LiabilitiesCurrent'] are set.  \n", "25                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        Sums up the values in the columns ['Liabilities', 'Equity'] into the column 'Assets', if the column 'Assets' is nan and if all columns ['Liabilities', 'Equity'] have a value  \n", "26                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         Calculates the value for the missing column 'Liabilities' by subtracting the values of the columns '['Equity']' from the column 'Assets' if all of the columns ['Assets', 'Equity'] are set.  \n", "27                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    Calculates the value for the missing column 'Equity' by subtracting the values of the columns '['Liabilities']' from the column 'Assets' if all of the columns ['Assets', 'Liabilities'] are set.  \n", "28                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            Sums up the values in the columns ['Liabilities', 'Equity'] into the column 'LiabilitiesAndEquity', if the column 'LiabilitiesAndEquity' is nan and if all columns ['Liabilities', 'Equity'] have a value  \n", "29                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             Calculates the value for the missing column 'Liabilities' by subtracting the values of the columns '['Equity']' from the column 'LiabilitiesAndEquity' if all of the columns ['LiabilitiesAndEquity', 'Equity'] are set.  \n", "30                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        Calculates the value for the missing column 'Equity' by subtracting the values of the columns '['Liabilities']' from the column 'LiabilitiesAndEquity' if all of the columns ['LiabilitiesAndEquity', 'Liabilities'] are set.  \n", "31                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       \n", "32                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  Sums up the availalbe values in the columns ['CashAndCashEquivalentsAtFairValue', 'CashAndDueFromBanks', 'CashCashEquivalentsAndFederalFundsSold', 'RestrictedCashAndCashEquivalentsAtCarryingValue', 'CashAndCashEquivalentsInForeignCurrencyAtCarryingValue'] into the column 'Cash', if the column 'Cash' is nan  \n", "33                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           Sums up the availalbe values in the columns ['RetainedEarningsUnappropriated', 'RetainedEarningsAppropriated'] into the column 'RetainedEarnings', if the column 'RetainedEarnings' is nan  \n", "34                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 Sums up the availalbe values in the columns ['LongTermDebtNoncurrent', 'LongTermDebtAndCapitalLeaseObligations'] into the column 'LongTermDebt', if the column 'LongTermDebt' is nan  \n", "35  Sums up the availalbe values in the columns ['AccruedIncomeTaxesNoncurrent', 'DeferredAndPayableIncomeTaxes', 'DeferredIncomeTaxesAndOtherLiabilitiesNoncurrent', 'DeferredIncomeTaxLiabilitiesNet', 'DeferredTaxLiabilitiesNoncurrent', 'DefinedBenefitPensionPlanLiabilitiesNoncurrent', 'DerivativeLiabilitiesNoncurrent', 'FinanceLeaseLiabilityNoncurrent', 'LiabilitiesOtherThanLongtermDebtNoncurrent', 'LiabilitiesSubjectToCompromise', 'LiabilityForUncertainTaxPositionsNoncurrent', 'LongTermDebt', 'LongTermRetirementBenefitsAndOtherLiabilities', 'OperatingLeaseLiabilityNoncurrent', 'OtherLiabilitiesNoncurrent', 'OtherPostretirementDefinedBenefitPlanLiabilitiesNoncurrent', 'PensionAndOtherPostretirementDefinedBenefitPlansLiabilitiesNoncurrent', 'RegulatoryLiabilityNoncurrent', 'SelfInsuranceReserveNoncurrent'] into the column 'LiabilitiesNoncurrent', if the column 'LiabilitiesNoncurrent' is nan  \n", "36                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       \n", "37                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 Copies the value of the column 'AssetsCurrent' into the column 'Assets' and sets the columns ['AssetsNoncurrent'] to 0.0 if the column 'AssetsCurrent is set and the columns ['Assets', 'AssetsNoncurrent'] are nan.  \n", "38                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 Copies the value of the column 'AssetsNoncurrent' into the column 'Assets' and sets the columns ['AssetsCurrent'] to 0.0 if the column 'AssetsNoncurrent is set and the columns ['Assets', 'AssetsCurrent'] are nan.  \n", "39                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   Copies the value of the column 'LiabilitiesCurrent' into the column 'Liabilities' and sets the columns ['LiabilitiesNoncurrent'] to 0.0 if the column 'LiabilitiesCurrent is set and the columns ['Liabilities', 'LiabilitiesNoncurrent'] are nan.  \n", "40                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   Copies the value of the column 'LiabilitiesNoncurrent' into the column 'Liabilities' and sets the columns ['LiabilitiesCurrent'] to 0.0 if the column 'LiabilitiesNoncurrent is set and the columns ['Liabilities', 'LiabilitiesCurrent'] are nan.  \n", "41                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       \n", "42                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       Copies the value of the 'Assets' to the first summand 'AssetsCurrent' and set the other summands ['AssetsNoncurrent'] to 0.0 if 'Assets is set and the summands ['AssetsCurrent', 'AssetsNoncurrent'] are nan.  \n", "43                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         Copies the value of the 'Liabilities' to the first summand 'LiabilitiesCurrent' and set the other summands ['LiabilitiesNoncurrent'] to 0.0 if 'Liabilities is set and the summands ['LiabilitiesCurrent', 'LiabilitiesNoncurrent'] are nan.  \n", "44                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          Set the value of the ['Assets', 'AssetsCurrent', 'AssetsNoncurrent'] to 0.0 if all ['Assets', 'AssetsCurrent', 'AssetsNoncurrent'] are nan.  \n", "45                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            Set the value of the ['Liabilities', 'LiabilitiesCurrent', 'LiabilitiesNoncurrent'] to 0.0 if all ['Liabilities', 'LiabilitiesCurrent', 'LiabilitiesNoncurrent'] are nan.  \n", "46                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  Set the value of the ['TemporaryEquity'] to 0.0 if all ['TemporaryEquity'] are nan.  \n", "47                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                Set the value of the ['RedeemableEquity'] to 0.0 if all ['RedeemableEquity'] are nan.  \n", "48                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  Set the value of the ['AdditionalPaidInCapital'] to 0.0 if all ['AdditionalPaidInCapital'] are nan.  \n", "49                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            Set the value of the ['TreasuryStockValue'] to 0.0 if all ['TreasuryStockValue'] are nan.  \n", "50                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         Checks whether the sum of ['AssetsCurrent', 'AssetsNoncurrent'] equals the value in 'Assets'  \n", "51                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          Checks whether the sum of ['LiabilitiesCurrent', 'LiabilitiesNoncurrent'] equals the value in 'Liabilities'  \n", "52                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       Checks whether the sum of ['Equity', 'Liabilities'] equals the value in 'LiabilitiesAndEquity'  \n", "53                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     Checks whether the sum of ['Equity', 'Liabilities'] equals the value in 'Assets'  "]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["bs_standardizer_result_bag.process_description_df"]}, {"cell_type": "markdown", "id": "a4690046-7c6e-4806-8397-b0bc75a0169a", "metadata": {}, "source": ["Let's discuss a few of the rules in detail:\n", "- **PREPIVOT_BS_PREPIV_#1_DeDup**<br>de-duplication of the dataset, so that pivoting is possible. Note: the prepivot rules log into the `appliedprepivot_rules_log` dataframe\n", "- **PRE_BS_PRE_#1_Assets/AssetsNoncurrent**<br> is a preprocess correction rule. There are actually about 120 reports in which the tags for Assets and AssetsNoncurrent were swapped. \n", "- **MAIN_BS_#1_BR_#1_Assets**<br> Most of the reports use the Assets tag. However, there are about 240 reports who use the AssetsNet tag. If this is the case, the value is copied to the Assets column.\n", "- **MAIN_BS_#1_BR_#2_Cash, MAIN_BS_#1_BR_#3_LiabilitiesAndEquity, MAIN_BS_#1_BR_#4_RetainedEarnings** <br> These are mainly \"renaming\" rules, to have a shorter term. \n", "- **MAIN_BS_#2_EQ_#1_HolderEquity, MAIN_BS_#2_EQ_#2_HolderEquity, MAIN_BS_#2_EQ_#3_HolderEquity** <br> This rules ensures the precedence is considered when it comes to tags, that can contain the stockholderequity or the partnercapital. This are mainly three different tags, that have to be considered: StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest, StockholdersEquity, and PartnerCapital. Generally, it is either PartnerCapital or some kind of stockholderequity. Furthermore, StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest and StockholdersEquity can appear together. If they do appear together, StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest has precedence over StockholdersEquity, since StockholdersEquity is a child tag of StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest. As you can see in the `applied_rules_sum_s` data, two thirds of the entries have only StockholdersEquity present, one quarter has StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest present, and a few thousands have PartnerCapital set.\n", "- **MAIN_BS_#2_EQ_#4_TemporaryEquity, MAIN_BS_#2_EQ_#5_RedeemableEquity**<br> Sometimes, Equity does not only include HolderEquity, but also TemporaryEquity and/or RedeemableEquity. Both of them have several tags that can define values which belongs to these catagories. So these two rules sum up all possible values for Temporary- and RedeemableEquity.\n", "- **MAIN_BS_#2_EQ_#6_Equity** <br> The sum of HolderEquity, TemporaryEquity, and RedeemableEquity. Most of the time (90%) just HodlerEquity is present\n", "- **MAIN_BS_#3_SC_#1_Assets, MAIN_BS_#3_SC_#2_AssetsCurrent, MAIN_BS_#3_SC_#2_AssetsNonurrent** (and the same for Liabilities) <br> If just one of the three (Assets, AssetsCurrent, and AssetsNoncurrent) the missing one is calculated based on the rule: Assets = AssetsCurrent + AssetsNoncurrent\n", "- **MAIN_BS_#3_SC_#7_Assets, MAIN_BS_#3_SC_#8_Liabilities, MAIN_BS_#3_SC_#12_Equity** <br> If just one of Assets, Liabilities, and Equity is missing, it is calculated based on Assets = Liabilities + Equity\n", "- **MAIN_BS_#4_SU_#3_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MAIN_BS_#4_SU_#4_LiabilitiesNoncurrent** <br> Both rules are used to calculate LiabilitiesNoncurrent, if it is not contained directly, or if it wasn't possible to calculate it with one of the previous rules. As you can see from the `applied_rules_sum_s` many reports do not have an entry for LiabilitiesNoncurrent (in about 50% of the reports, it was possible to calculate it with rule MAIN_1_BS_#3_SC_#6_LiabilitiesNoncurrent, based on Liabilities and LiabilitiesCurrent and in about 15% of the cases it was possible to calculate it with these rules.\n", "- **SetSumIfOnlyOneSummand Rules** <br> After applying the previous rules, we fill still empty Assets with either the value of AssetsCurrent or AssetsNoncurrent, if one of them is present. (same applies for Liabilities). So we kinda assume if there is only AssetsCurrent, and no AssetsNoncurrent, that there is actually no AssetsNoncurrent and hence, Assets equals AssetsCurrent and AssetsNoncurrent is 0.0.\n", "- **PostCopyToFirstSummand Rules** <br> if there is just a value for Assets, but none for AssetsCurrent and AssetsNoncurrent, we assume that there is only AssetsCurrent. The value from Assets is copied into AssetsCurrent and AssetsNoncurrent is set to 0 (same for Liabilities).\n", "- **PostSetToZero Rules** <br> These rules simply set nan values for different colums to 0.0."]}, {"cell_type": "markdown", "id": "c059bbc8-d260-4bbf-b5f2-26fe09ef8e14", "metadata": {}, "source": ["### Overview on applied rules\n", "It might be interesting to know how many rules are applied in general per report. In this example, we just look at the MAIN and PRE rules:"]}, {"cell_type": "code", "execution_count": 19, "id": "a6033eef-7032-4541-b973-b3c1a823c897", "metadata": {}, "outputs": [{"data": {"text/plain": ["0         70\n", "1         47\n", "2       1067\n", "3       4827\n", "4      13464\n", "5      51296\n", "6      49549\n", "7     114253\n", "8      87029\n", "9      23416\n", "10      1514\n", "11        38\n", "Name: count_true_values, dtype: int64"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["# just use a shorter variable name\n", "df=bs_standardizer_result_bag.applied_rules_log_df\n", "\n", "# we are just interested in the MAIN and PRE rules\n", "filtered_columns = df.columns[df.columns.str.contains('MAIN|PRE')]\n", "\n", "# count how many True values are in each row\n", "df['count_true_values'] = df[filtered_columns].sum(axis='columns')\n", "\n", "df.count_true_values.value_counts().sort_index()"]}, {"cell_type": "code", "execution_count": 20, "id": "3876170e-e71d-4262-a356-9c24673d644c", "metadata": {"tags": []}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "plt.hist(df.count_true_values)\n", "\n", "# Show the plot\n", "plt.show()"]}, {"cell_type": "markdown", "id": "f58fe161-8959-4dc9-b00d-8c1492b99349", "metadata": {}, "source": ["### Showing the applied rules for a specific report number\n", "If we analys a single report and want to know which rules were applied, we can do that with the following code:"]}, {"cell_type": "code", "execution_count": 21, "id": "7768b3f5-c855-4a24-a316-be0f533dcfb8", "metadata": {}, "outputs": [{"data": {"text/plain": ["['MAIN_1_BS_#1_BR_#2_Cash<-CashAndCashEquivalentsAtCarryingValue',\n", " 'MAIN_1_BS_#1_BR_#3_LiabilitiesAndEquity<-LiabilitiesAndStockholdersEquity',\n", " 'MAIN_1_BS_#1_BR_#4_RetainedEarnings<-RetainedEarningsAccumulatedDeficit',\n", " 'MAIN_1_BS_#2_EQ_#3_HolderEquity<-StockholdersEquity',\n", " 'MAIN_1_BS_#2_EQ_#6_Equity',\n", " 'MAIN_1_BS_#4_SU_#3_LongTermDebt',\n", " 'POST_BS_POST_#5_TemporaryEquity',\n", " 'POST_BS_POST_#6_RedeemableEquity',\n", " 'POST_BS_POST_#7_AdditionalPaidInCapital',\n", " 'POST_BS_POST_#8_TreasuryStockValue']"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["apple_10k_2022 = \"0000320193-22-000108\"\n", "apple_10k_2022_applied_rules_log_df = bs_standardizer_result_bag.applied_rules_log_df[bs_standardizer_result_bag.applied_rules_log_df.adsh==apple_10k_2022]\n", "\n", "# filter for the applied MAIN,PRE, and POST rules\n", "main_rule_cols =  df.columns[df.columns.str.contains('MAIN|PRE|POST')]\n", "main_rule_df = apple_10k_2022_applied_rules_log_df[main_rule_cols]\n", "\n", "# get the applied rules, by using the True and False values of main_rule_df.iloc[0] as a mask on the columns index\n", "main_rule_df.columns[main_rule_df.iloc[0]].tolist()"]}, {"cell_type": "code", "execution_count": null, "id": "99923615-d3ac-4721-9da6-9a1b860087bd", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}
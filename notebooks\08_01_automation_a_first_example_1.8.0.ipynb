{"cells": [{"cell_type": "markdown", "id": "e6f7f954-caa7-4fa1-b0d3-4b8e370e6a38", "metadata": {}, "source": ["## A working example of the postupdateprocesses function: `secfsdstools.x_examples.automation.automation.define_extra_processes` (introduced in 1.8.0)"]}, {"cell_type": "markdown", "id": "17038fee-d067-44e5-abb4-e2b353764c36", "metadata": {"tags": []}, "source": ["<span style=\"color: #FF8C00;\">==========================================================</span>\n", "\n", "**If you find this tool useful, a sponsorship would be greatly appreciated!**\n", "\n", "**https://github.com/sponsors/HansjoergW**\n", "\n", "How to get in touch\n", "\n", "* Found a bug: https://github.com/HansjoergW/sec-fincancial-statement-data-set/issues\n", "* Have a remark: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/general\n", "* Have an idea: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/ideas\n", "* Have a question: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/q-a\n", "* Have something to show: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/show-and-tell\n", "\n", "<span style=\"color: #FF8C00;\">==========================================================</span>"]}, {"cell_type": "markdown", "id": "e1ccc375-1c12-43bf-99b4-ae00e4357329", "metadata": {}, "source": ["### What this pipeline creates\n", "\n", "It result in creating the following bags:\n", "\n", "- a single joined bag per statement (BS, IS, CF, ..) that will contain the data from all available quarters.\n", "- standardized bags for BS, IS, CF that contain data from all the available quarters.\n", "- a single joined bag containing all the data from all statements from all available quarters.\n", "\n", "Moreover, all these bags are updated in an efficient way, as soon as new data becomes available at the SEC website.\n", "\n", "Note: especially the creation of the final single joined bag is quite memory intensive. (Have a look at notebook 08_02_automation_a_memory_optimized_example for a version that needs less memory)\n", "\n", "\n", "### How to use the example\n", "\n", "The package `secfsdstools.x_examples.automation` provides a default implemention of a postupdateprocesses function: `define_extra_processes`.\n", "\n", "You can use this function directly by adding it to your configuration file together with some additional configuration parameters used by it: \n", "<pre>\n", "[DEFAULT]\n", "...\n", "postupdateprocesses=secfsdstools.x_examples.automation.automation.define_extra_processes\n", "\n", "[Filter]\n", "filtered_dir_by_stmt_joined = C:/data/sec/automated/_1_filtered_by_stmt_joined\n", "\n", "[<PERSON><PERSON>]\n", "concat_dir_by_stmt_joined = C:/data/sec/automated/_2_concat_by_stmt_joined\n", "\n", "[Standardizer]\n", "standardized_dir = C:/data/sec/automated/_3_standardized\n", "\n", "; [SingleBag]\n", "; singlebag_dir = C:/data/sec/automated/_4_single_bag\n", "</pre>\n", "\n", "The function will add 3 additional steps and a fourth optional step. The optional step is only executed if the needed parameter `singlebag_dir` is defined. \n", "\n", "These steps add the following processing:\n", "\n", "The first step creates a joined bag for every zip file which is filtered for 10-K and 10-Q reports only\n", "and also applies the filters `ReportPeriodRawFilter`, `MainCoregRawFilter`, `USDOnlyRawFilter`, `OfficialTagsOnlyRawFilter`. \n", "Furthermore, the data is also split by stmt.\n", "The filtered joined bag is stored under the path that is defined under `filtered_dir_by_stmt_joined` in the configuration file.\n", "The resulting directory structure will look like this:\n", "\n", "\n", "    <filtered_dir_by_stmt_joined>\n", "        quarter\n", "            2009q2.zip\n", "                BS\n", "                CF\n", "                CI\n", "                CP\n", "                EQ\n", "                IS\n", "            ...\n", "\n", "The second step creates a single joined bag for every statement (balance sheet, income statement,\n", "cash flow, cover page, ...) that contains the data from all zip files, resp from all the\n", "available quarters. These bags are stored under the path defined as `concat_dir_by_stmt_joined`.\n", "The resulting directory structure will look like this:\n", "\n", "    <concat_dir_by_stmt_joined>\n", "        BS\n", "        CF\n", "        CI\n", "        CP\n", "        EQ\n", "        IS    \n", "\n", "\n", "The third step standardizes the data for balance sheet, income statement, and cash flow and stores\n", "the standardized bags under the path that is defined as `standardized_dir`.\n", "The resulting structure will look like this:\n", "\n", "    <standardized_dir>\n", "        BS\n", "        CF\n", "        IS    \n", "    \n", "\n", "The fourth step is optional and is only executed if the configuration file contains an entry\n", "for `singlebag_dir`. If it does, it will create a single joined bag concatenating all the bags\n", "created in the second step, so basically creating a single bag that contains all the filtered data from\n", "all the available zip files, resp. quarters. \n", "In framework versions prior to version 2.1, this step needed quite a lot of memory. This was improved \n", "in version 2.1, which does the concatenation directly on the filesystem, without loading the data into\n", "memory and hence has a very low memory footproint.\n", "The resulting directory structure will look like this:\n", "\n", "    <singlebag_dir>\n", "        all\n", "\n", "\n", "Hint -> data can directly be loaded with the JoinedDataBag load, resp with StandardizedBag load.\n", "\n", "\n", "### How the example is implemented.\n", "\n", "Let us have a look at the implementation of the the function `define_extra_processes`:\n"]}, {"cell_type": "code", "execution_count": 1, "id": "d6d7f489-3298-47a3-acf4-d9ec28bc6a2c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-05 06:20:25,256 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n"]}], "source": ["from typing import List\n", "\n", "from secfsdstools.a_config.configmodel import Configuration\n", "from secfsdstools.c_automation.task_framework import AbstractProcess\n", "\n", "# Note: that we are using a few basic implementations of the g_pipeline package\n", "from secfsdstools.g_pipelines.concat_process import ConcatByNewSubfoldersProcess, \\\n", "    ConcatByChangedTimestampProcess\n", "from secfsdstools.g_pipelines.filter_process import FilterProcess\n", "from secfsdstools.g_pipelines.standardize_process import StandardizeProcess\n", "\n", "\n", "def define_extra_processes(configuration: Configuration) -> List[AbstractProcess]:\n", "\n", "    # first, we read the configuration parameters. \n", "    joined_by_stmt_dir = configuration.config_parser.get(section=\"Filter\",\n", "                                                         option=\"filtered_dir_by_stmt_joined\")\n", "\n", "    concat_by_stmt_dir = configuration.config_parser.get(section=\"Concat\",\n", "                                                         option=\"concat_dir_by_stmt_joined\")\n", "\n", "    standardized_dir = configuration.config_parser.get(section=\"Standardizer\",\n", "                                                       option=\"standardized_dir\")\n", "\n", "    # note that the single bag dir is optional and therefore has a fallback value of \"\"\n", "    singlebag_dir = configuration.config_parser.get(section=\"SingleBag\",\n", "                                                    option=\"singlebag_dir\",\n", "                                                    fallback=\"\")\n", "\n", "    \n", "    processes: List[AbstractProcess] = []\n", "\n", "    # The first step filters the data. It is apllied on the data of every available transformed parquet folder.\n", "    # If nothing else is configured, it will filter for 10-K and 10-Q reports only.\n", "    # Moreover, it will also apply the filters ReportPeriodRawFilter, MainCoregRawFilter, USDOnlyRawFilter, and OfficialTagsOnlyRawFilter. \n", "    # You can actually configure wether you want the data to be saved as RawDataBag or a JoinedDataBag.\n", "    # In our case, we will use the JoinedDataBag.\n", "    # As another parameter, we can configure that the data is split up by stmt. So the data for every statement is saved in its on subfolder.\n", "    # Therefore, the result will be a folder for every quarter containing subfolders for every statement (BS, CF, CI, CP, EQ, and IS).\n", "    # Note that the execution processed in parallel.\n", "    processes.append(\n", "        # 1. Filter, join, and save by stmt\n", "        FilterProcess(db_dir=configuration.db_dir,\n", "                      target_dir=joined_by_stmt_dir,\n", "                      bag_type=\"joined\",\n", "                      save_by_stmt=True,\n", "                      execute_serial=configuration.no_parallel_processing\n", "                      )\n", "    )\n", "\n", "    # Next, we want to create a single JoinedBag for every stmt that should contain the data from all quarters.\n", "    # To do that, we can use the ConcatByNewSubfoldersProcess. \n", "    # It will load the joined bags from the appropriate subfolders inside the root_dir, concat them into single \n", "    # bag that is then stored in the target_dir.\n", "    # Note: This AbstractProcess implementation stores a metainf file in the target folder which contains a list\n", "    # of all the subfolders from the root-dir, that are already concatenated into the target. Using that file,\n", "    # it will only be executed, if a new subfolder appears in the root_dir.\n", "    # After these steps, we will have a joined bag for every statement (BS, CF, IS, ...) containing the data from all\n", "    # available quarters.\n", "    processes.extend([\n", "        # 2. building datasets with all entries by stmt\n", "        ConcatByNewSubfoldersProcess(root_dir=f\"{joined_by_stmt_dir}/quarter\",\n", "                                     target_dir=f\"{concat_by_stmt_dir}/BS\",\n", "                                     pathfilter=\"*/BS\"\n", "                                     ),\n", "        ConcatByNewSubfoldersProcess(root_dir=f\"{joined_by_stmt_dir}/quarter\",\n", "                                     target_dir=f\"{concat_by_stmt_dir}/CF\",\n", "                                     pathfilter=\"*/CF\"\n", "                                     ),\n", "        ConcatByNewSubfoldersProcess(root_dir=f\"{joined_by_stmt_dir}/quarter\",\n", "                                     target_dir=f\"{concat_by_stmt_dir}/CI\",\n", "                                     pathfilter=\"*/CI\"\n", "                                     ),\n", "        ConcatByNewSubfoldersProcess(root_dir=f\"{joined_by_stmt_dir}/quarter\",\n", "                                     target_dir=f\"{concat_by_stmt_dir}/CP\",\n", "                                     pathfilter=\"*/CP\"\n", "                                     ),\n", "        ConcatByNewSubfoldersProcess(root_dir=f\"{joined_by_stmt_dir}/quarter\",\n", "                                     target_dir=f\"{concat_by_stmt_dir}/EQ\",\n", "                                     pathfilter=\"*/EQ\"\n", "                                     ),\n", "        ConcatByNewSubfoldersProcess(root_dir=f\"{joined_by_stmt_dir}/quarter\",\n", "                                     target_dir=f\"{concat_by_stmt_dir}/IS\",\n", "                                     pathfilter=\"*/IS\"\n", "                                     )\n", "    ])\n", "\n", "    # As a third step, we standardize the data for BS, CF, and IS. \n", "    # This can be done with the StandardizeProcess. As input it expects a folder that contains the subfolders BS, CF, and IS\n", "    processes.append(\n", "        # 3. Standardize the data\n", "        StandardizeProcess(root_dir=f\"{concat_by_stmt_dir}\",\n", "                           target_dir=standardized_dir),\n", "    )\n", "\n", "    # If the parameter singlebag_dir is configured, we use the ConcatByChangedTimestampProcess\n", "    # to concat all the bags from the second step together into a single bag. \n", "    # This produces a single bag containing all the available data.\n", "    # Note: this used a lot of memory prior to version 2.1\n", "    # Note: this implementation stores a metainf file in the target folder, that contains the \n", "    # timestamp of the latest modifications inside the root_dir. Threfore, it will only be executed\n", "    # if any file in the root_dir has a newer timestamp than the saved one.\n", "    if singlebag_dir != \"\":\n", "        # 4. create a single joined bag with all the data, if it is defined\n", "        processes.append(\n", "            ConcatByChangedTimestampProcess(\n", "                root_dir=f\"{concat_by_stmt_dir}/\",\n", "                target_dir=f\"{singlebag_dir}/all\",\n", "            )\n", "        )\n", "\n", "    return processes\n"]}, {"cell_type": "markdown", "id": "f71f8553-b63b-4c8c-bc8b-891118d77f84", "metadata": {}, "source": ["If you want to know more about the implementation details of the FilterProcess, ConcatByNewSubfoldersProcess, ConcatByChangedTimestampProcess, and StandardizeProcess, have a look at the comments in the code."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "code", "execution_count": 1, "id": "5974588b-0809-468d-b6e3-e2df73afaacd", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "# ensure that all columns are shown and that colum content is not cut\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.max_colwidth', None)\n", "pd.set_option('display.width',1000)\n", "pd.set_option('display.max_rows', 500) # ensure that all rows are shown"]}, {"cell_type": "markdown", "id": "333f3746-2750-4526-a4d9-b97ca6a1e167", "metadata": {}, "source": ["# Bulk Data Processing Deep Dive\n", "The main advantage of this library is that the all data is downloaded down to your computer and therefore makes it easy to analyze all the data at once. \n", "\n", "For instance, if you want to implement your own screener.\n", "\n", "Just on the file system, the size of all parquet files is more than 5 GB and growing with every quarter of new data. Since the parquet format is also storage optimized, loading all the data into memory would need significantly more memory than a standard computer/laptop usually provides.\n", "\n", "Hence it is important to filter the data during the loading process, so that you only load the data into memory that is really needed."]}, {"cell_type": "markdown", "id": "b55f7fa2-9c40-4e7e-8f0f-9f3b00f41044", "metadata": {}, "source": ["**NOTE**: with the new automation feature (introduced with version 1.8.0), it is possible to add processing steps directly to the normal update process. That means, whenever a new file is detected on SEC.gov, not only downloading, transforming into parquet, and indexing it can be executed, but also customized additional processing steps - like the steps described in this notebook which filters the data and produces files containing all the data. For an example, that directly can be used and shows how it works, have a look at the 08_00_automation_bascis notebook."]}, {"cell_type": "markdown", "id": "901ec057-a8b4-4be9-bbf5-3495424d879a", "metadata": {"tags": []}, "source": ["<span style=\"color: #FF8C00;\">==========================================================</span>\n", "\n", "**If you find this tool useful, a sponsorship would be greatly appreciated!**\n", "\n", "**https://github.com/sponsors/HansjoergW**\n", "\n", "How to get in touch\n", "\n", "* Found a bug: https://github.com/HansjoergW/sec-fincancial-statement-data-set/issues\n", "* Have a remark: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/general\n", "* Have an idea: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/ideas\n", "* Have a question: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/q-a\n", "* Have something to show: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/show-and-tell\n", "\n", "<span style=\"color: #FF8C00;\">==========================================================</span>"]}, {"cell_type": "markdown", "id": "8ba379f1-5124-4bc4-bd2d-3cfc292ba03f", "metadata": {}, "source": ["## Prepare Datasets\n", "In the first part of this notebook, we will create four different datasets for all the balance sheet datapoints, the cashflow datapoints, the income statement datapoints, and the cover page datapoints.\n", "These datasets will be stored in their own directories, so that they can be easily loaded afterwards. Moreover, we will store the raw version (where the num_df and the pre_df are not joined) and the joined version, where num_df and pre_df are joined. Depending on what you want to do/analyze, you can use either one.\n", "\n", "**Note:** The code that is explained here is also available in the modul `bulk_loading` which is inside the `secfsdstools.u_usecase` package.\n", "\n", "This notebook will show two approaches. The first one is loading all the data in parallel, which you can do if you have enough resources in your computer. The second is doing it sequentially, which is slower, but needs less memory. In the end, you will create these datasets once and extend it when new quarterly zip files arrive, or you will recreate them once every quarter. So in the end it doesn't really matter if the process takes 15 minutes or an hour.\n", "\n", "We will also apply different filters:\n", "\n", "* only filter 10-K and 10-Q reports during loading\n", "* `ReportPeriodRawFilter`: since we are only interested in datapoints that belong to the period of the report\n", "* `MainCoregRawFilter`: since we don't want to see datapoints of a subsidiary\n", "* `OfficialTagsOnlyRawFilter`: since we want to be able to compare the content and therefore don't want to read tags that or not in the standard sec xbrl definition\n", "* `USDOnlyRawFilter`: since we are not interested in money datapoints that are not in USD"]}, {"cell_type": "markdown", "id": "c1904d65-28df-47e1-9385-9b60098afc6e", "metadata": {}, "source": ["### Basics\n", "First, we will defines some basic stuff that is used by both approaches."]}, {"cell_type": "code", "execution_count": 2, "id": "d0d01c1f-8479-4360-aab6-164068424126", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-03 06:20:18,881 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n"]}], "source": ["import os\n", "from secfsdstools.d_container.databagmodel import RawDataBag, JoinedDataBag\n", "from secfsdstools.e_collector.zipcollecting import ZipCollector"]}, {"cell_type": "markdown", "id": "f6e87306-9242-4523-9b81-0293069ddebe", "metadata": {}, "source": ["The following list defines which statements we want to load."]}, {"cell_type": "code", "execution_count": 3, "id": "594116ef-3645-4dcc-9942-201bfe343e07", "metadata": {"tags": []}, "outputs": [], "source": ["statements_to_load = [\"BS\", \"CF\", \"IS\", \"CP\"]"]}, {"cell_type": "markdown", "id": "133b535c-9ad4-4b00-9d39-bdf3dad43663", "metadata": {}, "source": ["Next, we define a filter function, that defines the whole chain. As mentioned in the 04_collector_deep_dive.ipynt notebook, we have to define the imports inside the function itself, if we want to use it in jupyter together with parallization."]}, {"cell_type": "code", "execution_count": 4, "id": "6d908348-8e48-461f-a26c-ec0d0f2bbfd0", "metadata": {"tags": []}, "outputs": [], "source": ["def postloadfilter(databag: RawDataBag) -> RawDataBag:\n", "    from secfsdstools.e_filter.rawfiltering import ReportPeriodRawFilter, MainCoregRawFilter, OfficialTagsOnlyRawFilter, USDOnlyRawFilter\n", "\n", "    return databag[ReportPeriodRawFilter()][MainCoregRawFilter()][OfficialTagsOnlyRawFilter()][USDOnlyRawFilter()]"]}, {"cell_type": "markdown", "id": "a0ff096f-b7e8-4e4a-a661-2a95e4b922f5", "metadata": {}, "source": ["Next is a simple function that takes a raw databag and creates the joined databag. Both, the rawdatabag and the joined databag are then stored in a specific folder.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "9395fe8a-06c3-4ca4-9953-fc22fa58b9f4", "metadata": {"tags": []}, "outputs": [], "source": ["def save_databag(databag: RawDataBag, financial_statement: str, base_path: str) -> JoinedDataBag:\n", "    target_path_raw = os.path.join(base_path, financial_statement, 'raw')\n", "    print(f\"store rawdatabag under {target_path_raw}\")\n", "    os.makedirs(target_path_raw, exist_ok=True)\n", "    databag.save(target_path_raw)\n", "    \n", "    target_path_joined = os.path.join(base_path, financial_statement, 'joined')\n", "    os.makedirs(target_path_joined, exist_ok=True)\n", "    print(\"create joined databag\")\n", "    joined_databag = databag.join()\n", "    \n", "    print(f\"store joineddatabag under {target_path_joined}\")\n", "    joined_databag.save(target_path_joined)\n", "    return joined_databag"]}, {"cell_type": "markdown", "id": "c564ba5e-3334-47a9-94c7-53ba5df7a9c5", "metadata": {}, "source": ["### Parallel Data Loading\n", "As stated above, we want to load all available 10-K and 10-Q reports. Therefore, we can use the `ZipCollector`which provides an option to load data from all available zip files. \n", "\n", "Moreover, the implementation of the ziploader uses all your cores in order to load data from your disk into memory. So you don't have to implement the parallization yourself. There are 50+ zip files that have to be loaded, so if you have 4 cores, you will load 4 at one time.\n", "\n", "Also, the `ZipCollector` provides parameters for filtering the report type (10-K and 10-Q) amd the financial statement type (Balance Sheet, Casch Flow, or Income Statement). These filters are directly applied during loading, since the data is stored in Parquet format. This will already reduce that amount of data that is being loaded into memory significantly.\n", "\n", "Moreover, it also provides the post_load_filter which we can use to apply the other filters, defined in the postloadfilter function."]}, {"cell_type": "code", "execution_count": 7, "id": "d18ac72e-0020-4ea5-9cd8-373e2f143fde", "metadata": {"tags": []}, "outputs": [], "source": ["def load_all_financial_statements_parallel(financial_statement: str) -> RawDataBag:\n", "    \"\"\" \n", "    financial_statement: either \"BS\", \"CF\", or \"IS\"\n", "    \"\"\"\n", "\n", "    collector: ZipCollector = ZipCollector.get_all_zips(forms_filter=[\"10-K\", \"10-Q\"],\n", "                                                        stmt_filter=[financial_statement],\n", "                                                        post_load_filter=postloadfilter)\n", "    return collector.collect()"]}, {"cell_type": "markdown", "id": "db367d8c-bda3-4ab1-a555-0e31c6f6bd52", "metadata": {}, "source": ["We loop over the statements that we want to load and collect their datapoints into a specific dataset.\n", "\n", "This process will take several minutes. On my laptop the execution time was approximately 30 minutes (32GB Ram / 4/8 Cores)."]}, {"cell_type": "code", "execution_count": 8, "id": "b158ec71-ce9d-49ea-9a23-6f111fcc8811", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-02 12:57:51,516 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 12:57:51,521 [INFO] parallelexecution      items to process: 63\n", "2025-02-02 13:03:12,884 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under ./set/parallel/BS\\raw\n", "create joined databag\n", "store joineddatabag under ./set/parallel/BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 13:05:41,117 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 13:05:41,125 [INFO] parallelexecution      items to process: 63\n", "2025-02-02 13:11:07,236 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under ./set/parallel/CF\\raw\n", "create joined databag\n", "store joineddatabag under ./set/parallel/CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 13:13:25,329 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 13:13:25,342 [INFO] parallelexecution      items to process: 63\n", "2025-02-02 13:18:26,399 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under ./set/parallel/IS\\raw\n", "create joined databag\n", "store joineddatabag under ./set/parallel/IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 13:20:40,704 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 13:20:40,712 [INFO] parallelexecution      items to process: 63\n", "2025-02-02 13:25:13,782 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under ./set/parallel/CP\\raw\n", "create joined databag\n", "store joineddatabag under ./set/parallel/CP\\joined\n"]}], "source": ["for statement_to_load in statements_to_load:\n", "    rawdatabag = load_all_financial_statements_parallel(financial_statement=statement_to_load)\n", "    save_databag(databag=rawdatabag, financial_statement=statement_to_load, base_path=\"./set/parallel/\")"]}, {"cell_type": "markdown", "id": "0c1b0d7d-683d-40fd-937c-7242c5eb84ce", "metadata": {}, "source": ["After processing, you have the following structure and sizes (with data up to 2024 Q4):\n", "<pre>\n", "- set/parallel\n", "  - BS\n", "    - raw     : 1.44 GB\n", "    - joined  :  650 MB\n", "  - CF\n", "    - raw     : 1.43 GB\n", "    - joined  :  426 MB\n", "  - IS\n", "    - raw     : 1.36 GB\n", "    - joined  :  483 MB\n", "  - CP\n", "    - raw     : 1.26 GB\n", "    - joined  :   19 MB    \n", "</pre>\n", "\n", "Especially the joined databags have a size that can be easily loaded. Moreover, loading them just takes a few seconds. "]}, {"cell_type": "code", "execution_count": 9, "id": "686cf90c-fe3e-4f28-a987-42d5961695fa", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loaded BS databag:  (19657047, 17)\n", "loaded CF databag:  (12883947, 17)\n", "loaded IS databag:  (17412439, 17)\n", "loaded CP databag:  (76, 17)\n"]}], "source": ["#load BS joined data\n", "joinedBS = JoinedDataBag.load(\"./set/parallel/BS/joined\")\n", "print(\"loaded BS databag: \", joinedBS.pre_num_df.shape)\n", "joinedCF = JoinedDataBag.load(\"./set/parallel/CF/joined\")\n", "print(\"loaded CF databag: \", joinedCF.pre_num_df.shape)\n", "joinedIS = JoinedDataBag.load(\"./set/parallel/IS/joined\")\n", "print(\"loaded IS databag: \", joinedIS.pre_num_df.shape)\n", "joinedCP = JoinedDataBag.load(\"./set/parallel/CP/joined\")\n", "print(\"loaded CP databag: \", joinedCP.pre_num_df.shape)"]}, {"cell_type": "markdown", "id": "1c15bf60-8266-405b-bf17-f1470c48933b", "metadata": {}, "source": ["### Serial Data Loading\n", "As mentioned above, parallel loading requires some minimal ressources on your laptop/computer. However, using a serial process, you still can create the databags for all balance sheet, cash flow, and income statments. Of course, we need more code and we will also save intermediate results on disk."]}, {"cell_type": "markdown", "id": "b26d7a4d-1e01-4349-b400-fcadfea40fb5", "metadata": {}, "source": ["The first thing which we need, is a list of all available zip-files. Actually, we just can copy the code from `ZipCollector.get_all_zips()`."]}, {"cell_type": "code", "execution_count": 6, "id": "96fdc620-20d7-4d3b-9294-9b1b696241e6", "metadata": {"tags": []}, "outputs": [], "source": ["from typing import List\n", "from secfsdstools.a_config.configmgt import ConfigurationManager\n", "from secfsdstools.c_index.indexdataaccess import ParquetDBIndexingAccessor\n", "\n", "def read_all_zip_names() -> List[str]:\n", "    configuration = ConfigurationManager.read_config_file()\n", "    dbaccessor = ParquetDBIndexingAccessor(db_dir=configuration.db_dir)\n", "\n", "    # exclude 2009q1.zip, since this is empty and causes an error when it is read with a filter\n", "    filenames = [x.fileName for x in dbaccessor.read_all_indexfileprocessing() if not x.fullPath.endswith(\"2009q1.zip\")]\n", "    return filenames"]}, {"cell_type": "code", "execution_count": 7, "id": "f2f566bf-0422-40dc-8d2f-b2bce2a842c6", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:22:59,558 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n"]}, {"name": "stdout", "output_type": "stream", "text": ["63\n", "['2012q4.zip', '2013q3.zip', '2018q2.zip', '2024q1.zip', '2015q1.zip', '2018q4.zip', '2019q1.zip', '2014q2.zip', '2012q1.zip', '2022q3.zip', '2016q3.zip', '2023q1.zip', '2024q3.zip', '2015q2.zip', '2015q3.zip', '2014q3.zip', '2013q2.zip', '2018q1.zip', '2014q1.zip', '2009q4.zip', '2019q2.zip', '2022q4.zip', '2011q1.zip', '2010q2.zip', '2017q1.zip', '2020q1.zip', '2016q1.zip', '2020q2.zip', '2024q4.zip', '2009q2.zip', '2021q3.zip', '2017q3.zip', '2021q4.zip', '2020q4.zip', '2011q2.zip', '2010q3.zip', '2014q4.zip', '2021q2.zip', '2022q2.zip', '2016q4.zip', '2019q3.zip', '2013q4.zip', '2010q1.zip', '2024q2.zip', '2022q1.zip', '2023q3.zip', '2009q3.zip', '2010q4.zip', '2018q3.zip', '2023q2.zip', '2017q2.zip', '2012q3.zip', '2011q3.zip', '2016q2.zip', '2012q2.zip', '2023q4.zip', '2019q4.zip', '2015q4.zip', '2021q1.zip', '2011q4.zip', '2013q1.zip', '2020q3.zip', '2017q4.zip']\n"]}], "source": ["all_zip_names = read_all_zip_names()\n", "print(len(all_zip_names))\n", "print(all_zip_names)"]}, {"cell_type": "markdown", "id": "d8ab160d-2b3c-439b-8fa1-ec87950138ce", "metadata": {}, "source": ["**Prepare the temporary dataset**\n", "Next, prepare the data for every single zip-file. So for every zip-file, we collect the datapoints for BS, CF, and IS and apply the aove defined filters. The following functions takes care of that."]}, {"cell_type": "code", "execution_count": 8, "id": "4d0d98e6-e6c5-418a-b775-320245dfecda", "metadata": {"tags": []}, "outputs": [], "source": ["def build_tmp_set(financial_statement: str, file_names: List[str], target_path: str = \"set/tmp/\"):\n", "    \"\"\" This function reads the data in sequence from the provided list of zip file names. It filters according to the \n", "        defined financial_statement and stores the data in specific subfolders.\n", "        \n", "        the folder structure will look like\n", "        <target_path>/<file_name>/<financial_statement>/raw\n", "        <target_path>/<file_name>/<financial_statement>/joined                                       \n", "        \"\"\"\n", "    \n", "    for file_name in file_names:\n", "        collector = ZipCollector.get_zip_by_name(name=file_name,\n", "                                 forms_filter=[\"10-K\", \"10-Q\"],\n", "                                 stmt_filter=[financial_statement],\n", "                                 post_load_filter=postloadfilter)\n", "\n", "        rawdatabag = collector.collect()\n", "\n", "        base_path = os.path.join(target_path, file_name)\n", "        # saving the raw databag, joining and saving the joined databag\n", "        save_databag(databag=rawdatabag, financial_statement=financial_statement, base_path=base_path)"]}, {"cell_type": "markdown", "id": "93d62cba-893c-4f41-9b06-ae99f8b87244", "metadata": {"tags": []}, "source": ["We call the function for every statement (BS, CF, IS, and CP).\n", "As a reference, running all four cells took about 15 minutes on my laptop (32GB Ram / 4/8 Cores)"]}, {"cell_type": "code", "execution_count": 9, "id": "56ae6366-922c-4f32-b63c-e0cc1eed53a5", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:23:37,097 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:23:37,102 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:23:37,103 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2012q4.zip\n", "2025-02-02 14:23:40,307 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2012q4.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2012q4.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:23:42,806 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:23:42,809 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:23:42,810 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2013q3.zip\n", "2025-02-02 14:23:45,864 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2013q3.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2013q3.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:23:48,467 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:23:48,470 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:23:48,470 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2018q2.zip\n", "2025-02-02 14:23:51,147 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2018q2.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2018q2.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:23:53,597 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:23:53,600 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:23:53,602 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2024q1.zip\n", "2025-02-02 14:23:57,296 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2024q1.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2024q1.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:24:00,231 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:24:00,234 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:24:00,235 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2015q1.zip\n", "2025-02-02 14:24:03,489 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2015q1.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2015q1.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:24:06,029 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:24:06,032 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:24:06,033 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2018q4.zip\n", "2025-02-02 14:24:08,650 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2018q4.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2018q4.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:24:11,183 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:24:11,187 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:24:11,188 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2019q1.zip\n", "2025-02-02 14:24:14,214 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2019q1.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2019q1.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:24:16,568 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:24:16,571 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:24:16,572 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2014q2.zip\n", "2025-02-02 14:24:19,462 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2014q2.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2014q2.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:24:22,066 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:24:22,069 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:24:22,070 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2012q1.zip\n", "2025-02-02 14:24:24,765 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2012q1.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2012q1.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:24:26,994 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:24:26,997 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:24:26,998 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2022q3.zip\n", "2025-02-02 14:24:30,242 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2022q3.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2022q3.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:24:33,082 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:24:33,085 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:24:33,086 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2016q3.zip\n", "2025-02-02 14:24:35,702 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2016q3.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2016q3.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:24:38,083 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:24:38,086 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:24:38,087 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2023q1.zip\n", "2025-02-02 14:24:41,616 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2023q1.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2023q1.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:24:44,253 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:24:44,256 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:24:44,257 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2024q3.zip\n", "2025-02-02 14:24:48,263 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2024q3.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2024q3.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:24:51,355 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:24:51,358 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:24:51,359 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2015q2.zip\n", "2025-02-02 14:24:54,054 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2015q2.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2015q2.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:24:56,547 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:24:56,550 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:24:56,552 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2015q3.zip\n", "2025-02-02 14:24:59,530 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2015q3.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2015q3.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:25:02,232 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:25:02,236 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:25:02,237 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2014q3.zip\n", "2025-02-02 14:25:05,535 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2014q3.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2014q3.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:25:08,219 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:25:08,222 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:25:08,223 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2013q2.zip\n", "2025-02-02 14:25:11,104 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2013q2.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2013q2.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:25:13,590 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:25:13,593 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:25:13,594 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2018q1.zip\n", "2025-02-02 14:25:16,740 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2018q1.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2018q1.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:25:19,863 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:25:19,867 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:25:19,869 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2014q1.zip\n", "2025-02-02 14:25:25,171 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2014q1.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2014q1.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:25:28,205 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:25:28,208 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:25:28,209 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2009q4.zip\n", "2025-02-02 14:25:28,395 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2009q4.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2009q4.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:25:28,629 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:25:28,632 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:25:28,633 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2019q2.zip\n", "2025-02-02 14:25:32,101 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2019q2.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2019q2.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:25:34,725 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:25:34,728 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:25:34,729 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2022q4.zip\n", "2025-02-02 14:25:38,853 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2022q4.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2022q4.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:25:41,910 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:25:41,913 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:25:41,914 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2011q1.zip\n", "2025-02-02 14:25:42,575 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2011q1.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2011q1.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:25:43,158 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:25:43,161 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:25:43,162 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2010q2.zip\n", "2025-02-02 14:25:43,338 [INFO] parallelexecution      commited chunk: 0\n", "2025-02-02 14:25:43,541 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2010q2.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2010q2.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:25:43,544 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:25:43,545 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2017q1.zip\n", "2025-02-02 14:25:46,913 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2017q1.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2017q1.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:25:49,560 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:25:49,563 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:25:49,564 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2020q1.zip\n", "2025-02-02 14:25:53,110 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2020q1.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2020q1.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:25:55,625 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:25:55,628 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:25:55,629 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2016q1.zip\n", "2025-02-02 14:25:59,082 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2016q1.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2016q1.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:26:01,630 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:26:01,633 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:26:01,634 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2020q2.zip\n", "2025-02-02 14:26:04,399 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2020q2.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2020q2.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:26:06,703 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:26:06,706 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:26:06,707 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2024q4.zip\n", "2025-02-02 14:26:10,863 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2024q4.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2024q4.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:26:14,149 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:26:14,152 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:26:14,153 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2009q2.zip\n", "2025-02-02 14:26:14,185 [INFO] parallelexecution      commited chunk: 0\n", "2025-02-02 14:26:14,268 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:26:14,271 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:26:14,272 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2021q3.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2009q2.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2009q2.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:26:17,505 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2021q3.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2021q3.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:26:20,219 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:26:20,222 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:26:20,223 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2017q3.zip\n", "2025-02-02 14:26:23,065 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2017q3.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2017q3.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:26:25,757 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:26:25,760 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:26:25,762 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2021q4.zip\n", "2025-02-02 14:26:29,424 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2021q4.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2021q4.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:26:32,276 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:26:32,279 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:26:32,280 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2020q4.zip\n", "2025-02-02 14:26:35,382 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2020q4.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2020q4.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:26:37,926 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:26:37,929 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:26:37,929 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2011q2.zip\n", "2025-02-02 14:26:38,443 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2011q2.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2011q2.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:26:38,994 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:26:38,997 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:26:38,998 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2010q3.zip\n", "2025-02-02 14:26:39,461 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2010q3.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2010q3.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:26:39,980 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:26:39,983 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:26:39,984 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2014q4.zip\n", "2025-02-02 14:26:43,016 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2014q4.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2014q4.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:26:45,674 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:26:45,677 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:26:45,678 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2021q2.zip\n", "2025-02-02 14:26:48,410 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2021q2.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2021q2.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:26:50,896 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:26:50,899 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:26:50,900 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2022q2.zip\n", "2025-02-02 14:26:53,859 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2022q2.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2022q2.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:26:56,824 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:26:56,827 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:26:56,829 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2016q4.zip\n", "2025-02-02 14:27:01,987 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2016q4.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2016q4.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:27:04,612 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:27:04,615 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:27:04,616 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2019q3.zip\n", "2025-02-02 14:27:07,781 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2019q3.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2019q3.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:27:10,454 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:27:10,457 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:27:10,458 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2013q4.zip\n", "2025-02-02 14:27:13,589 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2013q4.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2013q4.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:27:16,393 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:27:16,396 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:27:16,397 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2010q1.zip\n", "2025-02-02 14:27:16,620 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2010q1.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2010q1.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:27:16,873 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:27:16,876 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:27:16,877 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2024q2.zip\n", "2025-02-02 14:27:20,682 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2024q2.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2024q2.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:27:24,496 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:27:24,499 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:27:24,500 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2022q1.zip\n", "2025-02-02 14:27:27,788 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2022q1.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2022q1.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:27:30,568 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:27:30,571 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:27:30,572 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2023q3.zip\n", "2025-02-02 14:27:34,901 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2023q3.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2023q3.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:27:37,981 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:27:37,984 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:27:37,985 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2009q3.zip\n", "2025-02-02 14:27:38,137 [INFO] parallelexecution      commited chunk: 0\n", "2025-02-02 14:27:38,332 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:27:38,335 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:27:38,335 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2010q4.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2009q3.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2009q3.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:27:38,999 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2010q4.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2010q4.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:27:39,799 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:27:39,803 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:27:39,806 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2018q3.zip\n", "2025-02-02 14:27:43,239 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2018q3.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2018q3.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:27:46,243 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:27:46,246 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:27:46,247 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2023q2.zip\n", "2025-02-02 14:27:49,743 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2023q2.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2023q2.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:27:52,831 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:27:52,836 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:27:52,837 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2017q2.zip\n", "2025-02-02 14:27:55,388 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2017q2.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2017q2.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:27:57,834 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:27:57,837 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:27:57,839 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2012q3.zip\n", "2025-02-02 14:28:00,527 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2012q3.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2012q3.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:28:02,693 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:28:02,696 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:28:02,697 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2011q3.zip\n", "2025-02-02 14:28:04,722 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2011q3.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2011q3.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:28:06,670 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:28:06,673 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:28:06,675 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2016q2.zip\n", "2025-02-02 14:28:09,460 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2016q2.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2016q2.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:28:11,869 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:28:11,872 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:28:11,873 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2012q2.zip\n", "2025-02-02 14:28:14,400 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2012q2.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2012q2.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:28:16,734 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:28:16,737 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:28:16,739 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2023q4.zip\n", "2025-02-02 14:28:21,064 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2023q4.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2023q4.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:28:24,246 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:28:24,249 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:28:24,250 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2019q4.zip\n", "2025-02-02 14:28:27,846 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2019q4.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2019q4.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:28:30,777 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:28:30,780 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:28:30,781 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2015q4.zip\n", "2025-02-02 14:28:33,954 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2015q4.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2015q4.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:28:36,714 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:28:36,717 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:28:36,718 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2021q1.zip\n", "2025-02-02 14:28:39,633 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2021q1.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2021q1.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:28:41,970 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:28:41,973 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:28:41,974 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2011q4.zip\n", "2025-02-02 14:28:44,257 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2011q4.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2011q4.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:28:46,351 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:28:46,355 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:28:46,356 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2013q1.zip\n", "2025-02-02 14:28:49,935 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2013q1.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2013q1.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:28:52,390 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:28:52,394 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:28:52,396 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2020q3.zip\n", "2025-02-02 14:28:55,757 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2020q3.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2020q3.zip\\BS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:28:58,635 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:28:58,638 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:28:58,639 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2017q4.zip\n", "2025-02-02 14:29:01,600 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2017q4.zip\\BS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2017q4.zip\\BS\\joined\n"]}], "source": ["build_tmp_set(financial_statement=\"BS\", file_names=all_zip_names, target_path=\"set/tmp/\")"]}, {"cell_type": "code", "execution_count": 10, "id": "c01cfcac-5260-41de-a9dd-7ce7f890859f", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:29:04,489 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:29:04,493 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:29:04,495 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2012q4.zip\n", "2025-02-02 14:29:07,524 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2012q4.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2012q4.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:29:09,965 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:29:09,968 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:29:09,969 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2013q3.zip\n", "2025-02-02 14:29:12,869 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2013q3.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2013q3.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:29:15,262 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:29:15,265 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:29:15,266 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2018q2.zip\n", "2025-02-02 14:29:17,703 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2018q2.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2018q2.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:29:19,954 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:29:19,957 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:29:19,958 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2024q1.zip\n", "2025-02-02 14:29:23,292 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2024q1.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2024q1.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:29:25,607 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:29:25,610 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:29:25,612 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2015q1.zip\n", "2025-02-02 14:29:28,654 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2015q1.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2015q1.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:29:30,942 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:29:30,944 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:29:30,946 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2018q4.zip\n", "2025-02-02 14:29:33,402 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2018q4.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2018q4.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:29:35,739 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:29:35,742 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:29:35,743 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2019q1.zip\n", "2025-02-02 14:29:38,604 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2019q1.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2019q1.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:29:40,757 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:29:40,760 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:29:40,761 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2014q2.zip\n", "2025-02-02 14:29:43,365 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2014q2.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2014q2.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:29:45,580 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:29:45,584 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:29:45,585 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2012q1.zip\n", "2025-02-02 14:29:48,072 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2012q1.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2012q1.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:29:50,384 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:29:50,387 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:29:50,388 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2022q3.zip\n", "2025-02-02 14:29:53,570 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2022q3.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2022q3.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:29:56,122 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:29:56,125 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:29:56,126 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2016q3.zip\n", "2025-02-02 14:29:58,754 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2016q3.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2016q3.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:30:00,940 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:30:00,943 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:30:00,944 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2023q1.zip\n", "2025-02-02 14:30:04,402 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2023q1.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2023q1.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:30:06,769 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:30:06,772 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:30:06,773 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2024q3.zip\n", "2025-02-02 14:30:10,593 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2024q3.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2024q3.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:30:13,272 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:30:13,275 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:30:13,276 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2015q2.zip\n", "2025-02-02 14:30:15,767 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2015q2.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2015q2.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:30:17,950 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:30:17,953 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:30:17,954 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2015q3.zip\n", "2025-02-02 14:30:20,774 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2015q3.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2015q3.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:30:23,234 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:30:23,238 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:30:23,239 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2014q3.zip\n", "2025-02-02 14:30:26,114 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2014q3.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2014q3.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:30:28,569 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:30:28,572 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:30:28,574 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2013q2.zip\n", "2025-02-02 14:30:31,264 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2013q2.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2013q2.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:30:33,586 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:30:33,589 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:30:33,590 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2018q1.zip\n", "2025-02-02 14:30:36,522 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2018q1.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2018q1.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:30:38,716 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:30:38,719 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:30:38,720 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2014q1.zip\n", "2025-02-02 14:30:41,918 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2014q1.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2014q1.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:30:44,422 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:30:44,425 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:30:44,426 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2009q4.zip\n", "2025-02-02 14:30:44,614 [INFO] parallelexecution      commited chunk: 0\n", "2025-02-02 14:30:44,842 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:30:44,845 [INFO] parallelexecution      items to process: 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2009q4.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2009q4.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:30:44,846 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2019q2.zip\n", "2025-02-02 14:30:47,533 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2019q2.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2019q2.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:30:49,842 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:30:49,846 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:30:49,847 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2022q4.zip\n", "2025-02-02 14:30:53,757 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2022q4.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2022q4.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:30:56,480 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:30:56,483 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:30:56,484 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2011q1.zip\n", "2025-02-02 14:30:57,127 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2011q1.zip\\CF\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:30:57,662 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2011q1.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:30:57,665 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:30:57,666 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2010q2.zip\n", "2025-02-02 14:30:57,829 [INFO] parallelexecution      commited chunk: 0\n", "2025-02-02 14:30:58,000 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:30:58,003 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:30:58,004 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2017q1.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2010q2.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2010q2.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:31:01,063 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2017q1.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2017q1.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:31:03,227 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:31:03,230 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:31:03,231 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2020q1.zip\n", "2025-02-02 14:31:06,191 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2020q1.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2020q1.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:31:08,309 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:31:08,312 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:31:08,313 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2016q1.zip\n", "2025-02-02 14:31:11,515 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2016q1.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2016q1.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:31:13,978 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:31:13,980 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:31:13,981 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2020q2.zip\n", "2025-02-02 14:31:16,591 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2020q2.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2020q2.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:31:18,656 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:31:18,659 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:31:18,659 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2024q4.zip\n", "2025-02-02 14:31:22,889 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2024q4.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2024q4.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:31:25,820 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:31:25,823 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:31:25,824 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2009q2.zip\n", "2025-02-02 14:31:25,853 [INFO] parallelexecution      commited chunk: 0\n", "2025-02-02 14:31:25,945 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:31:25,948 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:31:25,949 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2021q3.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2009q2.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2009q2.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:31:29,233 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2021q3.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2021q3.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:31:31,690 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:31:31,692 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:31:31,693 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2017q3.zip\n", "2025-02-02 14:31:34,394 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2017q3.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2017q3.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:31:36,610 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:31:36,613 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:31:36,614 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2021q4.zip\n", "2025-02-02 14:31:40,661 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2021q4.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2021q4.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:31:43,070 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:31:43,073 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:31:43,074 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2020q4.zip\n", "2025-02-02 14:31:45,942 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2020q4.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2020q4.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:31:48,151 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:31:48,154 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:31:48,155 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2011q2.zip\n", "2025-02-02 14:31:48,642 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2011q2.zip\\CF\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:31:49,179 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2011q2.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:31:49,181 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:31:49,182 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2010q3.zip\n", "2025-02-02 14:31:49,646 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2010q3.zip\\CF\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:31:50,063 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:31:50,066 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:31:50,067 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2014q4.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2010q3.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:31:52,833 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2014q4.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2014q4.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:31:55,258 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:31:55,261 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:31:55,262 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2021q2.zip\n", "2025-02-02 14:31:57,631 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2021q2.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2021q2.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:31:59,646 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:31:59,648 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:31:59,649 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2022q2.zip\n", "2025-02-02 14:32:02,265 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2022q2.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2022q2.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:32:04,404 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:32:04,407 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:32:04,408 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2016q4.zip\n", "2025-02-02 14:32:06,980 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2016q4.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2016q4.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:32:09,233 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:32:09,236 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:32:09,237 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2019q3.zip\n", "2025-02-02 14:32:12,105 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2019q3.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2019q3.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:32:14,457 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:32:14,460 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:32:14,461 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2013q4.zip\n", "2025-02-02 14:32:17,241 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2013q4.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2013q4.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:32:19,674 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:32:19,676 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:32:19,677 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2010q1.zip\n", "2025-02-02 14:32:19,866 [INFO] parallelexecution      commited chunk: 0\n", "2025-02-02 14:32:20,083 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2010q1.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2010q1.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:32:20,086 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:32:20,087 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2024q2.zip\n", "2025-02-02 14:32:23,209 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2024q2.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2024q2.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:32:25,642 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:32:25,645 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:32:25,646 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2022q1.zip\n", "2025-02-02 14:32:28,441 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2022q1.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2022q1.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:32:30,614 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:32:30,617 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:32:30,619 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2023q3.zip\n", "2025-02-02 14:32:34,179 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2023q3.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2023q3.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:32:37,097 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:32:37,101 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:32:37,101 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2009q3.zip\n", "2025-02-02 14:32:37,255 [INFO] parallelexecution      commited chunk: 0\n", "2025-02-02 14:32:37,453 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:32:37,456 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:32:37,458 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2010q4.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2009q3.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2009q3.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:32:38,002 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2010q4.zip\\CF\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:32:38,505 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2010q4.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:32:38,509 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:32:38,510 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2018q3.zip\n", "2025-02-02 14:32:41,425 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2018q3.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2018q3.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:32:43,700 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:32:43,703 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:32:43,704 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2023q2.zip\n", "2025-02-02 14:32:46,746 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2023q2.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2023q2.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:32:49,122 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:32:49,125 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:32:49,126 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2017q2.zip\n", "2025-02-02 14:32:51,294 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2017q2.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2017q2.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:32:53,236 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:32:53,239 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:32:53,240 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2012q3.zip\n", "2025-02-02 14:32:55,646 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2012q3.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2012q3.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:32:57,653 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:32:57,656 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:32:57,657 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2011q3.zip\n", "2025-02-02 14:32:59,481 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2011q3.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2011q3.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:33:01,094 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:33:01,097 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:33:01,098 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2016q2.zip\n", "2025-02-02 14:33:03,312 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2016q2.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2016q2.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:33:05,311 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:33:05,314 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:33:05,315 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2012q2.zip\n", "2025-02-02 14:33:07,525 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2012q2.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2012q2.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:33:09,532 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:33:09,535 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:33:09,536 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2023q4.zip\n", "2025-02-02 14:33:13,217 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2023q4.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2023q4.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:33:15,889 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:33:15,892 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:33:15,893 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2019q4.zip\n", "2025-02-02 14:33:19,051 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2019q4.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2019q4.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:33:21,447 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:33:21,450 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:33:21,451 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2015q4.zip\n", "2025-02-02 14:33:24,139 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2015q4.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2015q4.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:33:26,472 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:33:26,475 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:33:26,476 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2021q1.zip\n", "2025-02-02 14:33:29,169 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2021q1.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2021q1.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:33:31,175 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:33:31,178 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:33:31,179 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2011q4.zip\n", "2025-02-02 14:33:33,283 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2011q4.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2011q4.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:33:35,202 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:33:35,205 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:33:35,206 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2013q1.zip\n", "2025-02-02 14:33:38,252 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2013q1.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2013q1.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:33:40,286 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:33:40,289 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:33:40,290 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2020q3.zip\n", "2025-02-02 14:33:43,196 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2020q3.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2020q3.zip\\CF\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:33:45,372 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:33:45,375 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:33:45,376 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2017q4.zip\n", "2025-02-02 14:33:47,945 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2017q4.zip\\CF\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2017q4.zip\\CF\\joined\n"]}], "source": ["build_tmp_set(financial_statement=\"CF\", file_names=all_zip_names, target_path=\"set/tmp/\")"]}, {"cell_type": "code", "execution_count": 11, "id": "24659372-b22b-42a2-b002-b28abdba7231", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:33:50,352 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:33:50,355 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:33:50,356 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2012q4.zip\n", "2025-02-02 14:33:53,036 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2012q4.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2012q4.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:33:55,379 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:33:55,382 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:33:55,387 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2013q3.zip\n", "2025-02-02 14:33:58,172 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2013q3.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2013q3.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:34:00,636 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:34:00,639 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:34:00,640 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2018q2.zip\n", "2025-02-02 14:34:03,009 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2018q2.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2018q2.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:34:05,097 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:34:05,100 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:34:05,101 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2024q1.zip\n", "2025-02-02 14:34:08,238 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2024q1.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2024q1.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:34:10,460 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:34:10,463 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:34:10,464 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2015q1.zip\n", "2025-02-02 14:34:13,409 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2015q1.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2015q1.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:34:15,606 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:34:15,609 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:34:15,610 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2018q4.zip\n", "2025-02-02 14:34:17,999 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2018q4.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2018q4.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:34:20,429 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:34:20,432 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:34:20,434 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2019q1.zip\n", "2025-02-02 14:34:23,316 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2019q1.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2019q1.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:34:25,390 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:34:25,393 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:34:25,393 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2014q2.zip\n", "2025-02-02 14:34:27,882 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2014q2.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2014q2.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:34:29,960 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:34:29,964 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:34:29,965 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2012q1.zip\n", "2025-02-02 14:34:32,355 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2012q1.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2012q1.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:34:34,152 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:34:34,155 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:34:34,155 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2022q3.zip\n", "2025-02-02 14:34:37,082 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2022q3.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2022q3.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:34:39,668 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:34:39,671 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:34:39,672 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2016q3.zip\n", "2025-02-02 14:34:42,036 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2016q3.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2016q3.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:34:44,211 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:34:44,214 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:34:44,215 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2023q1.zip\n", "2025-02-02 14:34:47,334 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2023q1.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2023q1.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:34:49,548 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:34:49,551 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:34:49,552 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2024q3.zip\n", "2025-02-02 14:34:53,136 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2024q3.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2024q3.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:34:55,959 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:34:55,962 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:34:55,962 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2015q2.zip\n", "2025-02-02 14:34:58,297 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2015q2.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2015q2.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:35:00,325 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:35:00,328 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:35:00,329 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2015q3.zip\n", "2025-02-02 14:35:02,924 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2015q3.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2015q3.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:35:05,398 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:35:05,401 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:35:05,401 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2014q3.zip\n", "2025-02-02 14:35:08,108 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2014q3.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2014q3.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:35:10,608 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:35:10,611 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:35:10,612 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2013q2.zip\n", "2025-02-02 14:35:13,198 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2013q2.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2013q2.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:35:15,306 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:35:15,309 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:35:15,310 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2018q1.zip\n", "2025-02-02 14:35:17,928 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2018q1.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2018q1.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:35:19,953 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:35:19,956 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:35:19,957 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2014q1.zip\n", "2025-02-02 14:35:22,983 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2014q1.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2014q1.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:35:25,173 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:35:25,176 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:35:25,177 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2009q4.zip\n", "2025-02-02 14:35:25,333 [INFO] parallelexecution      commited chunk: 0\n", "2025-02-02 14:35:25,539 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:35:25,542 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:35:25,543 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2019q2.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2009q4.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2009q4.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:35:27,983 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2019q2.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2019q2.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:35:30,099 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:35:30,101 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:35:30,102 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2022q4.zip\n", "2025-02-02 14:35:33,434 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2022q4.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2022q4.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:35:36,218 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:35:36,221 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:35:36,222 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2011q1.zip\n", "2025-02-02 14:35:36,823 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2011q1.zip\\IS\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:35:37,311 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:35:37,314 [INFO] parallelexecution      items to process: 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2011q1.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:35:37,315 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2010q2.zip\n", "2025-02-02 14:35:37,457 [INFO] parallelexecution      commited chunk: 0\n", "2025-02-02 14:35:37,617 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:35:37,620 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:35:37,620 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2017q1.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2010q2.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2010q2.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:35:40,360 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2017q1.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2017q1.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:35:42,365 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:35:42,368 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:35:42,369 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2020q1.zip\n", "2025-02-02 14:35:45,089 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2020q1.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2020q1.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:35:47,111 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:35:47,114 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:35:47,115 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2016q1.zip\n", "2025-02-02 14:35:50,129 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2016q1.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2016q1.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:35:52,231 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:35:52,234 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:35:52,235 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2020q2.zip\n", "2025-02-02 14:35:54,617 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2020q2.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2020q2.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:35:56,539 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:35:56,542 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:35:56,543 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2024q4.zip\n", "2025-02-02 14:36:00,195 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2024q4.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2024q4.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:36:03,012 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:36:03,015 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:36:03,016 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2009q2.zip\n", "2025-02-02 14:36:03,043 [INFO] parallelexecution      commited chunk: 0\n", "2025-02-02 14:36:03,125 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:36:03,128 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:36:03,129 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2021q3.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2009q2.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2009q2.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:36:05,954 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2021q3.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2021q3.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:36:08,438 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:36:08,441 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:36:08,442 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2017q3.zip\n", "2025-02-02 14:36:10,950 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2017q3.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2017q3.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:36:13,283 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:36:13,286 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:36:13,287 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2021q4.zip\n", "2025-02-02 14:36:16,282 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2021q4.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2021q4.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:36:18,830 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:36:18,832 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:36:18,833 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2020q4.zip\n", "2025-02-02 14:36:21,636 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2020q4.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2020q4.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:36:24,003 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:36:24,006 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:36:24,007 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2011q2.zip\n", "2025-02-02 14:36:24,483 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2011q2.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2011q2.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:36:24,983 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:36:24,986 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:36:24,987 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2010q3.zip\n", "2025-02-02 14:36:25,416 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2010q3.zip\\IS\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:36:25,839 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:36:25,842 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:36:25,843 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2014q4.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2010q3.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:36:28,592 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2014q4.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2014q4.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:36:31,082 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:36:31,085 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:36:31,086 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2021q2.zip\n", "2025-02-02 14:36:33,417 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2021q2.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2021q2.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:36:35,401 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:36:35,404 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:36:35,405 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2022q2.zip\n", "2025-02-02 14:36:37,929 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2022q2.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2022q2.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:36:39,997 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:36:40,000 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:36:40,001 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2016q4.zip\n", "2025-02-02 14:36:42,552 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2016q4.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2016q4.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:36:44,900 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:36:44,903 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:36:44,904 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2019q3.zip\n", "2025-02-02 14:36:47,724 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2019q3.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2019q3.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:36:50,327 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:36:50,329 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:36:50,330 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2013q4.zip\n", "2025-02-02 14:36:53,121 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2013q4.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2013q4.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:36:55,578 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:36:55,580 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:36:55,581 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2010q1.zip\n", "2025-02-02 14:36:55,762 [INFO] parallelexecution      commited chunk: 0\n", "2025-02-02 14:36:55,961 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:36:55,964 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:36:55,965 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2024q2.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2010q1.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2010q1.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:36:59,105 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2024q2.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2024q2.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:37:01,493 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:37:01,496 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:37:01,497 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2022q1.zip\n", "2025-02-02 14:37:04,310 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2022q1.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2022q1.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:37:06,372 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:37:06,375 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:37:06,376 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2023q3.zip\n", "2025-02-02 14:37:09,850 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2023q3.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2023q3.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:37:12,658 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:37:12,661 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:37:12,662 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2009q3.zip\n", "2025-02-02 14:37:12,795 [INFO] parallelexecution      commited chunk: 0\n", "2025-02-02 14:37:12,969 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:37:12,973 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:37:12,974 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2010q4.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2009q3.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2009q3.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:37:13,470 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2010q4.zip\\IS\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:37:13,943 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2010q4.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:37:13,946 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:37:13,947 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2018q3.zip\n", "2025-02-02 14:37:16,586 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2018q3.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2018q3.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:37:19,123 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:37:19,126 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:37:19,127 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2023q2.zip\n", "2025-02-02 14:37:22,114 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2023q2.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2023q2.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:37:24,433 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:37:24,436 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:37:24,437 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2017q2.zip\n", "2025-02-02 14:37:26,609 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2017q2.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2017q2.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:37:28,493 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:37:28,496 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:37:28,497 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2012q3.zip\n", "2025-02-02 14:37:30,887 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2012q3.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2012q3.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:37:32,980 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:37:32,983 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:37:32,984 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2011q3.zip\n", "2025-02-02 14:37:34,780 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2011q3.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2011q3.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:37:36,435 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:37:36,438 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:37:36,439 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2016q2.zip\n", "2025-02-02 14:37:38,648 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2016q2.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2016q2.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:37:40,556 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:37:40,559 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:37:40,560 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2012q2.zip\n", "2025-02-02 14:37:42,701 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2012q2.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2012q2.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:37:44,563 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:37:44,566 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:37:44,567 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2023q4.zip\n", "2025-02-02 14:37:48,165 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2023q4.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2023q4.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:37:51,051 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:37:51,054 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:37:51,055 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2019q4.zip\n", "2025-02-02 14:37:54,070 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2019q4.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2019q4.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:37:56,666 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:37:56,669 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:37:56,670 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2015q4.zip\n", "2025-02-02 14:37:59,324 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2015q4.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2015q4.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:38:01,851 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:38:01,854 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:38:01,855 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2021q1.zip\n", "2025-02-02 14:38:04,480 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2021q1.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2021q1.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:38:06,471 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:38:06,474 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:38:06,475 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2011q4.zip\n", "2025-02-02 14:38:08,550 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2011q4.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2011q4.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:38:10,545 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:38:10,547 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:38:10,548 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2013q1.zip\n", "2025-02-02 14:38:13,582 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2013q1.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2013q1.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:38:15,580 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:38:15,583 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:38:15,584 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2020q3.zip\n", "2025-02-02 14:38:18,485 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2020q3.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2020q3.zip\\IS\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:38:20,908 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:38:20,911 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:38:20,912 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2017q4.zip\n", "2025-02-02 14:38:23,468 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2017q4.zip\\IS\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2017q4.zip\\IS\\joined\n"]}], "source": ["build_tmp_set(financial_statement=\"IS\", file_names=all_zip_names, target_path=\"set/tmp/\")"]}, {"cell_type": "code", "execution_count": 12, "id": "62d8b73f-1cb1-4b7a-8bb4-a69061012633", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:38:25,875 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:38:25,879 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:38:25,880 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2012q4.zip\n", "2025-02-02 14:38:28,721 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2012q4.zip\\CP\\raw\n", "create joined databag\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:38:30,232 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:38:30,235 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:38:30,236 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2013q3.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store joineddatabag under set/tmp/2012q4.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:38:32,905 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2013q3.zip\\CP\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2013q3.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:38:34,450 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:38:34,453 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:38:34,453 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2018q2.zip\n", "2025-02-02 14:38:36,775 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2018q2.zip\\CP\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2018q2.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:38:38,191 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:38:38,194 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:38:38,195 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2024q1.zip\n", "2025-02-02 14:38:41,225 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2024q1.zip\\CP\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:38:42,596 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:38:42,599 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:38:42,600 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2015q1.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2024q1.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:38:45,479 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2015q1.zip\\CP\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2015q1.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:38:46,933 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:38:46,936 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:38:46,937 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2018q4.zip\n", "2025-02-02 14:38:49,252 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2018q4.zip\\CP\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2018q4.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:38:50,758 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:38:50,761 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:38:50,763 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2019q1.zip\n", "2025-02-02 14:38:53,480 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2019q1.zip\\CP\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:38:54,639 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:38:54,642 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:38:54,643 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2014q2.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2019q1.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:38:57,052 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2014q2.zip\\CP\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2014q2.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:38:58,441 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:38:58,444 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:38:58,445 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2012q1.zip\n", "2025-02-02 14:39:00,745 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2012q1.zip\\CP\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2012q1.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:39:01,939 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:39:01,942 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:39:01,943 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2022q3.zip\n", "2025-02-02 14:39:04,763 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2022q3.zip\\CP\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:39:06,104 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:39:06,107 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:39:06,109 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2016q3.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2022q3.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:39:08,397 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2016q3.zip\\CP\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2016q3.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:39:09,784 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:39:09,787 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:39:09,788 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2023q1.zip\n", "2025-02-02 14:39:12,806 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2023q1.zip\\CP\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:39:14,118 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:39:14,120 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:39:14,121 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2024q3.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2023q1.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:39:17,688 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2024q3.zip\\CP\\raw\n", "create joined databag\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:39:19,642 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:39:19,645 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:39:19,646 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2015q2.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store joineddatabag under set/tmp/2024q3.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:39:21,977 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2015q2.zip\\CP\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2015q2.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:39:23,372 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:39:23,376 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:39:23,377 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2015q3.zip\n", "2025-02-02 14:39:25,938 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2015q3.zip\\CP\\raw\n", "create joined databag\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:39:27,489 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:39:27,492 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:39:27,493 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2014q3.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store joineddatabag under set/tmp/2015q3.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:39:30,117 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2014q3.zip\\CP\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2014q3.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:39:31,661 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:39:31,664 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:39:31,665 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2013q2.zip\n", "2025-02-02 14:39:34,167 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2013q2.zip\\CP\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:39:35,308 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:39:35,310 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:39:35,312 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2018q1.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2013q2.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:39:37,881 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2018q1.zip\\CP\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:39:39,010 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:39:39,013 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:39:39,013 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2014q1.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2018q1.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:39:41,965 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2014q1.zip\\CP\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2014q1.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:39:43,442 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:39:43,444 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:39:43,445 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2009q4.zip\n", "2025-02-02 14:39:43,597 [INFO] parallelexecution      commited chunk: 0\n", "2025-02-02 14:39:43,735 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:39:43,738 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:39:43,739 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2019q2.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2009q4.zip\\CP\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2009q4.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:39:46,109 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2019q2.zip\\CP\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:39:47,287 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:39:47,290 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:39:47,291 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2022q4.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2019q2.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:39:50,600 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2022q4.zip\\CP\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:39:52,105 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:39:52,107 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:39:52,109 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2011q1.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2022q4.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:39:52,693 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2011q1.zip\\CP\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:39:53,036 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:39:53,039 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:39:53,040 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2010q2.zip\n", "2025-02-02 14:39:53,177 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2011q1.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:39:53,292 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:39:53,295 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:39:53,296 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2017q1.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2010q2.zip\\CP\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2010q2.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:39:55,994 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2017q1.zip\\CP\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2017q1.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:39:57,365 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:39:57,367 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:39:57,368 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2020q1.zip\n", "2025-02-02 14:40:00,021 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2020q1.zip\\CP\\raw\n", "create joined databag\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:40:01,416 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:40:01,419 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:40:01,420 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2016q1.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store joineddatabag under set/tmp/2020q1.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:40:04,306 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2016q1.zip\\CP\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2016q1.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:40:05,705 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:40:05,708 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:40:05,709 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2020q2.zip\n", "2025-02-02 14:40:07,977 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2020q2.zip\\CP\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:40:09,043 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:40:09,045 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:40:09,046 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2024q4.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2020q2.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:40:12,632 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2024q4.zip\\CP\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:40:14,300 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:40:14,302 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:40:14,303 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2009q2.zip\n", "2025-02-02 14:40:14,329 [INFO] parallelexecution      commited chunk: 0\n", "2025-02-02 14:40:14,409 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:40:14,412 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:40:14,413 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2021q3.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2024q4.zip\\CP\\joined\n", "store rawdatabag under set/tmp/2009q2.zip\\CP\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2009q2.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:40:17,181 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2021q3.zip\\CP\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:40:18,511 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:40:18,514 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:40:18,515 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2017q3.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2021q3.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:40:20,954 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2017q3.zip\\CP\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:40:22,176 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:40:22,179 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:40:22,179 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2021q4.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2017q3.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:40:25,106 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2021q4.zip\\CP\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:40:26,457 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:40:26,459 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:40:26,460 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2020q4.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2021q4.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:40:29,157 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2020q4.zip\\CP\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:40:30,408 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:40:30,411 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:40:30,411 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2011q2.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2020q4.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:40:30,844 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2011q2.zip\\CP\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:40:31,166 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:40:31,169 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:40:31,170 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2010q3.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2011q2.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:40:31,576 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2010q3.zip\\CP\\raw\n", "create joined databag\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:40:31,872 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:40:31,875 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:40:31,876 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2014q4.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store joineddatabag under set/tmp/2010q3.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:40:34,516 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2014q4.zip\\CP\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2014q4.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:40:36,097 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:40:36,100 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:40:36,101 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2021q2.zip\n", "2025-02-02 14:40:38,330 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2021q2.zip\\CP\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:40:39,441 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:40:39,444 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:40:39,445 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2022q2.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2021q2.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:40:41,907 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2022q2.zip\\CP\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2022q2.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:40:43,306 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:40:43,310 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:40:43,311 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2016q4.zip\n", "2025-02-02 14:40:45,768 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2016q4.zip\\CP\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2016q4.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:40:47,261 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:40:47,264 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:40:47,265 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2019q3.zip\n", "2025-02-02 14:40:50,037 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2019q3.zip\\CP\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:40:51,370 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:40:51,373 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:40:51,374 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2013q4.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2019q3.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:40:54,063 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2013q4.zip\\CP\\raw\n", "create joined databag\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:40:55,621 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:40:55,624 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:40:55,625 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2010q1.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store joineddatabag under set/tmp/2013q4.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:40:55,805 [INFO] parallelexecution      commited chunk: 0\n", "2025-02-02 14:40:55,962 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:40:55,965 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:40:55,966 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2024q2.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2010q1.zip\\CP\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2010q1.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:40:59,007 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2024q2.zip\\CP\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:41:00,496 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:41:00,499 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:41:00,500 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2022q1.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2024q2.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:41:03,173 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2022q1.zip\\CP\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2022q1.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:41:04,604 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:41:04,608 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:41:04,609 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2023q3.zip\n", "2025-02-02 14:41:08,015 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2023q3.zip\\CP\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:41:09,569 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:41:09,572 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:41:09,573 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2009q3.zip\n", "2025-02-02 14:41:09,702 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2023q3.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:41:09,834 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:41:09,837 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:41:09,838 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2010q4.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2009q3.zip\\CP\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2009q3.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:41:10,310 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2010q4.zip\\CP\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:41:10,611 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:41:10,614 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:41:10,615 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2018q3.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2010q4.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:41:13,171 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2018q3.zip\\CP\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:41:14,458 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:41:14,461 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:41:14,462 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2023q2.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2018q3.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:41:17,339 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2023q2.zip\\CP\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:41:18,761 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:41:18,763 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:41:18,765 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2017q2.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2023q2.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:41:20,850 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2017q2.zip\\CP\\raw\n", "create joined databag\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:41:22,110 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:41:22,113 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:41:22,114 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2012q3.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store joineddatabag under set/tmp/2017q2.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:41:24,418 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2012q3.zip\\CP\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2012q3.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:41:25,726 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:41:25,729 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:41:25,730 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2011q3.zip\n", "2025-02-02 14:41:27,457 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2011q3.zip\\CP\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:41:28,493 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2011q3.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:41:28,497 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:41:28,497 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2016q2.zip\n", "2025-02-02 14:41:30,625 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2016q2.zip\\CP\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2016q2.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:41:31,890 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:41:31,892 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:41:31,893 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2012q2.zip\n", "2025-02-02 14:41:33,962 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2012q2.zip\\CP\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2012q2.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:41:35,173 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:41:35,176 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:41:35,177 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2023q4.zip\n", "2025-02-02 14:41:38,765 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2023q4.zip\\CP\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:41:40,403 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:41:40,406 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:41:40,407 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2019q4.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2023q4.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:41:43,389 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2019q4.zip\\CP\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:41:44,777 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:41:44,779 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:41:44,781 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2015q4.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2019q4.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:41:48,100 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2015q4.zip\\CP\\raw\n", "create joined databag\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:41:49,729 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:41:49,732 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:41:49,733 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2021q1.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store joineddatabag under set/tmp/2015q4.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:41:52,518 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2021q1.zip\\CP\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:41:53,638 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:41:53,640 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:41:53,641 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2011q4.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2021q1.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:41:55,641 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2011q4.zip\\CP\\raw\n", "create joined databag\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:41:56,866 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:41:56,869 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:41:56,871 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2013q1.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store joineddatabag under set/tmp/2011q4.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:41:59,744 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2013q1.zip\\CP\\raw\n", "create joined databag\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:42:01,080 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:42:01,083 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:42:01,084 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2020q3.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store joineddatabag under set/tmp/2013q1.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:42:03,896 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2020q3.zip\\CP\\raw\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:42:05,166 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-02 14:42:05,169 [INFO] parallelexecution      items to process: 1\n", "2025-02-02 14:42:05,170 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2017q4.zip\n"]}, {"name": "stdout", "output_type": "stream", "text": ["create joined databag\n", "store joineddatabag under set/tmp/2020q3.zip\\CP\\joined\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-02 14:42:07,623 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/tmp/2017q4.zip\\CP\\raw\n", "create joined databag\n", "store joineddatabag under set/tmp/2017q4.zip\\CP\\joined\n"]}], "source": ["build_tmp_set(financial_statement=\"CP\", file_names=all_zip_names, target_path=\"set/tmp/\")"]}, {"cell_type": "markdown", "id": "b78e0305-9380-439d-8b60-bc26d22f4cd8", "metadata": {}, "source": ["We know have subfolders for BS, CF, IS, and CP for every quarterly zipfile with the corresponding datapoints."]}, {"cell_type": "markdown", "id": "12720d14-6be6-4eb9-9b11-6c6c79df7773", "metadata": {}, "source": ["**Create the rawdatabags**"]}, {"cell_type": "code", "execution_count": 13, "id": "0b3a9574-8895-4fe3-a9f0-1e48ef717bb0", "metadata": {"tags": []}, "outputs": [], "source": ["from glob import glob\n", "\n", "def create_rawdatabag(financial_statement: str, target_path: str):\n", "    raw_files = glob(f\"./set/tmp/*/{financial_statement}/raw/\", recursive = True)    \n", "    raw_databags = [RawDataBag.load(file) for file in raw_files]\n", "    raw_databag = RawDataBag.concat(raw_databags)\n", "    target_path_raw = os.path.join(target_path, financial_statement, 'raw')\n", "    print(f\"store rawdatabag under {target_path_raw}\")\n", "    os.makedirs(target_path_raw, exist_ok=True)\n", "    raw_databag.save(target_path_raw)      "]}, {"cell_type": "markdown", "id": "a0b6ce36-6b06-400b-9200-e45182d4b86c", "metadata": {}, "source": ["Next, concatenate the raw datasets together. Again, as a reference it took about 6-7 minutes to create all four rawdatabags."]}, {"cell_type": "code", "execution_count": 14, "id": "097a4940-2f44-434c-be19-b48cae0d1016", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/serial/BS\\raw\n"]}], "source": ["create_rawdatabag(financial_statement=\"BS\", target_path=\"set/serial/\")"]}, {"cell_type": "code", "execution_count": 15, "id": "d036f268-d664-4e18-96cb-5f50deb50257", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/serial/CF\\raw\n"]}], "source": ["create_rawdatabag(financial_statement=\"CF\", target_path=\"set/serial/\")"]}, {"cell_type": "code", "execution_count": 16, "id": "27464709-7766-4ca3-a95f-3d8fd668d866", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/serial/IS\\raw\n"]}], "source": ["create_rawdatabag(financial_statement=\"IS\", target_path=\"set/serial/\")"]}, {"cell_type": "code", "execution_count": 17, "id": "fc0a9310-3047-4c4b-b2ed-a629e9467e7d", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["store rawdatabag under set/serial/CP\\raw\n"]}], "source": ["create_rawdatabag(financial_statement=\"CP\", target_path=\"set/serial/\")"]}, {"cell_type": "markdown", "id": "5d429003-84a6-4e66-89d3-974d7629a442", "metadata": {}, "source": ["**Create the joined databags**"]}, {"cell_type": "code", "execution_count": 18, "id": "6c513474-b29e-4034-a6a4-afefe0a2070c", "metadata": {"tags": []}, "outputs": [], "source": ["from glob import glob\n", "\n", "def create_joineddatabag(financial_statement: str, target_path: str):\n", "    joined_files = glob(f\"./set/tmp/*/{financial_statement}/joined/\", recursive = True)\n", "    joined_databags = [JoinedDataBag.load(file) for file in joined_files]\n", "    joined_databag = JoinedDataBag.concat(joined_databags)\n", "    target_path_joined = os.path.join(target_path, financial_statement, 'joined')\n", "    print(f\"store joineddatabag under {target_path_joined}\")\n", "    os.makedirs(target_path_joined, exist_ok=True)\n", "    joined_databag.save(target_path_joined)   "]}, {"cell_type": "markdown", "id": "ffa8b6e8-4b20-4462-a931-d64e09ac5d33", "metadata": {}, "source": ["Finally, create the joined databags. To create all four datasets, it took about 90 seconds."]}, {"cell_type": "code", "execution_count": 19, "id": "6fb50096-a09b-4b26-b2cc-9a5374820d3b", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["store joineddatabag under set/serial/BS\\joined\n"]}], "source": ["create_joineddatabag(financial_statement=\"BS\", target_path=\"set/serial/\")"]}, {"cell_type": "code", "execution_count": 20, "id": "c8f9c719-17f6-4a85-aadb-4608bf685c3f", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["store joineddatabag under set/serial/CF\\joined\n"]}], "source": ["create_joineddatabag(financial_statement=\"CF\", target_path=\"set/serial/\")"]}, {"cell_type": "code", "execution_count": 21, "id": "117528e8-c577-47b0-b5b2-876b44a2af6d", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["store joineddatabag under set/serial/IS\\joined\n"]}], "source": ["create_joineddatabag(financial_statement=\"IS\", target_path=\"set/serial/\")"]}, {"cell_type": "code", "execution_count": 22, "id": "5088cac7-ebee-4dff-adf8-af33311abe21", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["store joineddatabag under set/serial/CP\\joined\n"]}], "source": ["create_joineddatabag(financial_statement=\"CP\", target_path=\"set/serial/\")"]}, {"cell_type": "markdown", "id": "8e3f5417-9726-4584-84a5-b284274681cb", "metadata": {"tags": []}, "source": ["Now we can read back all four prepared joined datasets. This only takes a few seconds."]}, {"cell_type": "code", "execution_count": 14, "id": "8b7f8a8d-3354-4e6b-8280-53c145338bd2", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loaded BS databag:  (19657047, 17)\n", "loaded CF databag:  (12883947, 17)\n", "loaded IS databag:  (17412439, 17)\n", "loaded CP databag:  (76, 17)\n"]}], "source": ["#load BS joined data\n", "joinedBS = JoinedDataBag.load(\"./set/serial/BS/joined\")\n", "print(\"loaded BS databag: \", joinedBS.pre_num_df.shape)\n", "joinedCF = JoinedDataBag.load(\"./set/serial/CF/joined\")\n", "print(\"loaded CF databag: \", joinedCF.pre_num_df.shape)\n", "joinedIS = JoinedDataBag.load(\"./set/serial/IS/joined\")\n", "print(\"loaded IS databag: \", joinedIS.pre_num_df.shape)\n", "joinedCP = JoinedDataBag.load(\"./set/serial/CP/joined\")\n", "print(\"loaded CP databag: \", joinedCP.pre_num_df.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "4c47bbfa-69e6-468d-8b8e-17cf1d8e40bf", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "324e70d1-9876-44ea-88e0-499d2defa890", "metadata": {}, "source": ["# Sec Financial Statement Data Sets Tools - Quickstart"]}, {"cell_type": "markdown", "id": "769c6343-9f43-496e-a282-7b144ff94929", "metadata": {"tags": []}, "source": ["<span style=\"color: #FF8C00;\">==========================================================</span>\n", "\n", "**If you find this tool useful, a sponsorship would be greatly appreciated!**\n", "\n", "**https://github.com/sponsors/HansjoergW**\n", "\n", "How to get in touch\n", "\n", "* Found a bug: https://github.com/HansjoergW/sec-fincancial-statement-data-set/issues\n", "* Have a remark: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/general\n", "* Have an idea: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/ideas\n", "* Have a question: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/q-a\n", "* Have something to show: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/show-and-tell\n", "\n", "<span style=\"color: #FF8C00;\">==========================================================</span>"]}, {"cell_type": "markdown", "id": "ec5fd5e7-16f8-406b-b5e4-2b23cf403638", "metadata": {}, "source": ["## TL;DR"]}, {"cell_type": "markdown", "id": "901fe0d2-d257-4b2d-847e-596438672c24", "metadata": {}, "source": ["This notebook gives a first introduction into using the secfsdstools (Sec Financial Data Sets Tools) python package: https://pypi.org/project/secfsdstools/\n", "\n", "It is designed to work with the data provided by the \"Sec Financial Statement Data Sets\" (SFSDS)(https://www.sec.gov/dera/data/financial-statement-data-sets).\n", "\n", "The SFSDS contains data from all reports that were filed with the SEC since 2012. For instance all annual and quarterly reports. The main assets that can be retrieved from this data set are the financial statemens (balance sheet, income statement, and cash flow).\n", "\n", "First, this notebook shows how the library is installed and configured. After that, it shows the different ways how the financial statements can be extracted from the data set.\n", "\n", "For a detailed definition of the data set see https://www.sec.gov/files/financial-statement-data-sets.pdf."]}, {"cell_type": "markdown", "id": "385ca081-3a9f-42b9-bc85-496c42821e7e", "metadata": {}, "source": ["## Principles / Concepts"]}, {"cell_type": "markdown", "id": "ffe4edea-7b2a-4efd-9b63-96257251a803", "metadata": {}, "source": ["The goal is bulk processing of the data.\n", "\n", "To improve efficiency, the zip files are downloaded and indexed using a SQLite database table.\n", "The index table contains information on all filed reports, over 500,000 in total. The first\n", "download of the data will take a couple of minutes but after that, all the data is on your local harddisk\n", "and new data will only be donwloaded every quarter.\n", "\n", "Using the index in the sqlite db allows for direct extraction of data for a specific report from the\n", "appropriate zip file, reducing the need to open and search through each zip file.\n", "\n", "Moreover, the downloaded zip files are converted to the parquet format which provides faster read access\n", "to the data compared to reading the csv files inside the zip files and still providing a similar \n", "compress ratio than zip.\n", "\n", "The library is designed to have a low memory footprint."]}, {"cell_type": "markdown", "id": "9e66226a-90c8-402e-9c61-85bb656e2e21", "metadata": {}, "source": ["## Installation\n", "In order to install the library, just use pip install:\n", "```\n", "pip install secfsdstools\n", "```"]}, {"cell_type": "markdown", "id": "fdd67276-7d80-4138-be4b-53c32126f9a2", "metadata": {"tags": []}, "source": ["## Configure logging in Jupyter"]}, {"cell_type": "code", "execution_count": 1, "id": "e9f97ba9-010c-45e3-ab0b-2e87c0099230", "metadata": {"tags": []}, "outputs": [], "source": ["# to ensure that the logging statements are shown in juypter output, run this cell\n", "import logging\n", "\n", "logger = logging.getLogger()\n", "logger.setLevel(logging.INFO)"]}, {"cell_type": "markdown", "id": "affe6107-00af-4603-9c31-2282d92e3a37", "metadata": {"tags": []}, "source": ["## Configure table output for Pandas"]}, {"cell_type": "code", "execution_count": 2, "id": "4e3daf46-7c76-40f8-8790-296b7495c4b7", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "# ensure that all columns are shown and that colum content is not cut\n", "pd.set_option('display.max_rows', 500) # ensure that all rows are shown\n", "pd.set_option('display.max_colwidth', None)\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width',1000)"]}, {"cell_type": "markdown", "id": "7c85678c-22c7-422d-8d86-00adc9008124", "metadata": {}, "source": ["## Configuration / Setup"]}, {"cell_type": "markdown", "id": "d076cff1-a3a6-486b-a681-ee27db71778f", "metadata": {}, "source": ["In order to be used, the library needs to know where to store the compressed files from the Financial Statement Data Sets and where to store the sqlite database file. This is configured in a configuration file.\n", "\n", "If you don't provide a config file, one will be created the first time you use the api. The configuration file will be created inside your home\n", "directory. You can then change the content of it or directly start with the downloading of the data.\n", "\n", "```\n", "[DEFAULT]\n", "downloaddirectory = <userhome>/secfsdstools/data/dld\n", "parquetdirectory = <userhome>/secfsdools/data/parquet\n", "dbdirectory = <userhome>/secfsdstools/data/db\n", "useragentemail = <EMAIL>\n", "```\n", "\n", "The downloaddirectory is the folder in which the compressed data files are downloaded.\n", "The parquetdirectory is the folder in which the transfomred parquet version is stored.\n", "The dbdirectory will contain sqlite db file.\n", "The useragentemail is set inside the header when requests to sec.gov are made. This should be your email-address, however, since we are only making very few requests, it doesn't really matter if you change it or not.\n", "\n", "If you want to start the download of the data \"manually\", just call the update method. However, the framework also checks at most every 24 hours if there is new data available and automatically downloads it."]}, {"cell_type": "code", "execution_count": 3, "id": "df8c6cd6-9713-4f85-afbf-fdebb7a02cb1", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-12 06:18:40,204 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-12 06:18:40,854 [INFO] updateprocess  Launching data update process ...\n", "2025-02-12 06:18:40,879 [INFO] task_framework  Starting process SecDownloadingProcess\n", "2025-02-12 06:18:40,882 [INFO] secdownloading_process  reading table in main page: https://www.sec.gov/dera/data/financial-statement-data-sets.html\n", "2025-02-12 06:18:41,310 [INFO] task_framework  Starting process ToParquetTransformerProcess\n", "2025-02-12 06:18:41,311 [INFO] task_framework  Starting process ReportParquetIndexerProcess\n"]}], "source": ["from secfsdstools.update import update\n", "\n", "update()"]}, {"cell_type": "markdown", "id": "9ab10736-1898-4110-9ee3-c51d59206748", "metadata": {}, "source": ["The following tasks will be executed:\n", "1. All currently available zip-files are downloaded form sec.gov (these are over 50 files that will need over 7 GB of space on your local drive)\n", "2. All the zipfiles are transformed and stored as parquet files. Per default, the zipfile is deleted afterwards. If you want to keep the zip files, set the parameter 'KeepZipFiles' in the config file to True.\n", "3. An index inside a sqlite db file is created\n", "\n", "The first download may take about 20 minutes.\n", "\n", "If you don't call update \"manually\", then the first time you call a function from the library, a download will be triggered.\n", "\n", "Moreover, at most once a day, the framework checks if there is a new zip file available on sec.gov. If there is, a download will be started automatically. \n", "If you don't want 'auto-update', set the 'AutoUpdate' in your config file to False.\n", "The new quarter zip files are available by the beginning of every quarter (January, April, July, October), hence, yo have to run the update() at the beginning of every quarter to get the data for the reprots from last quarter."]}, {"cell_type": "markdown", "id": "17d4c466-3431-48b7-b2c8-b8fe68710110", "metadata": {}, "source": ["**Note:** the first time downloading data will take a couple of minutes (>15 minutes), since over 7 GB of data will be downloaded and converted into parquet format.\n", "\n", "**Note:** if you only keep the transformed parquet data (which is the default configuration), you need about 5.5GB as per January 2025\n", "\n", "**Note: If you plan to use Jupyter, make sure that you configure the directories at a location where your Jupyter process has access. The used default directory (your user home directory) will work.**"]}, {"cell_type": "markdown", "id": "758ca848-83a3-408a-9259-f534638be908", "metadata": {}, "source": ["## A first simple example\n", "Goal: present the information in the balance sheet of Apple's 2022 10-K report in the same way as it appears in the\n", "original report on page 31 (\"CONSOLIDATED BALANCE SHEETS\"): https://www.sec.gov/ix?doc=/Archives/edgar/data/320193/**********22000108/aapl-20220924.htm\n", "\n", "**Note:** Version 2 of the framework supports now the `segments` that was introduced in January 2025. By adjusting the parameter `show_segments` you can define whether the segments information are shown or not\n"]}, {"cell_type": "code", "execution_count": 4, "id": "38bddd29-a291-4ea8-ab70-0d7a4fb9c4af", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-12 06:18:47,877 [INFO] __init__  loading secfsdstools ...\n", "2025-02-12 06:18:47,879 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-12 06:18:47,946 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n"]}, {"name": "stdout", "output_type": "stream", "text": ["                    adsh coreg                                              tag       version stmt  report  line segments     uom  negating  inpth  qrtrs_0/********  qrtrs_0/********\n", "0   **********-22-000108                  CashAndCashEquivalentsAtCarryingValue  us-gaap/2022   BS       5     3              USD         0      0      2.364600e+10      3.494000e+10\n", "1   **********-22-000108                            MarketableSecuritiesCurrent  us-gaap/2022   BS       5     4              USD         0      0      2.465800e+10      2.769900e+10\n", "2   **********-22-000108                           AccountsReceivableNetCurrent  us-gaap/2022   BS       5     5              USD         0      0      2.818400e+10      2.627800e+10\n", "3   **********-22-000108                                           InventoryNet  us-gaap/2022   BS       5     6              USD         0      0      4.946000e+09      6.580000e+09\n", "4   **********-22-000108                             NontradeReceivablesCurrent  us-gaap/2022   BS       5     7              USD         0      0      3.274800e+10      2.522800e+10\n", "5   **********-22-000108                                     OtherAssetsCurrent  us-gaap/2022   BS       5     8              USD         0      0      2.122300e+10      1.411100e+10\n", "6   **********-22-000108                                          AssetsCurrent  us-gaap/2022   BS       5     9              USD         0      0      1.354050e+11      1.348360e+11\n", "7   **********-22-000108                         MarketableSecuritiesNoncurrent  us-gaap/2022   BS       5    11              USD         0      0      1.208050e+11      1.278770e+11\n", "8   **********-22-000108                           PropertyPlantAndEquipmentNet  us-gaap/2022   BS       5    12              USD         0      0      4.211700e+10      3.944000e+10\n", "9   **********-22-000108                                  OtherAssetsNoncurrent  us-gaap/2022   BS       5    13              USD         0      0      5.442800e+10      4.884900e+10\n", "10  **********-22-000108                                       AssetsNoncurrent  us-gaap/2022   BS       5    14              USD         0      0      2.173500e+11      2.161660e+11\n", "11  **********-22-000108                                                 Assets  us-gaap/2022   BS       5    15              USD         0      0      3.527550e+11      3.510020e+11\n", "12  **********-22-000108                                 AccountsPayableCurrent  us-gaap/2022   BS       5    18              USD         0      0      6.411500e+10      5.476300e+10\n", "13  **********-22-000108                                OtherLiabilitiesCurrent  us-gaap/2022   BS       5    19              USD         0      0      6.084500e+10      4.749300e+10\n", "14  **********-22-000108                   ContractWithCustomerLiabilityCurrent  us-gaap/2022   BS       5    20              USD         0      0      7.912000e+09      7.612000e+09\n", "15  **********-22-000108                                        CommercialPaper  us-gaap/2022   BS       5    21              USD         0      0      9.982000e+09      6.000000e+09\n", "16  **********-22-000108                                    LongTermDebtCurrent  us-gaap/2022   BS       5    22              USD         0      0      1.112800e+10      9.613000e+09\n", "17  **********-22-000108                                     LiabilitiesCurrent  us-gaap/2022   BS       5    23              USD         0      0      1.539820e+11      1.254810e+11\n", "18  **********-22-000108                                 LongTermDebtNoncurrent  us-gaap/2022   BS       5    25              USD         0      0      9.895900e+10      1.091060e+11\n", "19  **********-22-000108                             OtherLiabilitiesNoncurrent  us-gaap/2022   BS       5    26              USD         0      0      4.914200e+10      5.332500e+10\n", "20  **********-22-000108                                  LiabilitiesNoncurrent  us-gaap/2022   BS       5    27              USD         0      0      1.481010e+11      1.624310e+11\n", "21  **********-22-000108                                            Liabilities  us-gaap/2022   BS       5    28              USD         0      0      3.020830e+11      2.879120e+11\n", "22  **********-22-000108           CommonStocksIncludingAdditionalPaidInCapital  us-gaap/2022   BS       5    31              USD         0      0      6.484900e+10      5.736500e+10\n", "23  **********-22-000108                     RetainedEarningsAccumulatedDeficit  us-gaap/2022   BS       5    32              USD         0      0     -3.068000e+09      5.562000e+09\n", "24  **********-22-000108        AccumulatedOtherComprehensiveIncomeLossNetOfTax  us-gaap/2022   BS       5    33              USD         0      0     -1.110900e+10      1.630000e+08\n", "25  **********-22-000108                                     StockholdersEquity  us-gaap/2022   BS       5    34              USD         0      0      5.067200e+10      6.309000e+10\n", "26  **********-22-000108                       LiabilitiesAndStockholdersEquity  us-gaap/2022   BS       5    35              USD         0      0      3.527550e+11      3.510020e+11\n", "27  **********-22-000108                    CommonStockParOrStatedValuePerShare  us-gaap/2022   BS       6     1              USD         0      1      0.000000e+00      0.000000e+00\n", "28  **********-22-000108                            CommonStockSharesAuthorized  us-gaap/2022   BS       6     2           shares         0      1      5.040000e+10      5.040000e+10\n", "29  **********-22-000108                                CommonStockSharesIssued  us-gaap/2022   BS       6     3           shares         0      1      1.594342e+10      1.642679e+10\n", "30  **********-22-000108                           CommonStockSharesOutstanding  us-gaap/2022   BS       6     4           shares         0      1      1.594342e+10      1.642679e+10\n"]}], "source": ["from secfsdstools.e_collector.reportcollecting import SingleReportCollector\n", "from secfsdstools.e_filter.rawfiltering import ReportPeriodAndPreviousPeriodRawFilter\n", "from secfsdstools.e_presenter.presenting import StandardStatementPresenter\n", "\n", "# the unique identifier for apple's 10-K report of 2022\n", "apple_10k_2022_adsh = \"**********-22-000108\"\n", "\n", "# us a Collector to grab the data of the 10-K report. filter for balancesheet information\n", "collector: SingleReportCollector = SingleReportCollector.get_report_by_adsh(\n", "      adsh=apple_10k_2022_adsh,\n", "      stmt_filter=[\"BS\"]\n", ")  \n", "rawdatabag = collector.collect() # load the data from the disk\n", "  \n", "bs_df = (rawdatabag\n", "         # ensure only data from the period (2022) of the previous period (2021) is in the data\n", "         .filter(ReportPeriodAndPreviousPeriodRawFilter())\n", "         # join the the content of the pre_txt and num_txt together\n", "         .join()  \n", "         # format the data in the same way as it appears in the report\n", "         .present(StandardStatementPresenter(show_segments=False))) \n", "print(bs_df) "]}, {"cell_type": "markdown", "id": "311c75f3-bcdf-445f-bf8b-6094215da914", "metadata": {}, "source": ["## Overview\n", "The following diagram gives an overview on SECFSDSTools library.\n", "\n", "![Overview](https://github.com/HansjoergW/sec-fincancial-statement-data-set/raw/main/docs/images/overview.png)\n", "\n", "It mainly exists out of two main processes. The first one ist the \"Date Update Process\" wich is responsible for the\n", "download of the Financial Statement Data Sets zip files from the sec.gov website, transforming the content into parquet\n", "format, and indexing the content of these files in a simple SQLite database. Again, this whole process can be started\n", "\"manually\" by calling the update method, or it is done automatically, as it is described above.\n", "\n", "The second main process is the \"Data Processing Process\", which is working with the data that is stored inside the\n", "sub.txt, pre.txt, and num.txt files from the zip files. The \"Data Processing Process\" mainly exists out of four steps:\n", "\n", "* **Collect** <br/> Collect the rawdata from one or more different zip files. For instance, get all the data for a single\n", "report, or get the data for all 10-K reports of a single report from several zip files.\n", "* **Raw Processing** <br/> Once the data is collected, the collected data for sub.txt, pre.txt, and num.txt is available\n", "as a pandas dataframe. Filters can be applied, the content can directly be saved and loaded.\n", "* **Joined Processing** <br/> From the \"Raw Data\", a \"joined\" representation can be created. This joins the data from\n", "the pre.txt and num.txt content together based on the \"adhs\", \"tag\", and \"version\" attributes. \"Joined data\" can also be\n", "filtered, concatenated, directly saved and loaded.\n", "* **Present** <br/> Produce a single pandas dataframe out of the data and use it for further processing or produce standardized views on Balance Sheets, Income Statements, and Cash Flow.\n", "\n", "The diagramm also shows the main classes with which a user interacts. The use of them  is described in the following chapters.\n"]}, {"cell_type": "markdown", "id": "a3354a79-cd7c-4e22-bf7f-035d0ecd7c04", "metadata": {"tags": []}, "source": ["## General\n", "Most of the classes you interact with have a factory method which name starts with \"get_\". All these factory methods\n", "take at least one **optional** parameter called configuration which is of type \"Configuration\".\n", "\n", "If you do not provide this parameter, the class will read the configuration info from you configuration file in your home\n", "directory. If, for whatever reason, you do want to provide an alternative configuration, you can overwrite it.\n", "\n", "However, normally you do not have to provide the \"configuration\" parameter."]}, {"cell_type": "markdown", "id": "adfc9b97-5209-404d-bb19-42d338bc5c71", "metadata": {}, "source": ["## Index: working with the index\n", "The first class that interacts with the index is the `IndexSearch` class. It provides a single method `find_company_by_name`\n", "which executes a SQL Like search on the name of the available companies and returns a pandas dataframe with the columns\n", "'name' and 'cik' (the central index key, or the unique id of a company in the financial statements data sets).\n", "The main purpose of this class is to find the cik for a company (of course, you can also directly search the cik on https://www.sec.gov/edgar/searchedgar/companysearch)."]}, {"cell_type": "code", "execution_count": 8, "id": "f4569625-869b-4112-bde1-6dcb70b517d2", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-01 07:11:32,192 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n"]}, {"name": "stdout", "output_type": "stream", "text": ["                             name      cik\n", "0       APPLE GREEN HOLDING, INC.  1510976\n", "1    APPLE HOSPITALITY REIT, INC.  1418121\n", "2                       APPLE INC   320193\n", "3       APPLE ISPORTS GROUP, INC.  1134982\n", "4          APPLE REIT EIGHT, INC.  1387361\n", "5           APPLE REIT NINE, INC.  1418121\n", "6          APPLE REIT SEVEN, INC.  1329011\n", "7              APPLE REIT SIX INC  1277151\n", "8            APPLE REIT TEN, INC.  1498864\n", "9          APPLETON PAPERS INC/WI  1144326\n", "10  DR PEPPER SNAPPLE GROUP, INC.  1418135\n", "11   MAUI LAND & PINEAPPLE CO INC    63330\n", "12          PINEAPPLE ENERGY INC.    22701\n", "13  PINEAPPLE EXPRESS CANNABIS CO  1710495\n", "14        PINEAPPLE EXPRESS, INC.  1654672\n", "15       PINEAPPLE FINANCIAL INC.  1938109\n", "16                PINEAPPLE, INC.  1654672\n"]}], "source": ["from secfsdstools.c_index.searching import IndexSearch\n", "\n", "index_search = IndexSearch.get_index_search()\n", "results = index_search.find_company_by_name(\"apple\")\n", "print(results)"]}, {"cell_type": "markdown", "id": "2f0e0584-599f-4684-ab22-66bd790151d6", "metadata": {"tags": []}, "source": ["Once you have the cik of a company, you can use the `CompanyIndexReader` to get information on available reports of a company.\n", "To get an instance of the class, you use the get `get_company_index_reader` method and provide the cik parameter."]}, {"cell_type": "code", "execution_count": 9, "id": "e5033f87-b67f-47cf-99f1-8fd527000fa6", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-01 07:11:48,999 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n"]}], "source": ["from secfsdstools.c_index.companyindexreading import CompanyIndexReader\n", "\n", "apple_cik = 320193\n", "apple_index_reader = CompanyIndexReader.get_company_index_reader(cik=apple_cik)"]}, {"cell_type": "markdown", "id": "4894c223-3652-432a-a82a-693896a5cf90", "metadata": {}, "source": ["First, you could use the method `get_latest_company_filing` which returns a dictionary with the latest filing of the company:"]}, {"cell_type": "code", "execution_count": 10, "id": "b92223bf-6589-4eba-ba22-d2596b47bbd6", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'adsh': '**********-24-000123', 'cik': 320193, 'name': 'APPLE INC', 'sic': 3571.0, 'countryba': 'US', 'stprba': 'CA', 'cityba': 'CUPERTINO', 'zipba': '95014', 'bas1': 'ONE APPLE PARK WAY', 'bas2': None, 'baph': '(*************', 'countryma': 'US', 'stprma': 'CA', 'cityma': 'CUPERTINO', 'zipma': '95014', 'mas1': 'ONE APPLE PARK WAY', 'mas2': None, 'countryinc': 'US', 'stprinc': 'CA', 'ein': *********, 'former': 'APPLE INC', 'changed': 20070109.0, 'afs': '1-LAF', 'wksi': 1, 'fye': '0930', 'form': '10-K', 'period': 20240930, 'fy': 2024.0, 'fp': 'FY', 'filed': 20241101, 'accepted': '2024-11-01 06:02:00.0', 'prevrpt': 0, 'detail': 1, 'instance': 'aapl-20240928_htm.xml', 'nciks': 1, 'aciks': None}\n"]}], "source": ["print(apple_index_reader.get_latest_company_filing())"]}, {"cell_type": "markdown", "id": "8c0ab77d-c444-41ca-8ece-dad1054caf19", "metadata": {}, "source": ["Next there are two methods which return the metadata of the reports that a company has filed. The result is either\n", "returned as a list of `IndexReport` instances, if you use the method `get_all_company_reports` or as pandas dataframe if\n", "you use the method `get_all_company_reports_df`. Both method can take an optional parameter `forms`, which defines the\n", "type of the report that is returned. For instance, if you are only interested in the annual and quarterly reports,\n", "set forms to `[\"10-K\", \"10-Q\"]`."]}, {"cell_type": "code", "execution_count": 11, "id": "5a6233a0-7543-43be-a581-8ed279787442", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                    adsh     cik       name  form     filed    period                                                     fullPath  originFile originFileType                                                                                               url\n", "0   **********-24-000123  320193  APPLE INC  10-K  20241101  20240930  C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2024q4.zip  2024q4.zip        quarter  https://www.sec.gov/Archives/edgar/data/320193/**********24000123/**********-24-000123-index.htm\n", "1   **********-23-000106  320193  APPLE INC  10-K  20231103  20230930  C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2023q4.zip  2023q4.zip        quarter  https://www.sec.gov/Archives/edgar/data/320193/**********23000106/**********-23-000106-index.htm\n", "2   **********-22-000108  320193  APPLE INC  10-K  20221028  ********  C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2022q4.zip  2022q4.zip        quarter  https://www.sec.gov/Archives/edgar/data/320193/**********22000108/**********-22-000108-index.htm\n", "3   **********-21-000105  320193  APPLE INC  10-K  20211029  ********  C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2021q4.zip  2021q4.zip        quarter  https://www.sec.gov/Archives/edgar/data/320193/**********21000105/**********-21-000105-index.htm\n", "4   **********-20-000096  320193  APPLE INC  10-K  20201030  20200930  C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2020q4.zip  2020q4.zip        quarter  https://www.sec.gov/Archives/edgar/data/320193/**********20000096/**********-20-000096-index.htm\n", "5   **********-19-000119  320193  APPLE INC  10-K  20191031  20190930  C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2019q4.zip  2019q4.zip        quarter  https://www.sec.gov/Archives/edgar/data/320193/**********19000119/**********-19-000119-index.htm\n", "6   **********-18-000145  320193  APPLE INC  10-K  20181105  20180930  C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2018q4.zip  2018q4.zip        quarter  https://www.sec.gov/Archives/edgar/data/320193/**********18000145/**********-18-000145-index.htm\n", "7   **********-17-000070  320193  APPLE INC  10-K  20171103  20170930  C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2017q4.zip  2017q4.zip        quarter  https://www.sec.gov/Archives/edgar/data/320193/**********17000070/**********-17-000070-index.htm\n", "8   0001628280-16-020309  320193  APPLE INC  10-K  20161026  20160930  C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2016q4.zip  2016q4.zip        quarter  https://www.sec.gov/Archives/edgar/data/320193/000162828016020309/0001628280-16-020309-index.htm\n", "9   0001193125-15-356351  320193  APPLE INC  10-K  20151028  20150930  C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2015q4.zip  2015q4.zip        quarter  https://www.sec.gov/Archives/edgar/data/320193/000119312515356351/0001193125-15-356351-index.htm\n", "10  0001193125-14-383437  320193  APPLE INC  10-K  20141027  20140930  C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2014q4.zip  2014q4.zip        quarter  https://www.sec.gov/Archives/edgar/data/320193/000119312514383437/0001193125-14-383437-index.htm\n", "11  0001193125-13-416534  320193  APPLE INC  10-K  20131030  20130930  C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2013q4.zip  2013q4.zip        quarter  https://www.sec.gov/Archives/edgar/data/320193/000119312513416534/0001193125-13-416534-index.htm\n", "12  0001193125-12-444068  320193  APPLE INC  10-K  20121031  20120930  C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2012q4.zip  2012q4.zip        quarter  https://www.sec.gov/Archives/edgar/data/320193/000119312512444068/0001193125-12-444068-index.htm\n", "13  0001193125-11-282113  320193  APPLE INC  10-K  20111026  20110930  C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2011q4.zip  2011q4.zip        quarter  https://www.sec.gov/Archives/edgar/data/320193/000119312511282113/0001193125-11-282113-index.htm\n", "14  0001193125-10-238044  320193  APPLE INC  10-K  20101027  20100930  C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2010q4.zip  2010q4.zip        quarter  https://www.sec.gov/Archives/edgar/data/320193/000119312510238044/0001193125-10-238044-index.htm\n", "15  0001193125-09-214859  320193  APPLE INC  10-K  20091027  20090930  C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2009q4.zip  2009q4.zip        quarter  https://www.sec.gov/Archives/edgar/data/320193/000119312509214859/0001193125-09-214859-index.htm\n"]}], "source": ["# only show the annual reports of apple\n", "print(apple_index_reader.get_all_company_reports_df(forms=[\"10-K\"]))"]}, {"cell_type": "markdown", "id": "fcf84f13-469d-42d3-9098-40707373c6d8", "metadata": {}, "source": ["**Note:** the entries in the url column above directly open the filing of that report on the sec.gov website."]}, {"cell_type": "markdown", "id": "da01caac-c8a4-416c-a6f2-42acc79a22bd", "metadata": {}, "source": ["## Collect: collecting the data from reports\n", "The previously introduced `IndexSearch` and `CompanyIndexReader` let you know what data is available, but they do not\n", "return the real data of the financial statements. This is what the `Collector` classes are used for.\n", "\n", "All the `Collector` classes have their own factory method(s) which instantiate the class. Most of these factory methods\n", "also provide parameters to filter the data directly when being loaded from the parquet files.\n", "These are\n", "* the `forms_filter` <br> lets you select which report type should be loaded (e.g. \"10-K\" or \"10-Q\").<br>\n", "  Note: the `forms` filter affects all dataframes (sub, pre, num).\n", "* the `stmt_filter` <br> defines the statements that should be loaded (e.g., \"BS\" if only \"Balance Sheet\" data should be loaded) <br>\n", "  Note: the stmt filter only affects the pre dataframe.\n", "* the `tag_filter` <br> defines the tags, that should be loaded (e.g., \"Assets\" if only the \"Assets\" tag should be loaded) <br>\n", "  Note: the tag filter affects the pre and num dataframes.\n", "\n", "It is also possible to apply filter for these attributes after the data is loaded, but since the `Collector` classes\n", "apply these filters directly during the load process from the parquet files (which means that fewer data is loaded from\n", "the disk) they are generally more efficient.\n", "\n", "All `Collector` classes have a `collect` method which then loads the data from the parquet files and returns an instance\n", "of `RawDataBag`. The `RawDataBag` instance contains then three pandas dataframe: one for the `sub` (subscription) data,\n", "one for the `pre` (presentation) data, and one for the `num` (the numeric values) data.\n", "\n", "The framework provides the following collectors:"]}, {"cell_type": "markdown", "id": "db1af938-7189-471f-9bcf-6e79be23cb45", "metadata": {}, "source": ["---\n", "* `SingleReportCollector` <br> As the name suggests, this `Collector` returns the data of a single report. It is \n", "  instantiated by providing the `adsh` of the desired report as parameter of the `get_report_by_adsh` factory method, \n", "  or by using an instance of the `IndexReport` as parameter of the `get_report_by_indexreport`. (As a reminder: \n", "  instances of `IndexReport` are returned by the `CompanyIndexReader` class)."]}, {"cell_type": "code", "execution_count": 15, "id": "cfc2ff08-5070-49da-a99d-1bbe12c1c3ee", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-01 07:16:51,465 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n"]}, {"name": "stdout", "output_type": "stream", "text": ["                   adsh     cik       name     sic countryba stprba     cityba  zipba                bas1  bas2            baph countryma stprma     cityma  zipma                mas1  mas2 countryinc stprinc        ein     former     changed    afs  wksi   fye  form    period      fy  fp     filed               accepted  prevrpt  detail               instance  nciks aciks\n", "0  **********-22-000108  320193  APPLE INC  3571.0        US     CA  CUPERTINO  95014  ONE APPLE PARK WAY  None  (*************        US     CA  CUPERTINO  95014  ONE APPLE PARK WAY  None         US      CA  *********  APPLE INC  20070109.0  1-LAF     1  0930  10-K  ********  2022.0  FY  20221028  2022-10-27 18:01:00.0        0       1  aapl-20220924_htm.xml      1  None\n", "\n", "-------------------------------------------------\n", "                   adsh  report  line stmt  inpth rfile                                                                                          tag       version                                    plabel  negating\n", "0  **********-22-000108       3     7   IS      0     H                                          RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2022                                 Net sales         0\n", "1  **********-22-000108       3     8   IS      0     H                                                                   CostOfGoodsAndServicesSold  us-gaap/2022                             Cost of sales         0\n", "2  **********-22-000108       3     9   IS      0     H                                                                                  GrossProfit  us-gaap/2022                              Gross margin         0\n", "3  **********-22-000108       3    11   IS      0     H                                                                ResearchAndDevelopmentExpense  us-gaap/2022                  Research and development         0\n", "4  **********-22-000108       3    12   IS      0     H                                                       SellingGeneralAndAdministrativeExpense  us-gaap/2022       Selling, general and administrative         0\n", "5  **********-22-000108       3    13   IS      0     H                                                                            OperatingExpenses  us-gaap/2022                  Total operating expenses         0\n", "6  **********-22-000108       3    14   IS      0     H                                                                          OperatingIncomeLoss  us-gaap/2022                          Operating income         0\n", "7  **********-22-000108       3    15   IS      0     H                                                                    NonoperatingIncomeExpense  us-gaap/2022               Other income/(expense), net         0\n", "8  **********-22-000108       3    16   IS      0     H  IncomeLossFromContinuingOperationsBeforeIncomeTaxesExtraordinaryItemsNoncontrollingInterest  us-gaap/2022  Income before provision for income taxes         0\n", "9  **********-22-000108       3    17   IS      0     H                                                                      IncomeTaxExpenseBenefit  us-gaap/2022                Provision for income taxes         0\n", "\n", "-------------------------------------------------\n", "                   adsh                                                                                     tag               version     ddate  qtrs  uom                                                                                       segments coreg         value footnote\n", "0  **********-22-000108                                        ProceedsFromSaleOfAvailableForSaleSecuritiesDebt          us-gaap/2022  ********     4  USD                                                                                                       3.744600e+10     None\n", "1  **********-22-000108                                                          MarketableSecuritiesNoncurrent          us-gaap/2022  ********     0  USD  FairValueByFairValueHierarchyLevel=FairValueInputsLevel2;FinancialInstrument=CommercialPaper;        0.000000e+00     None\n", "2  **********-22-000108                                                   CashAndCashEquivalentsAtCarryingValue          us-gaap/2022  ********     0  USD                                                                                                       3.494000e+10     None\n", "3  **********-22-000108                                                                   EarningsPerShareBasic          us-gaap/2022  ********     4  USD                                                                                                       6.150000e+00     None\n", "4  **********-22-000108                                         ProceedsFromPaymentsForOtherFinancingActivities          us-gaap/2022  ********     4  USD                                                                                                       9.760000e+08     None\n", "5  **********-22-000108                                         IncreaseDecreaseInContractWithCustomerLiability          us-gaap/2022  ********     4  USD                                                                                                       4.780000e+08     None\n", "6  **********-22-000108  OtherComprehensiveIncomeLossDerivativeInstrumentGainLossbeforeReclassificationafterTax  **********-22-000108  ********     4  USD                                                                                                       3.200000e+07     None\n", "7  **********-22-000108                                     RevenueFromContractWithCustomerExcludingAssessedTax          us-gaap/2022  20200930     4  USD                                                                         ProductOrService=IPad;        2.372400e+10     None\n", "8  **********-22-000108                           CashCashEquivalentsRestrictedCashAndRestrictedCashEquivalents          us-gaap/2022  20200930     0  USD                                                                                                       3.978900e+10     None\n", "9  **********-22-000108                                            CommonStocksIncludingAdditionalPaidInCapital          us-gaap/2022  ********     0  USD                                                                                                       5.736500e+10     None\n"]}], "source": ["from secfsdstools.e_collector.reportcollecting import SingleReportCollector\n", "\n", "apple_10k_2022_adsh = \"**********-22-000108\"\n", "\n", "collector: SingleReportCollector = SingleReportCollector.get_report_by_adsh(adsh=apple_10k_2022_adsh)\n", "rawdatabag = collector.collect()\n", "\n", "# as expected, there is just one entry in the submission dataframe\n", "print(rawdatabag.sub_df)\n", "print('\\n-------------------------------------------------')\n", "\n", "# just print the first 10 rows of the pre and num dataframes\n", "print(rawdatabag.pre_df[:10])\n", "print('\\n-------------------------------------------------')\n", "print(rawdatabag.num_df[:10])"]}, {"cell_type": "markdown", "id": "7815f6ed-bb29-4fca-a036-1d63dd725282", "metadata": {}, "source": ["---\n", "* `MultiReportCollector` <br> Contrary to the `SingleReportCollector`, this `Collector` can collect data from several\n", "  reports. Moreover, the data of the reports are loaded in parallel, this  especially improves the performance if the\n", "  reports are from different quarters (resp. are in different zip files). The class provides the factory methods \n", "  `get_reports_by_adshs` and `get_reports_by_indexreports`. The first takes a list of adsh strings, the second a list\n", "  of `IndexReport` instances."]}, {"cell_type": "code", "execution_count": 16, "id": "80c60929-bbe5-4c8f-86a3-a5f72d640fc9", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-01 07:17:51,200 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-01 07:17:51,203 [INFO] parallelexecution      items to process: 2\n", "2025-02-01 07:17:53,612 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["                   adsh     cik       name     sic countryba stprba     cityba  zipba                bas1  bas2            baph countryma stprma     cityma  zipma                mas1  mas2 countryinc stprinc          ein              former     changed    afs  wksi   fye  form    period      fy  fp     filed               accepted  prevrpt  detail               instance  nciks aciks\n", "0  **********-22-000108  320193  APPLE INC  3571.0        US     CA  CUPERTINO  95014  ONE APPLE PARK WAY  None  (*************        US     CA  CUPERTINO  95014  ONE APPLE PARK WAY  None         US      CA  *********.0           APPLE INC  20070109.0  1-LAF     1  0930  10-K  ********  2022.0  FY  20221028  2022-10-27 18:01:00.0        0       1  aapl-20220924_htm.xml      1  None\n", "1  0001193125-12-444068  320193  APPLE INC  3571.0        US     CA  CUPERTINO  95014   ONE INFINITE LOOP  None  (*************        US     CA  CUPERTINO  95014   ONE INFINITE LOOP  None         US      CA  *********.0  APPLE COMPUTER INC  19970808.0  1-LAF     1  0930  10-K  20120930  2012.0  FY  20121031  2012-10-31 17:07:00.0        0       1      aapl-20120929.xml      1  None \n", "\n", "                    adsh     tag       version     ddate  qtrs  uom                                      segments coreg         value footnote\n", "0   **********-22-000108  Assets  us-gaap/2022  ********     0  USD                                                      3.527550e+11     None\n", "1   **********-22-000108  Assets  us-gaap/2022  ********     0  USD                                                      3.510020e+11     None\n", "2   0001193125-12-444068  Assets  us-gaap/2012  20110930     0  USD              BusinessSegments=AsiaAndPacific;        1.710000e+09     None\n", "3   0001193125-12-444068  Assets  us-gaap/2012  20120930     0  USD                                                      1.760640e+11     None\n", "4   0001193125-12-444068  Assets  us-gaap/2012  20110930     0  USD                      BusinessSegments=Retail;        2.151000e+09     None\n", "5   0001193125-12-444068  Assets  us-gaap/2012  20110930     0  USD                      BusinessSegments=Europe;        1.520000e+09     None\n", "6   0001193125-12-444068  Assets  us-gaap/2012  20120930     0  USD  BusinessSegments=UnallocatedAmountToSegment;        1.607870e+11     None\n", "7   0001193125-12-444068  Assets  us-gaap/2012  20110930     0  USD                                                      1.163710e+11     None\n", "8   0001193125-12-444068  Assets  us-gaap/2012  20110930     0  USD                    BusinessSegments=Americas;        2.782000e+09     None\n", "9   0001193125-12-444068  Assets  us-gaap/2012  20120930     0  USD                      BusinessSegments=Europe;        3.095000e+09     None\n", "10  0001193125-12-444068  Assets  us-gaap/2012  20120930     0  USD                    BusinessSegments=Americas;        5.525000e+09     None\n", "11  0001193125-12-444068  Assets  us-gaap/2012  20120930     0  USD              BusinessSegments=AsiaAndPacific;        2.234000e+09     None\n", "12  0001193125-12-444068  Assets  us-gaap/2012  20110930     0  USD  BusinessSegments=UnallocatedAmountToSegment;        1.075710e+11     None\n", "13  0001193125-12-444068  Assets  us-gaap/2012  20120930     0  USD                      BusinessSegments=Retail;        2.725000e+09     None\n", "14  0001193125-12-444068  Assets  us-gaap/2012  20110930     0  USD           BusinessSegments=OperatingSegments;        8.800000e+09     None\n", "15  0001193125-12-444068  Assets  us-gaap/2012  20120930     0  USD                          BusinessSegments=JP;        1.698000e+09     None\n", "16  0001193125-12-444068  Assets  us-gaap/2012  20120930     0  USD           BusinessSegments=OperatingSegments;        1.527700e+10     None\n", "17  0001193125-12-444068  Assets  us-gaap/2012  20110930     0  USD                          BusinessSegments=JP;        6.370000e+08     None\n"]}], "source": ["from secfsdstools.e_collector.multireportcollecting import MultiReportCollector\n", "apple_10k_2022_adsh = \"**********-22-000108\"\n", "apple_10k_2012_adsh = \"0001193125-12-444068\"\n", "\n", "# load only the assets tags that are present in the 10-K report of apple in the years\n", "# 2022 and 2012\n", "collector: MultiReportCollector = MultiReportCollector.get_reports_by_adshs(\n", "                                              adshs=[apple_10k_2022_adsh, apple_10k_2012_adsh],\n", "                                              tag_filter=['Assets'])\n", "rawdatabag = collector.collect()\n", "# as expected, there are just two entries in the submission dataframe\n", "print(rawdatabag.sub_df, '\\n')\n", "print(rawdatabag.num_df)  "]}, {"cell_type": "markdown", "id": "e96a68a5-170d-45f9-865b-fa1f8087a677", "metadata": {}, "source": ["---\n", "* `ZipCollector` <br> This `Collector` collects the data of one single zip (resp. the folder that contains the parquet\n", "  files of this zip file). And since the original zip file contains the data for one quarter, the name you provide\n", "  in the `get_zip_by_name` factory method reflects the quarter which data you want to load: e.g. `2022q1.zip`."]}, {"cell_type": "code", "execution_count": 17, "id": "77f7c553-695f-4006-8026-97df10d4bf30", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-01 07:18:57,113 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-01 07:18:57,116 [INFO] parallelexecution      items to process: 1\n", "2025-02-01 07:18:57,117 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\quarter\\2022q1.zip\n", "2025-02-01 07:18:58,845 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["(4862, 36)\n", "(183226, 10)\n", "(2217834, 10)\n"]}], "source": ["from secfsdstools.e_collector.zipcollecting import ZipCollector\n", "\n", "# only collect the Balance Sheet of annual reports that\n", "# were filed during the first quarter in 2022\n", "collector: ZipCollector = ZipCollector.get_zip_by_name(name=\"2022q1.zip\",\n", "                                                       forms_filter=[\"10-K\"],\n", "                                                       stmt_filter=[\"BS\"])\n", "\n", "rawdatabag = collector.collect()\n", "\n", "# only show the size of the data frame\n", "# .. over 4000 companies filed a 10 K report in q1 2022\n", "print(rawdatabag.sub_df.shape)\n", "print(rawdatabag.pre_df.shape)\n", "print(rawdatabag.num_df.shape)    "]}, {"cell_type": "markdown", "id": "9b9ba431-fad6-4d58-ba4b-de3c9f837d5f", "metadata": {}, "source": ["---\n", "* `CompanyReportCollector` <br> This class returns reports for one or more companies. The factory method \n", "  `get_company_collector` provides the parameter `ciks` which takes a list of cik numbers."]}, {"cell_type": "code", "execution_count": 18, "id": "77165def-b05c-4713-bc86-02e7732fd35a", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-01 07:19:34,280 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-01 07:19:34,482 [INFO] parallelexecution      items to process: 16\n", "2025-02-01 07:19:41,020 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["                    adsh     cik       name     sic countryba stprba     cityba  zipba                bas1  bas2            baph countryma stprma     cityma  zipma                mas1  mas2 countryinc stprinc          ein              former     changed    afs  wksi   fye  form    period      fy  fp     filed               accepted  prevrpt  detail                  instance  nciks aciks\n", "0   **********-24-000123  320193  APPLE INC  3571.0        US     CA  CUPERTINO  95014  ONE APPLE PARK WAY  None  (*************        US     CA  CUPERTINO  95014  ONE APPLE PARK WAY  None         US      CA  *********.0           APPLE INC  20070109.0  1-LAF     1  0930  10-K  20240930  2024.0  FY  20241101  2024-11-01 06:02:00.0        0       1     aapl-20240928_htm.xml      1  None\n", "1   **********-23-000106  320193  APPLE INC  3571.0        US     CA  CUPERTINO  95014  ONE APPLE PARK WAY  None  (*************        US     CA  CUPERTINO  95014  ONE APPLE PARK WAY  None         US      CA  *********.0           APPLE INC  20070109.0  1-LAF     1  0930  10-K  20230930  2023.0  FY  20231103  2023-11-02 18:08:00.0        0       1     aapl-20230930_htm.xml      1  None\n", "2   **********-22-000108  320193  APPLE INC  3571.0        US     CA  CUPERTINO  95014  ONE APPLE PARK WAY  None  (*************        US     CA  CUPERTINO  95014  ONE APPLE PARK WAY  None         US      CA  *********.0           APPLE INC  20070109.0  1-LAF     1  0930  10-K  ********  2022.0  FY  20221028  2022-10-27 18:01:00.0        0       1     aapl-20220924_htm.xml      1  None\n", "3   **********-21-000105  320193  APPLE INC  3571.0        US     CA  CUPERTINO  95014  ONE APPLE PARK WAY  None  (*************        US     CA  CUPERTINO  95014  ONE APPLE PARK WAY  None         US      CA  *********.0           APPLE INC  20070109.0  1-LAF     1  0930  10-K  ********  2021.0  FY  20211029  2021-10-28 18:04:00.0        0       1     aapl-20210925_htm.xml      1  None\n", "4   **********-20-000096  320193  APPLE INC  3571.0        US     CA  CUPERTINO  95014  ONE APPLE PARK WAY  None  (*************        US     CA  CUPERTINO  95014  ONE APPLE PARK WAY  None         US      CA  *********.0           APPLE INC  20070109.0  1-LAF     1  0930  10-K  20200930  2020.0  FY  20201030  2020-10-29 18:06:00.0        0       1     aapl-20200926_htm.xml      1  None\n", "5   **********-19-000119  320193  APPLE INC  3571.0        US     CA  CUPERTINO  95014  ONE APPLE PARK WAY  None  (*************        US     CA  CUPERTINO  95014  ONE APPLE PARK WAY  None         US      CA  *********.0           APPLE INC  20070109.0  1-LAF     1  0930  10-K  20190930  2019.0  FY  20191031  2019-10-30 18:13:00.0        0       1  a10-k20199282019_htm.xml      1  None\n", "6   **********-18-000145  320193  APPLE INC  3571.0        US     CA  CUPERTINO  95014  ONE APPLE PARK WAY  None  (*************        US     CA  CUPERTINO  95014  ONE APPLE PARK WAY  None         US      CA  *********.0  APPLE COMPUTER INC  19970808.0  1-LAF     1  0930  10-K  20180930  2018.0  FY  20181105  2018-11-05 08:02:00.0        0       1         aapl-20180929.xml      1  None\n", "7   **********-17-000070  320193  APPLE INC  3571.0        US     CA  CUPERTINO  95014   ONE INFINITE LOOP  None  (*************        US     CA  CUPERTINO  95014   ONE INFINITE LOOP  None         US      CA  *********.0  APPLE COMPUTER INC  19970808.0  1-LAF     1  0930  10-K  20170930  2017.0  FY  20171103  2017-11-03 08:02:00.0        0       1         aapl-20170930.xml      1  None\n", "8   0001628280-16-020309  320193  APPLE INC  3571.0        US     CA  CUPERTINO  95014   ONE INFINITE LOOP  None  (*************        US     CA  CUPERTINO  95014   ONE INFINITE LOOP  None         US      CA  *********.0  APPLE COMPUTER INC  19970808.0  1-LAF     1  0930  10-K  20160930  2016.0  FY  20161026  2016-10-26 16:42:00.0        0       1         aapl-20160924.xml      1  None\n", "9   0001193125-15-356351  320193  APPLE INC  3571.0        US     CA  CUPERTINO  95014   ONE INFINITE LOOP  None  (*************        US     CA  CUPERTINO  95014   ONE INFINITE LOOP  None         US      CA  *********.0  APPLE COMPUTER INC  19970808.0  1-LAF     1  0930  10-K  20150930  2015.0  FY  20151028  2015-10-28 16:31:00.0        0       1         aapl-20150926.xml      1  None\n", "10  0001193125-14-383437  320193  APPLE INC  3571.0        US     CA  CUPERTINO  95014   ONE INFINITE LOOP  None  (*************        US     CA  CUPERTINO  95014   ONE INFINITE LOOP  None         US      CA  *********.0  APPLE COMPUTER INC  19970808.0  1-LAF     1  0930  10-K  20140930  2014.0  FY  20141027  2014-10-27 17:12:00.0        0       1         aapl-20140927.xml      1  None\n", "11  0001193125-13-416534  320193  APPLE INC  3571.0        US     CA  CUPERTINO  95014   ONE INFINITE LOOP  None  (*************        US     CA  CUPERTINO  95014   ONE INFINITE LOOP  None         US      CA  *********.0  APPLE COMPUTER INC  19970808.0  1-LAF     1  0930  10-K  20130930  2013.0  FY  20131030  2013-10-29 20:38:00.0        0       1         aapl-20130928.xml      1  None\n", "12  0001193125-12-444068  320193  APPLE INC  3571.0        US     CA  CUPERTINO  95014   ONE INFINITE LOOP  None  (*************        US     CA  CUPERTINO  95014   ONE INFINITE LOOP  None         US      CA  *********.0  APPLE COMPUTER INC  19970808.0  1-LAF     1  0930  10-K  20120930  2012.0  FY  20121031  2012-10-31 17:07:00.0        0       1         aapl-20120929.xml      1  None\n", "13  0001193125-11-282113  320193  APPLE INC  3571.0        US     CA  CUPERTINO  95014   ONE INFINITE LOOP  None  (*************        US     CA  CUPERTINO  95014   ONE INFINITE LOOP  None         US      CA  *********.0  APPLE COMPUTER INC  19970808.0  1-LAF     1  0930  10-K  20110930  2011.0  FY  20111026  2011-10-26 16:35:00.0        0       1         aapl-20110924.xml      1  None\n", "14  0001193125-10-238044  320193  APPLE INC  3571.0        US     CA  CUPERTINO  95014   ONE INFINITE LOOP  None  (*************        US     CA  CUPERTINO  95014   ONE INFINITE LOOP  None         US      CA  *********.0  APPLE COMPUTER INC  19970808.0  1-LAF     1  0930  10-K  20100930  2010.0  FY  20101027  2010-10-27 16:36:00.0        0       1         aapl-20100925.xml      1  None\n", "15  0001193125-09-214859  320193  APPLE INC  3571.0        US     CA  CUPERTINO  95014     1 INFINITE LOOP  None      4089961010        US     CA  CUPERTINO  95014   ONE INFINITE LOOP  None         US      CA  *********.0  APPLE COMPUTER INC  19970808.0  1-LAF     1  0930  10-K  20090930  2009.0  FY  20091027  2009-10-27 16:18:00.0        1       0         aapl-20090926.xml      1  None\n", "(1647, 10)\n", "(7976, 10)\n"]}], "source": ["from secfsdstools.e_collector.companycollecting import CompanyReportCollector\n", "\n", "apple_cik = 320193\n", "# load the data for all 10-K (annual) reports of apple\n", "collector = CompanyReportCollector.get_company_collector(ciks=[apple_cik],\n", "                                                         forms_filter=[\"10-K\"])\n", "\n", "rawdatabag = collector.collect()\n", "\n", "# all filed 10-K reports for apple since 2010 are in the databag\n", "print(rawdatabag.sub_df)\n", "\n", "print(rawdatabag.pre_df.shape)\n", "print(rawdatabag.num_df.shape) "]}, {"cell_type": "markdown", "id": "5bc8536b-c775-48a2-8c58-159d569e5acf", "metadata": {}, "source": ["## Raw Processing: working with the raw data\n", "When the `collect` method of a `Collector` class is called, the data for the sub, pre, and num dataframes are loaded\n", "and being stored in the sub_df, pre_df, and num_df attributes inside an instance of `RawDataBag`.\n", "\n", "The `RawDataBag` provides the following methods:\n", "* `save`, `load`<br> The content of a `RawDataBag` can be saved into a directory. Within that directory, \n", "   parquet files are stored for the content of the sub_df, pre_df, and num_df. In order to load this \n", "   data directly, the static method `RawDataBag.load()` can be used.\n", "* `concat`<br> Several instances of a `RawDataBag` can be concatenated in one single instance. In order to do \n", "   that, the static method `RawDataBag.concat()` takes a list of RawDataBag as parameter.\n", "* `join` <br> This method produces a `JoinedRawDataBag` by joining the content of the pre_df and num_df\n", "   based on the columns adsh, tag, and version. It is an inner join. The joined dataframe appears as pre_num_df in\n", "   the `JoinedRawDataBag`.\n", "* `filter` <br> The filter method takes a parameter of the type `FilterRaw`, applies it to the data and\n", "   produces a new instance of `RawDataBag` with the filtered data. Therefore, filters can also be chained like\n", "   `a_filtered_RawDataBag = a_RawDataBag.filter(filter1).filter(filter2)`. Moreover, the `__get__item` method\n", "   is forwarded to the filter method, so you can also write `a_filtered_RawDataBag = a_RawDataBag[filter1][filter2]`.\n", "\n", "It is simple to write your own filters, just get some inspiration from the once that are already present in the\n", "Framework (module `secfsdstools.e_filter.rawfiltering`:\n", "\n", "* `AdshRawFilter` <br> Filters the `RawDataBag` instance based on the list of adshs that were provided in the constructor. <br>\n", "   ````\n", "   a_filtered_RawDataBag = a_RawDataBag.filter(AdshRawFilter(adshs=['0001193125-09-214859', '0001193125-10-238044']))\n", "   ````\n", "* `StmtRawFilter` <br> Filters the `RawDataBag`instance based on the list of statements ('BS', 'CF', 'IS', ...). <br>\n", "   ````\n", "   a_filtered_RawDataBag = a_RawDataBag.filter(StmtRawFilter(stmts=['BS', 'IS']))\n", "   ````\n", "* `TagRawFilter` <br> Filters the `RawDataBag`instance based on the list of tags that is provided. <br>\n", "   ````\n", "   a_filtered_RawDataBag = a_RawDataBag.filter(TagRawFilter(tags=['Assets', 'Liabilities']))\n", "   ````\n", "* `MainCoregRawFilter` <br> Filters the `RawDataBag` so that data of subsidiaries are removed.\n", "   ````\n", "   a_filtered_RawDataBag = a_RawDataBag.filter(MainCoregRawFilter()) \n", "   ````\n", "* `ReportPeriodAndPreviousPeriodRawFilter` <br> The data of a report usually also contains data from previous years.\n", "  However, often you want just to analyze the data of the current and the previous year. This filter ensures that\n", "  only data for the current period and the previous period are contained in the data.\n", "   ````\n", "   a_filtered_RawDataBag = a_RawDataBag.filter(ReportPeriodAndPreviousPeriodRawFilter()) \n", "   ````\n", "* `ReportPeriodRawFilter` <br> If you are just interested only in the data of a report that is from the current period\n", "  of the report then you can use this filter. For instance, if you use a `CompanyReportCollector` to collect all\n", "  10-K reports of this company, you want to ensure that every report only contains data for its period and not for\n", "  previous periods.\n", "   ````\n", "   a_filtered_RawDataBag = a_RawDataBag.filter(ReportPeriodRawFilter()) \n", "   ````\n", "* `OfficialTagsOnlyRawFilter` <br> Sometimes companies report some data points with unofficial tags, meaning they are not defined\n", "  in the US-GAAP XBLR definition. Or they use an existing official tag name, but do not follow the definition of it. This filter makes\n", "  sure that only real US-GAAP XBRL tags are returned. Note: not official tags have the `adsh` number set as `version`.\n", "  ````\n", "   a_filtered_RawDataBag = a_RawDataBag.filter(OfficialTagsOnlyRawFilter()) \n", "  ````\n", "* `USDOnlyRawFilter` <br> Most of the datapoints are reported in USD. However, international companies often report certain data points\n", "  not only in USD, but also in other currencies. If you use this filter, you will get data points only for USD.\n", "  ````\n", "   a_filtered_RawDataBag = a_RawDataBag.filter(USDOnlyRawFilter()) \n", "  ```` \n", "* `NoSegmentInfoRawFilter` <br> Filters for rows in the num dataframe which do not contain values in the segments column.\n", "  ````\n", "   a_filtered_RawDataBag = a_RawDataBag.filter(NoSegmentInfoRawFilter()) \n", "  ```` \n", "  "]}, {"cell_type": "markdown", "id": "07abdfe1-96d0-4479-9cf4-e4db6b5f16e0", "metadata": {}, "source": ["## Joined Processing: working with joined data\n", "When the `join` method of a `RawDataBag` instance is called an instance of `JoinedDataBag` is returned. The returned\n", "instance contains an attribute sub_df, which is a reference to the same sub_df that is in the `RawDataBag`.\n", "In addition to that, the `JoinedDataBag` contains an attribut pre_num_df, which is an inner join of the pre_df and \n", "the num_df based on the columns adsh, tag, and version. Note that an entry in the pre_df can be joined with more than \n", "one entry in the num_df.\n", "\n", "The `JoinedDataBag` provides the following methods:\n", "* `save`, `load`<br> The content of a `JoinedDataBag` can be saved into a directory. Within that directory,\n", "  parquet files are stored for the content of the sub_df, pre_df, and num_df. In order to load this\n", "  data directly, the static method `JoinedDataBag.save()` can be used.\n", "* `concat`<br> Several instances of a `JoinedDataBag` can be concatenated in one single instance. In order to do\n", "  that, the static method `JoinedDataBag.concat()` takes a list of RawDataBag as parameter.\n", "* `filter` <br> The filter method takes a parameter of the type `FilterJoined`, applies it to the data and\n", "  produces a new instance of `JoinedDataBag` with the filtered data. Therefore, filters can also be chained like\n", "  `a_filtered_JoinedDataBag = a_JoinedDataBag.filter(filter1).filter(filter2)`. Moreover, the `__get__item` method\n", "  is forwarded to the filter method, so you can also write `a_filtered_JoinedDataBag = a_JoinedDataBag[filter1][filter2]`.\n", "  \n", "  **Note**: The same filters that are available for the `RawDataBag` are also available for the `JoinedDataBag`: `secfsdstools.e_filter.joinedfiltering`.\n", "* `present` <br> The idea of the present method is to make a final presentation of the data as pandas dataframe. \n", "  The method has a parameter presenter of type Presenter."]}, {"cell_type": "markdown", "id": "c308f46f-0d34-4595-9c04-74798f3b2021", "metadata": {}, "source": ["## Present\n", "It is simple to write your own presenter classes. So far, the framework provides the following Presenter \n", "implementations (module `secfsdstools.e_presenter.presenting`):\n", "\n", "* `StandardStatementPresenter` <br> This presenter provides the data in the same form, as you are used to see in\n", "  the reports itself. For instance, the primary financial statements balance sheet, income statement, and cash flow\n", "  display the different positions in rows and the columns contain the different dates/periods of the data.\n", "  Let us say you want to recreate the BS information of the apples 10-K report of 2022, you would write:\n", " "]}, {"cell_type": "code", "execution_count": 20, "id": "03cc9462-4bb1-4b6b-89b6-746c7a215dfb", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-01 07:28:23,320 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n"]}, {"name": "stdout", "output_type": "stream", "text": ["                    adsh coreg                                              tag       version stmt  report  line segments     uom  negating  inpth  qrtrs_0/********  qrtrs_0/********\n", "0   **********-22-000108                  CashAndCashEquivalentsAtCarryingValue  us-gaap/2022   BS       5     3              USD         0      0      2.364600e+10      3.494000e+10\n", "1   **********-22-000108                            MarketableSecuritiesCurrent  us-gaap/2022   BS       5     4              USD         0      0      2.465800e+10      2.769900e+10\n", "2   **********-22-000108                           AccountsReceivableNetCurrent  us-gaap/2022   BS       5     5              USD         0      0      2.818400e+10      2.627800e+10\n", "3   **********-22-000108                                           InventoryNet  us-gaap/2022   BS       5     6              USD         0      0      4.946000e+09      6.580000e+09\n", "4   **********-22-000108                             NontradeReceivablesCurrent  us-gaap/2022   BS       5     7              USD         0      0      3.274800e+10      2.522800e+10\n", "5   **********-22-000108                                     OtherAssetsCurrent  us-gaap/2022   BS       5     8              USD         0      0      2.122300e+10      1.411100e+10\n", "6   **********-22-000108                                          AssetsCurrent  us-gaap/2022   BS       5     9              USD         0      0      1.354050e+11      1.348360e+11\n", "7   **********-22-000108                         MarketableSecuritiesNoncurrent  us-gaap/2022   BS       5    11              USD         0      0      1.208050e+11      1.278770e+11\n", "8   **********-22-000108                           PropertyPlantAndEquipmentNet  us-gaap/2022   BS       5    12              USD         0      0      4.211700e+10      3.944000e+10\n", "9   **********-22-000108                                  OtherAssetsNoncurrent  us-gaap/2022   BS       5    13              USD         0      0      5.442800e+10      4.884900e+10\n", "10  **********-22-000108                                       AssetsNoncurrent  us-gaap/2022   BS       5    14              USD         0      0      2.173500e+11      2.161660e+11\n", "11  **********-22-000108                                                 Assets  us-gaap/2022   BS       5    15              USD         0      0      3.527550e+11      3.510020e+11\n", "12  **********-22-000108                                 AccountsPayableCurrent  us-gaap/2022   BS       5    18              USD         0      0      6.411500e+10      5.476300e+10\n", "13  **********-22-000108                                OtherLiabilitiesCurrent  us-gaap/2022   BS       5    19              USD         0      0      6.084500e+10      4.749300e+10\n", "14  **********-22-000108                   ContractWithCustomerLiabilityCurrent  us-gaap/2022   BS       5    20              USD         0      0      7.912000e+09      7.612000e+09\n", "15  **********-22-000108                                        CommercialPaper  us-gaap/2022   BS       5    21              USD         0      0      9.982000e+09      6.000000e+09\n", "16  **********-22-000108                                    LongTermDebtCurrent  us-gaap/2022   BS       5    22              USD         0      0      1.112800e+10      9.613000e+09\n", "17  **********-22-000108                                     LiabilitiesCurrent  us-gaap/2022   BS       5    23              USD         0      0      1.539820e+11      1.254810e+11\n", "18  **********-22-000108                                 LongTermDebtNoncurrent  us-gaap/2022   BS       5    25              USD         0      0      9.895900e+10      1.091060e+11\n", "19  **********-22-000108                             OtherLiabilitiesNoncurrent  us-gaap/2022   BS       5    26              USD         0      0      4.914200e+10      5.332500e+10\n", "20  **********-22-000108                                  LiabilitiesNoncurrent  us-gaap/2022   BS       5    27              USD         0      0      1.481010e+11      1.624310e+11\n", "21  **********-22-000108                                            Liabilities  us-gaap/2022   BS       5    28              USD         0      0      3.020830e+11      2.879120e+11\n", "22  **********-22-000108           CommonStocksIncludingAdditionalPaidInCapital  us-gaap/2022   BS       5    31              USD         0      0      6.484900e+10      5.736500e+10\n", "23  **********-22-000108                     RetainedEarningsAccumulatedDeficit  us-gaap/2022   BS       5    32              USD         0      0     -3.068000e+09      5.562000e+09\n", "24  **********-22-000108        AccumulatedOtherComprehensiveIncomeLossNetOfTax  us-gaap/2022   BS       5    33              USD         0      0     -1.110900e+10      1.630000e+08\n", "25  **********-22-000108                                     StockholdersEquity  us-gaap/2022   BS       5    34              USD         0      0      5.067200e+10      6.309000e+10\n", "26  **********-22-000108                       LiabilitiesAndStockholdersEquity  us-gaap/2022   BS       5    35              USD         0      0      3.527550e+11      3.510020e+11\n", "27  **********-22-000108                    CommonStockParOrStatedValuePerShare  us-gaap/2022   BS       6     1              USD         0      1      0.000000e+00      0.000000e+00\n", "28  **********-22-000108                            CommonStockSharesAuthorized  us-gaap/2022   BS       6     2           shares         0      1      5.040000e+10      5.040000e+10\n", "29  **********-22-000108                                CommonStockSharesIssued  us-gaap/2022   BS       6     3           shares         0      1      1.594342e+10      1.642679e+10\n", "30  **********-22-000108                           CommonStockSharesOutstanding  us-gaap/2022   BS       6     4           shares         0      1      1.594342e+10      1.642679e+10\n"]}], "source": ["from secfsdstools.e_collector.reportcollecting import SingleReportCollector\n", "from secfsdstools.e_filter.rawfiltering import ReportPeriodAndPreviousPeriodRawFilter\n", "from secfsdstools.e_presenter.presenting import StandardStatementPresenter\n", "\n", "apple_10k_2022_adsh = \"**********-22-000108\"\n", "\n", "collector: SingleReportCollector = SingleReportCollector.get_report_by_adsh(\n", "    adsh=apple_10k_2022_adsh,\n", "    stmt_filter=[\"BS\"]\n", ")\n", "rawdatabag = collector.collect()\n", "bs_df = (rawdatabag.filter(ReportPeriodAndPreviousPeriodRawFilter())\n", "                .join()\n", "                .present(StandardStatementPresenter(show_segments=False))) # switch show_segments to true if you want to see segments information\n", "print(bs_df) "]}, {"cell_type": "markdown", "id": "e39cd5f9-ace5-4ab4-ba80-01411bfcc23f", "metadata": {}, "source": [" If you compare this with the real report at https://www.sec.gov/ix?doc=/Archives/edgar/data/320193/**********22000108/aapl-20220924.htm\n", "  you will notice, that order of the tags and the values are the same."]}, {"cell_type": "markdown", "id": "ca08e156-cbea-4dad-93df-034c9d9d6f37", "metadata": {}, "source": ["Let us have a look at the income statement of a quarterly report. We use the third quarter of 2022 for apple."]}, {"cell_type": "code", "execution_count": 21, "id": "f07021d6-60cd-468e-acb4-32be98fc3c96", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-01 07:29:03,534 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n"]}, {"name": "stdout", "output_type": "stream", "text": ["                    adsh coreg                                                                                          tag       version stmt  report  line segments     uom  negating  inpth  qrtrs_3/20220630  qrtrs_3/20210630  qrtrs_1/20220630  qrtrs_1/20210630\n", "0   **********-22-000070                                                RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2021   IS       2     7              USD         0      0      3.041820e+11      2.824570e+11      8.295900e+10      8.143400e+10\n", "1   **********-22-000070                                                                         CostOfGoodsAndServicesSold  us-gaap/2021   IS       2     8              USD         0      0      1.714950e+11      1.647950e+11      4.707400e+10      4.617900e+10\n", "2   **********-22-000070                                                                                        GrossProfit  us-gaap/2021   IS       2     9              USD         0      0      1.326870e+11      1.176620e+11      3.588500e+10      3.525500e+10\n", "3   **********-22-000070                                                                      ResearchAndDevelopmentExpense  us-gaap/2021   IS       2    11              USD         0      0      1.949000e+10      1.614200e+10      6.797000e+09      5.717000e+09\n", "4   **********-22-000070                                                             SellingGeneralAndAdministrativeExpense  us-gaap/2021   IS       2    12              USD         0      0      1.865400e+10      1.635700e+10      6.012000e+09      5.412000e+09\n", "5   **********-22-000070                                                                                  OperatingExpenses  us-gaap/2021   IS       2    13              USD         0      0      3.814400e+10      3.249900e+10      1.280900e+10      1.112900e+10\n", "6   **********-22-000070                                                                                OperatingIncomeLoss  us-gaap/2021   IS       2    14              USD         0      0      9.454300e+10      8.516300e+10      2.307600e+10      2.412600e+10\n", "7   **********-22-000070                                                                          NonoperatingIncomeExpense  us-gaap/2021   IS       2    15              USD         0      0     -9.700000e+07      7.960000e+08     -1.000000e+07      2.430000e+08\n", "8   **********-22-000070        IncomeLossFromContinuingOperationsBeforeIncomeTaxesExtraordinaryItemsNoncontrollingInterest  us-gaap/2021   IS       2    16              USD         0      0      9.444600e+10      8.595900e+10      2.306600e+10      2.436900e+10\n", "9   **********-22-000070                                                                            IncomeTaxExpenseBenefit  us-gaap/2021   IS       2    17              USD         0      0      1.536400e+10      1.183000e+10      3.624000e+09      2.625000e+09\n", "10  **********-22-000070                                                                                      NetIncomeLoss  us-gaap/2021   IS       2    18              USD         0      0      7.908200e+10      7.412900e+10      1.944200e+10      2.174400e+10\n", "11  **********-22-000070                                                                              EarningsPerShareBasic  us-gaap/2021   IS       2    20              USD         0      0      4.860000e+00      4.420000e+00      1.200000e+00      1.310000e+00\n", "12  **********-22-000070                                                                            EarningsPerShareDiluted  us-gaap/2021   IS       2    21              USD         0      0      4.820000e+00      4.380000e+00      1.200000e+00      1.300000e+00\n", "13  **********-22-000070                                                      WeightedAverageNumberOfSharesOutstandingBasic  us-gaap/2021   IS       2    23           shares         0      0      1.627782e+10      1.677266e+10      1.616294e+10      1.662937e+10\n", "14  **********-22-000070                                                    WeightedAverageNumberOfDilutedSharesOutstanding  us-gaap/2021   IS       2    24           shares         0      0      1.639494e+10      1.694153e+10      1.626220e+10      1.678174e+10\n"]}], "source": ["from secfsdstools.e_collector.reportcollecting import SingleReportCollector\n", "from secfsdstools.e_filter.rawfiltering import ReportPeriodAndPreviousPeriodRawFilter\n", "from secfsdstools.e_presenter.presenting import StandardStatementPresenter\n", "\n", "apple_10q_3rd_2022_adsh = \"**********-22-000070\"\n", "\n", "collector: SingleReportCollector = SingleReportCollector.get_report_by_adsh(\n", "    adsh=apple_10q_3rd_2022_adsh,\n", "    stmt_filter=[\"IS\"]\n", ")\n", "rawdatabag = collector.collect()\n", "bs_df = (rawdatabag.filter(ReportPeriodAndPreviousPeriodRawFilter())\n", "                .join()\n", "                .present(StandardStatementPresenter()))\n", "print(bs_df) "]}, {"cell_type": "markdown", "id": "36bbf5bb-5ad9-4ee3-a5f4-fff9610f5b6b", "metadata": {}, "source": ["As you can see, every year has two columns. One starts with qtrs_3 and one with qtrs_1. These prefixes show for how many quarters the data actually is. So qtrs_1 means that the results are just for the third quarter (3 months), while qtrs_3 means, that the results are for the whole period since the beginning of the fiscal year (e.g., the combined revenue from quarter 1, 2, and 3, hence 9 months). In contrary, in the balance sheet, we only have \"qtrs_0\", since balance sheet values are values for a certain point in time and not a period."]}, {"cell_type": "markdown", "id": "de14f72c-72b4-4a07-8f9e-7de7466a2800", "metadata": {}, "source": ["## Standardize\n", "\n", "Special implementations of the `Presenter` interface are in the `standardize` package. Their goal is to provide standardized views on the Balance Sheet, Income Statement, and Cash Flow. The problem is that not all reports report all the same positions, resp. use the same tags. For instance, not all reports report InvestingActivities in the Cash Flow statement. Others use different tags to report the same thing, for instance Assets or AssetsNet are used in Balance Sheets. Further, some data is reported on different levels. E.g., a Balance Sheet can only report the data points that are under AssetsNoncurrent, but is not providing the summary as AssetsNoncurrent itself.\n", "\n", "The `standardizer` package tries to create a common view on the main financial statements, so that they actually become compareable or useable for ML training.\n", "\n", "(For details, see the `07_00_standardizer_basics`, `07_01_BS_standardizer`, `07_02_IS_standardizer`, and `07_03_CF_standardizer` notebooks.)"]}, {"cell_type": "markdown", "id": "d2596cbc-aaa4-4e7e-ae37-5bb67cb9689d", "metadata": {}, "source": ["As an example, we will standardize the reports for a few companies over the whole available time period.\n", "\n", "First, lets filter the whole dataset for the annual reports (10-K) of Microsoft, Alphabet, and Amazon (ciks 789019, 1652044, 1018724)."]}, {"cell_type": "code", "execution_count": 22, "id": "8fc847f0-471c-4bdb-9e75-a9cb14735cd9", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-01 07:29:43,879 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-01 07:29:44,141 [INFO] parallelexecution      items to process: 30\n", "2025-02-01 07:29:53,549 [INFO] parallelexecution      commited chunk: 0\n"]}], "source": ["from secfsdstools.e_collector.companycollecting import CompanyReportCollector\n", "from secfsdstools.e_filter.rawfiltering import ReportPeriodRawFilter, MainCoregRawFilter, OfficialTagsOnlyRawFilter, USDOnlyRawFilter\n", "\n", "bag = CompanyReportCollector.get_company_collector(forms_filter=['10-K'], ciks=[789019, 1652044,1018724]).collect() #Microsoft, Alphabet, Amazon\n", "filtered_bag = bag[ReportPeriodRawFilter()][MainCoregRawFilter()][OfficialTagsOnlyRawFilter()][USDOnlyRawFilter()]\n", "joined_bag = filtered_bag.join()"]}, {"cell_type": "markdown", "id": "bfe2ae66-e4db-4dec-b68f-84ca41cec9a9", "metadata": {}, "source": ["Next use the Balance Sheet Standardizer to provide a standardized view and to calculate missing values."]}, {"cell_type": "code", "execution_count": 23, "id": "8e890f65-6299-4055-bece-b7d1707342f6", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-01 07:30:16,687 [INFO] standardizing  start PRE processing ...\n", "2025-02-01 07:30:16,725 [INFO] standardizing  start MAIN processing ...\n", "2025-02-01 07:30:17,107 [INFO] standardizing  start POST processing ...\n", "2025-02-01 07:30:17,128 [INFO] standardizing  start FINALIZE ...\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>adsh</th>\n", "      <th>cik</th>\n", "      <th>name</th>\n", "      <th>form</th>\n", "      <th>fye</th>\n", "      <th>fy</th>\n", "      <th>fp</th>\n", "      <th>date</th>\n", "      <th>filed</th>\n", "      <th>coreg</th>\n", "      <th>report</th>\n", "      <th>ddate</th>\n", "      <th>qtrs</th>\n", "      <th>Assets</th>\n", "      <th>AssetsCurrent</th>\n", "      <th>Cash</th>\n", "      <th>AssetsNoncurrent</th>\n", "      <th>Liabilities</th>\n", "      <th>LiabilitiesCurrent</th>\n", "      <th>LiabilitiesNoncurrent</th>\n", "      <th>Equity</th>\n", "      <th>HolderEquity</th>\n", "      <th>RetainedEarnings</th>\n", "      <th>AdditionalPaidInCapital</th>\n", "      <th>TreasuryStockValue</th>\n", "      <th>TemporaryEquity</th>\n", "      <th>RedeemableEquity</th>\n", "      <th>LiabilitiesAndEquity</th>\n", "      <th>Assets<PERSON><PERSON>ck_error</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON>_cat</th>\n", "      <th>LiabilitiesCheck_error</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON>_cat</th>\n", "      <th>EquityCheck_error</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON>_cat</th>\n", "      <th>AssetsLiaEquCheck_error</th>\n", "      <th>AssetsLiaEquCheck_cat</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>0001193125-10-016098</td>\n", "      <td>1018724</td>\n", "      <td>AMAZON COM INC</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2009.0</td>\n", "      <td>FY</td>\n", "      <td>2009-12-31</td>\n", "      <td>20100129</td>\n", "      <td></td>\n", "      <td>4</td>\n", "      <td>20091231</td>\n", "      <td>0</td>\n", "      <td>1.381300e+10</td>\n", "      <td>9.797000e+09</td>\n", "      <td>3.444000e+09</td>\n", "      <td>4.016000e+09</td>\n", "      <td>8.556000e+09</td>\n", "      <td>7.364000e+09</td>\n", "      <td>1.192000e+09</td>\n", "      <td>5.257000e+09</td>\n", "      <td>5.257000e+09</td>\n", "      <td>1.720000e+08</td>\n", "      <td>5.736000e+09</td>\n", "      <td>-6.000000e+08</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.381300e+10</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>0001193125-10-171791</td>\n", "      <td>789019</td>\n", "      <td>MICROSOFT CORP</td>\n", "      <td>10-K</td>\n", "      <td>0630</td>\n", "      <td>2010.0</td>\n", "      <td>FY</td>\n", "      <td>2010-06-30</td>\n", "      <td>20100730</td>\n", "      <td></td>\n", "      <td>3</td>\n", "      <td>20100630</td>\n", "      <td>0</td>\n", "      <td>8.611300e+10</td>\n", "      <td>5.567600e+10</td>\n", "      <td>5.505000e+09</td>\n", "      <td>3.043700e+10</td>\n", "      <td>3.993800e+10</td>\n", "      <td>2.614700e+10</td>\n", "      <td>1.261300e+10</td>\n", "      <td>4.617500e+10</td>\n", "      <td>4.617500e+10</td>\n", "      <td>-1.668100e+10</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>8.611300e+10</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.029496</td>\n", "      <td>5.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>0001193125-11-016253</td>\n", "      <td>1018724</td>\n", "      <td>AMAZON COM INC</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2010.0</td>\n", "      <td>FY</td>\n", "      <td>2010-12-31</td>\n", "      <td>20110128</td>\n", "      <td></td>\n", "      <td>5</td>\n", "      <td>20101231</td>\n", "      <td>0</td>\n", "      <td>1.879700e+10</td>\n", "      <td>1.374700e+10</td>\n", "      <td>3.777000e+09</td>\n", "      <td>5.050000e+09</td>\n", "      <td>1.193300e+10</td>\n", "      <td>1.037200e+10</td>\n", "      <td>1.561000e+09</td>\n", "      <td>6.864000e+09</td>\n", "      <td>6.864000e+09</td>\n", "      <td>1.324000e+09</td>\n", "      <td>6.325000e+09</td>\n", "      <td>-6.000000e+08</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.879700e+10</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>0001193125-11-200680</td>\n", "      <td>789019</td>\n", "      <td>MICROSOFT CORP</td>\n", "      <td>10-K</td>\n", "      <td>0630</td>\n", "      <td>2011.0</td>\n", "      <td>FY</td>\n", "      <td>2011-06-30</td>\n", "      <td>20110728</td>\n", "      <td></td>\n", "      <td>3</td>\n", "      <td>20110630</td>\n", "      <td>0</td>\n", "      <td>1.087040e+11</td>\n", "      <td>7.491800e+10</td>\n", "      <td>9.610000e+09</td>\n", "      <td>3.378600e+10</td>\n", "      <td>5.162100e+10</td>\n", "      <td>2.877400e+10</td>\n", "      <td>2.284700e+10</td>\n", "      <td>5.708300e+10</td>\n", "      <td>5.708300e+10</td>\n", "      <td>NaN</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.087040e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>0001193125-12-032846</td>\n", "      <td>1018724</td>\n", "      <td>AMAZON COM INC</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2011.0</td>\n", "      <td>FY</td>\n", "      <td>2011-12-31</td>\n", "      <td>20120201</td>\n", "      <td></td>\n", "      <td>5</td>\n", "      <td>20111231</td>\n", "      <td>0</td>\n", "      <td>2.527800e+10</td>\n", "      <td>1.749000e+10</td>\n", "      <td>5.269000e+09</td>\n", "      <td>7.788000e+09</td>\n", "      <td>1.752100e+10</td>\n", "      <td>1.489600e+10</td>\n", "      <td>2.625000e+09</td>\n", "      <td>7.757000e+09</td>\n", "      <td>7.757000e+09</td>\n", "      <td>1.955000e+09</td>\n", "      <td>6.990000e+09</td>\n", "      <td>-8.770000e+08</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.527800e+10</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>0001193125-12-316848</td>\n", "      <td>789019</td>\n", "      <td>MICROSOFT CORP</td>\n", "      <td>10-K</td>\n", "      <td>0630</td>\n", "      <td>2012.0</td>\n", "      <td>FY</td>\n", "      <td>2012-06-30</td>\n", "      <td>20120726</td>\n", "      <td></td>\n", "      <td>3</td>\n", "      <td>20120630</td>\n", "      <td>0</td>\n", "      <td>1.212710e+11</td>\n", "      <td>8.508400e+10</td>\n", "      <td>6.938000e+09</td>\n", "      <td>3.618700e+10</td>\n", "      <td>5.490800e+10</td>\n", "      <td>3.268800e+10</td>\n", "      <td>2.222000e+10</td>\n", "      <td>6.636300e+10</td>\n", "      <td>6.636300e+10</td>\n", "      <td>NaN</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.212710e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>0001193125-13-028520</td>\n", "      <td>1018724</td>\n", "      <td>AMAZON COM INC</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2012.0</td>\n", "      <td>FY</td>\n", "      <td>2012-12-31</td>\n", "      <td>20130130</td>\n", "      <td></td>\n", "      <td>7</td>\n", "      <td>20121231</td>\n", "      <td>0</td>\n", "      <td>3.255500e+10</td>\n", "      <td>2.129600e+10</td>\n", "      <td>8.084000e+09</td>\n", "      <td>1.125900e+10</td>\n", "      <td>2.436300e+10</td>\n", "      <td>1.900200e+10</td>\n", "      <td>5.361000e+09</td>\n", "      <td>8.192000e+09</td>\n", "      <td>8.192000e+09</td>\n", "      <td>1.916000e+09</td>\n", "      <td>8.347000e+09</td>\n", "      <td>-1.837000e+09</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3.255500e+10</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>0001193125-13-310206</td>\n", "      <td>789019</td>\n", "      <td>MICROSOFT CORP</td>\n", "      <td>10-K</td>\n", "      <td>0630</td>\n", "      <td>2013.0</td>\n", "      <td>FY</td>\n", "      <td>2013-06-30</td>\n", "      <td>20130730</td>\n", "      <td></td>\n", "      <td>5</td>\n", "      <td>20130630</td>\n", "      <td>0</td>\n", "      <td>1.424310e+11</td>\n", "      <td>1.014660e+11</td>\n", "      <td>3.804000e+09</td>\n", "      <td>4.096500e+10</td>\n", "      <td>6.348700e+10</td>\n", "      <td>3.741700e+10</td>\n", "      <td>2.607000e+10</td>\n", "      <td>7.894400e+10</td>\n", "      <td>7.894400e+10</td>\n", "      <td>9.895000e+09</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.424310e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>0001018724-14-000006</td>\n", "      <td>1018724</td>\n", "      <td>AMAZON COM INC</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2013.0</td>\n", "      <td>FY</td>\n", "      <td>2013-12-31</td>\n", "      <td>20140131</td>\n", "      <td></td>\n", "      <td>7</td>\n", "      <td>20131231</td>\n", "      <td>0</td>\n", "      <td>4.015900e+10</td>\n", "      <td>2.462500e+10</td>\n", "      <td>8.658000e+09</td>\n", "      <td>1.553400e+10</td>\n", "      <td>3.041300e+10</td>\n", "      <td>2.298000e+10</td>\n", "      <td>7.433000e+09</td>\n", "      <td>9.746000e+09</td>\n", "      <td>9.746000e+09</td>\n", "      <td>2.190000e+09</td>\n", "      <td>9.573000e+09</td>\n", "      <td>-1.837000e+09</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>4.015900e+10</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>0001193125-14-289961</td>\n", "      <td>789019</td>\n", "      <td>MICROSOFT CORP</td>\n", "      <td>10-K</td>\n", "      <td>0630</td>\n", "      <td>2014.0</td>\n", "      <td>FY</td>\n", "      <td>2014-06-30</td>\n", "      <td>20140731</td>\n", "      <td></td>\n", "      <td>5</td>\n", "      <td>20140630</td>\n", "      <td>0</td>\n", "      <td>1.723840e+11</td>\n", "      <td>1.142460e+11</td>\n", "      <td>8.669000e+09</td>\n", "      <td>5.813800e+10</td>\n", "      <td>8.260000e+10</td>\n", "      <td>4.562500e+10</td>\n", "      <td>3.697500e+10</td>\n", "      <td>8.978400e+10</td>\n", "      <td>8.978400e+10</td>\n", "      <td>1.771000e+10</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.723840e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>0001018724-15-000006</td>\n", "      <td>1018724</td>\n", "      <td>AMAZON COM INC</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2014.0</td>\n", "      <td>FY</td>\n", "      <td>2014-12-31</td>\n", "      <td>20150130</td>\n", "      <td></td>\n", "      <td>7</td>\n", "      <td>20141231</td>\n", "      <td>0</td>\n", "      <td>5.450500e+10</td>\n", "      <td>3.132700e+10</td>\n", "      <td>1.455700e+10</td>\n", "      <td>2.317800e+10</td>\n", "      <td>4.376400e+10</td>\n", "      <td>2.808900e+10</td>\n", "      <td>1.567500e+10</td>\n", "      <td>1.074100e+10</td>\n", "      <td>1.074100e+10</td>\n", "      <td>1.949000e+09</td>\n", "      <td>1.113500e+10</td>\n", "      <td>-1.837000e+09</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>5.450500e+10</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>0001193125-15-272806</td>\n", "      <td>789019</td>\n", "      <td>MICROSOFT CORP</td>\n", "      <td>10-K</td>\n", "      <td>0630</td>\n", "      <td>2015.0</td>\n", "      <td>FY</td>\n", "      <td>2015-06-30</td>\n", "      <td>20150731</td>\n", "      <td></td>\n", "      <td>5</td>\n", "      <td>20150630</td>\n", "      <td>0</td>\n", "      <td>1.762230e+11</td>\n", "      <td>1.247120e+11</td>\n", "      <td>5.595000e+09</td>\n", "      <td>5.151100e+10</td>\n", "      <td>9.614000e+10</td>\n", "      <td>4.985800e+10</td>\n", "      <td>4.628200e+10</td>\n", "      <td>8.008300e+10</td>\n", "      <td>8.008300e+10</td>\n", "      <td>9.096000e+09</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.762230e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>0001652044-16-000012</td>\n", "      <td>1652044</td>\n", "      <td>ALPHABET INC.</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2015.0</td>\n", "      <td>FY</td>\n", "      <td>2015-12-31</td>\n", "      <td>20160211</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20151231</td>\n", "      <td>0</td>\n", "      <td>1.474610e+11</td>\n", "      <td>9.011400e+10</td>\n", "      <td>1.654900e+10</td>\n", "      <td>5.734700e+10</td>\n", "      <td>2.713000e+10</td>\n", "      <td>1.931000e+10</td>\n", "      <td>7.669000e+09</td>\n", "      <td>1.203310e+11</td>\n", "      <td>1.203310e+11</td>\n", "      <td>8.922300e+10</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.474610e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.005566</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>0001018724-16-000172</td>\n", "      <td>1018724</td>\n", "      <td>AMAZON COM INC</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2015.0</td>\n", "      <td>FY</td>\n", "      <td>2015-12-31</td>\n", "      <td>20160129</td>\n", "      <td></td>\n", "      <td>7</td>\n", "      <td>20151231</td>\n", "      <td>0</td>\n", "      <td>6.544400e+10</td>\n", "      <td>3.647400e+10</td>\n", "      <td>1.589000e+10</td>\n", "      <td>2.897000e+10</td>\n", "      <td>5.206000e+10</td>\n", "      <td>3.389900e+10</td>\n", "      <td>1.816100e+10</td>\n", "      <td>1.338400e+10</td>\n", "      <td>1.338400e+10</td>\n", "      <td>2.545000e+09</td>\n", "      <td>1.339400e+10</td>\n", "      <td>-1.837000e+09</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>6.544400e+10</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>0001193125-16-662209</td>\n", "      <td>789019</td>\n", "      <td>MICROSOFT CORP</td>\n", "      <td>10-K</td>\n", "      <td>0630</td>\n", "      <td>2016.0</td>\n", "      <td>FY</td>\n", "      <td>2016-06-30</td>\n", "      <td>20160728</td>\n", "      <td></td>\n", "      <td>5</td>\n", "      <td>20160630</td>\n", "      <td>0</td>\n", "      <td>1.936940e+11</td>\n", "      <td>1.396600e+11</td>\n", "      <td>6.510000e+09</td>\n", "      <td>5.403400e+10</td>\n", "      <td>1.216970e+11</td>\n", "      <td>5.935700e+10</td>\n", "      <td>6.234000e+10</td>\n", "      <td>7.199700e+10</td>\n", "      <td>7.199700e+10</td>\n", "      <td>2.282000e+09</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.936940e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>0001018724-17-000011</td>\n", "      <td>1018724</td>\n", "      <td>AMAZON COM INC</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2016.0</td>\n", "      <td>FY</td>\n", "      <td>2016-12-31</td>\n", "      <td>20170210</td>\n", "      <td></td>\n", "      <td>6</td>\n", "      <td>20161231</td>\n", "      <td>0</td>\n", "      <td>8.340200e+10</td>\n", "      <td>4.578100e+10</td>\n", "      <td>1.933400e+10</td>\n", "      <td>3.762100e+10</td>\n", "      <td>6.411700e+10</td>\n", "      <td>4.381600e+10</td>\n", "      <td>2.030100e+10</td>\n", "      <td>1.928500e+10</td>\n", "      <td>1.928500e+10</td>\n", "      <td>4.916000e+09</td>\n", "      <td>1.718600e+10</td>\n", "      <td>-1.837000e+09</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>8.340200e+10</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>0001652044-17-000008</td>\n", "      <td>1652044</td>\n", "      <td>ALPHABET INC.</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2016.0</td>\n", "      <td>FY</td>\n", "      <td>2016-12-31</td>\n", "      <td>20170203</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20161231</td>\n", "      <td>0</td>\n", "      <td>1.674970e+11</td>\n", "      <td>1.054080e+11</td>\n", "      <td>1.291800e+10</td>\n", "      <td>6.208900e+10</td>\n", "      <td>2.846100e+10</td>\n", "      <td>1.675600e+10</td>\n", "      <td>1.170500e+10</td>\n", "      <td>1.390360e+11</td>\n", "      <td>1.390360e+11</td>\n", "      <td>1.051310e+11</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.674970e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>0001564590-17-014900</td>\n", "      <td>789019</td>\n", "      <td>MICROSOFT CORP</td>\n", "      <td>10-K</td>\n", "      <td>0630</td>\n", "      <td>2017.0</td>\n", "      <td>FY</td>\n", "      <td>2017-06-30</td>\n", "      <td>20170802</td>\n", "      <td></td>\n", "      <td>5</td>\n", "      <td>20170630</td>\n", "      <td>0</td>\n", "      <td>2.410860e+11</td>\n", "      <td>1.598510e+11</td>\n", "      <td>7.663000e+09</td>\n", "      <td>8.123500e+10</td>\n", "      <td>1.686920e+11</td>\n", "      <td>6.452700e+10</td>\n", "      <td>1.041650e+11</td>\n", "      <td>7.239400e+10</td>\n", "      <td>7.239400e+10</td>\n", "      <td>2.648000e+09</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.410860e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>0001018724-18-000005</td>\n", "      <td>1018724</td>\n", "      <td>AMAZON COM INC</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2017.0</td>\n", "      <td>FY</td>\n", "      <td>2017-12-31</td>\n", "      <td>20180202</td>\n", "      <td></td>\n", "      <td>6</td>\n", "      <td>20171231</td>\n", "      <td>0</td>\n", "      <td>1.313100e+11</td>\n", "      <td>6.019700e+10</td>\n", "      <td>2.052200e+10</td>\n", "      <td>7.111300e+10</td>\n", "      <td>1.036010e+11</td>\n", "      <td>5.788300e+10</td>\n", "      <td>4.571800e+10</td>\n", "      <td>2.770900e+10</td>\n", "      <td>2.770900e+10</td>\n", "      <td>8.636000e+09</td>\n", "      <td>2.138900e+10</td>\n", "      <td>-1.837000e+09</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.313100e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>0001652044-18-000007</td>\n", "      <td>1652044</td>\n", "      <td>ALPHABET INC.</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2017.0</td>\n", "      <td>FY</td>\n", "      <td>2017-12-31</td>\n", "      <td>20180206</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20171231</td>\n", "      <td>0</td>\n", "      <td>1.972950e+11</td>\n", "      <td>1.243080e+11</td>\n", "      <td>1.071500e+10</td>\n", "      <td>7.298700e+10</td>\n", "      <td>4.479300e+10</td>\n", "      <td>2.418300e+10</td>\n", "      <td>2.061000e+10</td>\n", "      <td>1.525020e+11</td>\n", "      <td>1.525020e+11</td>\n", "      <td>1.132470e+11</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.972950e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>0001564590-18-019062</td>\n", "      <td>789019</td>\n", "      <td>MICROSOFT CORP</td>\n", "      <td>10-K</td>\n", "      <td>0630</td>\n", "      <td>2018.0</td>\n", "      <td>FY</td>\n", "      <td>2018-06-30</td>\n", "      <td>20180803</td>\n", "      <td></td>\n", "      <td>4</td>\n", "      <td>20180630</td>\n", "      <td>0</td>\n", "      <td>2.588480e+11</td>\n", "      <td>1.696620e+11</td>\n", "      <td>1.194600e+10</td>\n", "      <td>8.918600e+10</td>\n", "      <td>1.761300e+11</td>\n", "      <td>5.848800e+10</td>\n", "      <td>1.176420e+11</td>\n", "      <td>8.271800e+10</td>\n", "      <td>8.271800e+10</td>\n", "      <td>1.368200e+10</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.588480e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>0001652044-19-000004</td>\n", "      <td>1652044</td>\n", "      <td>ALPHABET INC.</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2018.0</td>\n", "      <td>FY</td>\n", "      <td>2018-12-31</td>\n", "      <td>20190205</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20181231</td>\n", "      <td>0</td>\n", "      <td>2.327920e+11</td>\n", "      <td>1.356760e+11</td>\n", "      <td>1.670100e+10</td>\n", "      <td>9.711600e+10</td>\n", "      <td>5.516400e+10</td>\n", "      <td>3.462000e+10</td>\n", "      <td>2.054400e+10</td>\n", "      <td>1.776280e+11</td>\n", "      <td>1.776280e+11</td>\n", "      <td>1.348850e+11</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.327920e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>0001018724-19-000004</td>\n", "      <td>1018724</td>\n", "      <td>AMAZON COM INC</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2018.0</td>\n", "      <td>FY</td>\n", "      <td>2018-12-31</td>\n", "      <td>20190201</td>\n", "      <td></td>\n", "      <td>6</td>\n", "      <td>20181231</td>\n", "      <td>0</td>\n", "      <td>1.626480e+11</td>\n", "      <td>7.510100e+10</td>\n", "      <td>3.175000e+10</td>\n", "      <td>8.754700e+10</td>\n", "      <td>1.190990e+11</td>\n", "      <td>6.839100e+10</td>\n", "      <td>5.070800e+10</td>\n", "      <td>4.354900e+10</td>\n", "      <td>4.354900e+10</td>\n", "      <td>1.962500e+10</td>\n", "      <td>2.679100e+10</td>\n", "      <td>-1.837000e+09</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.626480e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>0001564590-19-027952</td>\n", "      <td>789019</td>\n", "      <td>MICROSOFT CORP</td>\n", "      <td>10-K</td>\n", "      <td>0630</td>\n", "      <td>2019.0</td>\n", "      <td>FY</td>\n", "      <td>2019-06-30</td>\n", "      <td>20190801</td>\n", "      <td></td>\n", "      <td>4</td>\n", "      <td>20190630</td>\n", "      <td>0</td>\n", "      <td>2.865560e+11</td>\n", "      <td>1.755520e+11</td>\n", "      <td>1.135600e+10</td>\n", "      <td>1.110040e+11</td>\n", "      <td>1.842260e+11</td>\n", "      <td>6.942000e+10</td>\n", "      <td>1.148060e+11</td>\n", "      <td>1.023300e+11</td>\n", "      <td>1.023300e+11</td>\n", "      <td>2.415000e+10</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.865560e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>0001652044-20-000008</td>\n", "      <td>1652044</td>\n", "      <td>ALPHABET INC.</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2019.0</td>\n", "      <td>FY</td>\n", "      <td>2019-12-31</td>\n", "      <td>20200204</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20191231</td>\n", "      <td>0</td>\n", "      <td>2.759090e+11</td>\n", "      <td>1.525780e+11</td>\n", "      <td>1.849800e+10</td>\n", "      <td>1.233310e+11</td>\n", "      <td>7.446700e+10</td>\n", "      <td>4.522100e+10</td>\n", "      <td>2.924600e+10</td>\n", "      <td>2.014420e+11</td>\n", "      <td>2.014420e+11</td>\n", "      <td>1.521220e+11</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.759090e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>0001018724-20-000004</td>\n", "      <td>1018724</td>\n", "      <td>AMAZON COM INC</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2019.0</td>\n", "      <td>FY</td>\n", "      <td>2019-12-31</td>\n", "      <td>20200131</td>\n", "      <td></td>\n", "      <td>6</td>\n", "      <td>20191231</td>\n", "      <td>0</td>\n", "      <td>2.252480e+11</td>\n", "      <td>9.633400e+10</td>\n", "      <td>3.609200e+10</td>\n", "      <td>1.289140e+11</td>\n", "      <td>1.631880e+11</td>\n", "      <td>8.781200e+10</td>\n", "      <td>3.558500e+10</td>\n", "      <td>6.206000e+10</td>\n", "      <td>6.206000e+10</td>\n", "      <td>3.122000e+10</td>\n", "      <td>3.365800e+10</td>\n", "      <td>-1.837000e+09</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.252480e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.243835</td>\n", "      <td>100.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>0001564590-20-034944</td>\n", "      <td>789019</td>\n", "      <td>MICROSOFT CORP</td>\n", "      <td>10-K</td>\n", "      <td>0630</td>\n", "      <td>2020.0</td>\n", "      <td>FY</td>\n", "      <td>2020-06-30</td>\n", "      <td>20200731</td>\n", "      <td></td>\n", "      <td>4</td>\n", "      <td>20200630</td>\n", "      <td>0</td>\n", "      <td>3.013110e+11</td>\n", "      <td>1.819150e+11</td>\n", "      <td>1.357600e+10</td>\n", "      <td>1.193960e+11</td>\n", "      <td>1.830070e+11</td>\n", "      <td>7.231000e+10</td>\n", "      <td>1.106970e+11</td>\n", "      <td>1.183040e+11</td>\n", "      <td>1.183040e+11</td>\n", "      <td>3.456600e+10</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3.013110e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>0001652044-21-000010</td>\n", "      <td>1652044</td>\n", "      <td>ALPHABET INC.</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2020.0</td>\n", "      <td>FY</td>\n", "      <td>2020-12-31</td>\n", "      <td>20210203</td>\n", "      <td></td>\n", "      <td>2</td>\n", "      <td>20201231</td>\n", "      <td>0</td>\n", "      <td>3.196160e+11</td>\n", "      <td>1.742960e+11</td>\n", "      <td>2.646500e+10</td>\n", "      <td>1.453200e+11</td>\n", "      <td>9.707200e+10</td>\n", "      <td>5.683400e+10</td>\n", "      <td>4.023800e+10</td>\n", "      <td>2.225440e+11</td>\n", "      <td>2.225440e+11</td>\n", "      <td>1.634010e+11</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3.196160e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>0001018724-21-000004</td>\n", "      <td>1018724</td>\n", "      <td>AMAZON COM INC</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2020.0</td>\n", "      <td>FY</td>\n", "      <td>2020-12-31</td>\n", "      <td>20210203</td>\n", "      <td></td>\n", "      <td>6</td>\n", "      <td>20201231</td>\n", "      <td>0</td>\n", "      <td>3.211950e+11</td>\n", "      <td>1.327330e+11</td>\n", "      <td>4.212200e+10</td>\n", "      <td>1.884620e+11</td>\n", "      <td>2.277910e+11</td>\n", "      <td>1.263850e+11</td>\n", "      <td>4.883300e+10</td>\n", "      <td>9.340400e+10</td>\n", "      <td>9.340400e+10</td>\n", "      <td>5.255100e+10</td>\n", "      <td>4.286500e+10</td>\n", "      <td>-1.837000e+09</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3.211950e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.230795</td>\n", "      <td>100.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>0001564590-21-039151</td>\n", "      <td>789019</td>\n", "      <td>MICROSOFT CORP</td>\n", "      <td>10-K</td>\n", "      <td>0630</td>\n", "      <td>2021.0</td>\n", "      <td>FY</td>\n", "      <td>2021-06-30</td>\n", "      <td>20210729</td>\n", "      <td></td>\n", "      <td>4</td>\n", "      <td>20210630</td>\n", "      <td>0</td>\n", "      <td>3.337790e+11</td>\n", "      <td>1.844060e+11</td>\n", "      <td>1.422400e+10</td>\n", "      <td>1.493730e+11</td>\n", "      <td>1.917910e+11</td>\n", "      <td>8.865700e+10</td>\n", "      <td>1.031340e+11</td>\n", "      <td>1.419880e+11</td>\n", "      <td>1.419880e+11</td>\n", "      <td>5.705500e+10</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3.337790e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>0001652044-22-000019</td>\n", "      <td>1652044</td>\n", "      <td>ALPHABET INC.</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2021.0</td>\n", "      <td>FY</td>\n", "      <td>2021-12-31</td>\n", "      <td>20220202</td>\n", "      <td></td>\n", "      <td>3</td>\n", "      <td>20211231</td>\n", "      <td>0</td>\n", "      <td>3.592680e+11</td>\n", "      <td>1.881430e+11</td>\n", "      <td>2.094500e+10</td>\n", "      <td>1.711250e+11</td>\n", "      <td>1.076330e+11</td>\n", "      <td>6.425400e+10</td>\n", "      <td>4.337900e+10</td>\n", "      <td>2.516350e+11</td>\n", "      <td>2.516350e+11</td>\n", "      <td>1.914840e+11</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3.592680e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>0001018724-22-000005</td>\n", "      <td>1018724</td>\n", "      <td>AMAZON COM INC</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2021.0</td>\n", "      <td>FY</td>\n", "      <td>2021-12-31</td>\n", "      <td>20220204</td>\n", "      <td></td>\n", "      <td>7</td>\n", "      <td>20211231</td>\n", "      <td>0</td>\n", "      <td>4.205490e+11</td>\n", "      <td>1.615800e+11</td>\n", "      <td>3.622000e+10</td>\n", "      <td>2.589690e+11</td>\n", "      <td>2.823040e+11</td>\n", "      <td>1.422660e+11</td>\n", "      <td>7.238700e+10</td>\n", "      <td>1.382450e+11</td>\n", "      <td>1.382450e+11</td>\n", "      <td>8.591500e+10</td>\n", "      <td>5.553800e+10</td>\n", "      <td>-1.837000e+09</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>4.205490e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.239639</td>\n", "      <td>100.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>0001564590-22-026876</td>\n", "      <td>789019</td>\n", "      <td>MICROSOFT CORP</td>\n", "      <td>10-K</td>\n", "      <td>0630</td>\n", "      <td>2022.0</td>\n", "      <td>FY</td>\n", "      <td>2022-06-30</td>\n", "      <td>20220728</td>\n", "      <td></td>\n", "      <td>4</td>\n", "      <td>20220630</td>\n", "      <td>0</td>\n", "      <td>3.648400e+11</td>\n", "      <td>1.696840e+11</td>\n", "      <td>1.393100e+10</td>\n", "      <td>1.951560e+11</td>\n", "      <td>1.982980e+11</td>\n", "      <td>9.508200e+10</td>\n", "      <td>1.032160e+11</td>\n", "      <td>1.665420e+11</td>\n", "      <td>1.665420e+11</td>\n", "      <td>8.428100e+10</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3.648400e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>0001652044-23-000016</td>\n", "      <td>1652044</td>\n", "      <td>ALPHABET INC.</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2022.0</td>\n", "      <td>FY</td>\n", "      <td>2022-12-31</td>\n", "      <td>20230203</td>\n", "      <td></td>\n", "      <td>3</td>\n", "      <td>20221231</td>\n", "      <td>0</td>\n", "      <td>3.652640e+11</td>\n", "      <td>1.647950e+11</td>\n", "      <td>2.187900e+10</td>\n", "      <td>2.004690e+11</td>\n", "      <td>1.091200e+11</td>\n", "      <td>6.930000e+10</td>\n", "      <td>3.982000e+10</td>\n", "      <td>2.561440e+11</td>\n", "      <td>2.561440e+11</td>\n", "      <td>1.955630e+11</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3.652640e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0001018724-23-000004</td>\n", "      <td>1018724</td>\n", "      <td>AMAZON COM INC</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2022.0</td>\n", "      <td>FY</td>\n", "      <td>2022-12-31</td>\n", "      <td>20230203</td>\n", "      <td></td>\n", "      <td>7</td>\n", "      <td>20221231</td>\n", "      <td>0</td>\n", "      <td>4.626750e+11</td>\n", "      <td>1.467910e+11</td>\n", "      <td>5.388800e+10</td>\n", "      <td>3.158840e+11</td>\n", "      <td>3.166320e+11</td>\n", "      <td>1.553930e+11</td>\n", "      <td>8.827100e+10</td>\n", "      <td>1.460430e+11</td>\n", "      <td>1.460430e+11</td>\n", "      <td>8.319300e+10</td>\n", "      <td>7.506600e+10</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>4.626750e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.230450</td>\n", "      <td>100.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0000950170-23-035122</td>\n", "      <td>789019</td>\n", "      <td>MICROSOFT CORP</td>\n", "      <td>10-K</td>\n", "      <td>0630</td>\n", "      <td>2023.0</td>\n", "      <td>FY</td>\n", "      <td>2023-06-30</td>\n", "      <td>20230727</td>\n", "      <td></td>\n", "      <td>4</td>\n", "      <td>20230630</td>\n", "      <td>0</td>\n", "      <td>4.119760e+11</td>\n", "      <td>1.842570e+11</td>\n", "      <td>3.470400e+10</td>\n", "      <td>2.277190e+11</td>\n", "      <td>2.057530e+11</td>\n", "      <td>1.041490e+11</td>\n", "      <td>1.016040e+11</td>\n", "      <td>2.062230e+11</td>\n", "      <td>2.062230e+11</td>\n", "      <td>1.188480e+11</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>4.119760e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0001652044-24-000022</td>\n", "      <td>1652044</td>\n", "      <td>ALPHABET INC.</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2023.0</td>\n", "      <td>FY</td>\n", "      <td>2023-12-31</td>\n", "      <td>20240131</td>\n", "      <td></td>\n", "      <td>3</td>\n", "      <td>20231231</td>\n", "      <td>0</td>\n", "      <td>4.023920e+11</td>\n", "      <td>1.715300e+11</td>\n", "      <td>2.404800e+10</td>\n", "      <td>2.308620e+11</td>\n", "      <td>1.190130e+11</td>\n", "      <td>8.181400e+10</td>\n", "      <td>3.719900e+10</td>\n", "      <td>2.833790e+11</td>\n", "      <td>2.833790e+11</td>\n", "      <td>2.112470e+11</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>4.023920e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0001018724-24-000008</td>\n", "      <td>1018724</td>\n", "      <td>AMAZON COM INC</td>\n", "      <td>10-K</td>\n", "      <td>1231</td>\n", "      <td>2023.0</td>\n", "      <td>FY</td>\n", "      <td>2023-12-31</td>\n", "      <td>20240202</td>\n", "      <td></td>\n", "      <td>7</td>\n", "      <td>20231231</td>\n", "      <td>0</td>\n", "      <td>5.278540e+11</td>\n", "      <td>1.723510e+11</td>\n", "      <td>7.338700e+10</td>\n", "      <td>3.555030e+11</td>\n", "      <td>3.259790e+11</td>\n", "      <td>1.649170e+11</td>\n", "      <td>8.376500e+10</td>\n", "      <td>2.018750e+11</td>\n", "      <td>2.018750e+11</td>\n", "      <td>1.136180e+11</td>\n", "      <td>9.902500e+10</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>5.278540e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.237123</td>\n", "      <td>100.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0000950170-24-087843</td>\n", "      <td>789019</td>\n", "      <td>MICROSOFT CORP</td>\n", "      <td>10-K</td>\n", "      <td>0630</td>\n", "      <td>2024.0</td>\n", "      <td>FY</td>\n", "      <td>2024-06-30</td>\n", "      <td>20240730</td>\n", "      <td></td>\n", "      <td>4</td>\n", "      <td>20240630</td>\n", "      <td>0</td>\n", "      <td>5.121630e+11</td>\n", "      <td>1.597340e+11</td>\n", "      <td>1.831500e+10</td>\n", "      <td>3.524290e+11</td>\n", "      <td>2.436860e+11</td>\n", "      <td>1.252860e+11</td>\n", "      <td>1.184000e+11</td>\n", "      <td>2.684770e+11</td>\n", "      <td>2.684770e+11</td>\n", "      <td>1.731440e+11</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>5.121630e+11</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                    adsh      cik            name  form   fye      fy  fp       date     filed coreg  report     ddate  qtrs        Assets  AssetsCurrent          Cash  AssetsNoncurrent   Liabilities  LiabilitiesCurrent  LiabilitiesNoncurrent        Equity  HolderEquity  RetainedEarnings  AdditionalPaidInCapital  TreasuryStockValue  TemporaryEquity  RedeemableEquity  LiabilitiesAndEquity  AssetsCheck_error  AssetsCheck_cat  LiabilitiesCheck_error  LiabilitiesCheck_cat  EquityCheck_error  EquityCheck_cat  AssetsLiaEquCheck_error  AssetsLiaEquCheck_cat\n", "38  0001193125-10-016098  1018724  AMAZON COM INC  10-K  1231  2009.0  FY 2009-12-31  20100129             4  20091231     0  1.381300e+10   9.797000e+09  3.444000e+09      4.016000e+09  8.556000e+09        7.364000e+09           1.192000e+09  5.257000e+09  5.257000e+09      1.720000e+08             5.736000e+09       -6.000000e+08              0.0               0.0          1.381300e+10                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "37  0001193125-10-171791   789019  MICROSOFT CORP  10-K  0630  2010.0  FY 2010-06-30  20100730             3  20100630     0  8.611300e+10   5.567600e+10  5.505000e+09      3.043700e+10  3.993800e+10        2.614700e+10           1.261300e+10  4.617500e+10  4.617500e+10     -1.668100e+10             0.000000e+00        0.000000e+00              0.0               0.0          8.611300e+10                0.0              0.0                0.029496                   5.0                0.0              0.0                      0.0                    0.0\n", "36  0001193125-11-016253  1018724  AMAZON COM INC  10-K  1231  2010.0  FY 2010-12-31  20110128             5  20101231     0  1.879700e+10   1.374700e+10  3.777000e+09      5.050000e+09  1.193300e+10        1.037200e+10           1.561000e+09  6.864000e+09  6.864000e+09      1.324000e+09             6.325000e+09       -6.000000e+08              0.0               0.0          1.879700e+10                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "35  0001193125-11-200680   789019  MICROSOFT CORP  10-K  0630  2011.0  FY 2011-06-30  20110728             3  20110630     0  1.087040e+11   7.491800e+10  9.610000e+09      3.378600e+10  5.162100e+10        2.877400e+10           2.284700e+10  5.708300e+10  5.708300e+10               NaN             0.000000e+00        0.000000e+00              0.0               0.0          1.087040e+11                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "34  0001193125-12-032846  1018724  AMAZON COM INC  10-K  1231  2011.0  FY 2011-12-31  20120201             5  20111231     0  2.527800e+10   1.749000e+10  5.269000e+09      7.788000e+09  1.752100e+10        1.489600e+10           2.625000e+09  7.757000e+09  7.757000e+09      1.955000e+09             6.990000e+09       -8.770000e+08              0.0               0.0          2.527800e+10                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "33  0001193125-12-316848   789019  MICROSOFT CORP  10-K  0630  2012.0  FY 2012-06-30  20120726             3  20120630     0  1.212710e+11   8.508400e+10  6.938000e+09      3.618700e+10  5.490800e+10        3.268800e+10           2.222000e+10  6.636300e+10  6.636300e+10               NaN             0.000000e+00        0.000000e+00              0.0               0.0          1.212710e+11                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "32  0001193125-13-028520  1018724  AMAZON COM INC  10-K  1231  2012.0  FY 2012-12-31  20130130             7  20121231     0  3.255500e+10   2.129600e+10  8.084000e+09      1.125900e+10  2.436300e+10        1.900200e+10           5.361000e+09  8.192000e+09  8.192000e+09      1.916000e+09             8.347000e+09       -1.837000e+09              0.0               0.0          3.255500e+10                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "31  0001193125-13-310206   789019  MICROSOFT CORP  10-K  0630  2013.0  FY 2013-06-30  20130730             5  20130630     0  1.424310e+11   1.014660e+11  3.804000e+09      4.096500e+10  6.348700e+10        3.741700e+10           2.607000e+10  7.894400e+10  7.894400e+10      9.895000e+09             0.000000e+00        0.000000e+00              0.0               0.0          1.424310e+11                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "30  0001018724-14-000006  1018724  AMAZON COM INC  10-K  1231  2013.0  FY 2013-12-31  20140131             7  20131231     0  4.015900e+10   2.462500e+10  8.658000e+09      1.553400e+10  3.041300e+10        2.298000e+10           7.433000e+09  9.746000e+09  9.746000e+09      2.190000e+09             9.573000e+09       -1.837000e+09              0.0               0.0          4.015900e+10                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "29  0001193125-14-289961   789019  MICROSOFT CORP  10-K  0630  2014.0  FY 2014-06-30  20140731             5  20140630     0  1.723840e+11   1.142460e+11  8.669000e+09      5.813800e+10  8.260000e+10        4.562500e+10           3.697500e+10  8.978400e+10  8.978400e+10      1.771000e+10             0.000000e+00        0.000000e+00              0.0               0.0          1.723840e+11                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "28  0001018724-15-000006  1018724  AMAZON COM INC  10-K  1231  2014.0  FY 2014-12-31  20150130             7  20141231     0  5.450500e+10   3.132700e+10  1.455700e+10      2.317800e+10  4.376400e+10        2.808900e+10           1.567500e+10  1.074100e+10  1.074100e+10      1.949000e+09             1.113500e+10       -1.837000e+09              0.0               0.0          5.450500e+10                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "27  0001193125-15-272806   789019  MICROSOFT CORP  10-K  0630  2015.0  FY 2015-06-30  20150731             5  20150630     0  1.762230e+11   1.247120e+11  5.595000e+09      5.151100e+10  9.614000e+10        4.985800e+10           4.628200e+10  8.008300e+10  8.008300e+10      9.096000e+09             0.000000e+00        0.000000e+00              0.0               0.0          1.762230e+11                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "26  0001652044-16-000012  1652044   ALPHABET INC.  10-K  1231  2015.0  FY 2015-12-31  20160211             2  20151231     0  1.474610e+11   9.011400e+10  1.654900e+10      5.734700e+10  2.713000e+10        1.931000e+10           7.669000e+09  1.203310e+11  1.203310e+11      8.922300e+10             0.000000e+00        0.000000e+00              0.0               0.0          1.474610e+11                0.0              0.0                0.005566                   1.0                0.0              0.0                      0.0                    0.0\n", "25  0001018724-16-000172  1018724  AMAZON COM INC  10-K  1231  2015.0  FY 2015-12-31  20160129             7  20151231     0  6.544400e+10   3.647400e+10  1.589000e+10      2.897000e+10  5.206000e+10        3.389900e+10           1.816100e+10  1.338400e+10  1.338400e+10      2.545000e+09             1.339400e+10       -1.837000e+09              0.0               0.0          6.544400e+10                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "24  0001193125-16-662209   789019  MICROSOFT CORP  10-K  0630  2016.0  FY 2016-06-30  20160728             5  20160630     0  1.936940e+11   1.396600e+11  6.510000e+09      5.403400e+10  1.216970e+11        5.935700e+10           6.234000e+10  7.199700e+10  7.199700e+10      2.282000e+09             0.000000e+00        0.000000e+00              0.0               0.0          1.936940e+11                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "23  0001018724-17-000011  1018724  AMAZON COM INC  10-K  1231  2016.0  FY 2016-12-31  20170210             6  20161231     0  8.340200e+10   4.578100e+10  1.933400e+10      3.762100e+10  6.411700e+10        4.381600e+10           2.030100e+10  1.928500e+10  1.928500e+10      4.916000e+09             1.718600e+10       -1.837000e+09              0.0               0.0          8.340200e+10                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "22  0001652044-17-000008  1652044   ALPHABET INC.  10-K  1231  2016.0  FY 2016-12-31  20170203             2  20161231     0  1.674970e+11   1.054080e+11  1.291800e+10      6.208900e+10  2.846100e+10        1.675600e+10           1.170500e+10  1.390360e+11  1.390360e+11      1.051310e+11             0.000000e+00        0.000000e+00              0.0               0.0          1.674970e+11                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "21  0001564590-17-014900   789019  MICROSOFT CORP  10-K  0630  2017.0  FY 2017-06-30  20170802             5  20170630     0  2.410860e+11   1.598510e+11  7.663000e+09      8.123500e+10  1.686920e+11        6.452700e+10           1.041650e+11  7.239400e+10  7.239400e+10      2.648000e+09             0.000000e+00        0.000000e+00              0.0               0.0          2.410860e+11                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "20  0001018724-18-000005  1018724  AMAZON COM INC  10-K  1231  2017.0  FY 2017-12-31  20180202             6  20171231     0  1.313100e+11   6.019700e+10  2.052200e+10      7.111300e+10  1.036010e+11        5.788300e+10           4.571800e+10  2.770900e+10  2.770900e+10      8.636000e+09             2.138900e+10       -1.837000e+09              0.0               0.0          1.313100e+11                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "19  0001652044-18-000007  1652044   ALPHABET INC.  10-K  1231  2017.0  FY 2017-12-31  20180206             2  20171231     0  1.972950e+11   1.243080e+11  1.071500e+10      7.298700e+10  4.479300e+10        2.418300e+10           2.061000e+10  1.525020e+11  1.525020e+11      1.132470e+11             0.000000e+00        0.000000e+00              0.0               0.0          1.972950e+11                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "18  0001564590-18-019062   789019  MICROSOFT CORP  10-K  0630  2018.0  FY 2018-06-30  20180803             4  20180630     0  2.588480e+11   1.696620e+11  1.194600e+10      8.918600e+10  1.761300e+11        5.848800e+10           1.176420e+11  8.271800e+10  8.271800e+10      1.368200e+10             0.000000e+00        0.000000e+00              0.0               0.0          2.588480e+11                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "17  0001652044-19-000004  1652044   ALPHABET INC.  10-K  1231  2018.0  FY 2018-12-31  20190205             2  20181231     0  2.327920e+11   1.356760e+11  1.670100e+10      9.711600e+10  5.516400e+10        3.462000e+10           2.054400e+10  1.776280e+11  1.776280e+11      1.348850e+11             0.000000e+00        0.000000e+00              0.0               0.0          2.327920e+11                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "16  0001018724-19-000004  1018724  AMAZON COM INC  10-K  1231  2018.0  FY 2018-12-31  20190201             6  20181231     0  1.626480e+11   7.510100e+10  3.175000e+10      8.754700e+10  1.190990e+11        6.839100e+10           5.070800e+10  4.354900e+10  4.354900e+10      1.962500e+10             2.679100e+10       -1.837000e+09              0.0               0.0          1.626480e+11                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "15  0001564590-19-027952   789019  MICROSOFT CORP  10-K  0630  2019.0  FY 2019-06-30  20190801             4  20190630     0  2.865560e+11   1.755520e+11  1.135600e+10      1.110040e+11  1.842260e+11        6.942000e+10           1.148060e+11  1.023300e+11  1.023300e+11      2.415000e+10             0.000000e+00        0.000000e+00              0.0               0.0          2.865560e+11                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "14  0001652044-20-000008  1652044   ALPHABET INC.  10-K  1231  2019.0  FY 2019-12-31  20200204             2  20191231     0  2.759090e+11   1.525780e+11  1.849800e+10      1.233310e+11  7.446700e+10        4.522100e+10           2.924600e+10  2.014420e+11  2.014420e+11      1.521220e+11             0.000000e+00        0.000000e+00              0.0               0.0          2.759090e+11                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "13  0001018724-20-000004  1018724  AMAZON COM INC  10-K  1231  2019.0  FY 2019-12-31  20200131             6  20191231     0  2.252480e+11   9.633400e+10  3.609200e+10      1.289140e+11  1.631880e+11        8.781200e+10           3.558500e+10  6.206000e+10  6.206000e+10      3.122000e+10             3.365800e+10       -1.837000e+09              0.0               0.0          2.252480e+11                0.0              0.0                0.243835                 100.0                0.0              0.0                      0.0                    0.0\n", "12  0001564590-20-034944   789019  MICROSOFT CORP  10-K  0630  2020.0  FY 2020-06-30  20200731             4  20200630     0  3.013110e+11   1.819150e+11  1.357600e+10      1.193960e+11  1.830070e+11        7.231000e+10           1.106970e+11  1.183040e+11  1.183040e+11      3.456600e+10             0.000000e+00        0.000000e+00              0.0               0.0          3.013110e+11                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "11  0001652044-21-000010  1652044   ALPHABET INC.  10-K  1231  2020.0  FY 2020-12-31  20210203             2  20201231     0  3.196160e+11   1.742960e+11  2.646500e+10      1.453200e+11  9.707200e+10        5.683400e+10           4.023800e+10  2.225440e+11  2.225440e+11      1.634010e+11             0.000000e+00        0.000000e+00              0.0               0.0          3.196160e+11                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "10  0001018724-21-000004  1018724  AMAZON COM INC  10-K  1231  2020.0  FY 2020-12-31  20210203             6  20201231     0  3.211950e+11   1.327330e+11  4.212200e+10      1.884620e+11  2.277910e+11        1.263850e+11           4.883300e+10  9.340400e+10  9.340400e+10      5.255100e+10             4.286500e+10       -1.837000e+09              0.0               0.0          3.211950e+11                0.0              0.0                0.230795                 100.0                0.0              0.0                      0.0                    0.0\n", "9   0001564590-21-039151   789019  MICROSOFT CORP  10-K  0630  2021.0  FY 2021-06-30  20210729             4  20210630     0  3.337790e+11   1.844060e+11  1.422400e+10      1.493730e+11  1.917910e+11        8.865700e+10           1.031340e+11  1.419880e+11  1.419880e+11      5.705500e+10             0.000000e+00        0.000000e+00              0.0               0.0          3.337790e+11                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "8   0001652044-22-000019  1652044   ALPHABET INC.  10-K  1231  2021.0  FY 2021-12-31  20220202             3  20211231     0  3.592680e+11   1.881430e+11  2.094500e+10      1.711250e+11  1.076330e+11        6.425400e+10           4.337900e+10  2.516350e+11  2.516350e+11      1.914840e+11             0.000000e+00        0.000000e+00              0.0               0.0          3.592680e+11                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "7   0001018724-22-000005  1018724  AMAZON COM INC  10-K  1231  2021.0  FY 2021-12-31  20220204             7  20211231     0  4.205490e+11   1.615800e+11  3.622000e+10      2.589690e+11  2.823040e+11        1.422660e+11           7.238700e+10  1.382450e+11  1.382450e+11      8.591500e+10             5.553800e+10       -1.837000e+09              0.0               0.0          4.205490e+11                0.0              0.0                0.239639                 100.0                0.0              0.0                      0.0                    0.0\n", "6   0001564590-22-026876   789019  MICROSOFT CORP  10-K  0630  2022.0  FY 2022-06-30  20220728             4  20220630     0  3.648400e+11   1.696840e+11  1.393100e+10      1.951560e+11  1.982980e+11        9.508200e+10           1.032160e+11  1.665420e+11  1.665420e+11      8.428100e+10             0.000000e+00        0.000000e+00              0.0               0.0          3.648400e+11                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "5   0001652044-23-000016  1652044   ALPHABET INC.  10-K  1231  2022.0  FY 2022-12-31  20230203             3  20221231     0  3.652640e+11   1.647950e+11  2.187900e+10      2.004690e+11  1.091200e+11        6.930000e+10           3.982000e+10  2.561440e+11  2.561440e+11      1.955630e+11             0.000000e+00        0.000000e+00              0.0               0.0          3.652640e+11                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "4   0001018724-23-000004  1018724  AMAZON COM INC  10-K  1231  2022.0  FY 2022-12-31  20230203             7  20221231     0  4.626750e+11   1.467910e+11  5.388800e+10      3.158840e+11  3.166320e+11        1.553930e+11           8.827100e+10  1.460430e+11  1.460430e+11      8.319300e+10             7.506600e+10        0.000000e+00              0.0               0.0          4.626750e+11                0.0              0.0                0.230450                 100.0                0.0              0.0                      0.0                    0.0\n", "3   0000950170-23-035122   789019  MICROSOFT CORP  10-K  0630  2023.0  FY 2023-06-30  20230727             4  20230630     0  4.119760e+11   1.842570e+11  3.470400e+10      2.277190e+11  2.057530e+11        1.041490e+11           1.016040e+11  2.062230e+11  2.062230e+11      1.188480e+11             0.000000e+00        0.000000e+00              0.0               0.0          4.119760e+11                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "2   0001652044-24-000022  1652044   ALPHABET INC.  10-K  1231  2023.0  FY 2023-12-31  20240131             3  20231231     0  4.023920e+11   1.715300e+11  2.404800e+10      2.308620e+11  1.190130e+11        8.181400e+10           3.719900e+10  2.833790e+11  2.833790e+11      2.112470e+11             0.000000e+00        0.000000e+00              0.0               0.0          4.023920e+11                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0\n", "1   0001018724-24-000008  1018724  AMAZON COM INC  10-K  1231  2023.0  FY 2023-12-31  20240202             7  20231231     0  5.278540e+11   1.723510e+11  7.338700e+10      3.555030e+11  3.259790e+11        1.649170e+11           8.376500e+10  2.018750e+11  2.018750e+11      1.136180e+11             9.902500e+10        0.000000e+00              0.0               0.0          5.278540e+11                0.0              0.0                0.237123                 100.0                0.0              0.0                      0.0                    0.0\n", "0   0000950170-24-087843   789019  MICROSOFT CORP  10-K  0630  2024.0  FY 2024-06-30  20240730             4  20240630     0  5.121630e+11   1.597340e+11  1.831500e+10      3.524290e+11  2.436860e+11        1.252860e+11           1.184000e+11  2.684770e+11  2.684770e+11      1.731440e+11             0.000000e+00        0.000000e+00              0.0               0.0          5.121630e+11                0.0              0.0                0.000000                   0.0                0.0              0.0                      0.0                    0.0"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["from secfsdstools.f_standardize.bs_standardize import BalanceSheetStandardizer\n", "\n", "bs_standardizer = BalanceSheetStandardizer()      \n", "standardized_bs_df = joined_bag.present(bs_standardizer)\n", "\n", "standardized_bs_df"]}, {"cell_type": "markdown", "id": "2e4fcf44-3e7f-4958-8328-2651a9a29967", "metadata": {}, "source": ["Let us visualize the growth in Equity for these companies"]}, {"cell_type": "code", "execution_count": 24, "id": "be50f263-05e5-4090-bcb0-676ab45db5bc", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["<matplotlib.legend.Legend at 0x1744eedc7f0>"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "# Group by 'name' and plot equity for each group\n", "# Note: using the `present` method ensured that the same cik has always the same name even if the company name did change in the past\n", "for name, group in standardized_bs_df.groupby('name'):\n", "    plt.plot(group['date'], group['Equity'], label=name, linestyle='-')\n", "\n", "# Add labels and title\n", "plt.xlabel('Date')\n", "plt.ylabel('Equity')\n", "plt.title('Equity Over Time for Different Companies (CIKs)')\n", "\n", "# Display legend\n", "plt.legend()"]}, {"cell_type": "markdown", "id": "ea66b346-99fe-4353-997e-29b8549c316b", "metadata": {}, "source": ["Now, let us use the Income Statement Standardizer and plot the GrossProfit:"]}, {"cell_type": "code", "execution_count": 25, "id": "acdf0af0-6fd3-48a8-9728-ae3c82c1f465", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-01 07:31:09,126 [INFO] standardizing  start PRE processing ...\n", "2025-02-01 07:31:09,174 [INFO] standardizing  start MAIN processing ...\n", "2025-02-01 07:31:09,914 [INFO] standardizing  start POST processing ...\n", "2025-02-01 07:31:09,947 [INFO] standardizing  start FINALIZE ...\n"]}, {"data": {"text/plain": ["<matplotlib.legend.Legend at 0x1744eeb6e60>"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from secfsdstools.f_standardize.is_standardize import IncomeStatementStandardizer\n", "    \n", "standardizer = IncomeStatementStandardizer()\n", "standardized_is_df = joined_bag.present(standardizer)\n", "\n", "# sometimes, companies report in the annual report also the results for the last quarter itself. \n", "# To ensure, we use only the data for the whole year, we use only data that is for all 4 quarters\n", "standardized_is_df = standardized_is_df[standardized_is_df.qtrs==4].copy()\n", "    \n", "import matplotlib.pyplot as plt\n", "\n", "# Group by 'name' and plot equity for each group\n", "# Note: using the `present` method ensured that the same cik has always the same name even if the company name did change in the past\n", "for name, group in standardized_is_df.groupby('name'):\n", "    plt.plot(group['date'], group['GrossProfit'], label=name, linestyle='-')\n", "\n", "# Add labels and title\n", "plt.xlabel('Date')\n", "plt.ylabel('GrossProfit')\n", "plt.title('GrossProfit Over Time for Different Companies (CIKs)')\n", "\n", "# Display legend\n", "plt.legend()"]}, {"cell_type": "markdown", "id": "166d919e-6042-4824-b819-3e9e6b49cb34", "metadata": {}, "source": ["And last, use the Cash Flow Standardizer to visualize the NetCashProvidedByUsedInOperatingActivities."]}, {"cell_type": "code", "execution_count": 26, "id": "fb60cd9b-dc59-4ce4-91f7-a432c7a6bdce", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-01 07:32:02,648 [INFO] standardizing  start PRE processing ...\n", "2025-02-01 07:32:02,729 [INFO] standardizing  start MAIN processing ...\n", "2025-02-01 07:32:02,814 [INFO] standardizing  start POST processing ...\n", "2025-02-01 07:32:02,862 [INFO] standardizing  start FINALIZE ...\n"]}, {"data": {"text/plain": ["<matplotlib.legend.Legend at 0x17446ddaaa0>"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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*******************************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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from secfsdstools.f_standardize.cf_standardize import CashFlowStandardizer\n", "    \n", "standardizer = CashFlowStandardizer()\n", "\n", "standardized_cf_df = joined_bag.present(standardizer)\n", "\n", "# sometimes, companies report in the annual report also the results for the last quarter itself. \n", "# To ensure, we use only the data for the whole year, we use only data that is for all 4 quarters\n", "standardized_cf_df = standardized_cf_df[standardized_cf_df.qtrs==4].copy()\n", "\n", "import matplotlib.pyplot as plt\n", "# Group by 'name' and plot NetCashProvidedByUsedInOperatingActivities for each group\n", "# Note: using the `present` method ensured that the same cik has always the same name even if the company name did change in the past\n", "for name, group in standardized_cf_df.groupby('name'):\n", "    plt.plot(group['date'], group['NetCashProvidedByUsedInOperatingActivities'], label=name, linestyle='-')\n", "\n", "# Add labels and title\n", "plt.xlabel('Date')\n", "plt.ylabel('NetCashProvidedByUsedInOperatingActivities')\n", "plt.title('NetCashProvidedByUsedInOperatingActivities Over Time for Different Companies (CIKs)')\n", "\n", "# Display legend\n", "plt.legend()"]}, {"cell_type": "markdown", "id": "a16bfa5f-e965-464e-9479-4017e1745b88", "metadata": {}, "source": ["## What to do next\n", "Definitely checkout the notebook \"03_explore_with_interactive_notebook.ipynb\" which shows some example on how the data can be explored in an interactive way in Jupyter."]}, {"cell_type": "code", "execution_count": null, "id": "8313eff0-46db-46ae-a077-978bc0e0ec88", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}
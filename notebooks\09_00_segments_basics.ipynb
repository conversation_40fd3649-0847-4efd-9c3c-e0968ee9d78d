{"cells": [{"cell_type": "code", "execution_count": 1, "id": "c0c03ccb-b418-4fc6-8010-4f011a17be4d", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "# ensure that all columns are shown and that colum content is not cut\n", "pd.set_option('display.max_rows', 500) # ensure that all rows are shown\n", "pd.set_option('display.max_colwidth', None)\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width',1000)"]}, {"cell_type": "markdown", "id": "5ba2d3e8-779a-460b-a02f-3048833be7dd", "metadata": {}, "source": ["# Analyze Segments Information"]}, {"cell_type": "markdown", "id": "156bbbb1-6b04-4883-be7d-c7174196e465", "metadata": {"tags": []}, "source": ["<span style=\"color: #FF8C00;\">==========================================================</span>\n", "\n", "**If you find this tool useful, a sponsorship would be greatly appreciated!**\n", "\n", "**https://github.com/sponsors/HansjoergW**\n", "\n", "How to get in touch\n", "\n", "* Found a bug: https://github.com/HansjoergW/sec-fincancial-statement-data-set/issues\n", "* Have a remark: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/general\n", "* Have an idea: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/ideas\n", "* Have a question: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/q-a\n", "* Have something to show: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/show-and-tell\n", "\n", "<span style=\"color: #FF8C00;\">==========================================================</span>"]}, {"cell_type": "markdown", "id": "09a073e5-96b1-4a44-a429-e9347960ba36", "metadata": {}, "source": ["In this notebook, we analyze what information that is available in the segments column.\n", "\n", "To do that, we use the joined databag containing all the filtered and joined data (as created in the automation example in 08_00_automation_basics notebook). This will use quite some memory and also take a minute or so to load. \n", "\n", "As an alternative, you could also use only the data of 2024:\n", "<pre>\n", "# As an alternative, using the data of a single year\n", "from secfsdstools.d_container.databagmodel import JoinedDataBag\n", "from secfsdstools.e_collector.zipcollecting import ZipCollector\n", "from secfsdstools.u_usecases.bulk_loading import default_postloadfilter\n", "\n", "collector = ZipCollector.get_zip_by_names(names=[\"2024q1.zip\", \"2024q2.zip\", \"2024q3.zip\", \"2024q4.zip\"], \n", "                                          forms_filter=[\"10-K\", \"10-Q\"],                                        \n", "                                          post_load_filter=default_postloadfilter)\n", "\n", "all_joined_bag: JoinedDataBag = collector.collect().join()\n", "pre_num_df = all_joined_bag.pre_num_df\n", "</pre>"]}, {"cell_type": "code", "execution_count": 7, "id": "dea37d1a-5077-4e1d-a106-de98858d54c5", "metadata": {"tags": []}, "outputs": [], "source": ["from secfsdstools.d_container.databagmodel import JoinedDataBag\n", "\n", "path_to_all = \"C:/data/sec/automated/_4_single_bag/all\"\n", "all_joined_bag = JoinedDataBag.load(path_to_all)\n", "pre_num_df = all_joined_bag.pre_num_df"]}, {"cell_type": "markdown", "id": "87a58c0b-ebfc-490e-8e50-c16de5ddd204", "metadata": {}, "source": ["## Basic information"]}, {"cell_type": "code", "execution_count": 8, "id": "3fbc79e9-c502-480c-8d72-b7382ecbab40", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["62187005\n"]}], "source": ["print(len(pre_num_df))"]}, {"cell_type": "markdown", "id": "4d76b402-2fff-4d31-8901-b2159190d419", "metadata": {}, "source": ["The whole dataset (as of February 2025) has over **62 million** rows in the joined pre_num_df dataframe. Now, let's see how many rows have information inside the `segments` column:"]}, {"cell_type": "code", "execution_count": 9, "id": "eb0182a8-a51b-45fc-82a4-3511b0c8c61f", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["26381721\n"]}], "source": ["print(sum(~(pre_num_df.segments=='')))"]}, {"cell_type": "markdown", "id": "24a5dba6-2954-4271-8ae6-a64013ff307d", "metadata": {"tags": []}, "source": ["Around **40%** of the datapoints have segments information.\n", "\n", "Now let us see, how many different values we have in the `segments` column:"]}, {"cell_type": "code", "execution_count": 10, "id": "e520de79-0484-4281-8906-d5265c10f20d", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["844868\n"]}], "source": ["print(pre_num_df.segments.nunique(dropna=True))"]}, {"cell_type": "markdown", "id": "9f415310-cabf-4a7d-9b44-fc30dffd7e4c", "metadata": {}, "source": ["It seems as there are many different values within the segments column. So, it will be intersting to know, if certain values are more frequent and therefore more important than others."]}, {"cell_type": "markdown", "id": "02a2acda-5080-4f51-8851-cdce2f74e1eb", "metadata": {}, "source": ["## Category/Axis\n", "\n", "### Basics\n", "\n", "Usually, entries with segments information \"belong\" to an entry with the same `tag` that has None in its `segments` column. \n", "\n", "As an example, let us look at the Apple 10-Q report of the second quarter of 2024 which adsh equals \"0000320193-24-000069\". We will also filter for the Revenues tag `RevenueFromContractWithCustomerExcludingAssessedTax` and the values for only the second quarter (qtrs==1) and not for the combined values of quarter 1 and 2 (qtrs=2)."]}, {"cell_type": "code", "execution_count": 11, "id": "f13e1844-1488-46a3-9d09-83b857202a3b", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>adsh</th>\n", "      <th>tag</th>\n", "      <th>version</th>\n", "      <th>ddate</th>\n", "      <th>qtrs</th>\n", "      <th>uom</th>\n", "      <th>segments</th>\n", "      <th>coreg</th>\n", "      <th>value</th>\n", "      <th>footnote</th>\n", "      <th>report</th>\n", "      <th>line</th>\n", "      <th>stmt</th>\n", "      <th>inpth</th>\n", "      <th>rfile</th>\n", "      <th>plabel</th>\n", "      <th>negating</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>61209002</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=GreaterChinaSegment;</td>\n", "      <td></td>\n", "      <td>1.637200e+10</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61209005</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=IPad;</td>\n", "      <td></td>\n", "      <td>5.559000e+09</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61209006</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>9.075300e+10</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61209011</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=RestOfAsiaPacificSegment;</td>\n", "      <td></td>\n", "      <td>6.723000e+09</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61209012</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=EuropeSegment;</td>\n", "      <td></td>\n", "      <td>2.412300e+10</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61209015</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=JapanSegment;</td>\n", "      <td></td>\n", "      <td>6.262000e+09</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61209016</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=Service;</td>\n", "      <td></td>\n", "      <td>2.386700e+10</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61209018</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=Mac;</td>\n", "      <td></td>\n", "      <td>7.451000e+09</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61209019</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=WearablesHomeandAccessories;</td>\n", "      <td></td>\n", "      <td>7.913000e+09</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61209020</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=AmericasSegment;</td>\n", "      <td></td>\n", "      <td>3.727300e+10</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61209022</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=IPhone;</td>\n", "      <td></td>\n", "      <td>4.596300e+10</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61209023</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=Product;</td>\n", "      <td></td>\n", "      <td>6.688600e+10</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                          adsh                                                  tag       version     ddate  qtrs  uom                                       segments coreg         value footnote  report  line stmt  inpth rfile     plabel  negating\n", "61209002  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     1  USD          BusinessSegments=GreaterChinaSegment;        1.637200e+10     None       2     7   IS      0     H  Net sales         0\n", "61209005  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     1  USD                         ProductOrService=IPad;        5.559000e+09     None       2     7   IS      0     H  Net sales         0\n", "61209006  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     1  USD                                                       9.075300e+10     None       2     7   IS      0     H  Net sales         0\n", "61209011  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     1  USD     BusinessSegments=RestOfAsiaPacificSegment;        6.723000e+09     None       2     7   IS      0     H  Net sales         0\n", "61209012  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     1  USD                BusinessSegments=EuropeSegment;        2.412300e+10     None       2     7   IS      0     H  Net sales         0\n", "61209015  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     1  USD                 BusinessSegments=JapanSegment;        6.262000e+09     None       2     7   IS      0     H  Net sales         0\n", "61209016  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     1  USD                      ProductOrService=Service;        2.386700e+10     None       2     7   IS      0     H  Net sales         0\n", "61209018  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     1  USD                          ProductOrService=Mac;        7.451000e+09     None       2     7   IS      0     H  Net sales         0\n", "61209019  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     1  USD  ProductOrService=WearablesHomeandAccessories;        7.913000e+09     None       2     7   IS      0     H  Net sales         0\n", "61209020  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     1  USD              BusinessSegments=AmericasSegment;        3.727300e+10     None       2     7   IS      0     H  Net sales         0\n", "61209022  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     1  USD                       ProductOrService=IPhone;        4.596300e+10     None       2     7   IS      0     H  Net sales         0\n", "61209023  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     1  USD                      ProductOrService=Product;        6.688600e+10     None       2     7   IS      0     H  Net sales         0"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["example_segments = pre_num_df[(pre_num_df.adsh==\"0000320193-24-000069\") & (pre_num_df.tag==\"RevenueFromContractWithCustomerExcludingAssessedTax\") & (pre_num_df.qtrs==1)]\n", "example_segments"]}, {"cell_type": "markdown", "id": "fc8ef9a8-3064-4ed5-9493-ee8fa3c6c833", "metadata": {}, "source": ["Usually, entries in the segments columns have the format `<category/axis>=<value>` and in the above example, we see that we mainly have two axes: `BusinessSegments`and `ProductOrService`. The first one gives a more detailed view of the revenues that were made in different regiond. We would also expect, that the values sum up to the total value shown in the entry without `segments` information: 9.07+10. And indeed, they do: 1.637+10, 0.672+10, 2.412+10, 0.626+10, and 3.727+10 sum up to 9.07+10"]}, {"cell_type": "code", "execution_count": 14, "id": "288dac64-03b8-40e1-9175-a59e6fbe65bd", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["***********.0"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["sum(example_segments[example_segments.segments.str.startswith('BusinessSegments')].value)"]}, {"cell_type": "markdown", "id": "ab877b26-cf81-402c-9c1a-578315bf15d4", "metadata": {}, "source": ["The second axis `ProductOrService` is a little bit trickier, since it shows two levels. First, we have separation for Product (`ProductOrService=Product`) or Service (`ProductOrService=Service`). This two values will also sum up to the total of 9.0753+10: 6.6886+10 + 2.38670+10. But we also have the Revenues for different products: `ProductOrService=IPad`, `ProductOrService=IPhone`, ... . We expect, that the values of the products should sum up, or at least come close to the value of the total product value `ProductOrService=Product`: 6.6886+10."]}, {"cell_type": "code", "execution_count": 15, "id": "04e471ce-9950-4dea-a657-369a30074b61", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["***********.0"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["sum(example_segments[example_segments.segments.isin(['ProductOrService=IPad;','ProductOrService=Mac;','ProductOrService=WearablesHomeandAccessories;','ProductOrService=IPhone;'])].value)"]}, {"cell_type": "markdown", "id": "1cf5e58d-d98c-4792-ab25-15ac1e704c45", "metadata": {}, "source": ["**Conclusion**: we cannot simply expect, that value for certain \"axis\" will directly add up to the total value."]}, {"cell_type": "markdown", "id": "6e5bb074-c945-4748-a7cd-6bee769c921d", "metadata": {}, "source": ["## Overview on Categories/Axes"]}, {"cell_type": "markdown", "id": "13a7986c-7095-4d3a-8cde-76d57a3ee8d7", "metadata": {}, "source": ["Since the format of the `segments` column is `<category/axis>=<value>`, let's create a category column, so that we can investigate how many different categories we have and how often they appear. We simply split the string inside the segments column at the = sign and use the first part as `category`."]}, {"cell_type": "code", "execution_count": 16, "id": "bc4ac4c4-7801-4d6f-9276-c8c9bb06c29a", "metadata": {"tags": []}, "outputs": [], "source": ["pre_num_df['category'] = pre_num_df.segments.str.split(\"=\", n=1, expand=True)[0]"]}, {"cell_type": "markdown", "id": "f6882b0b-5059-48c9-a2c8-ab9cec59c2ce", "metadata": {}, "source": ["Let's see how many different categories we have:"]}, {"cell_type": "code", "execution_count": 18, "id": "51e869c9-c95c-4d11-b0aa-98ca8b7a3b7a", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["6050\n"]}], "source": ["print(pre_num_df.category.nunique(dropna=True))"]}, {"cell_type": "markdown", "id": "0fdb2961-a58d-4e1d-b195-39d3027eff8d", "metadata": {}, "source": ["There are around 6000 \"main\" cataegories, resp. axes.\n", "\n", "In order to know which categories are the most important ones, let's display to top 10 for every financial statement (BS, IS, CF):"]}, {"cell_type": "code", "execution_count": 12, "id": "5b9ead38-64dc-4a7f-ad9d-55cbcc935582", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Results for:  BS\n", "different categories in stmt 3457\n", "top ten\n", "\n", "                                       11365908\n", "EquityComponents                        1334326\n", "ClassOfStock                             800987\n", "FairValueByFairValueHierarchyLevel       702379\n", "InvestmentIdentifier                     496133\n", "BusinessSegments                         481380\n", "ConsolidatedEntities                     474227\n", "ConsolidationItems                       354201\n", "FinancingReceivablePortfolioSegment      250581\n", "FinancialInstrument                      224834\n", "Name: category, dtype: int64\n", "-------------------------------------\n", "\n", "\n", "Results for:  IS\n", "different categories in stmt 2663\n", "top ten\n", "\n", "                                                            10345352\n", "BusinessSegments                                             2315314\n", "ConsolidationItems                                            957705\n", "EquityComponents                                              738475\n", "ProductOrService                                              540249\n", "Geographical                                                  382707\n", "ConsolidatedEntities                                          341274\n", "ClassOfStock                                                  186131\n", "LegalEntity                                                   105392\n", "ReclassificationOutOfAccumulatedOtherComprehensiveIncome       75727\n", "Name: category, dtype: int64\n", "-------------------------------------\n", "\n", "\n", "Results for:  CF\n", "different categories in stmt 2309\n", "top ten\n", "\n", "                           10296319\n", "EquityComponents             529497\n", "BusinessSegments             341077\n", "ConsolidatedEntities         319963\n", "ConsolidationItems           315154\n", "DebtInstrument                66902\n", "LegalEntity                   56122\n", "IncomeStatementLocation       51111\n", "Scenario                      47435\n", "ClassOfStock                  47382\n", "Name: category, dtype: int64\n", "-------------------------------------\n", "\n", "\n"]}], "source": ["def get_value_counts(stmt: str) -> pd.Series:\n", "  print(\"Results for: \", stmt)\n", "  p_n_stmt_df = pre_num_df[(pre_num_df.stmt==stmt) & ~(pre_num_df.segments=='')]\n", "  categories_stmt =  p_n_stmt_df.category.value_counts()\n", "  print(\"different categories in\", \"stmt\", len(categories_stmt))\n", "  print(\"top ten\\n\")\n", "  print(categories_stmt[:10])\n", "  print(\"-------------------------------------\\n\\n\")\n", "  return categories_stmt\n", "\n", "bs_categories = get_value_counts(\"BS\")\n", "is_categories = get_value_counts(\"IS\")\n", "cf_categories = get_value_counts(\"CF\")"]}, {"cell_type": "markdown", "id": "057a583b-37ae-42d0-bece-fba7c08f5d6f", "metadata": {}, "source": ["**Conclusion**: EquityComponents, BusinessSegments, ClassOfStock, LegalEntity, ConsolidationItems, and ConsolidationEntities are among the top 10 of all statements."]}, {"cell_type": "markdown", "id": "24be47f3-3abf-4d88-b472-cc0b2c3f0a1f", "metadata": {}, "source": ["## An Example Deep Dive into Apple's 10-K\n", "\n", "Let us have a look at Apple's 10-K reports. \n", "\n", "Therefore we load by Apples's cik 320193 and the forms 10-K. Moreover, we are just interestes in IS reports. We use Predicate Pushdown on the \"big single bag\", but of course, we could also use the CompanyCollector instead to get all 10-K reports for Apple, or applying the filters on the already loaded dataset."]}, {"cell_type": "code", "execution_count": 3, "id": "6214362d-36f7-4b62-b467-122fa863221a", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-26 06:17:13,669 [INFO] databagmodel  apply sub_df filter: [('cik', 'in', [320193]), ('form', 'in', ['10-K'])]\n", "2025-02-26 06:17:13,856 [INFO] databagmodel  apply pre_num_df filter: [\"('adsh', 'in', ['0001193125-09-214859', '0001193125-10-238044', '0001193125-11-282113', '0001193125-...)\", \"('stmt', 'in', ['IS'])\"]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["(703, 17)\n"]}], "source": ["from secfsdstools.d_container.databagmodel import JoinedDataBag\n", "\n", "path_to_all = \"C:/data/sec/automated/_4_single_bag/all\"\n", "apple_10k_joined_bag = JoinedDataBag.load(path_to_all, ciks_filter=[320193], forms_filter=['10-K'], stmt_filter=['IS'])\n", "apple_10k_pre_num_df = apple_10k_joined_bag.pre_num_df\n", "print(apple_10k_pre_num_df.shape)"]}, {"cell_type": "code", "execution_count": 6, "id": "8fa3886a-7fba-46f3-a7bd-c6fc0971b915", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tags used in the IS reports of Apple's 10-K:\n", " ['IncomeLossFromContinuingOperationsBeforeIncomeTaxesMinorityInterestAndIncomeLossFromEquityMethodInvestments'\n", " 'SellingGeneralAndAdministrativeExpense' 'EarningsPerShareDiluted'\n", " 'CostOfGoodsAndServicesSold'\n", " 'WeightedAverageNumberOfSharesOutstandingBasic' 'IncomeTaxExpenseBenefit'\n", " 'SalesRevenueNet' 'NetIncomeLoss' 'GrossProfit'\n", " 'WeightedAverageNumberOfDilutedSharesOutstanding'\n", " 'ResearchAndDevelopmentExpense' 'NonoperatingIncomeExpense'\n", " 'EarningsPerShareBasic' 'OperatingIncomeLoss' 'OperatingExpenses'\n", " 'CommonStockDividendsPerShareDeclared'\n", " 'IncomeLossFromContinuingOperationsBeforeIncomeTaxesExtraordinaryItemsNoncontrollingInterest'\n", " 'Revenues' 'RevenueFromContractWithCustomerExcludingAssessedTax'] \n", "\n", "Tags containing 'revenue':\n", " ['SalesRevenueNet', 'Revenues', 'RevenueFromContractWithCustomerExcludingAssessedTax']\n"]}], "source": ["tags = apple_10k_pre_num_df.tag.unique()\n", "revenue_tags = [t for t in tags if 'revenue' in t.lower()]\n", "print(\"Tags used in the IS reports of Apple's 10-K:\\n\", tags, \"\\n\")\n", "print(\"Tags containing 'revenue':\\n\", revenue_tags)"]}, {"cell_type": "markdown", "id": "48230896-6aec-46b7-badf-6f1016074e04", "metadata": {}, "source": ["Let us look only at Tags containing 'revenue' for Apple's 10-K reports. We only want the data for the whole year, so we also filter for qtrs==4. Furthermore, we want to see the \"main\" value (meaning segments is empty) and segments values for \"ProductOrService=Service;\" and \"ProductOrService=Product;\""]}, {"cell_type": "code", "execution_count": 22, "id": "11dcc115-ff2c-4ea4-8323-b77b7dca3e02", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>adsh</th>\n", "      <th>tag</th>\n", "      <th>version</th>\n", "      <th>ddate</th>\n", "      <th>qtrs</th>\n", "      <th>uom</th>\n", "      <th>segments</th>\n", "      <th>coreg</th>\n", "      <th>value</th>\n", "      <th>footnote</th>\n", "      <th>report</th>\n", "      <th>line</th>\n", "      <th>stmt</th>\n", "      <th>inpth</th>\n", "      <th>rfile</th>\n", "      <th>plabel</th>\n", "      <th>negating</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>0001193125-09-214859</td>\n", "      <td>SalesRevenueNet</td>\n", "      <td>us-gaap/2009</td>\n", "      <td>20090930</td>\n", "      <td>4</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>3.653700e+10</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>X</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>0001193125-10-238044</td>\n", "      <td>SalesRevenueNet</td>\n", "      <td>us-gaap/2009</td>\n", "      <td>20100930</td>\n", "      <td>4</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>6.522500e+10</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>X</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>69</th>\n", "      <td>0001193125-11-282113</td>\n", "      <td>SalesRevenueNet</td>\n", "      <td>us-gaap/2011</td>\n", "      <td>20110930</td>\n", "      <td>4</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>1.082490e+11</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>114</th>\n", "      <td>0001193125-12-444068</td>\n", "      <td>SalesRevenueNet</td>\n", "      <td>us-gaap/2012</td>\n", "      <td>20120930</td>\n", "      <td>4</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>1.565080e+11</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>157</th>\n", "      <td>0001193125-13-416534</td>\n", "      <td>SalesRevenueNet</td>\n", "      <td>us-gaap/2013</td>\n", "      <td>20130930</td>\n", "      <td>4</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>1.709100e+11</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>221</th>\n", "      <td>0001193125-14-383437</td>\n", "      <td>SalesRevenueNet</td>\n", "      <td>us-gaap/2014</td>\n", "      <td>20140930</td>\n", "      <td>4</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>1.827950e+11</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>253</th>\n", "      <td>0001193125-15-356351</td>\n", "      <td>SalesRevenueNet</td>\n", "      <td>us-gaap/2015</td>\n", "      <td>20150930</td>\n", "      <td>4</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2.337150e+11</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>316</th>\n", "      <td>0001628280-16-020309</td>\n", "      <td>SalesRevenueNet</td>\n", "      <td>us-gaap/2015</td>\n", "      <td>20160930</td>\n", "      <td>4</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2.156390e+11</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>356</th>\n", "      <td>0000320193-17-000070</td>\n", "      <td>SalesRevenueNet</td>\n", "      <td>us-gaap/2017</td>\n", "      <td>20170930</td>\n", "      <td>4</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2.292340e+11</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>419</th>\n", "      <td>0000320193-18-000145</td>\n", "      <td>Revenues</td>\n", "      <td>us-gaap/2018</td>\n", "      <td>20180930</td>\n", "      <td>4</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2.655950e+11</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>425</th>\n", "      <td>0000320193-18-000145</td>\n", "      <td>Revenues</td>\n", "      <td>us-gaap/2018</td>\n", "      <td>20180930</td>\n", "      <td>4</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=Service;</td>\n", "      <td></td>\n", "      <td>3.719000e+10</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>452</th>\n", "      <td>0000320193-19-000119</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2019</td>\n", "      <td>20190930</td>\n", "      <td>4</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=Product;</td>\n", "      <td></td>\n", "      <td>2.138830e+11</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>453</th>\n", "      <td>0000320193-19-000119</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2019</td>\n", "      <td>20190930</td>\n", "      <td>4</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=Service;</td>\n", "      <td></td>\n", "      <td>4.629100e+10</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>461</th>\n", "      <td>0000320193-19-000119</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2019</td>\n", "      <td>20190930</td>\n", "      <td>4</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2.601740e+11</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>510</th>\n", "      <td>0000320193-20-000096</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2020</td>\n", "      <td>20200930</td>\n", "      <td>4</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=Product;</td>\n", "      <td></td>\n", "      <td>2.207470e+11</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>512</th>\n", "      <td>0000320193-20-000096</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2020</td>\n", "      <td>20200930</td>\n", "      <td>4</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=Service;</td>\n", "      <td></td>\n", "      <td>5.376800e+10</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>520</th>\n", "      <td>0000320193-20-000096</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2020</td>\n", "      <td>20200930</td>\n", "      <td>4</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2.745150e+11</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>554</th>\n", "      <td>0000320193-21-000105</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2021</td>\n", "      <td>20210930</td>\n", "      <td>4</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=Service;</td>\n", "      <td></td>\n", "      <td>6.842500e+10</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>555</th>\n", "      <td>0000320193-21-000105</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2021</td>\n", "      <td>20210930</td>\n", "      <td>4</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>3.658170e+11</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>561</th>\n", "      <td>0000320193-21-000105</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2021</td>\n", "      <td>20210930</td>\n", "      <td>4</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=Product;</td>\n", "      <td></td>\n", "      <td>2.973920e+11</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>591</th>\n", "      <td>0000320193-22-000108</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2022</td>\n", "      <td>20220930</td>\n", "      <td>4</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=Product;</td>\n", "      <td></td>\n", "      <td>3.161990e+11</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>600</th>\n", "      <td>0000320193-22-000108</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2022</td>\n", "      <td>20220930</td>\n", "      <td>4</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=Service;</td>\n", "      <td></td>\n", "      <td>7.812900e+10</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>601</th>\n", "      <td>0000320193-22-000108</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2022</td>\n", "      <td>20220930</td>\n", "      <td>4</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>3.943280e+11</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>634</th>\n", "      <td>0000320193-23-000106</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20230930</td>\n", "      <td>4</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>3.832850e+11</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>636</th>\n", "      <td>0000320193-23-000106</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20230930</td>\n", "      <td>4</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=Product;</td>\n", "      <td></td>\n", "      <td>2.980850e+11</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>637</th>\n", "      <td>0000320193-23-000106</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20230930</td>\n", "      <td>4</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=Service;</td>\n", "      <td></td>\n", "      <td>8.520000e+10</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>669</th>\n", "      <td>0000320193-24-000123</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2024</td>\n", "      <td>20240930</td>\n", "      <td>4</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=Service;</td>\n", "      <td></td>\n", "      <td>9.616900e+10</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>671</th>\n", "      <td>0000320193-24-000123</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2024</td>\n", "      <td>20240930</td>\n", "      <td>4</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>3.910350e+11</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>677</th>\n", "      <td>0000320193-24-000123</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2024</td>\n", "      <td>20240930</td>\n", "      <td>4</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=Product;</td>\n", "      <td></td>\n", "      <td>2.948660e+11</td>\n", "      <td>None</td>\n", "      <td>3</td>\n", "      <td>7</td>\n", "      <td>IS</td>\n", "      <td>0</td>\n", "      <td>H</td>\n", "      <td>Net sales</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                     adsh                                                  tag       version     ddate  qtrs  uom                   segments coreg         value footnote  report  line stmt  inpth rfile     plabel  negating\n", "6    0001193125-09-214859                                      SalesRevenueNet  us-gaap/2009  20090930     4  USD                                   3.653700e+10     None       3     3   IS      0     X  Net sales         0\n", "35   0001193125-10-238044                                      SalesRevenueNet  us-gaap/2009  20100930     4  USD                                   6.522500e+10     None       2     3   IS      0     X  Net sales         0\n", "69   0001193125-11-282113                                      SalesRevenueNet  us-gaap/2011  20110930     4  USD                                   1.082490e+11     None       2     3   IS      0     H  Net sales         0\n", "114  0001193125-12-444068                                      SalesRevenueNet  us-gaap/2012  20120930     4  USD                                   1.565080e+11     None       2     3   IS      0     H  Net sales         0\n", "157  0001193125-13-416534                                      SalesRevenueNet  us-gaap/2013  20130930     4  USD                                   1.709100e+11     None       2     3   IS      0     H  Net sales         0\n", "221  0001193125-14-383437                                      SalesRevenueNet  us-gaap/2014  20140930     4  USD                                   1.827950e+11     None       2     3   IS      0     H  Net sales         0\n", "253  0001193125-15-356351                                      SalesRevenueNet  us-gaap/2015  20150930     4  USD                                   2.337150e+11     None       2     3   IS      0     H  Net sales         0\n", "316  0001628280-16-020309                                      SalesRevenueNet  us-gaap/2015  20160930     4  USD                                   2.156390e+11     None       2     1   IS      0     H  Net sales         0\n", "356  0000320193-17-000070                                      SalesRevenueNet  us-gaap/2017  20170930     4  USD                                   2.292340e+11     None       2     1   IS      0     H  Net sales         0\n", "419  0000320193-18-000145                                             Revenues  us-gaap/2018  20180930     4  USD                                   2.655950e+11     None       2     1   IS      0     H  Net sales         0\n", "425  0000320193-18-000145                                             Revenues  us-gaap/2018  20180930     4  USD  ProductOrService=Service;        3.719000e+10     None       2     1   IS      0     H  Net sales         0\n", "452  0000320193-19-000119  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2019  20190930     4  USD  ProductOrService=Product;        2.138830e+11     None       2     7   IS      0     H  Net sales         0\n", "453  0000320193-19-000119  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2019  20190930     4  USD  ProductOrService=Service;        4.629100e+10     None       2     7   IS      0     H  Net sales         0\n", "461  0000320193-19-000119  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2019  20190930     4  USD                                   2.601740e+11     None       2     7   IS      0     H  Net sales         0\n", "510  0000320193-20-000096  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2020  20200930     4  USD  ProductOrService=Product;        2.207470e+11     None       2     7   IS      0     H  Net sales         0\n", "512  0000320193-20-000096  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2020  20200930     4  USD  ProductOrService=Service;        5.376800e+10     None       2     7   IS      0     H  Net sales         0\n", "520  0000320193-20-000096  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2020  20200930     4  USD                                   2.745150e+11     None       2     7   IS      0     H  Net sales         0\n", "554  0000320193-21-000105  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2021  20210930     4  USD  ProductOrService=Service;        6.842500e+10     None       2     7   IS      0     H  Net sales         0\n", "555  0000320193-21-000105  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2021  20210930     4  USD                                   3.658170e+11     None       2     7   IS      0     H  Net sales         0\n", "561  0000320193-21-000105  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2021  20210930     4  USD  ProductOrService=Product;        2.973920e+11     None       2     7   IS      0     H  Net sales         0\n", "591  0000320193-22-000108  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2022  20220930     4  USD  ProductOrService=Product;        3.161990e+11     None       3     7   IS      0     H  Net sales         0\n", "600  0000320193-22-000108  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2022  20220930     4  USD  ProductOrService=Service;        7.812900e+10     None       3     7   IS      0     H  Net sales         0\n", "601  0000320193-22-000108  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2022  20220930     4  USD                                   3.943280e+11     None       3     7   IS      0     H  Net sales         0\n", "634  0000320193-23-000106  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20230930     4  USD                                   3.832850e+11     None       3     7   IS      0     H  Net sales         0\n", "636  0000320193-23-000106  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20230930     4  USD  ProductOrService=Product;        2.980850e+11     None       3     7   IS      0     H  Net sales         0\n", "637  0000320193-23-000106  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20230930     4  USD  ProductOrService=Service;        8.520000e+10     None       3     7   IS      0     H  Net sales         0\n", "669  0000320193-24-000123  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2024  20240930     4  USD  ProductOrService=Service;        9.616900e+10     None       3     7   IS      0     H  Net sales         0\n", "671  0000320193-24-000123  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2024  20240930     4  USD                                   3.910350e+11     None       3     7   IS      0     H  Net sales         0\n", "677  0000320193-24-000123  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2024  20240930     4  USD  ProductOrService=Product;        2.948660e+11     None       3     7   IS      0     H  Net sales         0"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["apple_10k_pre_num_df[(apple_10k_pre_num_df.qtrs==4) & apple_10k_pre_num_df.tag.isin(revenue_tags) & apple_10k_pre_num_df.segments.isin(['', 'ProductOrService=Service;', 'ProductOrService=Product;'])]"]}, {"cell_type": "markdown", "id": "ee8314fd-c6e5-4983-8301-db3f1dca0dc3", "metadata": {}, "source": ["A few interesting points we see. Over the years, Apple was using different Tags to report the overall Revene. First, it was **SalesRevenueNet**, then just **Revenue** ein 2018, and from 2019 on it was **RevenueFromContractWithCustomerExcludingAssessedTax**. \n", "Moreover, reporting individual values for services and products sold started only in 2018. previous to that, they didn't report these more fine grained numbers. \n", "\n", "Note, there are also the tags **SalesRevenueGoodsNet** and **SalesRevenueServicesNet**. So it is very likely that find reports using those, instead of using ProductOrService=Service;\" and \"ProductOrService=Product;\" segmnets.\n", "\n", "**Conclusion**: The same value can be reported with different tags, and depending on the tag, there could also be ways on how to report the same value using segments. And more, even the same company can use different approaches over the years. So being able to standardize the information is crucial."]}, {"cell_type": "code", "execution_count": null, "id": "5c55cb5b-6944-4a1b-b536-8389d0b6752c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}
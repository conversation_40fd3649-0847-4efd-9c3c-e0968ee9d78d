{"cells": [{"cell_type": "markdown", "id": "f9421597-348f-463b-8438-6ea613e4b265", "metadata": {}, "source": ["# Automation"]}, {"cell_type": "markdown", "id": "4bd92aaa-a8df-4c3a-8062-f934759ed786", "metadata": {"tags": []}, "source": ["## TLDR\n", "\n", "- The automation features gives you the possibility to add additional processing steps after the default update steps: download of new zip files from SEC, transforming them to Parquet format and indexing them in the SQLite DB. This means, when a new zip file is detected at SEC, not only the mentioned three steps are automatcially executed but also additional steps that you define by yourself.\n", "- There are two hook methods you can define and use to implement additional logic. Both of them are activated by defining them in the configuration file.\n", "- The simpler of this two hook methods just receives the `Configuration` object and is called after the all updated steps, the default update stapes (downloading of zip files, transform to parquet, indexing) and additional user defined update steps, were executed.\n", "- The more complex one receives a `Configuration` object and has to return a list of instances derived from `AbstractProcess`. There are some basic implementations of `AbstractProcess`, that can be used to filter, concat, and standardize the data.\n", "- The library contains an example implementation of a hook function which filters the data and also directly applies the standardizer for balance sheet, income statement, and cash flow. See below on how it is implemented and how it can be directly used."]}, {"cell_type": "markdown", "id": "2215906d-638d-40a4-9e44-4e0fb51c74e0", "metadata": {"tags": []}, "source": ["<span style=\"color: #FF8C00;\">==========================================================</span>\n", "\n", "**If you find this tool useful, a sponsorship would be greatly appreciated!**\n", "\n", "**https://github.com/sponsors/HansjoergW**\n", "\n", "How to get in touch\n", "\n", "* Found a bug: https://github.com/HansjoergW/sec-fincancial-statement-data-set/issues\n", "* Have a remark: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/general\n", "* Have an idea: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/ideas\n", "* Have a question: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/q-a\n", "* Have something to show: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/show-and-tell\n", "\n", "<span style=\"color: #FF8C00;\">==========================================================</span>"]}, {"cell_type": "markdown", "id": "15f7324a-2a5a-47b4-92d6-9f18342863b9", "metadata": {}, "source": ["## Defining a simple postupdatehook function\n", "\n", "If you define a postupdatehook function in the configuration file then this function will be called after the all update steps were executed.\n", "\n", "It will be called, regardless if the previous steps did actually do something. For instance, if there was now new zip file detected to download, it will be called anyway, but not more than once every 24 hours (the usual period the framework checks for upates).\n", "\n", "Since the hook method is called even if there were no updates, it is your responsibility to check if actually something did change. Otherwise, if you implemented time consuming logic, it would be executed every 24 hours once.\n", "\n", "The postupdatehook function needs a `Configuration` parameter and does not return anything. It can have any name you like.\n", "\n", "<pre>\n", "# it is ok to import the Configuration class\n", "from secfsdstools.a_config.configmodel import Configuration \n", "from secfsdstools.c_index.indexdataaccess import ParquetDBIndexingAccessor\n", "from secfsdstools... import ...\n", "\n", "def my_postupdatehook_function(configuration: Configuration):\n", "    \n", "    # you can use the configuration for instance to instantiate access to the SQLite db\n", "    index_db = ParquetDBIndexingAccessor(db_dir=configuration.db_dir)\n", "    ...\n", "    \n", "</pre>\n", "\n", "To activate it, just define it in the DEFAULT section of the configuration file:\n", "\n", "<pre>\n", "[DEFAULT]\n", "downloaddirectory = C:/data/sec/automated/dld\n", "dbdirectory = C:/data/sec/automated/db\n", "parquetdirectory = C:/data/sec/automated/parquet\n", "useragentemail = <EMAIL>\n", "autoupdate = True\n", "keepzipfiles = False\n", "postupdatehook = mypackage.mymodule.my_postupdatehook_function\n", "</pre>"]}, {"cell_type": "markdown", "id": "1cbe8976-ad39-48d8-96a3-5efd6a13c4a7", "metadata": {}, "source": ["## Defining a postupdateprocesses function\n", "\n", "If you define a postupdateprocess function, it has to return a list of instances of `AbtractProcess`. These instances are then executed after the default steps download, transform to parquet, and indexing were executed.\n", "\n", "Also here, every \"process\" will be called once every 24 hours, and therefore, every process implementation has to check itself if something changed.\n", "\n", "As a parameter, the postupdatedprocesses function must have a `Configuration` parameter and also has to return list of instances `AbstractProcess`.\n", "\n", "*Note: There are some basic implementations of the `AbstractProcess` class within the `secfsdstools.g_pipelines` package that provide implementation to filter, to concat bags, and to standardize joined bags. \n", "Please have a look at the following section which show an example on how this basic implementations can be used.*\n", "\n", "<pre>\n", "# it is ok to import the Configuration and AbstractProcess classes\n", "from secfsdstools.a_config.configmodel import Configuration \n", "from secfsdstools.c_automation.task_framework import AbstractProcess\n", "\n", "\n", "def my_postupdateprocesses_function(configuration: Configuration) -> List[AbstractProcess]:\n", "    # do your secfsdstools imprts here\n", "    from secfsdstools... import ...\n", "    \n", "    processes: List[AbstractProcess] = []\n", "    ...\n", "    \n", "    return processes\n", "    \n", "</pre>\n", "\n", "To activate it, added the appropriate configuration in the DEFAULT section of the configuration file:\n", "\n", "<pre>\n", "[DEFAULT]\n", "downloaddirectory = C:/data/sec/automated/dld\n", "dbdirectory = C:/data/sec/automated/db\n", "parquetdirectory = C:/data/sec/automated/parquet\n", "useragentemail = <EMAIL>\n", "autoupdate = True\n", "keepzipfiles = False\n", "postupdateprocesses = mypackage.mymodule.my_postupdateprocesses_function\n", "</pre>"]}, {"cell_type": "code", "execution_count": null, "id": "82f85ce2-a805-4714-bfc8-047877531b9d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "609cb4c4-c697-4696-918f-e5e0c91e283c", "metadata": {"tags": []}, "source": ["<span style=\"color: #FF8C00;\">==========================================================</span>\n", "\n", "**If you find this tool useful, a sponsorship would be greatly appreciated!**\n", "\n", "**https://github.com/sponsors/HansjoergW**\n", "\n", "How to get in touch\n", "\n", "* Found a bug: https://github.com/HansjoergW/sec-fincancial-statement-data-set/issues\n", "* Have a remark: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/general\n", "* Have an idea: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/ideas\n", "* Have a question: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/q-a\n", "* Have something to show: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/show-and-tell\n", "\n", "<span style=\"color: #FF8C00;\">==========================================================</span>"]}, {"cell_type": "markdown", "id": "9cafe8ec-7214-4e4d-9bec-39ee5183bbac", "metadata": {}, "source": ["Note: for using the widgets inside jupyterlab, you need an appropriate environment with nodejs and the widegetextensions install. \n", "In order to develop the notebook, the following environment was used:\n", "\n", "- a new \"empty\" python 3.10 environment, for instance created with conda \n", "  - conda create -n secanalyzing python==3.10\n", "  - conda activate secanalyzing\n", "- pip install jupyterlab\n", "- jupyter labextension install @jupyter-widgets/jupyterlab-manager\n", "- pip install secfsdstools\n", "- pip install ipywidgets"]}, {"cell_type": "code", "execution_count": 1, "id": "1ae9d9be-6d78-48a3-bbda-a3383e6064dc", "metadata": {"ExecuteTime": {"end_time": "2024-04-06T04:51:33.933729Z", "start_time": "2024-04-06T04:51:33.791110Z"}, "tags": []}, "outputs": [], "source": ["# Basic import to support interactive widgets in notebooks\n", "import ipywidgets as widgets\n", "from IPython.display import display, Markdown\n", "from ipywidgets import interact, interact_manual\n", "\n", "import pandas as pd\n", "pd.set_option('display.max_rows', 500) # ensure that all rows are shown\n", "pd.set_option('display.max_colwidth', None)\n", "pd.set_option('display.max_columns', None)"]}, {"cell_type": "code", "execution_count": 2, "id": "c20dcfbf-13f7-4519-9649-dbfdf96b32d0", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-09-08 16:05:05,016 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-09-08 16:05:06,558 [INFO] updateprocess  Launching data update process ...\n", "2025-09-08 16:05:06,602 [INFO] updateprocess  Loading 38 post update processes from secfsdstools.x_examples.automation.memory_optimized_daily_automation.define_extra_processes\n", "2025-09-08 16:05:06,603 [INFO] task_framework  \n", "2025-09-08 16:05:06,603 [INFO] task_framework  ###############################################\n", "2025-09-08 16:05:06,604 [INFO] task_framework  Download Process for Quarterly Data Started\n", "2025-09-08 16:05:06,605 [INFO] task_framework  ###############################################\n", "2025-09-08 16:05:06,606 [INFO] task_framework  Starting process SecDownloadingProcess\n", "2025-09-08 16:05:06,609 [INFO] secdownloading_process  reading table in main page: https://www.sec.gov/dera/data/financial-statement-data-sets.html\n", "2025-09-08 16:05:07,331 [INFO] task_framework  Starting process ToParquetTransformerProcess\n", "2025-09-08 16:05:07,334 [INFO] task_framework  Starting process ReportParquetIndexerProcess\n", "2025-09-08 16:05:07,343 [INFO] task_framework  \n", "2025-09-08 16:05:07,344 [INFO] task_framework  ###########################################\n", "2025-09-08 16:05:07,346 [INFO] task_framework  Download Process for Daily Data Started\n", "2025-09-08 16:05:07,347 [INFO] task_framework  ###########################################\n", "2025-09-08 16:05:07,356 [INFO] dailypreparation_process  clearing daily index tables and daily parquet files before cut off: 20250700\n", "2025-09-08 16:05:08,151 [INFO] dailypreparation_process  starting daily processing after last processed quarter: 2025q2\n", "2025-09-08 16:05:08,199 [INFO] SecDaily  ==============================================================\n", "2025-09-08 16:05:08,200 [INFO] SecDaily  Process xbrl full index files\n", "2025-09-08 16:05:08,201 [INFO] SecDaily  ==============================================================\n", "2025-09-08 16:05:08,203 [INFO] SecDaily  \n", "2025-09-08 16:05:08,204 [INFO] SecDaily  --------------------------------------------------------------\n", "2025-09-08 16:05:08,204 [INFO] SecDaily  looking for new reports\n", "2025-09-08 16:05:08,205 [INFO] SecDaily  --------------------------------------------------------------\n"]}, {"name": "stdout", "output_type": "stream", "text": ["setup db: execute C:\\ieu\\Anaconda3\\envs\\secanalyzing\\lib\\site-packages\\secdaily\\_00_common\\sql\\V1__Create_SecReports_table.sql\n", "setup db: execute C:\\ieu\\Anaconda3\\envs\\secanalyzing\\lib\\site-packages\\secdaily\\_00_common\\sql\\V2__Create_SecFullIndex_table.sql\n", "setup db: execute C:\\ieu\\Anaconda3\\envs\\secanalyzing\\lib\\site-packages\\secdaily\\_00_common\\sql\\V3__Create_SecReportProcessing_table.sql\n", "setup db: execute C:\\ieu\\Anaconda3\\envs\\secanalyzing\\lib\\site-packages\\secdaily\\_00_common\\sql\\V4__Create_MassTesting_tables.sql\n", "setup db: execute C:\\ieu\\Anaconda3\\envs\\secanalyzing\\lib\\site-packages\\secdaily\\_00_common\\sql\\V5__Create_State_table.sql\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-09-08 16:05:09,117 [INFO] SecFullIndexFileProcessing  - updates for 3/2025 \n", "2025-09-08 16:05:09,312 [INFO] SecFullIndexFileProcessing     read entries: 460\n", "2025-09-08 16:05:09,347 [INFO] SecDaily  \n", "2025-09-08 16:05:09,349 [INFO] SecDaily  --------------------------------------------------------------\n", "2025-09-08 16:05:09,350 [INFO] SecDaily  add xbrl file urls\n", "2025-09-08 16:05:09,351 [INFO] SecDaily  --------------------------------------------------------------\n", "2025-09-08 16:05:09,715 [INFO] ParallelExecution      missing entries 460\n", "2025-09-08 16:05:28,168 [INFO] ParallelExecution      commited chunk: 0\n", "2025-09-08 16:05:44,401 [INFO] ParallelExecution      commited chunk: 100\n", "2025-09-08 16:06:00,622 [INFO] ParallelExecution      commited chunk: 200\n", "2025-09-08 16:06:16,834 [INFO] ParallelExecution      commited chunk: 300\n", "2025-09-08 16:06:24,932 [INFO] ParallelExecution      commited chunk: 400\n", "2025-09-08 16:06:24,943 [INFO] ParallelExecution      missing entries 1\n", "2025-09-08 16:06:25,958 [INFO] ParallelExecution      commited chunk: 0\n", "2025-09-08 16:06:25,979 [INFO] SecFullIndexFilePostProcessing  failed to process 0000950170-25-110629\n", "2025-09-08 16:06:25,980 [INFO] SecDaily  \n", "2025-09-08 16:06:25,981 [INFO] SecDaily  --------------------------------------------------------------\n", "2025-09-08 16:06:25,982 [INFO] SecDaily  check for duplicates\n", "2025-09-08 16:06:25,983 [INFO] SecDaily  --------------------------------------------------------------\n", "2025-09-08 16:06:26,026 [INFO] SecDaily  ==============================================================\n", "2025-09-08 16:06:26,028 [INFO] SecDaily  Process xbrl data files\n", "2025-09-08 16:06:26,029 [INFO] SecDaily  ==============================================================\n", "2025-09-08 16:06:26,030 [INFO] SecDaily  \n", "2025-09-08 16:06:26,031 [INFO] SecDaily  --------------------------------------------------------------\n", "2025-09-08 16:06:26,032 [INFO] SecDaily  preprocess xml files\n", "2025-09-08 16:06:26,034 [INFO] SecDaily  --------------------------------------------------------------\n", "2025-09-08 16:06:26,093 [INFO] SecXmlFilePreProcessing  459 entries copied into processing table\n", "2025-09-08 16:06:26,097 [INFO] SecDaily  \n", "2025-09-08 16:06:26,097 [INFO] SecDaily  --------------------------------------------------------------\n", "2025-09-08 16:06:26,098 [INFO] SecDaily  download lab xml files\n", "2025-09-08 16:06:26,099 [INFO] SecDaily  --------------------------------------------------------------\n", "2025-09-08 16:06:26,101 [INFO] SecXmlFileDownloading  download Label Files\n", "2025-09-08 16:06:26,405 [INFO] ParallelExecution      missing entries 418\n", "2025-09-08 16:06:42,547 [INFO] ParallelExecution      commited chunk: 0\n", "2025-09-08 16:06:58,705 [INFO] ParallelExecution      commited chunk: 100\n", "2025-09-08 16:07:14,873 [INFO] ParallelExecution      commited chunk: 200\n", "2025-09-08 16:07:31,045 [INFO] ParallelExecution      commited chunk: 300\n", "2025-09-08 16:07:34,084 [INFO] ParallelExecution      commited chunk: 400\n", "2025-09-08 16:07:34,110 [INFO] ParallelExecution      missing entries 0\n", "2025-09-08 16:07:34,129 [INFO] SecDaily  \n", "2025-09-08 16:07:34,135 [INFO] SecDaily  --------------------------------------------------------------\n", "2025-09-08 16:07:34,136 [INFO] SecDaily  download num xml files\n", "2025-09-08 16:07:34,137 [INFO] SecDaily  --------------------------------------------------------------\n", "2025-09-08 16:07:34,138 [INFO] SecXmlFileDownloading  download Num Files\n", "2025-09-08 16:07:34,158 [INFO] ParallelExecution      missing entries 459\n", "2025-09-08 16:07:50,346 [INFO] ParallelExecution      commited chunk: 0\n", "2025-09-08 16:08:06,533 [INFO] ParallelExecution      commited chunk: 100\n", "2025-09-08 16:08:22,704 [INFO] ParallelExecution      commited chunk: 200\n", "2025-09-08 16:08:38,887 [INFO] ParallelExecution      commited chunk: 300\n", "2025-09-08 16:08:46,981 [INFO] ParallelExecution      commited chunk: 400\n", "2025-09-08 16:08:47,021 [INFO] ParallelExecution      missing entries 0\n", "2025-09-08 16:08:47,032 [INFO] SecDaily  \n", "2025-09-08 16:08:47,032 [INFO] SecDaily  --------------------------------------------------------------\n", "2025-09-08 16:08:47,032 [INFO] SecDaily  download pre xml files\n", "2025-09-08 16:08:47,032 [INFO] SecDaily  --------------------------------------------------------------\n", "2025-09-08 16:08:47,050 [INFO] SecXmlFileDownloading  download Pre Files\n", "2025-09-08 16:08:47,076 [INFO] ParallelExecution      missing entries 418\n", "2025-09-08 16:09:03,217 [INFO] ParallelExecution      commited chunk: 0\n", "2025-09-08 16:09:19,398 [INFO] ParallelExecution      commited chunk: 100\n", "2025-09-08 16:09:35,545 [INFO] ParallelExecution      commited chunk: 200\n", "2025-09-08 16:09:51,725 [INFO] ParallelExecution      commited chunk: 300\n", "2025-09-08 16:09:54,763 [INFO] ParallelExecution      commited chunk: 400\n", "2025-09-08 16:09:54,783 [INFO] ParallelExecution      missing entries 0\n", "2025-09-08 16:09:54,801 [INFO] SecDaily  \n", "2025-09-08 16:09:54,801 [INFO] SecDaily  --------------------------------------------------------------\n", "2025-09-08 16:09:54,801 [INFO] SecDaily  parse lab xml files\n", "2025-09-08 16:09:54,801 [INFO] SecDaily  --------------------------------------------------------------\n", "2025-09-08 16:09:54,801 [INFO] SecXmlFileParsing  parsing Lab Files\n", "2025-09-08 16:09:54,827 [INFO] ParallelExecution      missing entries 418\n", "2025-09-08 16:09:59,932 [INFO] ParallelExecution      commited chunk: 0\n", "2025-09-08 16:10:04,280 [INFO] ParallelExecution      commited chunk: 100\n", "2025-09-08 16:10:07,200 [INFO] ParallelExecution      commited chunk: 200\n", "2025-09-08 16:10:11,662 [INFO] ParallelExecution      commited chunk: 300\n", "2025-09-08 16:10:12,688 [INFO] ParallelExecution      commited chunk: 400\n", "2025-09-08 16:10:12,708 [INFO] ParallelExecution      missing entries 0\n", "2025-09-08 16:10:12,729 [INFO] SecDaily  \n", "2025-09-08 16:10:12,731 [INFO] SecDaily  --------------------------------------------------------------\n", "2025-09-08 16:10:12,732 [INFO] SecDaily  parse num xml files\n", "2025-09-08 16:10:12,732 [INFO] SecDaily  --------------------------------------------------------------\n", "2025-09-08 16:10:12,733 [INFO] SecXmlFileParsing  parsing Num Files\n", "2025-09-08 16:10:12,752 [INFO] ParallelExecution      missing entries 459\n", "2025-09-08 16:10:19,986 [INFO] ParallelExecution      commited chunk: 0\n", "2025-09-08 16:10:27,120 [INFO] ParallelExecution      commited chunk: 100\n", "2025-09-08 16:10:32,160 [INFO] ParallelExecution      commited chunk: 200\n", "2025-09-08 16:10:39,304 [INFO] ParallelExecution      commited chunk: 300\n", "2025-09-08 16:10:45,254 [INFO] ParallelExecution      commited chunk: 400\n", "2025-09-08 16:10:45,264 [INFO] ParallelExecution      missing entries 0\n", "2025-09-08 16:10:45,298 [INFO] SecDaily  \n", "2025-09-08 16:10:45,299 [INFO] SecDaily  --------------------------------------------------------------\n", "2025-09-08 16:10:45,300 [INFO] SecDaily  parse pre xml files\n", "2025-09-08 16:10:45,303 [INFO] SecDaily  --------------------------------------------------------------\n", "2025-09-08 16:10:45,304 [INFO] SecXmlFileParsing  parsing Pre Files\n", "2025-09-08 16:10:45,328 [INFO] ParallelExecution      missing entries 418\n", "2025-09-08 16:10:49,821 [INFO] ParallelExecution      commited chunk: 0\n", "2025-09-08 16:10:53,543 [INFO] ParallelExecution      commited chunk: 100\n", "2025-09-08 16:10:56,988 [INFO] ParallelExecution      commited chunk: 200\n", "2025-09-08 16:10:59,905 [INFO] ParallelExecution      commited chunk: 300\n", "2025-09-08 16:11:00,854 [INFO] ParallelExecution      commited chunk: 400\n", "2025-09-08 16:11:00,875 [INFO] ParallelExecution      missing entries 0\n", "2025-09-08 16:11:00,895 [INFO] SecDaily  \n", "2025-09-08 16:11:00,896 [INFO] SecDaily  --------------------------------------------------------------\n", "2025-09-08 16:11:00,897 [INFO] SecDaily  create sec style files\n", "2025-09-08 16:11:00,898 [INFO] SecDaily  --------------------------------------------------------------\n", "2025-09-08 16:11:00,900 [INFO] SECStyleFormatting  SEC style formatting\n", "2025-09-08 16:11:00,924 [INFO] ParallelExecution      missing entries 418\n", "2025-09-08 16:11:05,005 [INFO] ParallelExecution      commited chunk: 0\n", "2025-09-08 16:11:09,134 [INFO] ParallelExecution      commited chunk: 100\n", "2025-09-08 16:11:12,601 [INFO] ParallelExecution      commited chunk: 200\n", "2025-09-08 16:11:16,380 [INFO] ParallelExecution      commited chunk: 300\n", "2025-09-08 16:11:17,074 [INFO] ParallelExecution      commited chunk: 400\n", "2025-09-08 16:11:17,095 [INFO] ParallelExecution      missing entries 0\n", "2025-09-08 16:11:17,116 [INFO] SecDaily  ==============================================================\n", "2025-09-08 16:11:17,117 [INFO] SecDaily  Create daily zip files\n", "2025-09-08 16:11:17,118 [INFO] SecDaily  ==============================================================\n", "2025-09-08 16:11:17,121 [INFO] DailyZipCreating  Daily zip creating\n", "2025-09-08 16:11:17,661 [INFO] DailyZipCreating  found 418 reports in 15 dates to process\n", "2025-09-08 16:11:24,946 [INFO] SecDaily  ==============================================================\n", "2025-09-08 16:11:24,947 [INFO] SecDaily  Housekeeping\n", "2025-09-08 16:11:24,948 [INFO] SecDaily  ==============================================================\n", "2025-09-08 16:11:24,950 [INFO] Housekeeping  Starting cleanup for quarters before 2025q3\n", "2025-09-08 16:11:24,952 [INFO] Housekeeping  Removed 0 files from C:\\Users\\<USER>\\secfsdstools/data\\dld_daily/_1_xml/ for quarters before 2025q3\n", "2025-09-08 16:11:24,954 [INFO] Housekeeping  Removed 0 files from C:\\Users\\<USER>\\secfsdstools/data\\dld_daily/_2_csv/ for quarters before 2025q3\n", "2025-09-08 16:11:24,956 [INFO] Housekeeping  Removed 0 files from C:\\Users\\<USER>\\secfsdstools/data\\dld_daily/_3_secstyle/ for quarters before 2025q3\n", "2025-09-08 16:11:24,996 [INFO] HousekeepingDataAccess  Deleted 0 reports from database before quarter 2025q3\n", "2025-09-08 16:11:24,996 [INFO] Housekeeping  Removed 0 quarter zip files from C:\\Users\\<USER>\\secfsdstools/data\\dld_daily/_5_quarter/ for quarters before 2025q3\n", "2025-09-08 16:11:25,012 [INFO] Housekeeping  Removed daily zips from C:\\Users\\<USER>\\secfsdstools/data\\dld_daily/_4_daily/ with 0 files for quarters before 2025q3\n", "2025-09-08 16:11:25,014 [INFO] Housekeeping  Cleanup completed for quarters before 2025q3\n", "2025-09-08 16:11:25,015 [INFO] task_framework  Starting process ToParquetTransformerProcess\n", "2025-09-08 16:11:25,020 [INFO] parallelexecution      items to process: 15\n", "2025-09-08 16:11:28,693 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:11:28,696 [INFO] task_framework  Starting process ReportParquetIndexerProcess\n", "2025-09-08 16:11:28,703 [INFO] parallelexecution      items to process: 15\n", "2025-09-08 16:11:29,371 [INFO] task_framework  Success: IndexingTask(file_path: C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250825.zip)\n", "2025-09-08 16:11:29,397 [INFO] task_framework  Success: IndexingTask(file_path: C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250827.zip)\n", "2025-09-08 16:11:29,445 [INFO] task_framework  Success: IndexingTask(file_path: C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250819.zip)\n", "2025-09-08 16:11:29,477 [INFO] task_framework  Success: IndexingTask(file_path: C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250821.zip)\n", "2025-09-08 16:11:29,493 [INFO] task_framework  Success: IndexingTask(file_path: C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250822.zip)\n", "2025-09-08 16:11:29,515 [INFO] task_framework  Success: IndexingTask(file_path: C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250818.zip)\n", "2025-09-08 16:11:29,533 [INFO] task_framework  Success: IndexingTask(file_path: C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250829.zip)\n", "2025-09-08 16:11:29,546 [INFO] task_framework  Success: IndexingTask(file_path: C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250902.zip)\n", "2025-09-08 16:11:29,563 [INFO] task_framework  Success: IndexingTask(file_path: C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250904.zip)\n", "2025-09-08 16:11:29,580 [INFO] task_framework  Success: IndexingTask(file_path: C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250903.zip)\n", "2025-09-08 16:11:29,597 [INFO] task_framework  Success: IndexingTask(file_path: C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250826.zip)\n", "2025-09-08 16:11:29,612 [INFO] task_framework  Success: IndexingTask(file_path: C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250820.zip)\n", "2025-09-08 16:11:29,630 [INFO] task_framework  Success: IndexingTask(file_path: C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250828.zip)\n", "2025-09-08 16:11:29,644 [INFO] task_framework  Success: IndexingTask(file_path: C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250815.zip)\n", "2025-09-08 16:11:29,662 [INFO] task_framework  Success: IndexingTask(file_path: C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250905.zip)\n", "2025-09-08 16:11:29,838 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:11:29,845 [INFO] task_framework  \n", "2025-09-08 16:11:29,846 [INFO] task_framework  ####################################################\n", "2025-09-08 16:11:29,847 [INFO] task_framework  Post Update Processes For Quarterly Data Started\n", "2025-09-08 16:11:29,848 [INFO] task_framework  ####################################################\n", "2025-09-08 16:11:29,849 [INFO] task_framework  Starting process FilterProcess\n", "2025-09-08 16:11:30,533 [INFO] task_framework  Starting process StandardizeProcess\n", "2025-09-08 16:11:30,539 [INFO] task_framework  Starting process ConcatByNewSubfoldersProcess\n", "2025-09-08 16:11:30,581 [INFO] task_framework  Starting process ConcatByNewSubfoldersProcess\n", "2025-09-08 16:11:30,593 [INFO] task_framework  Starting process ConcatByNewSubfoldersProcess\n", "2025-09-08 16:11:30,625 [INFO] task_framework  Starting process ConcatByNewSubfoldersProcess\n", "2025-09-08 16:11:30,641 [INFO] task_framework  Starting process ConcatByNewSubfoldersProcess\n", "2025-09-08 16:11:30,659 [INFO] task_framework  Starting process ConcatByNewSubfoldersProcess\n", "2025-09-08 16:11:30,680 [INFO] task_framework  Starting process ConcatByChangedTimestampProcess\n", "2025-09-08 16:11:30,695 [INFO] task_framework  Starting process ConcatByNewSubfoldersProcess\n", "2025-09-08 16:11:30,728 [INFO] task_framework  Starting process ConcatByNewSubfoldersProcess\n", "2025-09-08 16:11:30,753 [INFO] task_framework  Starting process ConcatByNewSubfoldersProcess\n", "2025-09-08 16:11:30,759 [INFO] task_framework  \n", "2025-09-08 16:11:30,759 [INFO] task_framework  ################################################\n", "2025-09-08 16:11:30,759 [INFO] task_framework  Post Update Processes For Daily Data Started\n", "2025-09-08 16:11:30,759 [INFO] task_framework  ################################################\n", "2025-09-08 16:11:30,794 [INFO] task_framework  Starting process FilterProcess\n", "2025-09-08 16:11:31,049 [INFO] parallelexecution      items to process: 15\n", "2025-09-08 16:11:31,059 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-09-08 16:11:31,064 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-09-08 16:11:31,066 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-09-08 16:11:31,066 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-09-08 16:11:31,067 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-09-08 16:11:31,067 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-09-08 16:11:31,067 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-09-08 16:11:31,067 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-09-08 16:11:31,071 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-09-08 16:11:31,071 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-09-08 16:11:31,071 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-09-08 16:11:31,071 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-09-08 16:11:31,078 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:11:31,087 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:11:31,090 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:11:31,092 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:11:31,092 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:11:31,092 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:11:31,109 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:11:31,109 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:11:31,109 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:11:31,109 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:11:31,109 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250819.zip\n", "2025-09-08 16:11:31,109 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:11:31,109 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250825.zip\n", "2025-09-08 16:11:31,109 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:11:31,109 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250818.zip\n", "2025-09-08 16:11:31,109 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250904.zip\n", "2025-09-08 16:11:31,109 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250820.zip\n", "2025-09-08 16:11:31,124 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250829.zip\n", "2025-09-08 16:11:31,126 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250905.zip\n", "2025-09-08 16:11:31,127 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250826.zip\n", "2025-09-08 16:11:31,128 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250827.zip\n", "2025-09-08 16:11:31,129 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250828.zip\n", "2025-09-08 16:11:31,132 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250903.zip\n", "2025-09-08 16:11:31,142 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250902.zip\n", "2025-09-08 16:11:31,302 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:11:31,376 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:11:31,392 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:11:31,461 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:11:31,464 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:11:31,481 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:11:31,497 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:11:31,524 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:11:31,536 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:11:31,544 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:11:31,568 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:11:31,573 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:11:32,005 [INFO] task_framework  Success: ByStmtFilterTask(filtered_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250825.zip)\n", "2025-09-08 16:11:32,043 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-09-08 16:11:32,060 [INFO] task_framework  Success: ByStmtFilterTask(filtered_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250904.zip)\n", "2025-09-08 16:11:32,067 [INFO] task_framework  Success: ByStmtFilterTask(filtered_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250820.zip)\n", "2025-09-08 16:11:32,075 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:11:32,101 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-09-08 16:11:32,106 [INFO] task_framework  Success: ByStmtFilterTask(filtered_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250818.zip)\n", "2025-09-08 16:11:32,114 [INFO] task_framework  Success: ByStmtFilterTask(filtered_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250905.zip)\n", "2025-09-08 16:11:32,115 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-09-08 16:11:32,116 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250821.zip\n", "2025-09-08 16:11:32,117 [INFO] task_framework  Success: ByStmtFilterTask(filtered_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250826.zip)\n", "2025-09-08 16:11:32,125 [INFO] task_framework  Success: ByStmtFilterTask(filtered_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250903.zip)\n", "2025-09-08 16:11:32,125 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:11:32,141 [INFO] task_framework  Success: ByStmtFilterTask(filtered_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250829.zip)\n", "2025-09-08 16:11:32,141 [INFO] task_framework  Success: ByStmtFilterTask(filtered_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250827.zip)\n", "2025-09-08 16:11:32,142 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:11:32,142 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250815.zip\n", "2025-09-08 16:11:32,162 [INFO] task_framework  Success: ByStmtFilterTask(filtered_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250828.zip)\n", "2025-09-08 16:11:32,164 [INFO] task_framework  Success: ByStmtFilterTask(filtered_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250902.zip)\n", "2025-09-08 16:11:32,164 [INFO] zipcollecting  processing C:\\Users\\<USER>\\secfsdstools\\data\\parquet\\daily\\20250822.zip\n", "2025-09-08 16:11:32,175 [INFO] task_framework  Success: ByStmtFilterTask(filtered_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250819.zip)\n", "2025-09-08 16:11:32,218 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:11:32,256 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:11:32,258 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:11:32,414 [INFO] task_framework  Success: ByStmtFilterTask(filtered_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250821.zip)\n", "2025-09-08 16:11:32,426 [INFO] task_framework  Success: ByStmtFilterTask(filtered_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250822.zip)\n", "2025-09-08 16:11:32,426 [INFO] task_framework  Success: ByStmtFilterTask(filtered_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250815.zip)\n", "2025-09-08 16:11:32,493 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:11:32,731 [INFO] task_framework  Starting process StandardizeProcess\n", "2025-09-08 16:11:32,739 [INFO] parallelexecution      items to process: 15\n", "2025-09-08 16:11:32,740 [INFO] standardize_process  create standardized BS dataset\n", "2025-09-08 16:11:32,776 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:32,816 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:33,126 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:33,152 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:33,333 [INFO] standardize_process  create standardized IS dataset\n", "2025-09-08 16:11:33,368 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:33,401 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:34,200 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:34,239 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:34,442 [INFO] standardize_process  create standardized CF dataset\n", "2025-09-08 16:11:34,469 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:34,513 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:34,579 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:34,636 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:34,800 [INFO] task_framework  Success: StandardizerTask(root_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250819.zip, target_path: C:\\data\\sec\\automated\\_1_by_day\\_2_standardized_by_stmt\\20250819.zip)\n", "2025-09-08 16:11:34,804 [INFO] standardize_process  create standardized BS dataset\n", "2025-09-08 16:11:34,827 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:34,860 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:35,153 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:35,178 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:35,326 [INFO] standardize_process  create standardized IS dataset\n", "2025-09-08 16:11:35,347 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:35,376 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:36,127 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:36,166 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:36,387 [INFO] standardize_process  create standardized CF dataset\n", "2025-09-08 16:11:36,410 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:36,454 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:36,515 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:36,564 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:36,716 [INFO] task_framework  Success: StandardizerTask(root_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250825.zip, target_path: C:\\data\\sec\\automated\\_1_by_day\\_2_standardized_by_stmt\\20250825.zip)\n", "2025-09-08 16:11:36,723 [INFO] standardize_process  create standardized BS dataset\n", "2025-09-08 16:11:36,742 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:36,767 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:37,047 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:37,069 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:37,240 [INFO] standardize_process  create standardized IS dataset\n", "2025-09-08 16:11:37,261 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:37,290 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:38,026 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:38,070 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:38,239 [INFO] standardize_process  create standardized CF dataset\n", "2025-09-08 16:11:38,276 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:38,322 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:38,409 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:38,466 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:38,605 [INFO] task_framework  Success: StandardizerTask(root_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250820.zip, target_path: C:\\data\\sec\\automated\\_1_by_day\\_2_standardized_by_stmt\\20250820.zip)\n", "2025-09-08 16:11:38,609 [INFO] standardize_process  create standardized BS dataset\n", "2025-09-08 16:11:38,625 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:38,642 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:38,895 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:38,906 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:39,061 [INFO] standardize_process  create standardized IS dataset\n", "2025-09-08 16:11:39,082 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:39,112 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:40,185 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:40,233 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:40,442 [INFO] standardize_process  create standardized CF dataset\n", "2025-09-08 16:11:40,461 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:40,496 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:40,538 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:40,593 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:40,740 [INFO] task_framework  Success: StandardizerTask(root_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250826.zip, target_path: C:\\data\\sec\\automated\\_1_by_day\\_2_standardized_by_stmt\\20250826.zip)\n", "2025-09-08 16:11:40,743 [INFO] standardize_process  create standardized BS dataset\n", "2025-09-08 16:11:40,755 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:40,786 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:41,064 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:41,085 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:41,229 [INFO] standardize_process  create standardized IS dataset\n", "2025-09-08 16:11:41,238 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:41,284 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:41,979 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:42,026 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:42,225 [INFO] standardize_process  create standardized CF dataset\n", "2025-09-08 16:11:42,237 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:42,297 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:42,370 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:42,433 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:42,587 [INFO] task_framework  Success: StandardizerTask(root_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250818.zip, target_path: C:\\data\\sec\\automated\\_1_by_day\\_2_standardized_by_stmt\\20250818.zip)\n", "2025-09-08 16:11:42,603 [INFO] standardize_process  create standardized BS dataset\n", "2025-09-08 16:11:42,623 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:42,653 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:42,871 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:42,907 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:43,090 [INFO] standardize_process  create standardized IS dataset\n", "2025-09-08 16:11:43,111 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:43,152 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:43,977 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:44,012 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:44,210 [INFO] standardize_process  create standardized CF dataset\n", "2025-09-08 16:11:44,236 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:44,280 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:44,337 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:44,403 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:44,536 [INFO] task_framework  Success: StandardizerTask(root_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250905.zip, target_path: C:\\data\\sec\\automated\\_1_by_day\\_2_standardized_by_stmt\\20250905.zip)\n", "2025-09-08 16:11:44,536 [INFO] standardize_process  create standardized BS dataset\n", "2025-09-08 16:11:44,570 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:44,586 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:44,862 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:44,885 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:45,047 [INFO] standardize_process  create standardized IS dataset\n", "2025-09-08 16:11:45,066 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:45,086 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:45,788 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:45,803 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:46,002 [INFO] standardize_process  create standardized CF dataset\n", "2025-09-08 16:11:46,032 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:46,077 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:46,143 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:46,191 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:46,350 [INFO] task_framework  Success: StandardizerTask(root_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250827.zip, target_path: C:\\data\\sec\\automated\\_1_by_day\\_2_standardized_by_stmt\\20250827.zip)\n", "2025-09-08 16:11:46,358 [INFO] standardize_process  create standardized BS dataset\n", "2025-09-08 16:11:46,381 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:46,413 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:46,657 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:46,676 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:46,829 [INFO] standardize_process  create standardized IS dataset\n", "2025-09-08 16:11:46,836 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:46,875 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:47,746 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:47,780 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:47,982 [INFO] standardize_process  create standardized CF dataset\n", "2025-09-08 16:11:48,008 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:48,059 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:48,137 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:48,190 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:48,346 [INFO] task_framework  Success: StandardizerTask(root_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250829.zip, target_path: C:\\data\\sec\\automated\\_1_by_day\\_2_standardized_by_stmt\\20250829.zip)\n", "2025-09-08 16:11:48,352 [INFO] standardize_process  create standardized BS dataset\n", "2025-09-08 16:11:48,376 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:48,403 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:48,651 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:48,668 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:48,844 [INFO] standardize_process  create standardized IS dataset\n", "2025-09-08 16:11:48,871 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:48,901 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:49,653 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:49,681 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:49,850 [INFO] standardize_process  create standardized CF dataset\n", "2025-09-08 16:11:49,870 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:49,900 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:49,989 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:50,047 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:50,204 [INFO] task_framework  Success: StandardizerTask(root_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250904.zip, target_path: C:\\data\\sec\\automated\\_1_by_day\\_2_standardized_by_stmt\\20250904.zip)\n", "2025-09-08 16:11:50,211 [INFO] standardize_process  create standardized BS dataset\n", "2025-09-08 16:11:50,237 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:50,264 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:50,533 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:50,566 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:50,697 [INFO] standardize_process  create standardized IS dataset\n", "2025-09-08 16:11:50,720 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:50,746 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:51,464 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:51,483 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:51,662 [INFO] standardize_process  create standardized CF dataset\n", "2025-09-08 16:11:51,684 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:51,716 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:51,767 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:51,818 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:51,983 [INFO] task_framework  Success: StandardizerTask(root_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250828.zip, target_path: C:\\data\\sec\\automated\\_1_by_day\\_2_standardized_by_stmt\\20250828.zip)\n", "2025-09-08 16:11:51,983 [INFO] standardize_process  create standardized BS dataset\n", "2025-09-08 16:11:51,999 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:52,035 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:52,318 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:52,350 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:52,502 [INFO] standardize_process  create standardized IS dataset\n", "2025-09-08 16:11:52,522 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:52,552 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:53,271 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:53,299 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:53,488 [INFO] standardize_process  create standardized CF dataset\n", "2025-09-08 16:11:53,511 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:53,532 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:53,614 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:53,663 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:53,827 [INFO] task_framework  Success: StandardizerTask(root_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250902.zip, target_path: C:\\data\\sec\\automated\\_1_by_day\\_2_standardized_by_stmt\\20250902.zip)\n", "2025-09-08 16:11:53,833 [INFO] standardize_process  create standardized BS dataset\n", "2025-09-08 16:11:53,860 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:53,890 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:54,218 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:54,239 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:54,405 [INFO] standardize_process  create standardized IS dataset\n", "2025-09-08 16:11:54,426 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:54,450 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:55,150 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:55,185 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:55,379 [INFO] standardize_process  create standardized CF dataset\n", "2025-09-08 16:11:55,402 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:55,448 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:55,508 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:55,553 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:55,694 [INFO] task_framework  Success: StandardizerTask(root_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250903.zip, target_path: C:\\data\\sec\\automated\\_1_by_day\\_2_standardized_by_stmt\\20250903.zip)\n", "2025-09-08 16:11:55,700 [INFO] standardize_process  create standardized BS dataset\n", "2025-09-08 16:11:55,719 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:55,741 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:55,993 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:56,018 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:56,176 [INFO] standardize_process  create standardized IS dataset\n", "2025-09-08 16:11:56,198 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:56,231 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:56,937 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:56,971 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:57,174 [INFO] standardize_process  create standardized CF dataset\n", "2025-09-08 16:11:57,195 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:57,252 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:57,322 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:57,367 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:57,525 [INFO] task_framework  Success: StandardizerTask(root_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250821.zip, target_path: C:\\data\\sec\\automated\\_1_by_day\\_2_standardized_by_stmt\\20250821.zip)\n", "2025-09-08 16:11:57,528 [INFO] standardize_process  create standardized BS dataset\n", "2025-09-08 16:11:57,555 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:57,578 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:57,873 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:57,894 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:58,048 [INFO] standardize_process  create standardized IS dataset\n", "2025-09-08 16:11:58,084 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:58,111 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:58,867 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:58,924 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:59,142 [INFO] standardize_process  create standardized CF dataset\n", "2025-09-08 16:11:59,173 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:59,227 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:59,344 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:59,432 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:11:59,586 [INFO] task_framework  Success: StandardizerTask(root_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250815.zip, target_path: C:\\data\\sec\\automated\\_1_by_day\\_2_standardized_by_stmt\\20250815.zip)\n", "2025-09-08 16:11:59,592 [INFO] standardize_process  create standardized BS dataset\n", "2025-09-08 16:11:59,612 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:11:59,644 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:11:59,939 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:11:59,965 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:12:00,149 [INFO] standardize_process  create standardized IS dataset\n", "2025-09-08 16:12:00,173 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:12:00,206 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:12:00,946 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:12:00,977 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:12:01,175 [INFO] standardize_process  create standardized CF dataset\n", "2025-09-08 16:12:01,198 [INFO] standardizing  start PRE processing ...\n", "2025-09-08 16:12:01,245 [INFO] standardizing  start MAIN processing ...\n", "2025-09-08 16:12:01,315 [INFO] standardizing  start POST processing ...\n", "2025-09-08 16:12:01,368 [INFO] standardizing  start FINALIZE ...\n", "2025-09-08 16:12:01,525 [INFO] task_framework  Success: StandardizerTask(root_path: C:\\data\\sec\\automated\\_1_by_day\\_1_filtered_joined_by_stmt\\daily\\20250822.zip, target_path: C:\\data\\sec\\automated\\_1_by_day\\_2_standardized_by_stmt\\20250822.zip)\n", "2025-09-08 16:12:01,526 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:12:01,528 [INFO] task_framework  Starting process ConcatByChangedTimestampProcess\n", "2025-09-08 16:12:01,725 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:12:01,727 [INFO] pipeline_utils  Concat on filesystem - number of paths: 47 - target: C:\\data\\sec\\automated\\_2_all_day\\_1_joined_by_stmt\\tmp_BS\n", "2025-09-08 16:12:03,126 [INFO] task_framework  Success: ConcatIfChangedTimestampTask(root_paths: [WindowsPath('C:/data/sec/automated/_1_by_day/_1_filtered_joined_by_stmt/daily')],pathfilter: */BS)\n", "2025-09-08 16:12:03,128 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:12:03,269 [INFO] task_framework  Starting process ConcatByChangedTimestampProcess\n", "2025-09-08 16:12:03,396 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:12:03,396 [INFO] pipeline_utils  Concat on filesystem - number of paths: 47 - target: C:\\data\\sec\\automated\\_2_all_day\\_1_joined_by_stmt\\tmp_CF\n", "2025-09-08 16:12:05,008 [INFO] task_framework  Success: ConcatIfChangedTimestampTask(root_paths: [WindowsPath('C:/data/sec/automated/_1_by_day/_1_filtered_joined_by_stmt/daily')],pathfilter: */CF)\n", "2025-09-08 16:12:05,011 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:12:05,138 [INFO] task_framework  Starting process ConcatByChangedTimestampProcess\n", "2025-09-08 16:12:05,254 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:12:05,256 [INFO] pipeline_utils  Concat on filesystem - number of paths: 47 - target: C:\\data\\sec\\automated\\_2_all_day\\_1_joined_by_stmt\\tmp_CI\n", "2025-09-08 16:12:06,537 [INFO] task_framework  Success: ConcatIfChangedTimestampTask(root_paths: [WindowsPath('C:/data/sec/automated/_1_by_day/_1_filtered_joined_by_stmt/daily')],pathfilter: */CI)\n", "2025-09-08 16:12:06,538 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:12:06,662 [INFO] task_framework  Starting process ConcatByChangedTimestampProcess\n", "2025-09-08 16:12:06,828 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:12:06,831 [INFO] pipeline_utils  Concat on filesystem - number of paths: 47 - target: C:\\data\\sec\\automated\\_2_all_day\\_1_joined_by_stmt\\tmp_CP\n", "2025-09-08 16:12:08,107 [INFO] task_framework  Success: ConcatIfChangedTimestampTask(root_paths: [WindowsPath('C:/data/sec/automated/_1_by_day/_1_filtered_joined_by_stmt/daily')],pathfilter: */CP)\n", "2025-09-08 16:12:08,109 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:12:08,227 [INFO] task_framework  Starting process ConcatByChangedTimestampProcess\n", "2025-09-08 16:12:08,355 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:12:08,357 [INFO] pipeline_utils  Concat on filesystem - number of paths: 47 - target: C:\\data\\sec\\automated\\_2_all_day\\_1_joined_by_stmt\\tmp_EQ\n", "2025-09-08 16:12:09,732 [INFO] task_framework  Success: ConcatIfChangedTimestampTask(root_paths: [WindowsPath('C:/data/sec/automated/_1_by_day/_1_filtered_joined_by_stmt/daily')],pathfilter: */EQ)\n", "2025-09-08 16:12:09,734 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:12:09,851 [INFO] task_framework  Starting process ConcatByChangedTimestampProcess\n", "2025-09-08 16:12:09,982 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:12:09,984 [INFO] pipeline_utils  Concat on filesystem - number of paths: 47 - target: C:\\data\\sec\\automated\\_2_all_day\\_1_joined_by_stmt\\tmp_IS\n", "2025-09-08 16:12:11,291 [INFO] task_framework  Success: ConcatIfChangedTimestampTask(root_paths: [WindowsPath('C:/data/sec/automated/_1_by_day/_1_filtered_joined_by_stmt/daily')],pathfilter: */IS)\n", "2025-09-08 16:12:11,293 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:12:11,418 [INFO] task_framework  Starting process ConcatByChangedTimestampProcess\n", "2025-09-08 16:12:11,433 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:12:11,435 [INFO] pipeline_utils  Concat on filesystem - number of paths: 6 - target: C:\\data\\sec\\automated\\_2_all_day\\tmp__2_joined\n", "2025-09-08 16:12:12,341 [INFO] task_framework  Success: ConcatIfChangedTimestampTask(root_paths: [WindowsPath('C:/data/sec/automated/_2_all_day/_1_joined_by_stmt')],pathfilter: *)\n", "2025-09-08 16:12:12,341 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:12:12,357 [INFO] task_framework  Starting process ConcatByNewSubfoldersProcess\n", "2025-09-08 16:12:12,374 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:12:12,391 [INFO] pipeline_utils  Concat in memory - number of paths: 16 - target: C:\\data\\sec\\automated\\_2_all_day\\_3_standardized_by_stmt\\tmp_BS\n", "2025-09-08 16:12:13,821 [INFO] task_framework  Success: ConcatIfNewSubfolderTask(root_path: C:\\data\\sec\\automated\\_1_by_day\\_2_standardized_by_stmt, pathfilter: */BS)\n", "2025-09-08 16:12:13,822 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:12:13,835 [INFO] task_framework  Starting process ConcatByNewSubfoldersProcess\n", "2025-09-08 16:12:13,840 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:12:13,840 [INFO] pipeline_utils  Concat in memory - number of paths: 16 - target: C:\\data\\sec\\automated\\_2_all_day\\_3_standardized_by_stmt\\tmp_CF\n", "2025-09-08 16:12:15,706 [INFO] task_framework  Success: ConcatIfNewSubfolderTask(root_path: C:\\data\\sec\\automated\\_1_by_day\\_2_standardized_by_stmt, pathfilter: */CF)\n", "2025-09-08 16:12:15,706 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:12:15,725 [INFO] task_framework  Starting process ConcatByNewSubfoldersProcess\n", "2025-09-08 16:12:15,735 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:12:15,737 [INFO] pipeline_utils  Concat in memory - number of paths: 16 - target: C:\\data\\sec\\automated\\_2_all_day\\_3_standardized_by_stmt\\tmp_IS\n", "2025-09-08 16:12:18,035 [INFO] task_framework  Success: ConcatIfNewSubfolderTask(root_path: C:\\data\\sec\\automated\\_1_by_day\\_2_standardized_by_stmt, pathfilter: */IS)\n", "2025-09-08 16:12:18,037 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:12:18,044 [INFO] task_framework  \n", "2025-09-08 16:12:18,045 [INFO] task_framework  #####################################################################\n", "2025-09-08 16:12:18,046 [INFO] task_framework  Post Update Processes To Combine Quarterly And Daily Data Started\n", "2025-09-08 16:12:18,047 [INFO] task_framework  #####################################################################\n", "2025-09-08 16:12:18,047 [INFO] task_framework  Starting process ConcatMultiRootByChangedTimestampProcess\n", "2025-09-08 16:12:18,059 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:12:18,061 [INFO] pipeline_utils  Concat on filesystem - number of paths: 2 - target: C:\\data\\sec\\automated\\_3_all\\_1_joined_by_stmt\\tmp_BS\n", "2025-09-08 16:12:46,665 [INFO] task_framework  Success: ConcatIfChangedTimestampTask(root_paths: [WindowsPath('C:/data/sec/automated/_2_all_quarter/_1_joined_by_stmt'), WindowsPath('C:/data/sec/automated/_2_all_day/_1_joined_by_stmt')],pathfilter: BS)\n", "2025-09-08 16:12:46,667 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:12:46,678 [INFO] task_framework  Starting process ConcatMultiRootByChangedTimestampProcess\n", "2025-09-08 16:12:46,688 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:12:46,690 [INFO] pipeline_utils  Concat on filesystem - number of paths: 2 - target: C:\\data\\sec\\automated\\_3_all\\_1_joined_by_stmt\\tmp_CF\n", "2025-09-08 16:13:05,451 [INFO] task_framework  Success: ConcatIfChangedTimestampTask(root_paths: [WindowsPath('C:/data/sec/automated/_2_all_quarter/_1_joined_by_stmt'), WindowsPath('C:/data/sec/automated/_2_all_day/_1_joined_by_stmt')],pathfilter: CF)\n", "2025-09-08 16:13:05,452 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:13:05,462 [INFO] task_framework  Starting process ConcatMultiRootByChangedTimestampProcess\n", "2025-09-08 16:13:05,468 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:13:05,474 [INFO] pipeline_utils  Concat on filesystem - number of paths: 2 - target: C:\\data\\sec\\automated\\_3_all\\_1_joined_by_stmt\\tmp_CI\n", "2025-09-08 16:13:11,955 [INFO] task_framework  Success: ConcatIfChangedTimestampTask(root_paths: [WindowsPath('C:/data/sec/automated/_2_all_quarter/_1_joined_by_stmt'), WindowsPath('C:/data/sec/automated/_2_all_day/_1_joined_by_stmt')],pathfilter: CI)\n", "2025-09-08 16:13:11,957 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:13:11,970 [INFO] task_framework  Starting process ConcatMultiRootByChangedTimestampProcess\n", "2025-09-08 16:13:11,979 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:13:11,981 [INFO] pipeline_utils  Concat on filesystem - number of paths: 2 - target: C:\\data\\sec\\automated\\_3_all\\_1_joined_by_stmt\\tmp_CP\n", "2025-09-08 16:13:15,986 [INFO] task_framework  Success: ConcatIfChangedTimestampTask(root_paths: [WindowsPath('C:/data/sec/automated/_2_all_quarter/_1_joined_by_stmt'), WindowsPath('C:/data/sec/automated/_2_all_day/_1_joined_by_stmt')],pathfilter: CP)\n", "2025-09-08 16:13:15,988 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:13:15,999 [INFO] task_framework  Starting process ConcatMultiRootByChangedTimestampProcess\n", "2025-09-08 16:13:16,014 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:13:16,016 [INFO] pipeline_utils  Concat on filesystem - number of paths: 2 - target: C:\\data\\sec\\automated\\_3_all\\_1_joined_by_stmt\\tmp_EQ\n", "2025-09-08 16:13:31,072 [INFO] task_framework  Success: ConcatIfChangedTimestampTask(root_paths: [WindowsPath('C:/data/sec/automated/_2_all_quarter/_1_joined_by_stmt'), WindowsPath('C:/data/sec/automated/_2_all_day/_1_joined_by_stmt')],pathfilter: EQ)\n", "2025-09-08 16:13:31,073 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:13:31,073 [INFO] task_framework  Starting process ConcatMultiRootByChangedTimestampProcess\n", "2025-09-08 16:13:31,092 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:13:31,094 [INFO] pipeline_utils  Concat on filesystem - number of paths: 2 - target: C:\\data\\sec\\automated\\_3_all\\_1_joined_by_stmt\\tmp_IS\n", "2025-09-08 16:13:54,364 [INFO] task_framework  Success: ConcatIfChangedTimestampTask(root_paths: [WindowsPath('C:/data/sec/automated/_2_all_quarter/_1_joined_by_stmt'), WindowsPath('C:/data/sec/automated/_2_all_day/_1_joined_by_stmt')],pathfilter: IS)\n", "2025-09-08 16:13:54,366 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:13:54,376 [INFO] task_framework  Starting process ConcatMultiRootByChangedTimestampProcess\n", "2025-09-08 16:13:54,378 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:13:54,378 [INFO] pipeline_utils  Concat on filesystem - number of paths: 2 - target: C:\\data\\sec\\automated\\_3_all\\tmp__2_joined\n", "2025-09-08 16:15:10,516 [INFO] task_framework  Success: ConcatIfChangedTimestampTask(root_paths: [WindowsPath('C:/data/sec/automated/_2_all_day/_2_joined'), WindowsPath('C:/data/sec/automated/_2_all_quarter/_2_joined')],pathfilter: )\n", "2025-09-08 16:15:10,518 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:15:10,535 [INFO] task_framework  Starting process ConcatMultiRootByChangedTimestampProcess\n", "2025-09-08 16:15:10,566 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:15:10,569 [INFO] pipeline_utils  Concat in memory - number of paths: 2 - target: C:\\data\\sec\\automated\\_3_all\\_3_standardized_by_stmt\\tmp_BS\n", "2025-09-08 16:15:12,630 [INFO] task_framework  Success: ConcatIfChangedTimestampTask(root_paths: [WindowsPath('C:/data/sec/automated/_2_all_day/_3_standardized_by_stmt'), WindowsPath('C:/data/sec/automated/_2_all_quarter/_3_standardized_by_stmt')],pathfilter: BS)\n", "2025-09-08 16:15:12,632 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:15:12,647 [INFO] task_framework  Starting process ConcatMultiRootByChangedTimestampProcess\n", "2025-09-08 16:15:12,656 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:15:12,658 [INFO] pipeline_utils  Concat in memory - number of paths: 2 - target: C:\\data\\sec\\automated\\_3_all\\_3_standardized_by_stmt\\tmp_CF\n", "2025-09-08 16:15:20,609 [INFO] task_framework  Success: ConcatIfChangedTimestampTask(root_paths: [WindowsPath('C:/data/sec/automated/_2_all_day/_3_standardized_by_stmt'), WindowsPath('C:/data/sec/automated/_2_all_quarter/_3_standardized_by_stmt')],pathfilter: CF)\n", "2025-09-08 16:15:20,611 [INFO] parallelexecution      commited chunk: 0\n", "2025-09-08 16:15:20,618 [INFO] task_framework  Starting process ConcatMultiRootByChangedTimestampProcess\n", "2025-09-08 16:15:20,628 [INFO] parallelexecution      items to process: 1\n", "2025-09-08 16:15:20,630 [INFO] pipeline_utils  Concat in memory - number of paths: 2 - target: C:\\data\\sec\\automated\\_3_all\\_3_standardized_by_stmt\\tmp_IS\n", "2025-09-08 16:15:34,364 [INFO] task_framework  Success: ConcatIfChangedTimestampTask(root_paths: [WindowsPath('C:/data/sec/automated/_2_all_day/_3_standardized_by_stmt'), WindowsPath('C:/data/sec/automated/_2_all_quarter/_3_standardized_by_stmt')],pathfilter: IS)\n", "2025-09-08 16:15:34,366 [INFO] parallelexecution      commited chunk: 0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[33m------------------------------------------------------------------------------\u001b[0m\n", "\u001b[1m\u001b[33m##############################################################################\u001b[0m\n", "\n", "\n", "\u001b[1m\u001b[37m    Support the developer behind secfsdstools – consider sponsoring today.    \u001b[0m\n", "\n", "\n", "\u001b[1m\u001b[37m    https://github.com/sponsors/HansjoergW\u001b[0m\n", "\n", "\n", "\u001b[37m    How to get in touch\n", "    - Found a bug:            https://github.com/HansjoergW/sec-fincancial-statement-data-set/issues\n", "    - Have a remark:          https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/general\n", "    - Have an idea:           https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/ideas\n", "    - Have a question:        https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/q-a\n", "    - Have something to show: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/show-and-tell\n", "\n", "\n", "\u001b[1m\u001b[33m##############################################################################\u001b[0m\n", "\u001b[33m------------------------------------------------------------------------------\u001b[0m\n", "\n", "\n", "\n"]}], "source": ["# imports from the secfsdstools package\n", "from secfsdstools.c_index.searching import IndexSearch\n", "from secfsdstools.c_index.companyindexreading import CompanyIndexReader\n", "from secfsdstools.e_collector.reportcollecting import SingleReportCollector\n", "from secfsdstools.e_filter.rawfiltering import ReportPeriodRawFilter, StmtRawFilter\n", "from secfsdstools.e_presenter.presenting import StandardStatementPresenter"]}, {"cell_type": "code", "execution_count": 3, "id": "7e65bb4e-ae08-4264-a26c-03e789c6ddbe", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-09-08 16:15:34,541 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n"]}], "source": ["# initialize the search class\n", "search = IndexSearch.get_index_search()\n", "\n", "# create a list with all known forms\n", "forms_list = sorted(search.dbaccessor.read_all_indexreports_df().form.unique().tolist())\n", "stmt_list = ['BS', 'CF', 'CI', 'CP', 'EQ', 'IS', 'SI', 'UN']"]}, {"cell_type": "markdown", "id": "9621bee7-5312-4d4e-8448-1616d182f2fc", "metadata": {}, "source": ["## Finding the CIK for a company\n", "The first interactive cell lets you search for a companies cik number by name.\n", "For instance, just start to type apple."]}, {"cell_type": "code", "execution_count": 7, "id": "7af46857-312c-4ce1-b3d1-574587707a58", "metadata": {"tags": []}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ac12d85f5e1441c288b1b87655b44dde", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(Text(value='', description='search_string'), Output()), _dom_classes=('widget-interact',…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# a simple way to find the cik for a company.\n", "# e.g., start typing apple and watch the list get filtered\n", "@interact(search_string=widgets.Text(value=''))\n", "def search_cik(search_string):\n", "    print(search_string)\n", "    result_df = search.find_company_by_name(search_string)\n", "    display(result_df)\n", "    if len(result_df) > 0:\n", "        print(result_df.cik.tolist()[0])"]}, {"cell_type": "markdown", "id": "00f2e324-f67d-438d-9816-3742f0011abd", "metadata": {"tags": []}, "source": ["## Finding reports for a cik number\n", "The next cell displays a list of availale reports for a company.<br>\n", "First, enter the cik into the cik field. E.g., use apple's cik '320193'.<br>\n", "Then chose the report types you want to filter for. The 10-K and 10-Q is preselected (annual and quarterly reports).<br>\n", "Use the rows dropdown to configure how many rows shall be displayed."]}, {"cell_type": "code", "execution_count": 4, "id": "07e40670-8598-47a0-a41a-b195b5b34150", "metadata": {"tags": []}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9b55b42c310f44bcaa0720c2b550c7fb", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(Text(value='0', description='cik'), SelectMultiple(description='forms', index=(4, 8), op…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# using a cik number, you can filter for all filed reports. Choose the type of report you want to see by selecting the entries in the forms widget.\n", "# e.g., use apples cik '320193' to see an overview of the reports that have were filed by apple\n", "\n", "from secfsdstools.e_collector.multireportcollecting import MultiReportCollector\n", "from secfsdstools.e_filter.joinedfiltering import StmtJoinedFilter\n", "from secfsdstools.u_usecases.bulk_loading import default_postloadfilter\n", "from secfsdstools.f_standardize.bs_standardize import BalanceSheetStandardizer\n", "from secfsdstools.f_standardize.is_standardize import IncomeStatementStandardizer\n", "from secfsdstools.f_standardize.cf_standardize import CashFlowStandardizer\n", "\n", "bs_standardizer = BalanceSheetStandardizer()\n", "is_standardizer = IncomeStatementStandardizer()\n", "cf_standardizer = CashFlowStandardizer()\n", "\n", "@interact\n", "def reports(cik=widgets.Text(value='0'), forms=widgets.SelectMultiple(options=forms_list, rows=6, value=['10-K', '10-Q']), rows=[10, 25, 50, 100], is_show_single_qtrs=widgets.Checkbox(value=False)):\n", "    \"\"\"\n", "    is_show_single_qtrs: Show also single quarter results in income statements for Q2, Q3, and FY. \n", "    \"\"\"\n", "    \n", "    reader = CompanyIndexReader.get_company_index_reader(cik=int(cik))\n", "    reports = reader.get_all_company_reports_df(forms=list(forms))\n", "    \n", "    if len(reports) == 0:\n", "        return\n", "    display(Markdown(\"## List of filings\"))\n", "    display(reports.head(rows))\n", "    display(Markdown(\"<br>\"))\n", "    # display the data of the report\n", "    display(Markdown(\"## Financial Statements \"))\n", "    display(Markdown(\"**_Loading Report Details and standardize them ..._** \"))\n", "    \n", "    collector: MultiReportCollector = \\\n", "        MultiReportCollector.get_reports_by_adshs(adshs=reports.adsh.tolist(), stmt_filter=['BS', 'IS', 'CF'])\n", "    joined_df = default_postloadfilter(collector.collect()).join()\n", "    \n", "    bs_joined_df = joined_df[StmtJoinedFilter(stmts=['BS'])]\n", "    is_joined_df = joined_df[StmtJoinedFilter(stmts=['IS'])]\n", "    cf_joined_df = joined_df[StmtJoinedFilter(stmts=['CF'])]\n", "    \n", "    # standardize the data\n", "    bs_standardized = bs_joined_df.present(bs_standardizer)\n", "    is_standardized = is_joined_df.present(is_standardizer)\n", "    cf_standardized = cf_joined_df.present(cf_standardizer)\n", "    \n", "    if not is_show_single_qtrs:\n", "        # only show qtrs = 1 for Q1, qtrs=2 for Q2, qtrs=3 for Q4, and qtrs=4 for FY\n", "        mask_q1_is = ((is_standardized.fp=='Q1') & (is_standardized.qtrs==1)) \n", "        mask_q2_is = ((is_standardized.fp=='Q2') & (is_standardized.qtrs==2)) \n", "        mask_q3_is = ((is_standardized.fp=='Q3') & (is_standardized.qtrs==3)) \n", "        mask_fy_is = ((is_standardized.fp=='FY') & (is_standardized.qtrs==4))\n", "        is_standardized = is_standardized[mask_q1_is | mask_q2_is | mask_q3_is | mask_fy_is]    \n", "\n", "        mask_q1_cf = ((cf_standardized.fp=='Q1') & (cf_standardized.qtrs==1)) \n", "        mask_q2_cf = ((cf_standardized.fp=='Q2') & (cf_standardized.qtrs==2)) \n", "        mask_q3_cf = ((cf_standardized.fp=='Q3') & (cf_standardized.qtrs==3)) \n", "        mask_fy_cf = ((cf_standardized.fp=='FY') & (cf_standardized.qtrs==4))\n", "        cf_standardized = cf_standardized[mask_q1_cf | mask_q2_cf | mask_q3_cf | mask_fy_cf]    \n", "\n", "    display(Markdown(\"## Balance Sheets overview ... \"))\n", "    # drop check error columns\n", "    cols = [x for x in bs_standardized.columns.tolist() if not x.endswith('error')]\n", "    display(bs_standardized[cols])\n", "    \n", "    display(Markdown(\"## Income Statements overview ... \"))\n", "    # drop check error columns\n", "    cols = [x for x in is_standardized.columns.tolist() if not x.endswith('error')]\n", "    display(is_standardized[cols])\n", "\n", "    display(Markdown(\"## Cash Flow overview ... \"))\n", "    # drop check error columns\n", "    cols = [x for x in cf_standardized.columns.tolist() if not x.endswith('error')]\n", "    display(cf_standardized[cols])"]}, {"cell_type": "markdown", "id": "e38e3982-dbc6-4fad-8ea8-27d4823a44db", "metadata": {}, "source": ["## Showing the details of a report\n", "Now we are ready to show the details of a report. <br>\n", "<br>\n", "Therefore, enter the report id in the adsh field. E.g. use '0000320193-22-000108' for the annual report of 2022 from apple.<br>\n", "Use the stmts list to configure which which statements data should be shown. Per default, BS (balance sheet), IS (income statement), and CF (cash flow are activated). <br>\n", "Select the number of displayed rows with the rows drop-down box.<br>\n", "Sometimes, data is shown as positive even if the value is actually negative (or vice-versa). This is indicating by the negating flag. This is often the case in CF statements. There is a checkbox 'invert_negated' which switch the sign of number, if the have a value of one in the negating column. To see the effect, select only the CF stmt from the '0000320193-22-000108' report and swith the 'invert_negated' checkbox.<br><br>\n", "**Note how fast the data is reloaded if you change the settings or display another report**  This is due to the fact, that we use the parquet format and a simple index.<br>\n", "**Moreover, the data is sorted in the correct order as displayed in the original report.** Just click on the url in the 'Basic Information' to open the filed report directly at sec.gov and compare it."]}, {"cell_type": "code", "execution_count": 5, "id": "ae10ee12-4208-4b29-8869-16c34e7095ef", "metadata": {"tags": []}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bd2197f67cfe4fcc99362d48bd9b1a15", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(Text(value='0', description='adsh'), SelectMultiple(description='stmts', index=(0, 5, 1)…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from secfsdstools.c_index.indexing_process import IndexingTask\n", "from secfsdstools.u_usecases.bulk_loading import default_postloadfilter\n", "from secfsdstools.e_filter.joinedfiltering import StmtJoinedFilter\n", "from secfsdstools.f_standardize.bs_standardize import BalanceSheetStandardizer\n", "from secfsdstools.f_standardize.is_standardize import IncomeStatementStandardizer\n", "from secfsdstools.f_standardize.cf_standardize import CashFlowStandardizer\n", "\n", "bs_standardizer = BalanceSheetStandardizer()\n", "is_standardizer = IncomeStatementStandardizer()\n", "cf_standardizer = CashFlowStandardizer()\n", "\n", "@interact\n", "def reports(adsh=widgets.Text(value='0'), stmts=widgets.SelectMultiple(options=stmt_list, rows=6, value=['BS', 'IS', 'CF']), rows=[50, 100, 200], invert_negated=widgets.Checkbox(), show_segments=widgets.Checkbox(value=True)):\n", "    if adsh=='0':\n", "        display('Nothing selected')\n", "        return\n", "    display('loading...')\n", "    reader = SingleReportCollector.get_report_by_adsh(adsh=adsh, stmt_filter=stmts)\n", "    \n", "    raw_data = reader.collect()\n", "    if len(raw_data.sub_df) == 0:\n", "        display(f'No data found for {adsh}')\n", "        return        \n", "    \n", "    display(f'invert negated: {invert_negated}')\n", "    display(f'show segments: {show_segments}')\n", "\n", "    display('Loading data... may take a few seconds')    \n", "    \n", "    filterd_data = raw_data.filter(ReportPeriodRawFilter())\n", "    \n", "    raw_stmts_data = filterd_data.filter(StmtRawFilter(stmts=stmt_list))\n", "\n", "    joined_df = filterd_data.join()\n", "    report_data = joined_df.present(StandardStatementPresenter(invert_negating=invert_negated, show_segments=show_segments))\n", "            \n", "    # get some key infos of the report\n", "    submission_data = {k:v for k,v in raw_data.sub_df.loc[0].to_dict().items() if k in ['cik', 'adsh', 'name', 'cityba', 'form', 'period', 'filed']}\n", "\n", "    # create and display the url on which the report is published on sec.gov, so that it can directly be opened    \n", "    url = IndexingTask.URL_PREFIX + str(submission_data['cik']) + '/' + submission_data['adsh'].replace('-','') + '/' + submission_data['adsh'] + '-index.htm'\n", "    display(Markdown(\"## Basic Information\"))\n", "    display(url)\n", "\n", "    # display the key submission data of the report\n", "    display(submission_data)    \n", "    \n", "    display(Markdown(\"<br>\"))\n", "    \n", "    # display the data of the report\n", "    display(Markdown(\"## Details\"))\n", "    display(report_data.head(rows))\n", "    \n", "    display(Markdown(\"<br>\"))\n", "    display(Markdown(\"<br>\"))\n", "    \n", "    # loading stardized view of BS, IS\n", "    std_joined_df = default_postloadfilter(raw_stmts_data).join()\n", "    \n", "    # standardize the data\n", "    if 'BS' in stmts:\n", "        bs_joined_df = std_joined_df[StmtJoinedFilter(stmts=['BS'])]\n", "        bs_standardized = bs_joined_df.present(bs_standardizer)\n", "        display(Markdown(\"## Standardized Balance Sheets overview ... \"))\n", "        # drop check error columns\n", "        cols = [x for x in bs_standardized.columns.tolist() if not x.endswith('error')]\n", "        display(bs_standardized[cols])\n", "    \n", "    if 'IS' in stmts:\n", "        is_joined_df = std_joined_df[StmtJoinedFilter(stmts=['IS'])]\n", "        is_standardized = is_joined_df.present(is_standardizer)\n", "        display(Markdown(\"## Standardized Income Statements overview ... \"))\n", "        # drop check error columns\n", "        cols = [x for x in is_standardized.columns.tolist() if not x.endswith('error')]\n", "        display(is_standardized[cols])\n", "\n", "    if 'CF' in stmts:\n", "        cf_joined_df = std_joined_df[StmtJoinedFilter(stmts=['CF'])]\n", "        cf_standardized = cf_joined_df.present(cf_standardizer)\n", "        display(Markdown(\"## Standardized Cash Flow overview ... \"))\n", "        # drop check error columns\n", "        cols = [x for x in cf_standardized.columns.tolist() if not x.endswith('error')]\n", "        display(cf_standardized[cols])\n", "        \n", "    # showing the raw data of the report\n", "    display(Markdown(\"<br>\"))\n", "    display(Markdown(\"<br>\"))\n", "    display(Mark<PERSON>(\"## Raw\"))\n", "    pre_def_sorted = raw_stmts_data.pre_df.sort_values(['report', 'line'])\n", "    display(pre_def_sorted.head(rows))\n", "    display(raw_stmts_data.num_df.head(rows))"]}, {"cell_type": "code", "execution_count": null, "id": "5197349b-83fc-4506-85aa-1a7c6e28669e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "markdown", "id": "609cb4c4-c697-4696-918f-e5e0c91e283c", "metadata": {"tags": []}, "source": ["<span style=\"color: #FF8C00;\">==========================================================</span>\n", "\n", "**If you find this tool useful, a sponsorship would be greatly appreciated!**\n", "\n", "**https://github.com/sponsors/HansjoergW**\n", "\n", "How to get in touch\n", "\n", "* Found a bug: https://github.com/HansjoergW/sec-fincancial-statement-data-set/issues\n", "* Have a remark: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/general\n", "* Have an idea: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/ideas\n", "* Have a question: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/q-a\n", "* Have something to show: https://github.com/HansjoergW/sec-fincancial-statement-data-set/discussions/categories/show-and-tell\n", "\n", "<span style=\"color: #FF8C00;\">==========================================================</span>"]}, {"cell_type": "markdown", "id": "9cafe8ec-7214-4e4d-9bec-39ee5183bbac", "metadata": {}, "source": ["Note: for using the widgets inside jupyterlab, you need an appropriate environment with nodejs and the widegetextensions install. \n", "In order to develop the notebook, the following environment was used:\n", "\n", "- a new \"empty\" python 3.10 environment, for instance created with conda \n", "  - conda create -n secanalyzing python==3.10\n", "  - conda activate secanalyzing\n", "- pip install jupyterlab\n", "- jupyter labextension install @jupyter-widgets/jupyterlab-manager\n", "- pip install secfsdstools\n", "- pip install ipywidgets"]}, {"cell_type": "code", "execution_count": 1, "id": "1ae9d9be-6d78-48a3-bbda-a3383e6064dc", "metadata": {"ExecuteTime": {"end_time": "2024-04-06T04:51:33.933729Z", "start_time": "2024-04-06T04:51:33.791110Z"}, "tags": []}, "outputs": [], "source": ["# Basic import to support interactive widgets in notebooks\n", "import ipywidgets as widgets\n", "from IPython.display import display, Markdown\n", "from ipywidgets import interact, interact_manual\n", "\n", "import pandas as pd\n", "pd.set_option('display.max_rows', 500) # ensure that all rows are shown\n", "pd.set_option('display.max_colwidth', None)\n", "pd.set_option('display.max_columns', None)"]}, {"cell_type": "code", "execution_count": 2, "id": "c20dcfbf-13f7-4519-9649-dbfdf96b32d0", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-12 07:21:31,887 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n"]}], "source": ["# imports from the secfsdstools package\n", "from secfsdstools.c_index.searching import IndexSearch\n", "from secfsdstools.c_index.companyindexreading import CompanyIndexReader\n", "from secfsdstools.e_collector.reportcollecting import SingleReportCollector\n", "from secfsdstools.e_filter.rawfiltering import ReportPeriodRawFilter, StmtRawFilter\n", "from secfsdstools.e_presenter.presenting import StandardStatementPresenter"]}, {"cell_type": "code", "execution_count": 3, "id": "7e65bb4e-ae08-4264-a26c-03e789c6ddbe", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-12 07:21:33,367 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n"]}], "source": ["# initialize the search class\n", "search = IndexSearch.get_index_search()\n", "\n", "# create a list with all known forms\n", "forms_list = sorted(search.dbaccessor.read_all_indexreports_df().form.unique().tolist())\n", "stmt_list = ['BS', 'CF', 'CI', 'CP', 'EQ', 'IS', 'SI', 'UN']"]}, {"cell_type": "markdown", "id": "9621bee7-5312-4d4e-8448-1616d182f2fc", "metadata": {}, "source": ["## Finding the CIK for a company\n", "The first interactive cell lets you search for a companies cik number by name.\n", "For instance, just start to type apple."]}, {"cell_type": "code", "execution_count": 4, "id": "7af46857-312c-4ce1-b3d1-574587707a58", "metadata": {"tags": []}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ffa1da1eb3cb4c59a9a6cfa667787d4a", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(Text(value='', description='search_string'), Output()), _dom_classes=('widget-interact',…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# a simple way to find the cik for a company.\n", "# e.g., start typing apple and watch the list get filtered\n", "@interact(search_string=widgets.Text(value=''))\n", "def search_cik(search_string):\n", "    print(search_string)\n", "    result_df = search.find_company_by_name(search_string)\n", "    display(result_df)\n", "    if len(result_df) > 0:\n", "        print(result_df.cik.tolist()[0])"]}, {"cell_type": "markdown", "id": "00f2e324-f67d-438d-9816-3742f0011abd", "metadata": {"tags": []}, "source": ["## Finding reports for a cik number\n", "The next cell displays a list of availale reports for a company.<br>\n", "First, enter the cik into the cik field. E.g., use apple's cik '320193'.<br>\n", "Then chose the report types you want to filter for. The 10-K and 10-Q is preselected (annual and quarterly reports).<br>\n", "Use the rows dropdown to configure how many rows shall be displayed."]}, {"cell_type": "code", "execution_count": 6, "id": "07e40670-8598-47a0-a41a-b195b5b34150", "metadata": {"tags": []}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "88cae699e08e4c9db9467eaf74b9aadd", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(Text(value='0', description='cik'), SelectMultiple(description='forms', index=(4, 8), op…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# using a cik number, you can filter for all filed reports. Choose the type of report you want to see by selecting the entries in the forms widget.\n", "# e.g., use apples cik '320193' to see an overview of the reports that have were filed by apple\n", "\n", "from secfsdstools.e_collector.multireportcollecting import MultiReportCollector\n", "from secfsdstools.e_filter.joinedfiltering import StmtJoinedFilter\n", "from secfsdstools.u_usecases.bulk_loading import default_postloadfilter\n", "from secfsdstools.f_standardize.bs_standardize import BalanceSheetStandardizer\n", "from secfsdstools.f_standardize.is_standardize import IncomeStatementStandardizer\n", "from secfsdstools.f_standardize.cf_standardize import CashFlowStandardizer\n", "\n", "bs_standardizer = BalanceSheetStandardizer()\n", "is_standardizer = IncomeStatementStandardizer()\n", "cf_standardizer = CashFlowStandardizer()\n", "\n", "@interact\n", "def reports(cik=widgets.Text(value='0'), forms=widgets.SelectMultiple(options=forms_list, rows=6, value=['10-K', '10-Q']), rows=[10, 25, 50, 100], is_show_single_qtrs=widgets.Checkbox(value=False)):\n", "    \"\"\"\n", "    is_show_single_qtrs: Show also single quarter results in income statements for Q2, Q3, and FY. \n", "    \"\"\"\n", "    \n", "    reader = CompanyIndexReader.get_company_index_reader(cik=int(cik))\n", "    reports = reader.get_all_company_reports_df(forms=list(forms))\n", "    \n", "    if len(reports) == 0:\n", "        return\n", "    display(Markdown(\"## List of filings\"))\n", "    display(reports.head(rows))\n", "    display(Markdown(\"<br>\"))\n", "    # display the data of the report\n", "    display(Markdown(\"## Financial Statements \"))\n", "    display(Markdown(\"**_Loading Report Details and standardize them ..._** \"))\n", "    \n", "    collector: MultiReportCollector = \\\n", "        MultiReportCollector.get_reports_by_adshs(adshs=reports.adsh.tolist(), stmt_filter=['BS', 'IS', 'CF'])\n", "    joined_df = default_postloadfilter(collector.collect()).join()\n", "    \n", "    bs_joined_df = joined_df[StmtJoinedFilter(stmts=['BS'])]\n", "    is_joined_df = joined_df[StmtJoinedFilter(stmts=['IS'])]\n", "    cf_joined_df = joined_df[StmtJoinedFilter(stmts=['CF'])]\n", "    \n", "    # standardize the data\n", "    bs_standardized = bs_joined_df.present(bs_standardizer)\n", "    is_standardized = is_joined_df.present(is_standardizer)\n", "    cf_standardized = cf_joined_df.present(cf_standardizer)\n", "    \n", "    if not is_show_single_qtrs:\n", "        # only show qtrs = 1 for Q1, qtrs=2 for Q2, qtrs=3 for Q4, and qtrs=4 for FY\n", "        mask_q1_is = ((is_standardized.fp=='Q1') & (is_standardized.qtrs==1)) \n", "        mask_q2_is = ((is_standardized.fp=='Q2') & (is_standardized.qtrs==2)) \n", "        mask_q3_is = ((is_standardized.fp=='Q3') & (is_standardized.qtrs==3)) \n", "        mask_fy_is = ((is_standardized.fp=='FY') & (is_standardized.qtrs==4))\n", "        is_standardized = is_standardized[mask_q1_is | mask_q2_is | mask_q3_is | mask_fy_is]    \n", "\n", "        mask_q1_cf = ((cf_standardized.fp=='Q1') & (cf_standardized.qtrs==1)) \n", "        mask_q2_cf = ((cf_standardized.fp=='Q2') & (cf_standardized.qtrs==2)) \n", "        mask_q3_cf = ((cf_standardized.fp=='Q3') & (cf_standardized.qtrs==3)) \n", "        mask_fy_cf = ((cf_standardized.fp=='FY') & (cf_standardized.qtrs==4))\n", "        cf_standardized = cf_standardized[mask_q1_cf | mask_q2_cf | mask_q3_cf | mask_fy_cf]    \n", "\n", "    display(Markdown(\"## Balance Sheets overview ... \"))\n", "    # drop check error columns\n", "    cols = [x for x in bs_standardized.columns.tolist() if not x.endswith('error')]\n", "    display(bs_standardized[cols])\n", "    \n", "    display(Markdown(\"## Income Statements overview ... \"))\n", "    # drop check error columns\n", "    cols = [x for x in is_standardized.columns.tolist() if not x.endswith('error')]\n", "    display(is_standardized[cols])\n", "\n", "    display(Markdown(\"## Cash Flow overview ... \"))\n", "    # drop check error columns\n", "    cols = [x for x in cf_standardized.columns.tolist() if not x.endswith('error')]\n", "    display(cf_standardized[cols])"]}, {"cell_type": "markdown", "id": "e38e3982-dbc6-4fad-8ea8-27d4823a44db", "metadata": {}, "source": ["## Showing the details of a report\n", "Now we are ready to show the details of a report. <br>\n", "<br>\n", "Therefore, enter the report id in the adsh field. E.g. use '0000320193-22-000108' for the annual report of 2022 from apple.<br>\n", "Use the stmts list to configure which which statements data should be shown. Per default, BS (balance sheet), IS (income statement), and CF (cash flow are activated). <br>\n", "Select the number of displayed rows with the rows drop-down box.<br>\n", "Sometimes, data is shown as positive even if the value is actually negative (or vice-versa). This is indicating by the negating flag. This is often the case in CF statements. There is a checkbox 'invert_negated' which switch the sign of number, if the have a value of one in the negating column. To see the effect, select only the CF stmt from the '0000320193-22-000108' report and swith the 'invert_negated' checkbox.<br><br>\n", "**Note how fast the data is reloaded if you change the settings or display another report**  This is due to the fact, that we use the parquet format and a simple index.<br>\n", "**Moreover, the data is sorted in the correct order as displayed in the original report.** Just click on the url in the 'Basic Information' to open the filed report directly at sec.gov and compare it."]}, {"cell_type": "code", "execution_count": 7, "id": "ae10ee12-4208-4b29-8869-16c34e7095ef", "metadata": {"tags": []}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "644194ba86d045f990bac528d2332be0", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(Text(value='0', description='adsh'), SelectMultiple(description='stmts', index=(0, 5, 1)…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from secfsdstools.c_index.indexing_process import IndexingTask\n", "from secfsdstools.u_usecases.bulk_loading import default_postloadfilter\n", "from secfsdstools.e_filter.joinedfiltering import StmtJoinedFilter\n", "from secfsdstools.f_standardize.bs_standardize import BalanceSheetStandardizer\n", "from secfsdstools.f_standardize.is_standardize import IncomeStatementStandardizer\n", "from secfsdstools.f_standardize.cf_standardize import CashFlowStandardizer\n", "\n", "bs_standardizer = BalanceSheetStandardizer()\n", "is_standardizer = IncomeStatementStandardizer()\n", "cf_standardizer = CashFlowStandardizer()\n", "\n", "@interact\n", "def reports(adsh=widgets.Text(value='0'), stmts=widgets.SelectMultiple(options=stmt_list, rows=6, value=['BS', 'IS', 'CF']), rows=[50, 100, 200], invert_negated=widgets.Checkbox(), show_segments=widgets.Checkbox(value=True)):\n", "    if adsh=='0':\n", "        display('Nothing selected')\n", "        return\n", "    display('loading...')\n", "    reader = SingleReportCollector.get_report_by_adsh(adsh=adsh, stmt_filter=stmts)\n", "    \n", "    raw_data = reader.collect()\n", "    if len(raw_data.sub_df) == 0:\n", "        display(f'No data found for {adsh}')\n", "        return        \n", "    \n", "    display(f'invert negated: {invert_negated}')\n", "    display(f'show segments: {show_segments}')\n", "\n", "    display('Loading data... may take a few seconds')    \n", "    \n", "    filterd_data = raw_data.filter(ReportPeriodRawFilter())\n", "    \n", "    raw_stmts_data = filterd_data.filter(StmtRawFilter(stmts=stmt_list))\n", "\n", "    joined_df = filterd_data.join()\n", "    report_data = joined_df.present(StandardStatementPresenter(invert_negating=invert_negated, show_segments=show_segments))\n", "            \n", "    # get some key infos of the report\n", "    submission_data = {k:v for k,v in raw_data.sub_df.loc[0].to_dict().items() if k in ['cik', 'adsh', 'name', 'cityba', 'form', 'period', 'filed']}\n", "\n", "    # create and display the url on which the report is published on sec.gov, so that it can directly be opened    \n", "    url = IndexingTask.URL_PREFIX + str(submission_data['cik']) + '/' + submission_data['adsh'].replace('-','') + '/' + submission_data['adsh'] + '-index.htm'\n", "    display(Markdown(\"## Basic Information\"))\n", "    display(url)\n", "\n", "    # display the key submission data of the report\n", "    display(submission_data)    \n", "    \n", "    display(Markdown(\"<br>\"))\n", "    \n", "    # display the data of the report\n", "    display(Markdown(\"## Details\"))\n", "    display(report_data.head(rows))\n", "    \n", "    display(Markdown(\"<br>\"))\n", "    display(Markdown(\"<br>\"))\n", "    \n", "    # loading stardized view of BS, IS\n", "    std_joined_df = default_postloadfilter(raw_stmts_data).join()\n", "    \n", "    # standardize the data\n", "    if 'BS' in stmts:\n", "        bs_joined_df = std_joined_df[StmtJoinedFilter(stmts=['BS'])]\n", "        bs_standardized = bs_joined_df.present(bs_standardizer)\n", "        display(Markdown(\"## Standardized Balance Sheets overview ... \"))\n", "        # drop check error columns\n", "        cols = [x for x in bs_standardized.columns.tolist() if not x.endswith('error')]\n", "        display(bs_standardized[cols])\n", "    \n", "    if 'IS' in stmts:\n", "        is_joined_df = std_joined_df[StmtJoinedFilter(stmts=['IS'])]\n", "        is_standardized = is_joined_df.present(is_standardizer)\n", "        display(Markdown(\"## Standardized Income Statements overview ... \"))\n", "        # drop check error columns\n", "        cols = [x for x in is_standardized.columns.tolist() if not x.endswith('error')]\n", "        display(is_standardized[cols])\n", "\n", "    if 'CF' in stmts:\n", "        cf_joined_df = std_joined_df[StmtJoinedFilter(stmts=['CF'])]\n", "        cf_standardized = cf_joined_df.present(cf_standardizer)\n", "        display(Markdown(\"## Standardized Cash Flow overview ... \"))\n", "        # drop check error columns\n", "        cols = [x for x in cf_standardized.columns.tolist() if not x.endswith('error')]\n", "        display(cf_standardized[cols])\n", "        \n", "    # showing the raw data of the report\n", "    display(Markdown(\"<br>\"))\n", "    display(Markdown(\"<br>\"))\n", "    display(Mark<PERSON>(\"## Raw\"))\n", "    pre_def_sorted = raw_stmts_data.pre_df.sort_values(['report', 'line'])\n", "    display(pre_def_sorted.head(rows))\n", "    display(raw_stmts_data.num_df.head(rows))"]}, {"cell_type": "code", "execution_count": null, "id": "5197349b-83fc-4506-85aa-1a7c6e28669e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}
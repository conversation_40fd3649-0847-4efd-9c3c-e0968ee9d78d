{"cells": [{"cell_type": "code", "execution_count": 1, "id": "969dd967-ba07-4966-9178-8efab2c71884", "metadata": {"tags": []}, "outputs": [], "source": ["# to ensure that the logging statements are shown in juypter output, run this cell\n", "import logging\n", "\n", "logger = logging.getLogger()\n", "logger.setLevel(logging.INFO)"]}, {"cell_type": "code", "execution_count": 2, "id": "f2e342a4-cf14-4986-8ae0-5fa78e29fc6e", "metadata": {"tags": []}, "outputs": [], "source": ["import pandas as pd\n", "# ensure that all columns are shown and that colum content is not cut\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.max_colwidth', None)\n", "pd.set_option('display.width',1000)"]}, {"cell_type": "code", "execution_count": 24, "id": "3fda2d05-1488-4826-9820-929b5ea25e32", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-08-07 06:34:30,159 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2024-08-07 06:34:30,173 [INFO] updateprocess  Check if new report zip files are available...\n", "2024-08-07 06:34:30,228 [INFO] updateprocess  check if there are new files to download from sec.gov ...\n", "2024-08-07 06:34:30,925 [INFO] updateprocess  start to transform to parquet format ...\n", "2024-08-07 06:34:30,938 [INFO] updateprocess  start to index parquet files ...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["No rapid-api-key is set: \n", "If you are interested in daily updates, please have a look at https://rapidapi.com/hansjoerg.wingeier/api/daily-sec-financial-statement-dataset\n"]}], "source": ["from secfsdstools.update import update\n", "\n", "update()"]}, {"cell_type": "code", "execution_count": 3, "id": "75107132-aed1-4c46-a54b-1faaaf5b6a5d", "metadata": {"tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-10 16:30:03,763 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n", "2025-02-10 16:30:04,435 [INFO] updateprocess  Launching data update process ...\n", "2025-02-10 16:30:04,463 [INFO] task_framework  Starting process SecDownloadingProcess\n", "2025-02-10 16:30:04,465 [INFO] secdownloading_process  reading table in main page: https://www.sec.gov/dera/data/financial-statement-data-sets.html\n", "2025-02-10 16:30:06,060 [INFO] task_framework  Starting process ToParquetTransformerProcess\n", "2025-02-10 16:30:06,065 [INFO] task_framework  Starting process ReportParquetIndexerProcess\n", "2025-02-10 16:30:06,104 [INFO] configmgt  reading configuration from C:\\Users\\<USER>\\.secfsdstools.cfg\n"]}], "source": ["from secfsdstools.e_collector.reportcollecting import SingleReportCollector\n", "from secfsdstools.e_filter.rawfiltering import ReportPeriodAndPreviousPeriodRawFilter\n", "from secfsdstools.e_presenter.presenting import StandardStatementPresenter\n", "\n", "# the unique identifier for apple's 10-Q Q2 report of 2024\n", "apple_10q_q2_2024_adsh = \"0000320193-24-000069\"\n", "\n", "# us a Collector to grab the data of the 10-K report. filter for balancesheet information\n", "collector: SingleReportCollector = SingleReportCollector.get_report_by_adsh(\n", "      adsh=apple_10q_q2_2024_adsh\n", ")  \n", "rawdatabag = collector.collect() # load the data from the disk"]}, {"cell_type": "code", "execution_count": 4, "id": "f0d1f6eb-be14-4559-a1de-3e86c6829d9f", "metadata": {"tags": []}, "outputs": [], "source": ["sub_df = rawdatabag.sub_df\n", "num_df = rawdatabag.num_df\n", "pre_df = rawdatabag.pre_df"]}, {"cell_type": "code", "execution_count": 5, "id": "773d9974-e0af-415c-a87f-d504d95ababf", "metadata": {"tags": []}, "outputs": [], "source": ["sub_df = sub_df[sub_df.adsh==\"0000320193-24-000069\"]\n", "num_df = num_df[num_df.adsh==\"0000320193-24-000069\"]\n", "pre_df = pre_df[pre_df.adsh==\"0000320193-24-000069\"]"]}, {"cell_type": "code", "execution_count": 6, "id": "7330ff7d-e76b-4282-aeed-cec1d0b63661", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1, 36)\n", "(433, 10)\n", "(95, 10)\n"]}], "source": ["print(sub_df.shape)\n", "print(num_df.shape)\n", "print(pre_df.shape)"]}, {"cell_type": "code", "execution_count": 7, "id": "c2e93793-c621-458a-952d-cdc894145142", "metadata": {"tags": []}, "outputs": [], "source": ["num_df.coreg = None"]}, {"cell_type": "code", "execution_count": 8, "id": "70cc890b-63d0-4cc8-a794-715eadd14dbb", "metadata": {"tags": []}, "outputs": [], "source": ["num_df = num_df[num_df.coreg.isna()]"]}, {"cell_type": "code", "execution_count": 9, "id": "d8ef7776-87ba-43e3-a313-6104840a2a66", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["USD       419\n", "shares     14\n", "Name: uom, dtype: int64"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["num_df.uom.value_counts()"]}, {"cell_type": "code", "execution_count": 14, "id": "6090f499-2a79-48af-b5f9-8769946b62a2", "metadata": {}, "outputs": [], "source": ["# we want to keep all values with uoms that are  not in upper case\n", "mask_has_lower = ~num_df.uom.str.isupper()\n", "\n", "# currency has always 3 letters, so we want to keep everything that has a different length\n", "mask_is_none_currency = num_df.uom.str.len() != 3\n", "\n", "# keep USD\n", "mask_usd_only = num_df.uom == \"USD\"\n", "\n", "num_df = num_df[mask_has_lower | mask_is_none_currency | mask_usd_only]"]}, {"cell_type": "code", "execution_count": 15, "id": "4b272203-92b4-44c2-96a2-15a0343fbe94", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(344, 10)\n"]}], "source": ["print(num_df.shape)"]}, {"cell_type": "code", "execution_count": 16, "id": "27a013f2-8d61-41f7-815e-123dc2572cd2", "metadata": {"tags": []}, "outputs": [], "source": ["# get the value of the \"period\" column for the entry in sub_df\n", "# (there is only one entry left, since filtered for a certain adsh)\n", "period = sub_df.iloc[0].period\n", "\n", "# mask the datapoints for the current period\n", "mask_current = num_df.ddate == period\n", "\n", "# since period and ddate are actually numbers in the form of YYYYMMDD\n", "# we can simply subtract 10'000 to get the previous year\n", "# !!!! ATTENTION !!! \n", "# When the period is end of February, we have to consider the leap years!\n", "mask_previous = num_df.ddate == (period - 10000)\n", "\n", "num_df = num_df[mask_current | mask_previous]"]}, {"cell_type": "code", "execution_count": 17, "id": "8e34ad98-94bf-43db-8d74-45e1481d7513", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(344, 10)\n"]}], "source": ["print(num_df.shape)"]}, {"cell_type": "code", "execution_count": 18, "id": "c4c63a09-4c6c-45d0-92f1-66e19c2d379f", "metadata": {"tags": []}, "outputs": [], "source": ["num_df = num_df[num_df.qtrs.isin([1,2])]"]}, {"cell_type": "code", "execution_count": 19, "id": "ec01c561-92dc-4178-be69-cb45c2dfcd1f", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>adsh</th>\n", "      <th>tag</th>\n", "      <th>version</th>\n", "      <th>ddate</th>\n", "      <th>qtrs</th>\n", "      <th>uom</th>\n", "      <th>segments</th>\n", "      <th>coreg</th>\n", "      <th>value</th>\n", "      <th>footnote</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=GreaterChinaSegment;</td>\n", "      <td>None</td>\n", "      <td>1.637200e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>2</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>2.103280e+11</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>2</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=RestOfAsiaPacificSegment;</td>\n", "      <td>None</td>\n", "      <td>1.688500e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>65</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20230331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>9.483600e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>70</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20230331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=IPad;</td>\n", "      <td>None</td>\n", "      <td>6.670000e+09</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=IPad;</td>\n", "      <td>None</td>\n", "      <td>5.559000e+09</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20230331</td>\n", "      <td>2</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=Mac;</td>\n", "      <td>None</td>\n", "      <td>1.490300e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>98</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>9.075300e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>100</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20230331</td>\n", "      <td>2</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=Service;</td>\n", "      <td>None</td>\n", "      <td>4.167300e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>107</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>2</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=GreaterChinaSegment;</td>\n", "      <td>None</td>\n", "      <td>3.719100e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>108</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20230331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=JapanSegment;</td>\n", "      <td>None</td>\n", "      <td>7.176000e+09</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>2</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=EuropeSegment;</td>\n", "      <td>None</td>\n", "      <td>5.452000e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>117</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20230331</td>\n", "      <td>2</td>\n", "      <td>USD</td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td>2.119900e+11</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>119</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20230331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=WearablesHomeandAccessories;</td>\n", "      <td>None</td>\n", "      <td>8.757000e+09</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>138</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20230331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=GreaterChinaSegment;</td>\n", "      <td>None</td>\n", "      <td>1.781200e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>157</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20230331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=EuropeSegment;</td>\n", "      <td>None</td>\n", "      <td>2.394500e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>179</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>2</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=WearablesHomeandAccessories;</td>\n", "      <td>None</td>\n", "      <td>1.986600e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>182</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20230331</td>\n", "      <td>2</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=JapanSegment;</td>\n", "      <td>None</td>\n", "      <td>1.393100e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>186</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>2</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=Mac;</td>\n", "      <td>None</td>\n", "      <td>1.523100e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>197</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20230331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=IPhone;</td>\n", "      <td>None</td>\n", "      <td>5.133400e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>209</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20230331</td>\n", "      <td>2</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=IPad;</td>\n", "      <td>None</td>\n", "      <td>1.606600e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>222</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20230331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=Product;</td>\n", "      <td>None</td>\n", "      <td>7.392900e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>224</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=RestOfAsiaPacificSegment;</td>\n", "      <td>None</td>\n", "      <td>6.723000e+09</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>239</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=EuropeSegment;</td>\n", "      <td>None</td>\n", "      <td>2.412300e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>246</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20230331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=Service;</td>\n", "      <td>None</td>\n", "      <td>2.090700e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>254</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20230331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=Mac;</td>\n", "      <td>None</td>\n", "      <td>7.168000e+09</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>265</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>2</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=JapanSegment;</td>\n", "      <td>None</td>\n", "      <td>1.402900e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>277</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>2</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=Product;</td>\n", "      <td>None</td>\n", "      <td>1.633440e+11</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>279</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=JapanSegment;</td>\n", "      <td>None</td>\n", "      <td>6.262000e+09</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>285</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20230331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=RestOfAsiaPacificSegment;</td>\n", "      <td>None</td>\n", "      <td>8.119000e+09</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>289</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20230331</td>\n", "      <td>2</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=RestOfAsiaPacificSegment;</td>\n", "      <td>None</td>\n", "      <td>1.765400e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>291</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=Service;</td>\n", "      <td>None</td>\n", "      <td>2.386700e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>311</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20230331</td>\n", "      <td>2</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=Product;</td>\n", "      <td>None</td>\n", "      <td>1.703170e+11</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>313</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>2</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=IPad;</td>\n", "      <td>None</td>\n", "      <td>1.258200e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>319</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20230331</td>\n", "      <td>2</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=AmericasSegment;</td>\n", "      <td>None</td>\n", "      <td>8.706200e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>325</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=Mac;</td>\n", "      <td>None</td>\n", "      <td>7.451000e+09</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>334</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=WearablesHomeandAccessories;</td>\n", "      <td>None</td>\n", "      <td>7.913000e+09</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>344</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=AmericasSegment;</td>\n", "      <td>None</td>\n", "      <td>3.727300e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>345</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20230331</td>\n", "      <td>2</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=WearablesHomeandAccessories;</td>\n", "      <td>None</td>\n", "      <td>2.223900e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>360</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20230331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=AmericasSegment;</td>\n", "      <td>None</td>\n", "      <td>3.778400e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>382</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20230331</td>\n", "      <td>2</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=GreaterChinaSegment;</td>\n", "      <td>None</td>\n", "      <td>4.171700e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>398</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20230331</td>\n", "      <td>2</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=EuropeSegment;</td>\n", "      <td>None</td>\n", "      <td>5.162600e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>400</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20230331</td>\n", "      <td>2</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=IPhone;</td>\n", "      <td>None</td>\n", "      <td>1.171090e+11</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>405</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>2</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=IPhone;</td>\n", "      <td>None</td>\n", "      <td>1.156650e+11</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>409</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=IPhone;</td>\n", "      <td>None</td>\n", "      <td>4.596300e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>423</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>1</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=Product;</td>\n", "      <td>None</td>\n", "      <td>6.688600e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>428</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>2</td>\n", "      <td>USD</td>\n", "      <td>BusinessSegments=AmericasSegment;</td>\n", "      <td>None</td>\n", "      <td>8.770300e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>430</th>\n", "      <td>0000320193-24-000069</td>\n", "      <td>RevenueFromContractWithCustomerExcludingAssessedTax</td>\n", "      <td>us-gaap/2023</td>\n", "      <td>20240331</td>\n", "      <td>2</td>\n", "      <td>USD</td>\n", "      <td>ProductOrService=Service;</td>\n", "      <td>None</td>\n", "      <td>4.698400e+10</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                     adsh                                                  tag       version     ddate  qtrs  uom                                       segments coreg         value footnote\n", "2    0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     1  USD          BusinessSegments=GreaterChinaSegment;  None  1.637200e+10     None\n", "15   0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     2  USD                                                 None  2.103280e+11     None\n", "39   0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     2  USD     BusinessSegments=RestOfAsiaPacificSegment;  None  1.688500e+10     None\n", "65   0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20230331     1  USD                                                 None  9.483600e+10     None\n", "70   0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20230331     1  USD                         ProductOrService=IPad;  None  6.670000e+09     None\n", "77   0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     1  USD                         ProductOrService=IPad;  None  5.559000e+09     None\n", "81   0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20230331     2  USD                          ProductOrService=Mac;  None  1.490300e+10     None\n", "98   0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     1  USD                                                 None  9.075300e+10     None\n", "100  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20230331     2  USD                      ProductOrService=Service;  None  4.167300e+10     None\n", "107  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     2  USD          BusinessSegments=GreaterChinaSegment;  None  3.719100e+10     None\n", "108  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20230331     1  USD                 BusinessSegments=JapanSegment;  None  7.176000e+09     None\n", "110  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     2  USD                BusinessSegments=EuropeSegment;  None  5.452000e+10     None\n", "117  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20230331     2  USD                                                 None  2.119900e+11     None\n", "119  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20230331     1  USD  ProductOrService=WearablesHomeandAccessories;  None  8.757000e+09     None\n", "138  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20230331     1  USD          BusinessSegments=GreaterChinaSegment;  None  1.781200e+10     None\n", "157  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20230331     1  USD                BusinessSegments=EuropeSegment;  None  2.394500e+10     None\n", "179  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     2  USD  ProductOrService=WearablesHomeandAccessories;  None  1.986600e+10     None\n", "182  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20230331     2  USD                 BusinessSegments=JapanSegment;  None  1.393100e+10     None\n", "186  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     2  USD                          ProductOrService=Mac;  None  1.523100e+10     None\n", "197  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20230331     1  USD                       ProductOrService=IPhone;  None  5.133400e+10     None\n", "209  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20230331     2  USD                         ProductOrService=IPad;  None  1.606600e+10     None\n", "222  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20230331     1  USD                      ProductOrService=Product;  None  7.392900e+10     None\n", "224  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     1  USD     BusinessSegments=RestOfAsiaPacificSegment;  None  6.723000e+09     None\n", "239  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     1  USD                BusinessSegments=EuropeSegment;  None  2.412300e+10     None\n", "246  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20230331     1  USD                      ProductOrService=Service;  None  2.090700e+10     None\n", "254  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20230331     1  USD                          ProductOrService=Mac;  None  7.168000e+09     None\n", "265  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     2  USD                 BusinessSegments=JapanSegment;  None  1.402900e+10     None\n", "277  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     2  USD                      ProductOrService=Product;  None  1.633440e+11     None\n", "279  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     1  USD                 BusinessSegments=JapanSegment;  None  6.262000e+09     None\n", "285  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20230331     1  USD     BusinessSegments=RestOfAsiaPacificSegment;  None  8.119000e+09     None\n", "289  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20230331     2  USD     BusinessSegments=RestOfAsiaPacificSegment;  None  1.765400e+10     None\n", "291  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     1  USD                      ProductOrService=Service;  None  2.386700e+10     None\n", "311  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20230331     2  USD                      ProductOrService=Product;  None  1.703170e+11     None\n", "313  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     2  USD                         ProductOrService=IPad;  None  1.258200e+10     None\n", "319  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20230331     2  USD              BusinessSegments=AmericasSegment;  None  8.706200e+10     None\n", "325  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     1  USD                          ProductOrService=Mac;  None  7.451000e+09     None\n", "334  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     1  USD  ProductOrService=WearablesHomeandAccessories;  None  7.913000e+09     None\n", "344  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     1  USD              BusinessSegments=AmericasSegment;  None  3.727300e+10     None\n", "345  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20230331     2  USD  ProductOrService=WearablesHomeandAccessories;  None  2.223900e+10     None\n", "360  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20230331     1  USD              BusinessSegments=AmericasSegment;  None  3.778400e+10     None\n", "382  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20230331     2  USD          BusinessSegments=GreaterChinaSegment;  None  4.171700e+10     None\n", "398  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20230331     2  USD                BusinessSegments=EuropeSegment;  None  5.162600e+10     None\n", "400  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20230331     2  USD                       ProductOrService=IPhone;  None  1.171090e+11     None\n", "405  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     2  USD                       ProductOrService=IPhone;  None  1.156650e+11     None\n", "409  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     1  USD                       ProductOrService=IPhone;  None  4.596300e+10     None\n", "423  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     1  USD                      ProductOrService=Product;  None  6.688600e+10     None\n", "428  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     2  USD              BusinessSegments=AmericasSegment;  None  8.770300e+10     None\n", "430  0000320193-24-000069  RevenueFromContractWithCustomerExcludingAssessedTax  us-gaap/2023  20240331     2  USD                      ProductOrService=Service;  None  4.698400e+10     None"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["num_df[num_df.tag==\"RevenueFromContractWithCustomerExcludingAssessedTax\"]"]}, {"cell_type": "code", "execution_count": 20, "id": "e98def99-5a7a-4043-a6e2-59a9c9dd8653", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(263, 10)\n"]}], "source": ["print(num_df.shape)"]}, {"cell_type": "code", "execution_count": 21, "id": "d9159736-fd92-450d-8f61-3c36958b6023", "metadata": {"tags": []}, "outputs": [], "source": ["pre_df = pre_df[pre_df.stmt == 'IS']"]}, {"cell_type": "code", "execution_count": 22, "id": "1cb3e255-922b-415a-8829-7b68fcae4473", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(15, 10)\n"]}], "source": ["print(pre_df.shape)"]}, {"cell_type": "code", "execution_count": 23, "id": "4d9d2b71-9cdd-4c25-a38c-3dc12a893fba", "metadata": {"tags": []}, "outputs": [], "source": ["pre_num_df = pd.merge(num_df,\n", "                      pre_df,\n", "                      on=['adsh', 'tag', 'version'])"]}, {"cell_type": "code", "execution_count": 24, "id": "4ecbfaeb-200c-431c-9f6f-6665a3dfbf26", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(144, 17)\n"]}], "source": ["print(pre_num_df.shape)"]}, {"cell_type": "code", "execution_count": 25, "id": "6db0acfb-bc85-4987-aa68-27af3f7db752", "metadata": {"tags": []}, "outputs": [], "source": ["# mask the entries with the negating flag set and inverse the value column\n", "pre_num_df.loc[pre_num_df.negating == 1, 'value'] = -pre_num_df.value"]}, {"cell_type": "code", "execution_count": 26, "id": "4827bb70-fdbc-4da3-9462-28d4c64ab56a", "metadata": {}, "outputs": [], "source": ["pre_num_df = pre_num_df[['tag', 'line', 'report', 'segments', 'uom', 'value', 'ddate', 'qtrs']]"]}, {"cell_type": "code", "execution_count": 27, "id": "cf996d00-29d5-473f-af7b-e8d93f41c6ee", "metadata": {"tags": []}, "outputs": [], "source": ["pivot_df = pre_num_df.pivot_table(\n", "                index=['tag','report', 'line', 'segments', 'uom'],\n", "                columns=['qtrs', 'ddate'],\n", "                values='value')"]}, {"cell_type": "code", "execution_count": 28, "id": "012e4f7b-7dd7-4568-bc5a-a735eaa12a35", "metadata": {"tags": []}, "outputs": [], "source": ["sort_df = pivot_df.sort_values(['report', 'line'])"]}, {"cell_type": "code", "execution_count": 37, "id": "f9a507ed-49be-4b6e-b98e-19f00ac80f06", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th>qtrs</th>\n", "      <th colspan=\"2\" halign=\"left\">1</th>\n", "      <th colspan=\"2\" halign=\"left\">2</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th>ddate</th>\n", "      <th>20230331</th>\n", "      <th>20240331</th>\n", "      <th>20230331</th>\n", "      <th>20240331</th>\n", "    </tr>\n", "    <tr>\n", "      <th>tag</th>\n", "      <th>report</th>\n", "      <th>line</th>\n", "      <th>segments</th>\n", "      <th>uom</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"12\" valign=\"top\">RevenueFromContractWithCustomerExcludingAssessedTax</th>\n", "      <th rowspan=\"12\" valign=\"top\">2</th>\n", "      <th rowspan=\"12\" valign=\"top\">7</th>\n", "      <th></th>\n", "      <th>USD</th>\n", "      <td>9.483600e+10</td>\n", "      <td>9.075300e+10</td>\n", "      <td>2.119900e+11</td>\n", "      <td>2.103280e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BusinessSegments=AmericasSegment;</th>\n", "      <th>USD</th>\n", "      <td>3.778400e+10</td>\n", "      <td>3.727300e+10</td>\n", "      <td>8.706200e+10</td>\n", "      <td>8.770300e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BusinessSegments=EuropeSegment;</th>\n", "      <th>USD</th>\n", "      <td>2.394500e+10</td>\n", "      <td>2.412300e+10</td>\n", "      <td>5.162600e+10</td>\n", "      <td>5.452000e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BusinessSegments=GreaterChinaSegment;</th>\n", "      <th>USD</th>\n", "      <td>1.781200e+10</td>\n", "      <td>1.637200e+10</td>\n", "      <td>4.171700e+10</td>\n", "      <td>3.719100e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BusinessSegments=JapanSegment;</th>\n", "      <th>USD</th>\n", "      <td>7.176000e+09</td>\n", "      <td>6.262000e+09</td>\n", "      <td>1.393100e+10</td>\n", "      <td>1.402900e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BusinessSegments=RestOfAsiaPacificSegment;</th>\n", "      <th>USD</th>\n", "      <td>8.119000e+09</td>\n", "      <td>6.723000e+09</td>\n", "      <td>1.765400e+10</td>\n", "      <td>1.688500e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ProductOrService=IPad;</th>\n", "      <th>USD</th>\n", "      <td>6.670000e+09</td>\n", "      <td>5.559000e+09</td>\n", "      <td>1.606600e+10</td>\n", "      <td>1.258200e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ProductOrService=IPhone;</th>\n", "      <th>USD</th>\n", "      <td>5.133400e+10</td>\n", "      <td>4.596300e+10</td>\n", "      <td>1.171090e+11</td>\n", "      <td>1.156650e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ProductOrService=Mac;</th>\n", "      <th>USD</th>\n", "      <td>7.168000e+09</td>\n", "      <td>7.451000e+09</td>\n", "      <td>1.490300e+10</td>\n", "      <td>1.523100e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ProductOrService=Product;</th>\n", "      <th>USD</th>\n", "      <td>7.392900e+10</td>\n", "      <td>6.688600e+10</td>\n", "      <td>1.703170e+11</td>\n", "      <td>1.633440e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ProductOrService=Service;</th>\n", "      <th>USD</th>\n", "      <td>2.090700e+10</td>\n", "      <td>2.386700e+10</td>\n", "      <td>4.167300e+10</td>\n", "      <td>4.698400e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ProductOrService=WearablesHomeandAccessories;</th>\n", "      <th>USD</th>\n", "      <td>8.757000e+09</td>\n", "      <td>7.913000e+09</td>\n", "      <td>2.223900e+10</td>\n", "      <td>1.986600e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"3\" valign=\"top\">CostOfGoodsAndServicesSold</th>\n", "      <th rowspan=\"3\" valign=\"top\">2</th>\n", "      <th rowspan=\"3\" valign=\"top\">8</th>\n", "      <th></th>\n", "      <th>USD</th>\n", "      <td>5.286000e+10</td>\n", "      <td>4.848200e+10</td>\n", "      <td>1.196820e+11</td>\n", "      <td>1.132020e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ProductOrService=Product;</th>\n", "      <th>USD</th>\n", "      <td>4.679500e+10</td>\n", "      <td>4.242400e+10</td>\n", "      <td>1.075600e+11</td>\n", "      <td>1.008640e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ProductOrService=Service;</th>\n", "      <th>USD</th>\n", "      <td>6.065000e+09</td>\n", "      <td>6.058000e+09</td>\n", "      <td>1.212200e+10</td>\n", "      <td>1.233800e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GrossProfit</th>\n", "      <th>2</th>\n", "      <th>9</th>\n", "      <th></th>\n", "      <th>USD</th>\n", "      <td>4.197600e+10</td>\n", "      <td>4.227100e+10</td>\n", "      <td>9.230800e+10</td>\n", "      <td>9.712600e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">ResearchAndDevelopmentExpense</th>\n", "      <th rowspan=\"2\" valign=\"top\">2</th>\n", "      <th rowspan=\"2\" valign=\"top\">11</th>\n", "      <th></th>\n", "      <th>USD</th>\n", "      <td>7.457000e+09</td>\n", "      <td>7.903000e+09</td>\n", "      <td>1.516600e+10</td>\n", "      <td>1.559900e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ConsolidationItems=MaterialReconcilingItems;</th>\n", "      <th>USD</th>\n", "      <td>7.457000e+09</td>\n", "      <td>7.903000e+09</td>\n", "      <td>1.516600e+10</td>\n", "      <td>1.559900e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SellingGeneralAndAdministrativeExpense</th>\n", "      <th>2</th>\n", "      <th>12</th>\n", "      <th></th>\n", "      <th>USD</th>\n", "      <td>6.201000e+09</td>\n", "      <td>6.468000e+09</td>\n", "      <td>1.280800e+10</td>\n", "      <td>1.325400e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>OperatingExpenses</th>\n", "      <th>2</th>\n", "      <th>13</th>\n", "      <th></th>\n", "      <th>USD</th>\n", "      <td>1.365800e+10</td>\n", "      <td>1.437100e+10</td>\n", "      <td>2.797400e+10</td>\n", "      <td>2.885300e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"7\" valign=\"top\">OperatingIncomeLoss</th>\n", "      <th rowspan=\"7\" valign=\"top\">2</th>\n", "      <th rowspan=\"7\" valign=\"top\">14</th>\n", "      <th></th>\n", "      <th>USD</th>\n", "      <td>2.831800e+10</td>\n", "      <td>2.790000e+10</td>\n", "      <td>6.433400e+10</td>\n", "      <td>6.827300e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BusinessSegments=AmericasSegment;</th>\n", "      <th>USD</th>\n", "      <td>1.392700e+10</td>\n", "      <td>1.507400e+10</td>\n", "      <td>3.179100e+10</td>\n", "      <td>3.543100e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BusinessSegments=EuropeSegment;</th>\n", "      <th>USD</th>\n", "      <td>9.368000e+09</td>\n", "      <td>9.991000e+09</td>\n", "      <td>1.938500e+10</td>\n", "      <td>2.270200e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BusinessSegments=GreaterChinaSegment;</th>\n", "      <th>USD</th>\n", "      <td>7.531000e+09</td>\n", "      <td>6.700000e+09</td>\n", "      <td>1.796800e+10</td>\n", "      <td>1.532200e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BusinessSegments=JapanSegment;</th>\n", "      <th>USD</th>\n", "      <td>3.394000e+09</td>\n", "      <td>3.135000e+09</td>\n", "      <td>6.630000e+09</td>\n", "      <td>6.954000e+09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BusinessSegments=RestOfAsiaPacificSegment;</th>\n", "      <th>USD</th>\n", "      <td>3.268000e+09</td>\n", "      <td>2.806000e+09</td>\n", "      <td>7.119000e+09</td>\n", "      <td>7.385000e+09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ConsolidationItems=OperatingSegments;</th>\n", "      <th>USD</th>\n", "      <td>3.748800e+10</td>\n", "      <td>3.770600e+10</td>\n", "      <td>8.289300e+10</td>\n", "      <td>8.779400e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NonoperatingIncomeExpense</th>\n", "      <th>2</th>\n", "      <th>15</th>\n", "      <th></th>\n", "      <th>USD</th>\n", "      <td>6.400000e+07</td>\n", "      <td>1.580000e+08</td>\n", "      <td>-3.290000e+08</td>\n", "      <td>1.080000e+08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IncomeLossFromContinuingOperationsBeforeIncomeTaxesExtraordinaryItemsNoncontrollingInterest</th>\n", "      <th>2</th>\n", "      <th>16</th>\n", "      <th></th>\n", "      <th>USD</th>\n", "      <td>2.838200e+10</td>\n", "      <td>2.805800e+10</td>\n", "      <td>6.400500e+10</td>\n", "      <td>6.838100e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IncomeTaxExpenseBenefit</th>\n", "      <th>2</th>\n", "      <th>17</th>\n", "      <th></th>\n", "      <th>USD</th>\n", "      <td>4.222000e+09</td>\n", "      <td>4.422000e+09</td>\n", "      <td>9.847000e+09</td>\n", "      <td>1.082900e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">NetIncomeLoss</th>\n", "      <th rowspan=\"2\" valign=\"top\">2</th>\n", "      <th rowspan=\"2\" valign=\"top\">18</th>\n", "      <th></th>\n", "      <th>USD</th>\n", "      <td>2.416000e+10</td>\n", "      <td>2.363600e+10</td>\n", "      <td>5.415800e+10</td>\n", "      <td>5.755200e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>EquityComponents=RetainedEarnings;</th>\n", "      <th>USD</th>\n", "      <td>2.416000e+10</td>\n", "      <td>2.363600e+10</td>\n", "      <td>5.415800e+10</td>\n", "      <td>5.755200e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>EarningsPerShareBasic</th>\n", "      <th>2</th>\n", "      <th>20</th>\n", "      <th></th>\n", "      <th>USD</th>\n", "      <td>1.530000e+00</td>\n", "      <td>1.530000e+00</td>\n", "      <td>3.420000e+00</td>\n", "      <td>3.720000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>EarningsPerShareDiluted</th>\n", "      <th>2</th>\n", "      <th>21</th>\n", "      <th></th>\n", "      <th>USD</th>\n", "      <td>1.520000e+00</td>\n", "      <td>1.530000e+00</td>\n", "      <td>3.410000e+00</td>\n", "      <td>3.710000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>WeightedAverageNumberOfSharesOutstandingBasic</th>\n", "      <th>2</th>\n", "      <th>23</th>\n", "      <th></th>\n", "      <th>shares</th>\n", "      <td>1.578715e+10</td>\n", "      <td>1.540586e+10</td>\n", "      <td>1.583994e+10</td>\n", "      <td>1.545781e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>WeightedAverageNumberOfDilutedSharesOutstanding</th>\n", "      <th>2</th>\n", "      <th>24</th>\n", "      <th></th>\n", "      <th>shares</th>\n", "      <td>1.584705e+10</td>\n", "      <td>1.546471e+10</td>\n", "      <td>1.590138e+10</td>\n", "      <td>1.552068e+10</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["qtrs                                                                                                                                                                     1                           2              \n", "ddate                                                                                                                                                             20230331      20240331      20230331      20240331\n", "tag                                                                                         report line segments                                      uom                                                           \n", "RevenueFromContractWithCustomerExcludingAssessedTax                                         2      7                                                  USD     9.483600e+10  9.075300e+10  2.119900e+11  2.103280e+11\n", "                                                                                                        BusinessSegments=AmericasSegment;             USD     3.778400e+10  3.727300e+10  8.706200e+10  8.770300e+10\n", "                                                                                                        BusinessSegments=EuropeSegment;               USD     2.394500e+10  2.412300e+10  5.162600e+10  5.452000e+10\n", "                                                                                                        BusinessSegments=GreaterChinaSegment;         USD     1.781200e+10  1.637200e+10  4.171700e+10  3.719100e+10\n", "                                                                                                        BusinessSegments=JapanSegment;                USD     7.176000e+09  6.262000e+09  1.393100e+10  1.402900e+10\n", "                                                                                                        BusinessSegments=RestOfAsiaPacificSegment;    USD     8.119000e+09  6.723000e+09  1.765400e+10  1.688500e+10\n", "                                                                                                        ProductOrService=IPad;                        USD     6.670000e+09  5.559000e+09  1.606600e+10  1.258200e+10\n", "                                                                                                        ProductOrService=IPhone;                      USD     5.133400e+10  4.596300e+10  1.171090e+11  1.156650e+11\n", "                                                                                                        ProductOrService=Mac;                         USD     7.168000e+09  7.451000e+09  1.490300e+10  1.523100e+10\n", "                                                                                                        ProductOrService=Product;                     USD     7.392900e+10  6.688600e+10  1.703170e+11  1.633440e+11\n", "                                                                                                        ProductOrService=Service;                     USD     2.090700e+10  2.386700e+10  4.167300e+10  4.698400e+10\n", "                                                                                                        ProductOrService=WearablesHomeandAccessories; USD     8.757000e+09  7.913000e+09  2.223900e+10  1.986600e+10\n", "CostOfGoodsAndServicesSold                                                                  2      8                                                  USD     5.286000e+10  4.848200e+10  1.196820e+11  1.132020e+11\n", "                                                                                                        ProductOrService=Product;                     USD     4.679500e+10  4.242400e+10  1.075600e+11  1.008640e+11\n", "                                                                                                        ProductOrService=Service;                     USD     6.065000e+09  6.058000e+09  1.212200e+10  1.233800e+10\n", "GrossProfit                                                                                 2      9                                                  USD     4.197600e+10  4.227100e+10  9.230800e+10  9.712600e+10\n", "ResearchAndDevelopmentExpense                                                               2      11                                                 USD     7.457000e+09  7.903000e+09  1.516600e+10  1.559900e+10\n", "                                                                                                        ConsolidationItems=MaterialReconcilingItems;  USD     7.457000e+09  7.903000e+09  1.516600e+10  1.559900e+10\n", "SellingGeneralAndAdministrativeExpense                                                      2      12                                                 USD     6.201000e+09  6.468000e+09  1.280800e+10  1.325400e+10\n", "OperatingExpenses                                                                           2      13                                                 USD     1.365800e+10  1.437100e+10  2.797400e+10  2.885300e+10\n", "OperatingIncomeLoss                                                                         2      14                                                 USD     2.831800e+10  2.790000e+10  6.433400e+10  6.827300e+10\n", "                                                                                                        BusinessSegments=AmericasSegment;             USD     1.392700e+10  1.507400e+10  3.179100e+10  3.543100e+10\n", "                                                                                                        BusinessSegments=EuropeSegment;               USD     9.368000e+09  9.991000e+09  1.938500e+10  2.270200e+10\n", "                                                                                                        BusinessSegments=GreaterChinaSegment;         USD     7.531000e+09  6.700000e+09  1.796800e+10  1.532200e+10\n", "                                                                                                        BusinessSegments=JapanSegment;                USD     3.394000e+09  3.135000e+09  6.630000e+09  6.954000e+09\n", "                                                                                                        BusinessSegments=RestOfAsiaPacificSegment;    USD     3.268000e+09  2.806000e+09  7.119000e+09  7.385000e+09\n", "                                                                                                        ConsolidationItems=OperatingSegments;         USD     3.748800e+10  3.770600e+10  8.289300e+10  8.779400e+10\n", "NonoperatingIncomeExpense                                                                   2      15                                                 USD     6.400000e+07  1.580000e+08 -3.290000e+08  1.080000e+08\n", "IncomeLossFromContinuingOperationsBeforeIncomeTaxesExtraordinaryItemsNoncontrollingInterest 2      16                                                 USD     2.838200e+10  2.805800e+10  6.400500e+10  6.838100e+10\n", "IncomeTaxExpenseBenefit                                                                     2      17                                                 USD     4.222000e+09  4.422000e+09  9.847000e+09  1.082900e+10\n", "NetIncomeLoss                                                                               2      18                                                 USD     2.416000e+10  2.363600e+10  5.415800e+10  5.755200e+10\n", "                                                                                                        EquityComponents=RetainedEarnings;            USD     2.416000e+10  2.363600e+10  5.415800e+10  5.755200e+10\n", "EarningsPerShareBasic                                                                       2      20                                                 USD     1.530000e+00  1.530000e+00  3.420000e+00  3.720000e+00\n", "EarningsPerShareDiluted                                                                     2      21                                                 USD     1.520000e+00  1.530000e+00  3.410000e+00  3.710000e+00\n", "WeightedAverageNumberOfSharesOutstandingBasic                                               2      23                                                 shares  1.578715e+10  1.540586e+10  1.583994e+10  1.545781e+10\n", "WeightedAverageNumberOfDilutedSharesOutstanding                                             2      24                                                 shares  1.584705e+10  1.546471e+10  1.590138e+10  1.552068e+10"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["sort_df"]}, {"cell_type": "code", "execution_count": 47, "id": "acc5e63e-cb33-4319-b3d7-637baa86dd7d", "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th>qtrs</th>\n", "      <th colspan=\"2\" halign=\"left\">1</th>\n", "      <th colspan=\"2\" halign=\"left\">2</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th>ddate</th>\n", "      <th>20230331</th>\n", "      <th>20240331</th>\n", "      <th>20230331</th>\n", "      <th>20240331</th>\n", "    </tr>\n", "    <tr>\n", "      <th>tag</th>\n", "      <th>report</th>\n", "      <th>line</th>\n", "      <th>segments</th>\n", "      <th>uom</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"3\" valign=\"top\">RevenueFromContractWithCustomerExcludingAssessedTax</th>\n", "      <th rowspan=\"3\" valign=\"top\">2</th>\n", "      <th rowspan=\"3\" valign=\"top\">7</th>\n", "      <th></th>\n", "      <th>USD</th>\n", "      <td>9.483600e+10</td>\n", "      <td>9.075300e+10</td>\n", "      <td>2.119900e+11</td>\n", "      <td>2.103280e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ProductOrService=Product;</th>\n", "      <th>USD</th>\n", "      <td>7.392900e+10</td>\n", "      <td>6.688600e+10</td>\n", "      <td>1.703170e+11</td>\n", "      <td>1.633440e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ProductOrService=Service;</th>\n", "      <th>USD</th>\n", "      <td>2.090700e+10</td>\n", "      <td>2.386700e+10</td>\n", "      <td>4.167300e+10</td>\n", "      <td>4.698400e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"3\" valign=\"top\">CostOfGoodsAndServicesSold</th>\n", "      <th rowspan=\"3\" valign=\"top\">2</th>\n", "      <th rowspan=\"3\" valign=\"top\">8</th>\n", "      <th></th>\n", "      <th>USD</th>\n", "      <td>5.286000e+10</td>\n", "      <td>4.848200e+10</td>\n", "      <td>1.196820e+11</td>\n", "      <td>1.132020e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ProductOrService=Product;</th>\n", "      <th>USD</th>\n", "      <td>4.679500e+10</td>\n", "      <td>4.242400e+10</td>\n", "      <td>1.075600e+11</td>\n", "      <td>1.008640e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ProductOrService=Service;</th>\n", "      <th>USD</th>\n", "      <td>6.065000e+09</td>\n", "      <td>6.058000e+09</td>\n", "      <td>1.212200e+10</td>\n", "      <td>1.233800e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GrossProfit</th>\n", "      <th>2</th>\n", "      <th>9</th>\n", "      <th></th>\n", "      <th>USD</th>\n", "      <td>4.197600e+10</td>\n", "      <td>4.227100e+10</td>\n", "      <td>9.230800e+10</td>\n", "      <td>9.712600e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ResearchAndDevelopmentExpense</th>\n", "      <th>2</th>\n", "      <th>11</th>\n", "      <th></th>\n", "      <th>USD</th>\n", "      <td>7.457000e+09</td>\n", "      <td>7.903000e+09</td>\n", "      <td>1.516600e+10</td>\n", "      <td>1.559900e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SellingGeneralAndAdministrativeExpense</th>\n", "      <th>2</th>\n", "      <th>12</th>\n", "      <th></th>\n", "      <th>USD</th>\n", "      <td>6.201000e+09</td>\n", "      <td>6.468000e+09</td>\n", "      <td>1.280800e+10</td>\n", "      <td>1.325400e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>OperatingExpenses</th>\n", "      <th>2</th>\n", "      <th>13</th>\n", "      <th></th>\n", "      <th>USD</th>\n", "      <td>1.365800e+10</td>\n", "      <td>1.437100e+10</td>\n", "      <td>2.797400e+10</td>\n", "      <td>2.885300e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>OperatingIncomeLoss</th>\n", "      <th>2</th>\n", "      <th>14</th>\n", "      <th></th>\n", "      <th>USD</th>\n", "      <td>2.831800e+10</td>\n", "      <td>2.790000e+10</td>\n", "      <td>6.433400e+10</td>\n", "      <td>6.827300e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NonoperatingIncomeExpense</th>\n", "      <th>2</th>\n", "      <th>15</th>\n", "      <th></th>\n", "      <th>USD</th>\n", "      <td>6.400000e+07</td>\n", "      <td>1.580000e+08</td>\n", "      <td>-3.290000e+08</td>\n", "      <td>1.080000e+08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IncomeLossFromContinuingOperationsBeforeIncomeTaxesExtraordinaryItemsNoncontrollingInterest</th>\n", "      <th>2</th>\n", "      <th>16</th>\n", "      <th></th>\n", "      <th>USD</th>\n", "      <td>2.838200e+10</td>\n", "      <td>2.805800e+10</td>\n", "      <td>6.400500e+10</td>\n", "      <td>6.838100e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>IncomeTaxExpenseBenefit</th>\n", "      <th>2</th>\n", "      <th>17</th>\n", "      <th></th>\n", "      <th>USD</th>\n", "      <td>4.222000e+09</td>\n", "      <td>4.422000e+09</td>\n", "      <td>9.847000e+09</td>\n", "      <td>1.082900e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NetIncomeLoss</th>\n", "      <th>2</th>\n", "      <th>18</th>\n", "      <th></th>\n", "      <th>USD</th>\n", "      <td>2.416000e+10</td>\n", "      <td>2.363600e+10</td>\n", "      <td>5.415800e+10</td>\n", "      <td>5.755200e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>EarningsPerShareBasic</th>\n", "      <th>2</th>\n", "      <th>20</th>\n", "      <th></th>\n", "      <th>USD</th>\n", "      <td>1.530000e+00</td>\n", "      <td>1.530000e+00</td>\n", "      <td>3.420000e+00</td>\n", "      <td>3.720000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>EarningsPerShareDiluted</th>\n", "      <th>2</th>\n", "      <th>21</th>\n", "      <th></th>\n", "      <th>USD</th>\n", "      <td>1.520000e+00</td>\n", "      <td>1.530000e+00</td>\n", "      <td>3.410000e+00</td>\n", "      <td>3.710000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>WeightedAverageNumberOfSharesOutstandingBasic</th>\n", "      <th>2</th>\n", "      <th>23</th>\n", "      <th></th>\n", "      <th>shares</th>\n", "      <td>1.578715e+10</td>\n", "      <td>1.540586e+10</td>\n", "      <td>1.583994e+10</td>\n", "      <td>1.545781e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>WeightedAverageNumberOfDilutedSharesOutstanding</th>\n", "      <th>2</th>\n", "      <th>24</th>\n", "      <th></th>\n", "      <th>shares</th>\n", "      <td>1.584705e+10</td>\n", "      <td>1.546471e+10</td>\n", "      <td>1.590138e+10</td>\n", "      <td>1.552068e+10</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["qtrs                                                                                                                                                 1                           2              \n", "ddate                                                                                                                                         20230331      20240331      20230331      20240331\n", "tag                                                                                         report line segments                  uom                                                           \n", "RevenueFromContractWithCustomerExcludingAssessedTax                                         2      7                              USD     9.483600e+10  9.075300e+10  2.119900e+11  2.103280e+11\n", "                                                                                                        ProductOrService=Product; USD     7.392900e+10  6.688600e+10  1.703170e+11  1.633440e+11\n", "                                                                                                        ProductOrService=Service; USD     2.090700e+10  2.386700e+10  4.167300e+10  4.698400e+10\n", "CostOfGoodsAndServicesSold                                                                  2      8                              USD     5.286000e+10  4.848200e+10  1.196820e+11  1.132020e+11\n", "                                                                                                        ProductOrService=Product; USD     4.679500e+10  4.242400e+10  1.075600e+11  1.008640e+11\n", "                                                                                                        ProductOrService=Service; USD     6.065000e+09  6.058000e+09  1.212200e+10  1.233800e+10\n", "GrossProfit                                                                                 2      9                              USD     4.197600e+10  4.227100e+10  9.230800e+10  9.712600e+10\n", "ResearchAndDevelopmentExpense                                                               2      11                             USD     7.457000e+09  7.903000e+09  1.516600e+10  1.559900e+10\n", "SellingGeneralAndAdministrativeExpense                                                      2      12                             USD     6.201000e+09  6.468000e+09  1.280800e+10  1.325400e+10\n", "OperatingExpenses                                                                           2      13                             USD     1.365800e+10  1.437100e+10  2.797400e+10  2.885300e+10\n", "OperatingIncomeLoss                                                                         2      14                             USD     2.831800e+10  2.790000e+10  6.433400e+10  6.827300e+10\n", "NonoperatingIncomeExpense                                                                   2      15                             USD     6.400000e+07  1.580000e+08 -3.290000e+08  1.080000e+08\n", "IncomeLossFromContinuingOperationsBeforeIncomeTaxesExtraordinaryItemsNoncontrollingInterest 2      16                             USD     2.838200e+10  2.805800e+10  6.400500e+10  6.838100e+10\n", "IncomeTaxExpenseBenefit                                                                     2      17                             USD     4.222000e+09  4.422000e+09  9.847000e+09  1.082900e+10\n", "NetIncomeLoss                                                                               2      18                             USD     2.416000e+10  2.363600e+10  5.415800e+10  5.755200e+10\n", "EarningsPerShareBasic                                                                       2      20                             USD     1.530000e+00  1.530000e+00  3.420000e+00  3.720000e+00\n", "EarningsPerShareDiluted                                                                     2      21                             USD     1.520000e+00  1.530000e+00  3.410000e+00  3.710000e+00\n", "WeightedAverageNumberOfSharesOutstandingBasic                                               2      23                             shares  1.578715e+10  1.540586e+10  1.583994e+10  1.545781e+10\n", "WeightedAverageNumberOfDilutedSharesOutstanding                                             2      24                             shares  1.584705e+10  1.546471e+10  1.590138e+10  1.552068e+10"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["sort_df[sort_df.index.get_level_values('segments').isin(['', 'ProductOrService=Service;', 'ProductOrService=Product;'])]"]}, {"cell_type": "code", "execution_count": null, "id": "764deb35-3dfd-4d37-bc49-cb5de2d9b3a3", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}